package inks.gateway.auth.controller;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import java.util.Date;

// 用于生成访问 Nacos 的 JWT Token
public class JwtTokenGenerator {

    // 必须是Docker启动时设置的环境变量，-e NACOS_AUTH_TOKEN=VGhpc0lzTXlDdXN0b21TZWNyZXRLZXkwMjAyNDA1MTU=
    private static final String SECRET_KEY = "VGhpc0lzTXlDdXN0b21TZWNyZXRLZXkwMjAyNDA1MTU=";

    public static String generateToken() {
        return Jwts.builder()
                .setSubject("nacos")
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + 86400000)) // 1 day expiry
                .signWith(SignatureAlgorithm.HS256, SECRET_KEY)
                .compact();
    }

//    public static void main(String[] args) {
//        String token = generateToken();
//        // 当调用http://192.168.99.21:8848/nacos/v1/ns/instance/list?serviceName=system
//        // 时，请求头中需要添加Authorization: Bearer +空格 + token
//        System.out.println("Generated Token: " + token);
//    }
}
