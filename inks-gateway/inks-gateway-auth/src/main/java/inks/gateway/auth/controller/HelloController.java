package inks.gateway.auth.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping
public class HelloController {
    @Autowired
    private DiscoveryClient discoveryClient;

    @GetMapping("/test")
    public List<ServiceInstance> test() {
        return discoveryClient.getInstances("system");
    }
    @GetMapping("/hello")
    public Object index() {
        return "hello world by gateway auth 1";
    }
}
