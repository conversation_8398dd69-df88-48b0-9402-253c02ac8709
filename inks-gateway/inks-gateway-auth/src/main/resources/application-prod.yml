spring:
  application:
    name: gateway
  cloud:
    nacos:
      discovery:
        server-addr: **************:8848
#        server-addr: **************:8848
#        username: nacos
#        password: inks0820
        ip: **************
        port: 30080
    # gateway的配置
    gateway:
      #路由规则
      routes:
        # system
        - id: system  # 路由的唯一标识
          uri: lb://system  #需要转发的地址   lb: 使用nacos中的本地负载均衡策略服务名
          #断言规则 用于路由规则的匹配
          predicates:
            - Path=/system/**
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        # auth
        - id: auth  # 路由的唯一标识
          uri: lb://auth  #需要转发的地址   lb: 使用nacos中的本地负载均衡策略服务名
          #断言规则 用于路由规则的匹配
          predicates:
            - Path=/auth/**
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        # 销售管理
        - id: sale  # 路由的唯一标识
          uri: lb://sale  #需要转发的地址   lb: 使用nacos中的本地负载均衡策略服务名
          #断言规则 用于路由规则的匹配
          predicates:
            - Path=/sale/**
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        # 采购管理
        - id: buy  # 路由的唯一标识
          uri: lb://buy  #需要转发的地址   lb: 使用nacos中的本地负载均衡策略服务名
          #断言规则 用于路由规则的匹配
          predicates:
            - Path=/buy/**
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        # 仓库管理
        - id: store  # 路由的唯一标识
          uri: lb://store  #需要转发的地址   lb: 使用nacos中的本地负载均衡策略服务名
          #断言规则 用于路由规则的匹配
          predicates:
            - Path=/store/**
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        # 生产管理
        - id: manu  # 路由的唯一标识
          uri: lb://manu  #需要转发的地址   lb: 使用nacos中的本地负载均衡策略服务名
          #断言规则 用于路由规则的匹配
          predicates:
            - Path=/manu/**
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        # 品质管理
        - id: qms  # 路由的唯一标识
          uri: lb://qms  #需要转发的地址   lb: 使用nacos中的本地负载均衡策略服务名
          #断言规则 用于路由规则的匹配
          predicates:
            - Path=/qms/**
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        # 财务管理
        - id: fm  # 路由的唯一标识
          uri: lb://fm  #需要转发的地址   lb: 使用nacos中的本地负载均衡策略服务名
          #断言规则 用于路由规则的匹配
          predicates:
            - Path=/fm/**
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        #  OA管理
        - id: oa  # 路由的唯一标识
          uri: lb://oa  #需要转发的地址   lb: 使用nacos中的本地负载均衡策略服务名
          #断言规则 用于路由规则的匹配
          predicates:
            - Path=/oa/**
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        #  dev管理
        - id: dev  # 路由的唯一标识
          uri: lb://dev  #需要转发的地址   lb: 使用nacos中的本地负载均衡策略服务名
          #断言规则 用于路由规则的匹配
          predicates:
            - Path=/dev/**
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        #  crm管理
        - id: crm  # 路由的唯一标识
          uri: lb://crm  #需要转发的地址   lb: 使用nacos中的本地负载均衡策略服务名
          #断言规则 用于路由规则的匹配
          predicates:
            - Path=/crm/**
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        #  pms管理
        - id: pms  # 路由的唯一标识
          uri: lb://pms  #需要转发的地址   lb: 使用nacos中的本地负载均衡策略服务名
          #断言规则 用于路由规则的匹配
          predicates:
            - Path=/pms/**
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        - id: hrm  # 路由的唯一标识
          uri: lb://hrm  #需要转发的地址   lb: 使用nacos中的本地负载均衡策略服务名
          #断言规则 用于路由规则的匹配
          predicates:
            - Path=/hrm/**
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        #  goods管理
        - id: goods  # 路由的唯一标识
          uri: lb://goods  #需要转发的地址   lb: 使用nacos中的本地负载均衡策略服务名
          #断言规则 用于路由规则的匹配
          predicates:
            - Path=/goods/**
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        #  pms管理
        - id: utils  # 路由的唯一标识
          uri: lb://utils  #需要转发的地址   lb: 使用nacos中的本地负载均衡策略服务名
          #断言规则 用于路由规则的匹配
          predicates:
            - Path=/utils/**
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        #  pms管理
        - id: job  # 路由的唯一标识
          uri: lb://job  #需要转发的地址   lb: 使用nacos中的本地负载均衡策略服务名
          #断言规则 用于路由规则的匹配
          predicates:
            - Path=/job/**
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        - id: file  # 路由的唯一标识
          uri: lb://file  #需要转发的地址   lb: 使用nacos中的本地负载均衡策略服务名
          #断言规则 用于路由规则的匹配
          predicates:
            - Path=/file/**
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        #  oemwh管理
        - id: oemwh  # 路由的唯一标识
          uri: lb://oemwh  #需要转发的地址   lb: 使用nacos中的本地负载均衡策略服务名
          #断言规则 用于路由规则的匹配
          predicates:
            - Path=/oemwh/**
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        # Std补丁类 Dome
        - id: stdDemo
          uri: lb://std
          predicates:
            - Path=/std/**
            - Query=tid,1fd9b97f-1379-48b7-8dbf-4991041277bf
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        # Std补丁类 Dev
        - id: stddev
          uri: lb://std
          predicates:
            - Path=/std/**
            - Query=tid,36c6eeb2-fc01-448c-b8ac-73eb97464972
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        # Pcb补丁类 Dev
        - id: pcbdev
          uri: lb://pcb
          predicates:
            - Path=/pcb/**
            - Query=tid,5a7f9f3a-6dba-4bb6-8057-935cd7878c50
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        # Pcb补丁类 Dome
        - id: pcbDemo
          uri: lb://pcb
          predicates:
            - Path=/pcb/**
            - Query=tid,626e7b0e-5dbc-4789-9d94-1ae8ae40269e
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        # Std檀鑫
        - id: std2009001
          uri: lb://std2009001
          predicates:
            - Path=/std/**
            - Query=tid,06a138b4-5c83-4fce-b431-e7ffaead958e
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        # Std合永
        - id: std1904002
          uri: lb://std1904002
          predicates:
            - Path=/std/**
            - Query=tid,b9f29395-0755-4908-95f6-b8d486d71779
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        # Pcb补丁类 Dome
        - id: pcb2021010
          uri: lb://pcb2021010
          predicates:
            - Path=/pcb/**
            - Query=tid,5599188b-28bc-4dac-8c55-a26e7189ca91
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        # Rous
        - id: rous
          uri: lb://rous
          predicates:
            - Path=/std/**
            - Query=tid,f58550d9-9345-4c22-820b-4fd3cb66ebd0
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        # fyd
        - id: fyd
          uri: lb://stdfyd
          predicates:
            - Path=/std/**
            - Query=tid,16dea0f9-869f-41c1-9e3a-f4c4b279c5aa
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        #  oemym管理
        - id: oemym  # 路由的唯一标识
          uri: lb://oemym  #需要转发的地址   lb: 使用nacos中的本地负载均衡策略服务名
          #断言规则 用于路由规则的匹配
          predicates:
            - Path=/oemym/**
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        #  oemgm管理
        - id: oemgm  # 路由的唯一标识
          uri: lb://oemgm  #需要转发的地址   lb: 使用nacos中的本地负载均衡策略服务名
          #断言规则 用于路由规则的匹配
          predicates:
            - Path=/oemgm/**
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径
        # Rous
        - id: rousdev
          uri: lb://std
          predicates:
            - Path=/std/**
            - Query=tid,724b7f8b-ddf3-46cd-a759-6e1a0bc5ded2
          filters:
            - StripPrefix=1  # 转发之前去掉第一层路径



