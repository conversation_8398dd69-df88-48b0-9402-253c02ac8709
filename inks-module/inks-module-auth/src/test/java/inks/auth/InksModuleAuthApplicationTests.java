package inks.auth;

import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.tea.TeaModel;
import inks.auth.utils.SmsUtis;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
class InksModuleAuthApplicationTests {
    @Resource
    private SmsUtis smsUtis;
    public void sendRegisterNanno(SendSmsRequest sendSmsRequest, String accessKeyId, String accessKeySecret) throws Exception {
        com.aliyun.dysmsapi20170525.Client client = SmsUtis.createClient(accessKeyId,accessKeySecret);
//        SendSmsRequest sendSmsRequest = new SendSmsRequest()
//                .setPhoneNumbers(phone)
//                .setSignName("应凯软件")
//                .setTemplateCode("SMS_223541873")
//                .setTemplateParam("{\"code\":\""+Captcha+"\"}");
        SendSmsResponse resp = client.sendSms(sendSmsRequest);
        com.aliyun.teaconsole.Client.log(com.aliyun.teautil.Common.toJSONString(TeaModel.buildMap(resp)));
    }
    @Test
    void smsUtis() throws Exception {
        String phone = "17340526857";
        String Captcha = "12138";
                SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setPhoneNumbers(phone)
                .setSignName("聪和东户外运动")
                .setTemplateCode("SMS_294010255")
                .setTemplateParam("{\"code\":\""+Captcha+"\"}");
        smsUtis.sendRegisterNanno( sendSmsRequest,"LTAI5tGejtfNfs9d7vdLck1j", "******************************");
        System.out.println("发送成功");
    }

}
