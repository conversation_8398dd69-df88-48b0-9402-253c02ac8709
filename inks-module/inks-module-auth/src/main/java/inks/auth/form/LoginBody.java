package inks.auth.form;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 用户登录对象
 * 
 * <AUTHOR>
 */
public class LoginBody
{
    /**
     * 用户名
     */
    private String UserName;

    /**
     * 用户密码
     */
    private String Password;

    /**
     * 验证码
     */
    private String Code;

    // 租户id
    private String tid;

    /**
     * 唯一标识
     */
    private String UUid = "";
    @JsonProperty("UserName")
    public String getUserName() {
        return UserName;
    }

    public void setUserName(String userName) {
        UserName = userName;
    }
    @JsonProperty("Password")
    public String getPassword() {
        return Password;
    }

    public void setPassword(String password) {
        Password = password;
    }
    @JsonProperty("Code")
    public String getCode() {
        return Code;
    }

    public void setCode(String code) {
        Code = code;
    }
    @JsonProperty("UUid")
    public String getUUid() {
        return UUid;
    }

    public void setUUid(String UUid) {
        this.UUid = UUid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }
}
