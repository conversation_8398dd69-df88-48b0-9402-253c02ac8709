package inks.auth.mapper;

import inks.auth.domain.PiTenant;
import inks.common.core.domain.TenantInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashSet;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: song
 * @Date: 2021/09/16/9:47
 * @Description:
 */
@Mapper
public interface PiTenantMapper {
    List<PiTenant> getPageList(String id);
    Integer getIsAdmin(@Param("key") String key,@Param("tid") String tid);
    //根据账号查询Userid
    String getUserId(String key);
     // 获得租户信息
    TenantInfo getTenantInfo(@Param("key") String key);
    // 获得租户名称
    String getTenantName(@Param("key") String key);

    //通过FunctionCode和tid查询可设置的所有权限
    HashSet<String> getFunctionPermsByFunctionCode(String functionCode, String tid);
}
