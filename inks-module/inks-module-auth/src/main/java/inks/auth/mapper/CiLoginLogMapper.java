package inks.auth.mapper;

import inks.auth.domain.CiLoginLogEntity;
import org.apache.ibatis.annotations.Mapper;

/**
 * 登录日志Mapper接口
 *
 * <AUTHOR>
 * @date 2021-10-06
 */
@Mapper
public interface CiLoginLogMapper {
    /**
     * 查询登录日志
     *
     * @param id 登录日志主键
     * @return 登录日志
     */
    public CiLoginLogEntity getEntity(String id);


    /**
     * 新增登录日志
     *
     * @param ciLoginLog 登录日志
     * @return 结果
     */
    public int insert(CiLoginLogEntity ciLoginLog);

    /**
     * 修改登录日志
     *
     * @param ciLoginLog 登录日志
     * @return 结果
     */
    public int update(CiLoginLogEntity ciLoginLog);

    /**
     * 删除登录日志
     *
     * @param id 登录日志主键
     * @return 结果
     */
    public int delete(String id);

    /**
     * 批量删除登录日志
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCiLoginLogByIds(String[] ids);
}
