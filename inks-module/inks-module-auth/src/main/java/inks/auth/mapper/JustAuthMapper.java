package inks.auth.mapper;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.TenantInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface JustAuthMapper {
    LoginUser getLoginUserByJust( @Param("userid") String userid,@Param("type") String type,@Param("tid") String tid);
    TenantInfo getTenantInfo(@Param("tid") String tid);

    String getUseridByEncrypt(String useridEncrypt, String type, String tid);

    LoginUser getLoginUserByJustEncrypt(String useridEncrypt, String type, String tid);
}
