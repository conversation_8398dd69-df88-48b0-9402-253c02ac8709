package inks.auth.mapper;

import inks.auth.domain.PiTenant;
import inks.auth.domain.PirmsuserPojo;
import inks.common.core.domain.JustauthPojo;
import inks.common.core.domain.LoginUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface SysRmsLogingMapper {
    LoginUser login(@Param("userName") String userName, @Param("passWord") String passWord);

    Integer count(String userName);

    List<PirmsuserPojo> getListByOpenid(String openid);

    List<PiTenant> getListInTids(@Param("tidList") List<String> tidList);

    List<PirmsuserPojo> getListByUserid(String userid);

    PirmsuserPojo getRmsUserByUserid(@Param("userid")String userid, @Param("tid")String tid);

    void bindOpenid(String userid, String openid, String tid);

    JustauthPojo getPiRmsJustAuthByUseridAndAuthType(String userid, String authType);

    void insertPiRmsJustAuth(JustauthPojo justauthPojo);
}
