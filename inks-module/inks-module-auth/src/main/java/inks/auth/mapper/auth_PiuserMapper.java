package inks.auth.mapper;

import inks.auth.domain.Piadmin;
import inks.auth.domain.Piuser;
import inks.common.core.domain.LoginUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: song
 * @Date: 2021/09/04/10:46
 * @Description:
 */
@Mapper
public interface auth_PiuserMapper {
    int getPruserCount(String userName);
    LoginUser getUserByName(String userName);
    LoginUser login(@Param("userName") String userName, @Param("passWord") String passWord);
    LoginUser loginByOpenid(String openid);
    void inserPiuser(Piuser piuser);

    Piadmin getEntityByOpenid(String openid);

    void bindOpenid(String userid, String openid);
}
