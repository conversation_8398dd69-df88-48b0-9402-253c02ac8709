package inks.auth.mapper;

import inks.auth.domain.PiTenant;
import inks.auth.domain.PidmsuserPojo;
import inks.common.core.domain.LoginUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysDmsLogingMapper {
    LoginUser login(@Param("userName") String userName, @Param("passWord") String passWord);

    Integer count(String userName);

    List<PidmsuserPojo> getListByOpenid(String openid);

    List<PiTenant> getListInTids(@Param("tidList") List<String> tidList);

    List<PidmsuserPojo> getListByUserid(String userid);

    PidmsuserPojo getDmsUserByUserid(@Param("userid")String userid, @Param("tid")String tid);
}
