package inks.auth.mapper;

import inks.auth.domain.PiTenant;
import inks.auth.domain.PiscmuserPojo;
import inks.common.core.domain.LoginUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysScmLogingMapper {
    LoginUser login(@Param("userName") String userName, @Param("passWord") String passWord);

    Integer count(String userName);

    List<PiscmuserPojo> getListByOpenid(String openid);

    List<PiTenant> getListInTids(@Param("tidList") List<String> tidList);

    List<PiscmuserPojo> getListByUserid(String userid);

    PiscmuserPojo getScmUserByUserid(@Param("userid")String userid, @Param("tid")String tid);
}
