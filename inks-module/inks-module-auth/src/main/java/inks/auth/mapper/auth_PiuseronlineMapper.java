package inks.auth.mapper;

import inks.auth.domain.PiuseronlinePojo;
import inks.common.core.domain.QueryParam;
import inks.auth.domain.PiuseronlineEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 用户在线(Piuseronline)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-13 13:14:43
 */
@Mapper
public interface auth_PiuseronlineMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiuseronlinePojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PiuseronlinePojo> getPageList(QueryParam queryParam);


    
    /**
     * 新增数据
     *
     * @param piuseronlineEntity 实例对象
     * @return 影响行数
     */
    int insert(PiuseronlineEntity piuseronlineEntity);

    
    /**
     * 修改数据
     *
     * @param piuseronlineEntity 实例对象
     * @return 影响行数
     */
    int update(PiuseronlineEntity piuseronlineEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    PiuseronlinePojo getEntityByUserid(@Param("userid") String userid, @Param("terminalType") int terminalType, @Param("tid") String tenantid);
}

