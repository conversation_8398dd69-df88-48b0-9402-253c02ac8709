package inks.auth.mapper;

import inks.auth.domain.PiuserLogin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: song
 * @Date: 2021/09/15/15:33
 * @Description:
 */
@Mapper
public interface PiuserLoginMapper {
    void insertPiuserLogin(PiuserLogin piuserLogin);
    void updatePwd(@Param("key") String key,@Param("pwd") String pwd);
}
