package inks.auth.mapper;

import inks.auth.domain.PiauthcodePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 授权码(Piauthcode)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-22 12:44:38
 */
@Mapper
public interface auth_PiauthcodeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param code 主键
     * @return 实例对象
     */
    PiauthcodePojo getEntityByEnabled(@Param("code") String code);

}

