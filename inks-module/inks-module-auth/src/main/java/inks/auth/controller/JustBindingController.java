package inks.auth.controller;

import com.alibaba.fastjson.JSON;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.JustauthPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.security.service.TokenService;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthDingTalkRequest;
import me.zhyd.oauth.request.AuthRequest;
import me.zhyd.oauth.request.AuthWeChatEnterpriseQrcodeRequest;
import me.zhyd.oauth.utils.AuthStateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

//三方登录账号绑定
@RestController
@RequestMapping("/justbinding")
public class JustBindingController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(JustBindingController.class);
    @Resource
    private TokenService tokenService;
    @Resource
    private SystemFeignService systemFeignService;
    @Value("${inks.justauth.api}")
    private String justauthurl;

    @RequestMapping("/render/{type}/{token}")
    public void render(@PathVariable("type") String type,
                       @PathVariable("token") String token,
                       HttpServletResponse response) throws IOException {
        AuthRequest authRequest = getAuthRequest(type, token);
        response.sendRedirect(authRequest.authorize(AuthStateUtils.createState()));
    }

    @RequestMapping("/callback/{type}/{token}")
    public Object callback(@PathVariable("type") String type,
                           @PathVariable("token") String token,
                           AuthCallback callback,
                           HttpServletResponse servletResponse) {
        try {
            AuthRequest authRequest = getAuthRequest(type, token);
            AuthResponse<AuthUser> response = authRequest.login(callback);
            if (response.getCode() == 2000) {

                LoginUser loginUser = this.tokenService.getLoginUser(token);
                if (loginUser != null) {//获取用户成功
                    //判断是否已绑定
                    R<List<JustauthPojo>> r = this.systemFeignService.getListByUnionid(response.getData().getUuid());
                    if (r.getData().size() > 0) {
                        String html = "<h1>账号已有绑定信息,请先解绑</h1>";
                        servletResponse.setContentType("text/html;character=UTF-8");
                        servletResponse.setHeader("Content-type", "text/html;charset=UTF-8");
                        servletResponse.setCharacterEncoding("UTF-8");
                        servletResponse.getWriter().write(html);
                        return null;
                    }

                    JustauthPojo justauthPojo = new JustauthPojo();
                    justauthPojo.setUserid(loginUser.getUserid());
                    justauthPojo.setUsername(loginUser.getUsername());
                    justauthPojo.setRealname(loginUser.getRealname());
                    justauthPojo.setNickname(response.getData().getNickname());
                    switch (type.toLowerCase()) {
                        case "dingtalk":
                            justauthPojo.setAuthtype("ding");
                            break;
                        case "wechat_enterprise":
                            justauthPojo.setAuthtype("wxe");
                            break;
                    }
                    justauthPojo.setAuthavatar(response.getData().getAvatar());
                    justauthPojo.setUnionid(response.getData().getUuid());
                    justauthPojo.setTenantid(loginUser.getTenantid());
                    this.systemFeignService.createJust(JSON.toJSONString(justauthPojo), token);
                    String html = "<h1>账号成功,关闭</h1>";
                    servletResponse.setContentType("text/html;character=UTF-8");
                    servletResponse.setHeader("Content-type", "text/html;charset=UTF-8");
                    servletResponse.setCharacterEncoding("UTF-8");
                    servletResponse.getWriter().write(html);
                    return null;
                } else { //获取用户失败 token过期或不存在
                    String html = "<h1>登录过期或者账号不存在</h1>";
                    servletResponse.setContentType("text/html;character=UTF-8");
                    servletResponse.setHeader("Content-type", "text/html;charset=UTF-8");
                    servletResponse.setCharacterEncoding("UTF-8");
                    servletResponse.getWriter().write(html);
                    return null;
                }

            } else {
                return R.fail(JSON.toJSONString(response.getData()));
            }
        } catch (Exception ex) {
            return R.fail(ex.getMessage());
        }

    }


    private AuthRequest getAuthRequest(String type, String token) {
        AuthRequest authRequest = null;
        switch (type.toLowerCase()) {
            case "dingtalk":
                authRequest = new AuthDingTalkRequest(AuthConfig.builder()
                        .clientId("dingoa43wwh1kvqgpvangn")
                        .clientSecret("1eGSxvhF6kCTruk66q4bzC3zZiDz7eVGBCCD9AMwHU_8Ja0NdrNiRcWDrwOOkpEN")
                        .redirectUri(justauthurl + "/auth/justbinding/callback/dingtalk/" + token)
                        .build());
                break;
            case "wechat_enterprise":
                authRequest = new AuthWeChatEnterpriseQrcodeRequest(AuthConfig.builder()
                        .clientId("ww2373931e70ddd7ef")
                        .clientSecret("fE5vO4mfI8OwBEVeX3h66K15mpe9f4AaG0yIDlF5ZmI")
                        .redirectUri(justauthurl + "/auth/justbinding/callback/wechat_enterprise" + token)
                        .agentId("1000013")
                        .build());
                break;
        }
        return authRequest;
    }


}
