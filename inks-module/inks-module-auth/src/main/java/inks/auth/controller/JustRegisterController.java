package inks.auth.controller; /*
 *功能描述
 * <AUTHOR>
 * @date  2022/1/5
 * @param 钉钉第三方登录
 */

import com.alibaba.fastjson.JSON;
import inks.api.feign.SystemFeignService;
import inks.common.core.domain.JustauthPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.service.TokenService;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthDingTalkRequest;
import me.zhyd.oauth.request.AuthRequest;
import me.zhyd.oauth.request.AuthWeChatEnterpriseQrcodeRequest;
import me.zhyd.oauth.utils.AuthStateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/justregister")
public class JustRegisterController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(JustRegisterController.class);
    @Resource
    private TokenService tokenService;
    @Resource
    private SystemFeignService systemFeignService;
    @Value("${inks.justauth.api}")
    private String justauthurl;

    @RequestMapping("/render/{type}")
    public void renderAuth(@PathVariable("type") String type, HttpServletResponse response) throws IOException {
        AuthRequest authRequest = getAuthRequest(type);
        response.sendRedirect(authRequest.authorize(AuthStateUtils.createState()));

    }

    @RequestMapping("/callback/{type}")
    public R<Map<String, Object>> login(@PathVariable("type") String type, AuthCallback callback) {
        try {
            AuthRequest authRequest = getAuthRequest(type);
            AuthResponse<AuthUser> response = authRequest.login(callback);
            if (response.getCode() == 2000) {
                
                LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
                R<List<JustauthPojo>> rinfo = this.systemFeignService.getListByUnionid(response.getData().getUuid());
                if (rinfo.getCode() == 200) {
                    if (rinfo.getData().size() > 0) {

                        loginUser.setUserid(rinfo.getData().get(0).getUserid());
                        loginUser.setRealname(response.getData().getUsername());
                        Map<String, Object> map = this.tokenService.createToken(loginUser);
                        map.put("Unionid", response.getData().getUuid());
                        map.put("justauth", response.getData());
                        return R.ok(map);
                    } else {

                        Map<String, Object> map = new HashMap<>();
                        map.put("access_token", loginUser.getToken());
                        map.put("Unionid", response.getData().getUuid());
                        map.put("justauth", response.getData());
                        return R.ok(map);
                    }
                } else {
                    return R.fail(rinfo.getMsg());
                }
            } else {
                return R.fail(JSON.toJSONString(response.getData()));
            }
        } catch (Exception ex) {
            return R.fail(ex.getMessage());
        }

    }


    private AuthRequest getAuthRequest(String type) {
        AuthRequest authRequest = null;
        switch (type.toLowerCase()) {
            case "dingtalk":
                authRequest = new AuthDingTalkRequest(AuthConfig.builder()
                        .clientId("dingoa43wwh1kvqgpvangn")
                        .clientSecret("1eGSxvhF6kCTruk66q4bzC3zZiDz7eVGBCCD9AMwHU_8Ja0NdrNiRcWDrwOOkpEN")
                        .redirectUri(justauthurl + "/auth/justauth/callback/dingtalk")
                        .build());
                break;
            case "wechat_enterprise":
                authRequest = new AuthWeChatEnterpriseQrcodeRequest(AuthConfig.builder()
                        .clientId("ww2373931e70ddd7ef")
                        .clientSecret("fE5vO4mfI8OwBEVeX3h66K15mpe9f4AaG0yIDlF5ZmI")
                        .redirectUri(justauthurl + "/auth/justauth/callback/wechat_enterprise")
                        .agentId("1000013")
                        .build());
                break;
        }
        return authRequest;
    }


}
