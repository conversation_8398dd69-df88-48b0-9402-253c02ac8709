package inks.auth.controller;

import inks.auth.service.PhoneSmsService;
import inks.auth.service.RegisterCaptchaService;
import inks.common.core.constant.Constants;
import inks.common.core.domain.R;
import inks.common.redis.service.RedisService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 用于手机或邮箱发验证码
 *
 * @Author: Eric
 * @Date: 2021/09/15/14:50
 * @Description:
 */

@RestController
@RequestMapping("/captcha")
public class MsgCaptchaController {
    @Value("${spring.alimsg.accessKeyId:LTAI5tL76QGhNx5eSkzkLnbv}")
    private String accessKeyId;
    @Value("${spring.alimsg.accessKeySecret:******************************}")
    private String accessKeySecret;

    @Resource
    private RegisterCaptchaService registerCaptchaService;
    @Resource
    private RedisService redisService;
    @Resource
    private PhoneSmsService phoneMsgService;


    @ApiOperation(value = "手机验证码", notes = "手机验证码", produces = "application/json")
    @RequestMapping(value = "/phoneCaptcha", method = RequestMethod.GET)
    public R phoneRegisterCaptcha(String phone) {
        try {
            this.phoneMsgService.PhoneCaptcha(phone, accessKeyId, accessKeySecret);
            return R.ok(phone, "验证码获取成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "邮箱验证码", notes = "邮箱验证码", produces = "application/json")
    @RequestMapping(value = "/emailCaptcha", method = RequestMethod.GET)
    public R emailRegisterCaptcha(String email) {
        try {
            this.registerCaptchaService.emaiCaptcha(email);
            return R.ok(email, "验证码获取成功");
        } catch (Exception e) {
            return R.fail("", e.getMessage());
        }
    }

    @ApiOperation(value = "比对验证码", notes = "比对验证码", produces = "application/json")
    @RequestMapping(value = "/checkCaptcha", method = RequestMethod.GET)
    public R checkCaptcha(String username, String code) {
        String verifyKey = Constants.CAPTCHA_CODE_KEY + username;
        String captcha = this.redisService.getCacheObject(verifyKey);
        if (captcha == null) {
            return R.fail(username, "验证码过期");
        } else if (!captcha.equals(code)) {
            return R.fail(code, "验证码错误");
        } else {
            this.redisService.deleteObject(verifyKey);
            return R.ok(username, "验证通过");
        }
    }
}
