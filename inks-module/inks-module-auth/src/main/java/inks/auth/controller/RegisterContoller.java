package inks.auth.controller;

import com.alibaba.fastjson.JSON;
import inks.auth.form.RegisterBody;
import inks.auth.service.PhoneSmsService;
import inks.auth.service.RegisterCaptchaService;
import inks.auth.service.SysLoginService;
import inks.common.core.constant.Constants;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: song
 * @Date: 2021/09/15/14:50
 * @Description:
 */
@RestController
@RequestMapping("")
public class RegisterContoller {
    @Value("${spring.alimsg.accessKeyId:LTAI5tL76QGhNx5eSkzkLnbv}")
    private String accessKeyId;
    @Value("${spring.alimsg.accessKeySecret:******************************}")
    private String accessKeySecret;
    @Resource
    private SysLoginService service;
    @Resource
    private JavaMailSenderImpl mailSender;
    @Resource
    private RegisterCaptchaService registerCaptchaService;
    @Resource
    private RedisService redisService;
    @Resource
    private PhoneSmsService phoneMsgService;
    @Autowired
    private TokenService tokenService;

    @ApiOperation(value = "用户注册", notes = "用户注册", produces = "application/json")
    @RequestMapping(value = "/register", method = RequestMethod.POST)
    public R<Map<String, Object>> register(@RequestBody String json, HttpServletRequest request) {
        try {
            RegisterBody registerBody = JSON.parseObject(json, RegisterBody.class);
            LoginUser l = service.register(registerBody, request);
            Map<String, Object> map = tokenService.createToken(l);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "检查用户名是否存在", notes = "检查用户名是否存在", produces = "application/json")
    @RequestMapping(value = "/inspect", method = RequestMethod.GET)
    public R inspect(String userName) {
        try {
            service.inspect(userName);
            return R.ok("");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "邮箱验证码", notes = "邮箱验证码", produces = "application/json")
    @RequestMapping(value = "/emailRegisterCaptcha", method = RequestMethod.GET)
    public R emailRegisterCaptcha(String email) {
        try {
            registerCaptchaService.emaiRegisterCaptcha(email);
            return R.ok("验证码获取成功");
        } catch (Exception e) {
            return R.fail("验证码获取失败");
        }
    }

    @ApiOperation(value = "手机验证码", notes = "手机验证码", produces = "application/json")
    @RequestMapping(value = "/phoneRegisterCaptcha", method = RequestMethod.GET)
    public R phoneRegisterCaptcha(String phone) {
        try {
            phoneMsgService.PhoneRegister(phone, accessKeyId, accessKeySecret);
            return R.ok("验证码获取成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "验证验证码", notes = "验证验证码", produces = "application/json")
    @RequestMapping(value = "/checkCaptcha", method = RequestMethod.GET)
    public R checkCaptcha(String username, String code) {
        String verifyKey = Constants.EMAIL_CODE_KEY + username;
        String captcha = redisService.getCacheObject(verifyKey);
        if (captcha == null || !captcha.equals(code)) {
            return R.fail("验证码错误");
        }
        redisService.deleteObject(verifyKey);
        return R.ok("");
    }
}
