package inks.auth.controller; /*
 *功能描述
 * <AUTHOR>
 * @date  2022/1/5
 * @param 钉钉第三方登录
 */

import com.alibaba.fastjson.JSON;
import inks.api.feign.SystemFeignService;
import inks.auth.service.JustAuthService;
import inks.auth.service.SysLoginService;
import inks.common.core.domain.JustauthPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthDingTalkRequest;
import me.zhyd.oauth.request.AuthRequest;
import me.zhyd.oauth.request.AuthWeChatEnterpriseQrcodeRequest;
import me.zhyd.oauth.utils.AuthStateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/justauth")
public class JustAuthController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(JustAuthController.class);
    @Resource
    private TokenService tokenService;
    @Resource
    private JustAuthService justAuthService;
    @Resource
    private SystemFeignService systemFeignService;
    @Resource
    private SysLoginService sysLoginService;
    @Resource
    private RedisService redisService;
    @Value("${inks.justauth.api}")
    private String justauthurl;

    @RequestMapping("/render/{type}/{url}")
    public void renderAuth(@PathVariable("type") String type,
                           @PathVariable("url") String url,
                           HttpServletResponse response) throws IOException {
        logger.info(url);
        AuthRequest authRequest = getAuthRequest(type, url);
        response.sendRedirect(authRequest.authorize(AuthStateUtils.createState()));

    }


    @RequestMapping("/callback/{type}/{url}")
    public Object login(@PathVariable("type") String type,
                        @PathVariable("url") String url,
                        AuthCallback callback,
                        HttpServletResponse response2) throws Exception {
        try {
            AuthRequest authRequest = getAuthRequest(type, url);
            AuthResponse<AuthUser> response = authRequest.login(callback);

            if (response.getCode() == 2000) {

                R<List<JustauthPojo>> rinfo = this.systemFeignService.getListByUnionid(response.getData().getUuid());
                if (rinfo.getCode() == 200) {
                    if (rinfo.getData().size() > 0) {  //查询到用户绑定信息
                        LoginUser loginUser = new LoginUser();
                        loginUser.setUserid(rinfo.getData().get(0).getUserid());
                        loginUser.setRealname(rinfo.getData().get(0).getUsername());
                        Map<String, Object> map = this.tokenService.createToken(loginUser);
                        map.put("Unionid", response.getData().getUuid());
                        map.put("justauth", response.getData());
                        String loginurl = "http://" + url + "/#/login?key=" + map.get("access_token");
                        response2.sendRedirect(loginurl);
                        return R.ok(map);
                    } else {
                        // 未绑定钉钉账号
                        String loginurl = "<h1>未绑定钉钉账号，点击前往<a href=\"" + "http://" + url + "/login" + "\">绑定</a></h1>";
                        response2.setContentType("text/html;character=UTF-8");
                        response2.setHeader("Content-type", "text/html;charset=UTF-8");
                        response2.setCharacterEncoding("UTF-8");
                        response2.getWriter().write(loginurl);
                        return null;
                    }
                } else {
                    return R.fail(rinfo.getMsg());
                }
            } else {
                return R.fail(JSON.toJSONString(response.getData()));
            }
        } catch (Exception ex) {
            return R.fail(ex.getMessage());
        }
    }


    private AuthRequest getAuthRequest(String type, String url) {
        AuthRequest authRequest = null;
        switch (type.toLowerCase()) {
            case "dingtalk":
                authRequest = new AuthDingTalkRequest(AuthConfig.builder()
                        .clientId("dingoa43wwh1kvqgpvangn")
                        .clientSecret("1eGSxvhF6kCTruk66q4bzC3zZiDz7eVGBCCD9AMwHU_8Ja0NdrNiRcWDrwOOkpEN")
                        .redirectUri(justauthurl + "/auth/justauth/callback/dingtalk/" + url)
                        .build());
                break;
            case "wechat_enterprise":
                authRequest = new AuthWeChatEnterpriseQrcodeRequest(AuthConfig.builder()
                        .clientId("ww2373931e70ddd7ef")
                        .clientSecret("fE5vO4mfI8OwBEVeX3h66K15mpe9f4AaG0yIDlF5ZmI")
                        .redirectUri(justauthurl + "/auth/justauth/callback/wechat_enterprise" + url)
                        .agentId("1000013")
                        .build());
                break;
        }
        return authRequest;
    }


}
