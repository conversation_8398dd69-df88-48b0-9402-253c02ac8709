package inks.auth.controller;


import inks.api.feign.SystemFeignService;
import inks.auth.domain.CiLoginLogEntity;
import inks.auth.domain.PiTenant;
import inks.auth.domain.PirmsuserPojo;
import inks.auth.form.LoginBody;
import inks.auth.mapper.CiLoginLogMapper;
import inks.auth.mapper.PiTenantMapper;
import inks.auth.mapper.SysRmsLogingMapper;
import inks.auth.service.SysLoginService;
import inks.auth.service.SysRmsLogingService;
import inks.common.core.constant.InksConstants;
import inks.common.core.domain.DeptinfoPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.domain.TenantInfo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.StringUtils;
import inks.common.security.service.TokenService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.ParseException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.*;


@RestController
@RequestMapping("/rms")
public class RmsTokenController {
    @Autowired
    private TokenService tokenService;
    @Resource
    private SysRmsLogingService sysRmsLogingService;

    @Resource
    private SysLoginService sysLoginService;
    @Resource
    private CiLoginLogMapper ciLoginLogMapper;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;
    @Resource
    private SysRmsLogingMapper sysRmsLogingMapper;
    @Resource
    private PiTenantMapper piTenantMapper;

    @ApiOperation(value = "rms用户登陆", notes = "用户登陆", produces = "application/json")
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public R<Object> login(@RequestBody LoginBody loginBody, HttpServletRequest request) {
        try {
            LoginUser loginUser = sysRmsLogingService.login(loginBody.getUserName(), loginBody.getPassword(), request);
            Map<String, Object> map = tokenService.createToken(loginUser);
            return R.ok(map.get("loginuser"));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @DeleteMapping("logout")
    public R logout(HttpServletRequest request) throws ParseException {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser)) {
            String username = loginUser.getUsername();
            // 删除用户缓存记录
            tokenService.delLoginUser(loginUser.getToken());
            sysRmsLogingService.record(loginUser, request);
        }
        return R.ok("");
    }

    @ApiOperation(value = "根据当前用户获得租户关系表", notes = "根据当前用户获得租户关系表", produces = "application/json")
    @RequestMapping(value = "/getListByUser", method = RequestMethod.GET)
    public R<List<PiTenant>> getListByUser() {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            List<PirmsuserPojo> pirmsuserPojoList = sysRmsLogingMapper.getListByUserid(loginUser.getUserid());
            List<String> tidList = pirmsuserPojoList.stream()
                    .map(PirmsuserPojo::getTenantid)
                    .collect(Collectors.toList());
            List<PiTenant> piTenantList = sysRmsLogingMapper.getListInTids(tidList);
            return R.ok(piTenantList);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "选择租户后生成新token", notes = "选择租户后生成新token", produces = "application/json")
    @RequestMapping(value = "/token", method = RequestMethod.POST)
    public R<Map<String, Object>> token(@RequestBody String key) {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            loginUser.setTenantid(key);
//            //加入租户信息
            TenantInfo tenantInfo = this.sysLoginService.getTenantInfo(key);
            tenantInfo.setIsdeptadmin(0);
            // 加入架构信息
            List<DeptinfoPojo> lstdept = null;
            PirmsuserPojo pirmsuserPojo = sysRmsLogingMapper.getRmsUserByUserid(loginUser.getUserid(), loginUser.getTenantid());
//            R<Map<String, Object>> rinfo = this.systemFeignService.getTenantRmsUserByUser(loginUser.getUserid(), loginUser.getTenantid());
            if (pirmsuserPojo != null) {
//                String groupids = rinfo.getData().get("groupids") == null ? "" : rinfo.getData().get("groupids").toString();
                String groupids = pirmsuserPojo.getGroupids();
                //给groupids加上单引号  由 aa,ss,dd 改为'aa','ss','dd'
                groupids = Arrays.stream(groupids.split(","))
                        .map(groupId -> "'" + groupId + "'")
                        .collect(Collectors.joining(","));
                loginUser.setGroupids(groupids);
                //int isadmin = rinfo.getData().get("isadmin") == null ? 0 : Integer.parseInt(rinfo.getData().get("isadmin").toString());
                //loginUser.setIsadmin(isadmin);
            }
            loginUser.setTenantinfo(tenantInfo);
            // 更新登录日志的tid
            CiLoginLogEntity ciLoginLogEntity = new CiLoginLogEntity();
            ciLoginLogEntity.setTenantid(key);
            ciLoginLogEntity.setTenantName(piTenantMapper.getTenantName(key));
            ciLoginLogEntity.setId(loginUser.getLogid());
            ciLoginLogMapper.update(ciLoginLogEntity);
            // 建立新的loginUser
            Map<String, Object> map = tokenService.createToken(loginUser);
            //加入权限编码
            R<Map<String, Object>> r = systemFeignService.updateToken(loginUser.getToken());
            if (r.getCode() == 200) map = r.getData();
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //------------------------------------------20240520: OEM公众号扫码登录-----------------------------------------

    @ApiOperation(value = "通过openidToken登录(oam服务redis中有键值对openid_token:openid)", notes = "openidToken登录", produces = "application/json")
    @RequestMapping(value = "/loginByOpenidToken", method = RequestMethod.POST)
    public R<Object> loginByOpenidToken(String openidToken, HttpServletRequest request) {
        try {
            String openidFromOam = AdminTokenController.getOpenidFromOam(openidToken);
            LoginUser loginUser = sysRmsLogingService.scanLogin(openidFromOam, null, request);
            loginUser.setTenantid(InksConstants.DEFAULT_TENANT);  //加入默认租户；
            Map<String, Object> map = tokenService.createAdminToken(loginUser);
            return R.ok(map.get("loginuser"));
        } catch (BaseBusinessException | IOException e) {
            return R.fail(e.getMessage());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "绑定/解绑openid: 传入的是openidToken,后端转为openid后存入JustAuth表中的AuthUuid字段 解绑不传openidToken", notes = "修改用户", produces = "application/json")
    @RequestMapping(value = "/bindOpenid", method = RequestMethod.GET)
    public R<PirmsuserPojo> bindOpenid(@RequestParam(required = false) String openidToken) {
            LoginUser loginUser = tokenService.getLoginUser();
        try {
            String openid = "";
            if (isNotBlank(openidToken)) {
                // 从oam服务中获取openid
                openid = AdminTokenController.getOpenidFromOam(openidToken);
            }
            // 绑定Adminid(userid)和openid
            return R.ok(this.sysRmsLogingService.bindOpenid(loginUser, openid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
