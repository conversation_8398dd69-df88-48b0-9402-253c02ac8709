package inks.auth.controller;

import inks.auth.service.PersonalinfoService;
import inks.common.core.domain.R;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@RestController
@RequestMapping("/info")
public class PersonalinfoController {
    @Resource
    private PersonalinfoService personalinfoService;

    @ApiOperation(value = "个人信息", notes = "个人信息", produces = "application/json")
    @RequestMapping(value = "/getInfo", method = RequestMethod.POST)
    public R<Map<String, Object>> getInfo(String key) {
        try {
            return R.ok(personalinfoService.getInfo(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
