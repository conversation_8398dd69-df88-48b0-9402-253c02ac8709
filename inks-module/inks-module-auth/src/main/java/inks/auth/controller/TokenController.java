package inks.auth.controller;


import cn.hutool.core.net.NetUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import eu.bitwalker.useragentutils.UserAgent;
import inks.api.feign.SystemFeignService;
import inks.auth.domain.CiLoginLogEntity;
import inks.auth.domain.PiTenant;
import inks.auth.domain.PiauthcodePojo;
import inks.auth.domain.PiuseronlinePojo;
import inks.auth.form.LoginBody;
import inks.auth.mapper.CiLoginLogMapper;
import inks.auth.mapper.PiTenantMapper;
import inks.auth.service.*;
import inks.common.core.domain.DeptinfoPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.domain.TenantInfo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.*;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import io.swagger.annotations.ApiOperation;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static inks.common.core.constant.CacheConstants.AUTHCODE_KEY;

/**
 * token 控制
 *
 * <AUTHOR>
 */
@RestController
public class TokenController {
    public static final String RTKEY_PRE_REDIS = "rtkey:";
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(WxeLoginController.class);
    @Value("${spring.alimsg.accessKeyId:LTAI5tL76QGhNx5eSkzkLnbv}")
    private String accessKeyId;
    @Value("${spring.alimsg.accessKeySecret:******************************}")
    private String accessKeySecret;
    @Autowired
    private TokenService tokenService;
    @Resource
    private JavaMailSenderImpl mailSender;
    @Resource
    private SysLoginService service;
    @Resource
    private PhoneSmsService phoneMsgService;
    @Resource
    private RegisterCaptchaService registerCaptchaService;
    @Resource
    private auth_PiuseronlineService authPiuseronlineService;
    /**
     * 引用FeignService服务
     */
    @Resource
    private SystemFeignService systemFeignService;
    @Resource
    private RedisService redisService;
    @Resource
    private RedisTemplate redisTemplate;
    /**
     * 服务对象
     */
    @Resource
    private auth_PiauthcodeService authPiauthcodeService;
    @Resource
    private CiLoginLogMapper ciLoginLogMapper;
    @Resource
    private PiTenantMapper piTenantMapper;
    //默认公网应凯科技:http://oam.inksyun.com [wx7850d75f765d0dce]
    @Value("${inks.oam.api:http://oam.inksyun.com}")
    private String OAM_API;
    @Value("${inks.oam.appid:wx7850d75f765d0dce}")
    private String OAM_APPID;

    @ApiOperation(value = "用户登陆", notes = "用户登陆", produces = "application/json")
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public R<Map<String, Object>> login(@RequestBody LoginBody loginBody, HttpServletRequest request) {
        try {
            LoginUser loginUser = service.login(loginBody.getUserName(), loginBody.getPassword(), request);
            Map<String, Object> map = tokenService.createToken(loginUser);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "通过openidToken登录(oam服务redis中有键值对openid_token:openid)", notes = "openidToken登录", produces = "application/json")
    @RequestMapping(value = "/loginByOpenidToken", method = RequestMethod.POST)
    public R<Map<String, Object>> loginByOpenidToken(String openidToken, HttpServletRequest request) {
        try {
            String openid_FromOam = getOpenidFromOam(openidToken);
            LoginUser loginUser = service.loginByOpenid(openid_FromOam, request);
            Map<String, Object> map = tokenService.createToken(loginUser);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    private String getOpenidFromOam(String openidToken) throws IOException {
        // 创建 OkHttpClient 对象
        OkHttpClient client = new OkHttpClient();
        // 创建请求 URL
        String url = OAM_API + "/wx/qrcode/{appid}/getOpenidByOpenidToken";
        url = url.replace("{appid}", OAM_APPID) + "?openidToken=" + openidToken;
        // 创建请求
        Request requestOam = new Request.Builder()
                .url(url)
                .get()
                .build();
        // 发起请求并处理响应
        try (Response response = client.newCall(requestOam).execute()) {
            if (!response.isSuccessful()) {
                throw new BaseBusinessException("OkHttp3发送请求失败：" + response.code());
            }
            //oam.responseBody格式--->     有openid:{"code":200,"msg":null,"data":"ozJNq6-aO6sKR4ehV3BSmqooAvFI"}
            //                            无openid:{"code":500,"msg":"redis中未找到openidToken或已过期","data":null}
            String responseBody = response.body().string(); // response.body()会消耗响应体的内容，并将其关闭,若再次尝试访问响应体时就会抛出 java.lang.IllegalStateException: closed 异常
            JSONObject jsonObject = JSON.parseObject(responseBody);
            String openid = jsonObject.getString("data");
            if (org.apache.commons.lang3.StringUtils.isBlank(openid)) {
                throw new BaseBusinessException(responseBody);
            }
            return openid;
        }
    }

    @ApiOperation(value = "获取租户", notes = "获取租户", produces = "application/json")
    @RequestMapping(value = "/getTenant", method = RequestMethod.POST)
    public R<List<PiTenant>> getTenant() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            System.out.println(loginUser.toString());
            List<PiTenant> list = service.getPiTenant(loginUser.getUserid());
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @PostMapping("/refresh")
    public R refresh(HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        try {
            if (StringUtils.isNotNull(loginUser)) {
                // 刷新令牌有效期
                tokenService.refreshToken(loginUser);
                return R.ok("");
            }
            return R.fail("");
            // 刷新令牌有效期
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @return R
     * @Description 续命token2小时5分钟
     * <AUTHOR>
     * @time 2023/4/1 9:30
     */
    @GetMapping("/renewal")
    @ApiOperation(value = "续命token2小时5分钟", notes = "", produces = "application/json")
    public R renewal() {
        LoginUser loginUser = tokenService.getLoginUser();
        try {
            if (StringUtils.isNotNull(loginUser)) {
                String userKey = "login_tokens:" + loginUser.getToken();
                // 获取原来的过期时间
                long expire = redisTemplate.getExpire(userKey, TimeUnit.SECONDS);
                // 延长令牌有效期2小时5分钟
                if (expire > 0) {
                    redisService.setCacheObject(userKey, loginUser, expire + 7500, TimeUnit.SECONDS);
                    return R.ok("");
                } else {
                    return R.fail("Token has expired.");
                }
            } else {
                return R.fail("Token does not exist.");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    // 前端用于: 切换服务Function
    @ApiOperation(value = "长期token+tid 生成新token", notes = "", produces = "application/json")
    @RequestMapping(value = "/authByRtKey", method = RequestMethod.POST)
    public R<Map<String, Object>> authByRtKey(@RequestBody String json, HttpServletRequest request) {
        JSONObject jsonObject = JSON.parseObject(json);
        // 调用/login方法返回的长期token
        String rtKey_NoPre = jsonObject.getString("rtkey");
        String rtKey_RedisKey = RTKEY_PRE_REDIS + rtKey_NoPre;
        String rtKey_RedisValue = redisService.getCacheObject(rtKey_RedisKey);
        // rtKey_RedisValue 格式: {"refreshtoken":"abc","tenantid": "t1"}
        if (rtKey_RedisValue == null) {
            return R.fail("rtKey不存在或已过期");
        }
        return tokenRtKey(rtKey_RedisValue, rtKey_NoPre, request);
    }


    //"login_tokens:XXXXX" 只传了XXXXX, 前缀"login_tokens:"手动加上
    // 传入json格式: {"refreshtoken":"abc","tenantid": "t1"}
    // 前端用于:1.首次登录(更新登录日志的tid,TenantName)  2.续期(不更新日志)
    @ApiOperation(value = "长期token+tid 生成短期token(不传入rtKey则生成新rtKey并返回)", notes = "", produces = "application/json")
    @PostMapping(path = {"/tokenRtKet", "/tokenRtKey"})
    //@PostMapping("/tokenRtKey")
    public R<Map<String, Object>> tokenRtKey(@RequestBody String json, @RequestParam(required = false) String rtKey_NoPre, HttpServletRequest request) {
        // 首先把json存入Redis, 键为rtkey:XXX,返回无前缀的rtKey
        if (StringUtils.isBlank(rtKey_NoPre)) {
            rtKey_NoPre = this.rtKeySave(json);
        }
        JSONObject jsonObject = JSON.parseObject(json);
        // 调用/login方法返回的长期token
        String token_long = jsonObject.getString("refreshtoken");
        String tokenKey = "login_tokens:" + token_long;
        LoginUser loginUser = this.redisService.getCacheObject(tokenKey);
        if (loginUser == null) {
            return R.fail("长期refreshtoken不存在或已过期");
        }
        // 本次想要切换的租户tid,服务Code
        String tid = jsonObject.getString("tenantid");
        String fncode = jsonObject.getString("fncode");//即FunctionCode服务编码
        String first = jsonObject.getString("first");//是否第一次登录 为true时记录登录日志
        try {
            // 20240715不再Redis删除旧token(为了单点登录)
//            tokenService.delLoginUser(loginUser.getToken());
            loginUser.setTenantid(tid);
            loginUser.setIsadmin(service.getIsAdmin(loginUser.getUserid(), tid));
            //加入租户信息
            TenantInfo tenantInfo = service.getTenantInfo(tid);
            tenantInfo.setIsdeptadmin(0);
            // 加入架构信息
            List<DeptinfoPojo> lstdept = null;
            R<List<DeptinfoPojo>> rinfo = this.systemFeignService.getDeptinfolistByUser(loginUser.getUserid(), loginUser.getTenantid());
            if (rinfo.getCode() == 200) lstdept = rinfo.getData();
            tenantInfo.setLstdept(lstdept);
            if (lstdept != null && !lstdept.isEmpty()) {
                tenantInfo.setDeptid(lstdept.get(0).getDeptid());
                tenantInfo.setDeptcode(lstdept.get(0).getDeptcode());
                tenantInfo.setDeptname(lstdept.get(0).getDeptname());
                tenantInfo.setIsdeptadmin(lstdept.get(0).getIsdeptadmin());
            }
            loginUser.setTenantinfo(tenantInfo);
            if ("true".equals(first)) {
                // 更新登录日志的tid,TenantName
                CiLoginLogEntity ciLoginLogEntity = new CiLoginLogEntity();
                ciLoginLogEntity.setTenantid(tid);
                ciLoginLogEntity.setTenantName(piTenantMapper.getTenantName(tid));
                ciLoginLogEntity.setId(loginUser.getLogid());
                ciLoginLogMapper.update(ciLoginLogEntity);
            }
            // 建立新的loginUser 生成短期token 10分钟
            Map<String, Object> map = tokenService.createAppToken(loginUser);
            //加入权限编码 (此时为userid关联的全部服务的权限permissions、参数configs)
            R<Map<String, Object>> r = systemFeignService.updateTokenByFnCode(loginUser.getToken(), fncode);
            if (r.getCode() == 200) map = r.getData();

            //20240313 token记录到用户在线表中
            recordTokenOnline(map, request);
            // 加入rtKey  refreshtoken:长期token
            map.put("rtkey", rtKey_NoPre);
            map.put("refreshtoken", token_long);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //    @ApiOperation(value = "随机数rtKey绑定json(长期token+tid),存入Redis 键为rtkey:16000", notes = "", produces = "application/json")
    //    @RequestMapping(value = "/rtKeySave", method = RequestMethod.POST)
    public String rtKeySave(@RequestBody String json) {
        String rtKey_NoPre = inksSnowflake.getSnowflake().nextIdStr();
        // 存入redis 键:随机数 值:json {"refreshtoken":"abc","tenantid": "t1"}
        String rtKey_RedisKey = RTKEY_PRE_REDIS + rtKey_NoPre;
        redisService.setCacheObject(rtKey_RedisKey, json, 601L, TimeUnit.SECONDS);
        return rtKey_NoPre;
    }

    /**
     * 在线用户表，主要用于管理：
     * 1、租户中用户只可登录一处，如有别一个地址登录就清除；
     * 2、权限设定后，可以批量清除用户对应token;
     */
    @ApiOperation(value = "(原始)选择租户tid后生成新token", notes = "选择住户后生成新token", produces = "application/json")
    @RequestMapping(value = "/token", method = RequestMethod.POST)
    public R<Map<String, Object>> token(@RequestBody String key, HttpServletRequest request) { // 即tid
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            // (原始版本需要)删除旧token
            tokenService.delLoginUser(loginUser.getToken());
            loginUser.setTenantid(key);
            loginUser.setIsadmin(service.getIsAdmin(loginUser.getUserid(), key));
            //加入租户信息
            TenantInfo tenantInfo = service.getTenantInfo(key);
            tenantInfo.setIsdeptadmin(0);
            // 加入架构信息
            List<DeptinfoPojo> lstdept = null;
            R<List<DeptinfoPojo>> rinfo = this.systemFeignService.getDeptinfolistByUser(loginUser.getUserid(), loginUser.getTenantid());
            if (rinfo.getCode() == 200) lstdept = rinfo.getData();
            tenantInfo.setLstdept(lstdept);
            if (lstdept != null && !lstdept.isEmpty()) {
                tenantInfo.setDeptid(lstdept.get(0).getDeptid());
                tenantInfo.setDeptcode(lstdept.get(0).getDeptcode());
                tenantInfo.setDeptname(lstdept.get(0).getDeptname());
                tenantInfo.setIsdeptadmin(lstdept.get(0).getIsdeptadmin());
            }
            loginUser.setTenantinfo(tenantInfo);
            // 更新登录日志的tid,TenantName
            CiLoginLogEntity ciLoginLogEntity = new CiLoginLogEntity();
            ciLoginLogEntity.setTenantid(key);
            ciLoginLogEntity.setTenantName(piTenantMapper.getTenantName(key));
            ciLoginLogEntity.setId(loginUser.getLogid());
            ciLoginLogMapper.update(ciLoginLogEntity);
            // 建立新的loginUser
            Map<String, Object> map = tokenService.createToken(loginUser);
            //加入权限编码
            R<Map<String, Object>> r = systemFeignService.updateToken(loginUser.getToken());
            if (r.getCode() == 200) map = r.getData();

            //20240313 token记录到用户在线表中
            recordTokenOnline(map, request);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    private void recordTokenOnline(Map<String, Object> loginUserMap, HttpServletRequest request) {
        //获取请求浏览器操作系统
        UserAgent userAgent = UserAgent.parseUserAgentString(request.getHeader("User-Agent"));
        String hostSystem = userAgent.getOperatingSystem().getName();
        String browserName = userAgent.getBrowser().getName();
        int terminalType = 0; //0其他/1web/2APP/3h5/4ipad/5winform
        // winform 这个类型是选租户时前端请求头terminaltype传入的，其他类型是根据请求头User-Agent判断的
        if (Objects.equals(request.getHeader("terminaltype"), "winform")) {
            terminalType = 5;
        } else {
            String hostSystemLower = hostSystem.toLowerCase();
            if (hostSystemLower.contains("iphone") || hostSystemLower.contains("android")) {
                terminalType = 2;
            } else if (browserName.contains("micromessenger")) {
                terminalType = 3;
            } else if (hostSystemLower.contains("windows") || hostSystemLower.contains("macintosh") || hostSystemLower.contains("linux")) {
                terminalType = 1;
            }
        }

        String newToken = (String) loginUserMap.get("access_token");
        String loginUserJSON = JSON.toJSONString(loginUserMap.get("loginuser"));
        LoginUser loginUser = JSON.parseObject(loginUserJSON, new TypeReference<LoginUser>() {
        });
        String tid = loginUser.getTenantid();
        // 读取指定系统参数:是否允许同时登录 checkonline为"true"时检查(未设置也默认检查)   onlinelimit允许在线用户数量
        String checkonline = systemFeignService.getConfigValue("system.user.checkonline", tid, loginUser.getToken()).getData();
//        String onlinelimit = systemFeignService.getConfigValue("system.user.onlinelimit", tid, loginUser.getToken()).getData();
        // checkonline = "flase"不检查
        // terminalType = 5 winform也不检查
        if (terminalType != 5 && !"false".equals(checkonline)) {
            // 检查userid+terminalType+tid是否在在线表中有对应记录(token)，如果有，更新并删除redis中的token
            PiuseronlinePojo userOnlineDB = authPiuseronlineService.getEntityByUserid(loginUser.getUserid(), terminalType, tid);
            if (userOnlineDB == null) {
                userOnlineDB = new PiuseronlinePojo();
            }
            String oldToken = userOnlineDB.getToken();// 数据库记录的旧token
            userOnlineDB.setUserid(loginUser.getUserid());
            userOnlineDB.setUsername(loginUser.getUsername());
            userOnlineDB.setRealname(loginUser.getRealname());
            userOnlineDB.setTenantid(tid);
            userOnlineDB.setTenantname(loginUser.getTenantinfo().getTenantname());
            userOnlineDB.setIpaddress(loginUser.getIpaddr());
            userOnlineDB.setIplocation(UserAgentUtil.getRealAddressByIP(loginUser.getIpaddr()));
            userOnlineDB.setMacaddress(NetUtil.getLocalMacAddress());  //hutool包里的获取MAC地址
            userOnlineDB.setBrowsername(browserName);
            userOnlineDB.setHostsystem(hostSystem);
            userOnlineDB.setTerminaltype(terminalType); // 0其他/1web/2APP/3h5/4ipad/5winform
            userOnlineDB.setToken(newToken);
            userOnlineDB.setLogindate(new Date());
            userOnlineDB.setLastactivitydate(new Date());
            userOnlineDB.setTokenexpirydate(new Date(2099, 1, 1)); // 2099+1900年
            if (userOnlineDB.getId() != null) {
                // 有则更新
                authPiuseronlineService.update(userOnlineDB);
                // redis删除旧token
                tokenService.delLoginUser(oldToken);
            } else {
                authPiuseronlineService.insert(userOnlineDB);
            }
        }

    }


    @DeleteMapping("logout")
    public R logout(HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (StringUtils.isNotNull(loginUser)) {
                String username = loginUser.getUsername();
                // 删除用户缓存记录
                tokenService.delLoginUser(loginUser.getToken());
                service.record(loginUser, request);
            }
            return R.ok("");
        } catch (Exception e) {
            return R.ok("");
        }
    }

    @ApiOperation(value = "忘记密码", notes = "忘记密码", produces = "application/json")
    @RequestMapping(value = "/forgetPwd", method = RequestMethod.POST)
    public R<Map<String, Object>> forgetPwd(String key, String password) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            if (loginUser.getUserid().equals(key)) {
                service.updatePwd(key, password);
            } else {
                R.fail("用户id不一致,禁止修改");
            }
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "邮箱验证码", notes = "邮箱验证码", produces = "application/json")
    @RequestMapping(value = "/emailCaptcha", method = RequestMethod.GET)
    public R emailCaptcha(String email) {
        try {
            registerCaptchaService.emaiCaptcha(email);
            return R.ok("验证码获取成功");
        } catch (Exception e) {
            return R.fail("验证码获取失败");
        }
    }

    @ApiOperation(value = "手机验证码", notes = "手机验证码", produces = "application/json")
    @RequestMapping(value = "/phoneCaptcha", method = RequestMethod.GET)
    public R phoneCaptcha(String phone) {
        try {
            phoneMsgService.PhoneCaptcha(phone, accessKeyId, accessKeySecret);
            return R.ok("手机验证码获取成功");
        } catch (Exception e) {
            return R.fail("手机验证码获取失败");
        }
    }

    @ApiOperation(value = "读取LoginUser用户信息", notes = "读取LoginUser解析用户信息,uptoken=1更新token,2克隆token", produces = "application/json")
    @GetMapping("/getLoginUser")
    @ResponseBody
    public R<Map<String, Object>> getLoginUser(String key, Integer uptoken) {
        try {
            //解析是用户信息
            LoginUser loginUser = tokenService.getLoginUser(key);
            if (loginUser == null) {
                logger.info("key已过期失效：" + key);
                return R.fail("key已过期失效");
            }
            logger.info("Loginuser：" + loginUser.getUsername());
            //删除缓存用户信息
            if (uptoken != null) {
                if (uptoken == 1) {
                    R<Map<String, Object>> rmap = this.systemFeignService.updateToken(key);
                    if (rmap.getCode() == 200) {
                        logger.info("最终token：" + rmap.getData().get("access_token").toString());
                        //重新存入redis
                        Map<String, Object> map = rmap.getData();
                        return R.ok(map);
                    } else {
                        return R.fail("key更新Token失效");
                    }
                } else {    // uptoken == 2
                    Map<String, Object> map = new HashMap();
                    String token = IdUtils.fastUUID();
                    loginUser.setToken(token);
                    map.put("access_token", token);
                    map.put("loginuser", loginUser);
                    map.put("expires_in", 43200L);
                    this.redisService.setCacheObject("login_tokens:" + token, loginUser, 43200L, TimeUnit.SECONDS);
                    return R.ok(map);
                }
            } else {
                Map<String, Object> map = new HashMap();
                map.put("access_token", key);
                map.put("loginuser", loginUser);
                map.put("expires_in", 43200L);
                return R.ok(map);
            }


        } catch (Exception e) {
            logger.warn(e.getMessage());
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "用户一次性登陆", notes = "用户一次性登陆", produces = "application/json")
    @RequestMapping(value = "/loginTenant", method = RequestMethod.POST)
    public R<Map<String, Object>> loginTenant(@RequestBody LoginBody loginBody, HttpServletRequest request) {
        try {
            LoginUser l = service.login(loginBody.getUserName(), loginBody.getPassword(), request);
            String token = this.tokenService.createToken(l).get("access_token").toString();
            LoginUser loginUser = tokenService.getLoginUser(token);
            String tid = loginBody.getTid();
            loginUser.setTenantid(tid);
            loginUser.setIsadmin(service.getIsAdmin(loginUser.getUserid(), tid));
            //加入租户信息
            TenantInfo tenantInfo = service.getTenantInfo(tid);
            tenantInfo.setIsdeptadmin(0);
            // 加入架构信息
            List<DeptinfoPojo> lstdept = null;
            R<List<DeptinfoPojo>> rinfo = this.systemFeignService.getDeptinfolistByUser(loginUser.getUserid(), loginUser.getTenantid());
            if (rinfo.getCode() == 200) lstdept = rinfo.getData();
            tenantInfo.setLstdept(lstdept);
            if (lstdept != null && lstdept.size() > 0) {
                tenantInfo.setDeptid(lstdept.get(0).getDeptid());
                tenantInfo.setDeptcode(lstdept.get(0).getDeptcode());
                tenantInfo.setDeptname(lstdept.get(0).getDeptname());
                tenantInfo.setIsdeptadmin(lstdept.get(0).getIsdeptadmin());
            }
            loginUser.setTenantinfo(tenantInfo);
            // 建立新的loginUser
            Map<String, Object> map = tokenService.createToken(loginUser);
            //加入权限编码
            R<Map<String, Object>> r = systemFeignService.updateToken(loginUser.getToken());
            if (r.getCode() == 200) map = r.getData();
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "授权码登陆", notes = "授权码登陆", produces = "application/json")
    @RequestMapping(value = "/loginByAuthCode", method = RequestMethod.GET)
    public R<Map<String, Object>> loginByAuthCode(String key, HttpServletRequest request) {//key即auth
        try {

            PiauthcodePojo piauthcodePojo = this.authPiauthcodeService.getEntityByEnabled(key);
            if (piauthcodePojo == null) {
                return R.fail("授权码无效,请联系管理员");
            }
            // 登录并获取token
            LoginUser l = service.login(piauthcodePojo.getUsername(), piauthcodePojo.getUserpassword(), request);
            String token = this.tokenService.createToken(l).get("access_token").toString();
            LoginUser loginUser = tokenService.getLoginUser(token);
            String tid = piauthcodePojo.getTenantid();
            loginUser.setTenantid(tid);
            loginUser.setIsadmin(service.getIsAdmin(loginUser.getUserid(), tid));
            //加入租户信息
            TenantInfo tenantInfo = service.getTenantInfo(tid);
            tenantInfo.setIsdeptadmin(0);
            // 加入架构信息
            List<DeptinfoPojo> lstdept = null;
            R<List<DeptinfoPojo>> rinfo = this.systemFeignService.getDeptinfolistByUser(loginUser.getUserid(), loginUser.getTenantid());
            if (rinfo.getCode() == 200) lstdept = rinfo.getData();
            tenantInfo.setLstdept(lstdept);
            if (lstdept != null && !lstdept.isEmpty()) {
                tenantInfo.setDeptid(lstdept.get(0).getDeptid());
                tenantInfo.setDeptcode(lstdept.get(0).getDeptcode());
                tenantInfo.setDeptname(lstdept.get(0).getDeptname());
                tenantInfo.setIsdeptadmin(lstdept.get(0).getIsdeptadmin());
            }
            loginUser.setTenantinfo(tenantInfo);
            // 建立新的loginUser
            Map<String, Object> map = tokenService.createToken(loginUser);
            //加入权限编码
            R<Map<String, Object>> r = systemFeignService.updateToken(loginUser.getToken());
            if (r.getCode() == 200) map = r.getData();
            // 授权码-token关联存入redis    键:AUTHCODE_KEY + AESUtil.Encrypt(auth)   值:token
            redisService.setCacheObject(AUTHCODE_KEY + AESUtil.Encrypt(key), map.get("access_token"), 43200L, TimeUnit.SECONDS);
            return R.ok(map);

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
