package inks.auth.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import inks.auth.domain.Piadmin;
import inks.auth.form.LoginBody;
import inks.auth.service.SysAdminLogingService;
import inks.common.core.constant.InksConstants;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.StringUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import io.swagger.annotations.ApiOperation;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.ParseException;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/admin")
public class AdminTokenController {
    //    静态方法不能直接访问使用@Value注解注入的非静态成员变量。可以在一个非静态方法中将注入的值赋值给静态变量，然后通过@PostConstruct注解来确保这个方法在bean初始化之后执行
    private static String OAM_API;
    private static String OAM_APPID;
    //------------------------------------扫码登录(WX小程序)---------------------------------
    //------------------------------------20240520 pms废弃,改为OEM公众号扫码登录---------------------------------
    private final String SCANLOGIN_CODE = "scanlogin_code:";
    @Autowired
    private TokenService tokenService;
    @Resource
    private SysAdminLogingService sysAdminLogingService;
    @Resource
    private RedisService redisService;
    //默认公网应凯科技:http://oam.inksyun.com [wx7850d75f765d0dce]
    @Value("${inks.oam.api:http://oam.inksyun.com}")
    private String oamApi;
    @Value("${inks.oam.appid:wx7850d75f765d0dce}")
    private String oamAppid;
    @Value("${inks.justauth.api}")
    private String api;

    public static String getOpenidFromOam(String openidToken) throws IOException {
        // 创建 OkHttpClient 对象
        OkHttpClient client = new OkHttpClient();
        // 创建请求 URL
        String url = OAM_API + "/wx/qrcode/{appid}/getOpenidByOpenidToken";
        url = url.replace("{appid}", OAM_APPID) + "?openidToken=" + openidToken;
        // 创建请求
        Request requestOam = new Request.Builder()
                .url(url)
                .get()
                .build();
        // 发起请求并处理响应
        try (Response response = client.newCall(requestOam).execute()) {
            if (!response.isSuccessful()) {
                throw new BaseBusinessException("OkHttp3发送请求失败：" + response.code());
            }
            //oam.responseBody格式--->     有openid:{"code":200,"msg":null,"data":"ozJNq6-aO6sKR4ehV3BSmqooAvFI"}
            //                            无openid:{"code":500,"msg":"redis中未找到openidToken或已过期","data":null}
            String responseBody = response.body().string(); // response.body()会消耗响应体的内容，并将其关闭,若再次尝试访问响应体时就会抛出 java.lang.IllegalStateException: closed 异常
            JSONObject jsonObject = JSON.parseObject(responseBody);
            String openid = jsonObject.getString("data");
            if (org.apache.commons.lang3.StringUtils.isBlank(openid)) {
                throw new BaseBusinessException(responseBody);
            }
            return openid;
        }
    }

    @PostConstruct
    public void init() {
        OAM_API = oamApi;
        OAM_APPID = oamAppid;
    }

    @ApiOperation(value = "用户登陆", notes = "用户登陆", produces = "application/json")
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public R<Object> login(@RequestBody LoginBody loginBody, HttpServletRequest request) {
        try {
//            JSONObject jsonObject = JSONObject.parseObject(json);
            LoginUser loginUser = sysAdminLogingService.login(loginBody.getUserName(), loginBody.getPassword(), request);
            loginUser.setTenantid(InksConstants.DEFAULT_TENANT);  //加入默认租户；
            Map<String, Object> map = tokenService.createAdminToken(loginUser);
            return R.ok(map.get("loginuser"));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @DeleteMapping("logout")
    public R logout(HttpServletRequest request) throws ParseException {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser)) {
            String username = loginUser.getUsername();
            // 删除用户缓存记录
            tokenService.delLoginUser(loginUser.getToken());
            sysAdminLogingService.record(loginUser, request);
        }
        return R.ok("");
    }

    @ApiOperation(value = "获取登录二维码String", notes = "获取登录二维码String", produces = "application/json")
    @RequestMapping(value = "/getScanLoginCode", method = RequestMethod.GET)
    public R<Map<String, Object>> getScanLoginCode() {
        try {
            //生成key 即UUID
            String uuid = inksSnowflake.getSnowflake().nextIdStr();
            //code100存入redis
            Map<String, Object> missionMsg = new HashMap<>();
            missionMsg.put("code", "100");
            missionMsg.put("msg", "任务开始处理");
            this.redisService.setCacheMapValue(SCANLOGIN_CODE, uuid, missionMsg);
            //设置过期时间6分钟
            redisService.expire(SCANLOGIN_CODE, 60 * 6);
            //返回前端type,date(key)
            Map<String, Object> map = new HashMap<>();
            map.put("type", "webadminlogin");
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("key", uuid);
            dataMap.put("api", api);
            map.put("data", dataMap);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 接受pms服务传来的openid,key为redis中的key
     */
    @ApiOperation(value = "PMS扫码登录", notes = "扫码登录", produces = "application/json")
    @RequestMapping(value = "/scanLoginCode", method = RequestMethod.GET)
    public R<String> scanLoginCode(String openid, String key, HttpServletRequest request) throws ParseException {
        Map<String, Object> tokenMap = null;
        LoginUser loginUser = sysAdminLogingService.scanLogin(openid, key, request);
        loginUser.setTenantid(InksConstants.DEFAULT_TENANT);  //加入默认租户；
        tokenMap = tokenService.createAdminToken(loginUser);
        //设置当前计算任务进度
        Map<String, Object> missionMsg = new HashMap<>();
        missionMsg.put("code", "200");
        missionMsg.put("msg", "登录成功");
        missionMsg.put("token", tokenMap);
        this.redisService.setCacheMapValue(SCANLOGIN_CODE, key, missionMsg);
        redisService.expire(SCANLOGIN_CODE, 60 * 6);
        return R.ok("扫码登录成功");
    }


    //------------------------------------------20240520: OEM公众号扫码登录-----------------------------------------

    @ApiOperation(value = "获取扫码登录状态", notes = "获取扫码登录状态", produces = "application/json")
    @RequestMapping(value = "/getScanLoginState", method = RequestMethod.GET)
    public R<Map<String, Object>> getScanLoginState(@RequestParam String key) {
        Map<String, Object> scanLoginState = this.redisService.getCacheMapValue(SCANLOGIN_CODE, key);
        return R.ok(scanLoginState);
    }

    @ApiOperation(value = "通过openidToken登录(oam服务redis中有键值对openid_token:openid)", notes = "openidToken登录", produces = "application/json")
    @RequestMapping(value = "/loginByOpenidToken", method = RequestMethod.POST)
    public R<Object> loginByOpenidToken(String openidToken, HttpServletRequest request) {
        try {
            String openidFromOam = getOpenidFromOam(openidToken);
            LoginUser loginUser = sysAdminLogingService.scanLogin(openidFromOam, null, request);
            loginUser.setTenantid(InksConstants.DEFAULT_TENANT);  //加入默认租户；
            Map<String, Object> map = tokenService.createAdminToken(loginUser);
            return R.ok(map.get("loginuser"));
        } catch (BaseBusinessException | IOException e) {
            return R.fail(e.getMessage());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "传入的是openidToken,后端转为openid后存入Wxopenid", notes = "修改用户", produces = "application/json")
    @RequestMapping(value = "/bindOpenid", method = RequestMethod.GET)
    public R<Piadmin> bindOpenid(String openidToken) {
        LoginUser loginUser = tokenService.getLoginUser();
        try {
            String openidFromOam = getOpenidFromOam(openidToken);
            // 绑定Adminid(userid)和openid
            return R.ok(this.sysAdminLogingService.bindOpenid(loginUser.getUserid(), openidFromOam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
