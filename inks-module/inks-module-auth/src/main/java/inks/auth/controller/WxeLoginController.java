package inks.auth.controller;

import com.alibaba.fastjson.JSONObject;
import inks.api.feign.SystemFeignService;
import inks.auth.service.JustAuthService;
import inks.auth.service.SysLoginService;
import inks.auth.utils.wxe.QyWeChat;
import inks.auth.utils.wxe.QyWeChatUtils;
import inks.auth.utils.wxe.SendRequest;
import inks.common.core.constant.ConfigConstant;
import inks.common.core.domain.DeptinfoPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.domain.TenantInfo;
import inks.common.core.text.inksSnowflake;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Controller
@RequestMapping("/wxelogin")
public class WxeLoginController {

    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(WxeLoginController.class);
    @Resource
    private JustAuthService justAuthService;
    @Resource
    private TokenService tokenService;
    @Resource
    private RedisService redisService;
    @Resource
    private SystemFeignService systemFeignService;
    @Resource
    private SysLoginService sysLoginService;

    @GetMapping("/redirect")
    @ApiOperation(value = "企业微信扫码登录", notes = "企业微信扫码登录", produces = "application/json")
    public void doLogin(String tid, String code, String state, HttpServletRequest request, HttpServletResponse response) throws IOException {
        R r = new R();
        logger.info("------开始企业微信免费登-------");
        //获取企业ID
        r = systemFeignService.getConfigValue("D08M11.corpid", state, "");
        if (r.getCode() == 200) {
            QyWeChat.corpId = r.getData().toString();
            logger.info("企业微信免登 corpId:" + QyWeChat.corpId);
        } else {
            logger.info("获取应用corpId失败" + r);
        }
        //获取应用密钥
        r = systemFeignService.getConfigValue("D08M11.agentsecret", state, "");
        if (r.getCode() == 200) {
            QyWeChat.agentSecret = r.getData().toString();
            logger.info("企业微信免登 agentSecret:" + QyWeChat.agentSecret);
        } else {
            logger.info("获取应用agentSecret失败" + r);
        }
        // 通过 corpid + agentSecret 获得企业微信Token
        // https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}
        String token = QyWeChatUtils.refreshToken("agentToken");
        logger.info("企业微信免登 token:" + token);

        // 通过 Token + Authorization Code 获得用户信息
        // https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token={access_token}&code={code}
        String url = QyWeChat.getUser.replace("{access_token}", token).replace("{code}", code);
        logger.info("企业微信免登 url:" + url);
        JSONObject jsonObject = SendRequest.sendGet(url);
        logger.info("企业微信免登返回:" + jsonObject.toString());

        // 提取json中的企业微信userid
        String userid = jsonObject.getString("UserId"); // jsonObject.get("UserId").toString(); 如null时会报错

        logger.info("企业微信免登 DeviceId:" + jsonObject.getString("DeviceId"));
        if (userid != null) {
            logger.info("企业微信免登 uuid:" + userid);
        } else {
            logger.info("企业微信免登 errmsg:" + jsonObject.getString("errmsg"));
        }

        // 按userid + Tid 通第三方登录表，返回loginUser对象
        LoginUser loginUser = this.justAuthService.getLoginUserByJust(userid, "wxe", state);
        if (loginUser == null) {
            response.sendRedirect("");
            return;
        }

        // 设置租户信息
        // loginUser.setTenantid(state);
        // loginUser.setIsadmin(this.sysLoginService.getIsAdmin(loginUser.getUserid(), state));
        //加入租户信息
        TenantInfo tenantInfo = this.sysLoginService.getTenantInfo(state);
        tenantInfo.setIsdeptadmin(0);
        // 加入架构信息
        List<DeptinfoPojo> lstdept = null;
        R<List<DeptinfoPojo>> rinfo = this.systemFeignService.getDeptinfolistByUser(loginUser.getUserid(), loginUser.getTenantid());
        if (rinfo.getCode() == 200) lstdept = rinfo.getData();
        tenantInfo.setLstdept(lstdept);
        if (lstdept != null && lstdept.size() > 0) {
            tenantInfo.setDeptid(lstdept.get(0).getDeptid());
            tenantInfo.setDeptcode(lstdept.get(0).getDeptcode());
            tenantInfo.setDeptname(lstdept.get(0).getDeptname());
            tenantInfo.setIsdeptadmin(lstdept.get(0).getIsdeptadmin());
        }
        loginUser.setTenantinfo(tenantInfo);


        // 将logUser存入redis  设置5分钟后过期
        String tokenuuid = inksSnowflake.getSnowflake().nextIdStr();
        redisService.setCacheObject("login_tokens:" + tokenuuid, loginUser, 300L, TimeUnit.SECONDS);
        logger.info("企业微信免登 临时login_tokens:" + tokenuuid);
        // 参数中获得前端登录页 + tokenuuid
        String loginurl = "";
        r = systemFeignService.getConfigValue("D08M11.loginurl", state, "");
        if (r.getCode() == 200) {
            loginurl = r.getData().toString().replace("{key}", tokenuuid);
            logger.info("企业微信免登 loginurl:" + r.getData().toString());
        } else {
            logger.info("企业微信免登 loginurl 失败" + r);
        }
        // 抛出网址
        response.sendRedirect(loginurl);
    }

    @GetMapping("/redirectWeb")
    @ApiOperation(value = "企业微信扫码登录", notes = "企业微信扫码登录", produces = "application/json")
    public void redirectWeb(String tid, String code, String state, HttpServletRequest request, HttpServletResponse response) throws IOException {
        logger.info("------开始企业微信免费登,获得参数-------");
        String loginurl = "";
        R<Map<String, String>> rcfg = systemFeignService.getConfigTenAll(0, tid, "");
        if (rcfg.getCode() == 200) {
            Map<String, String> mapcfg = rcfg.getData();
            QyWeChat.corpId = mapcfg.get(ConfigConstant.WXE_CORPID);
            logger.info("企业微信免登 corpId:" + QyWeChat.corpId);
            QyWeChat.agentSecret = mapcfg.get(ConfigConstant.WXE_APPSECRET);
            logger.info("企业微信免登 agentSecret:" + QyWeChat.agentSecret);
            loginurl = mapcfg.get(ConfigConstant.WXE_WEBLOGINURL);
        } else {
            logger.info("获取Config参数失败" + rcfg.getMsg());
            R.fail("获取Config参数失败");
        }

        // 通过 corpid + agentSecret 获得企业微信Token
        // https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}
        String token = QyWeChatUtils.refreshToken("agentToken");
        logger.info("企业微信免登 token:" + token);

        // 通过 Token + Authorization Code 获得用户信息
        // https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token={access_token}&code={code}
        String url = QyWeChat.getUser.replace("{access_token}", token).replace("{code}", code);
        logger.info("企业微信免登 url:" + url);
        JSONObject jsonObject = SendRequest.sendGet(url);
        logger.info("企业微信免登返回:" + jsonObject.toString());

        // 提取json中的企业微信userid
        String userid = jsonObject.getString("UserId"); // jsonObject.get("UserId").toString(); 如null时会报错

        logger.info("企业微信免登 DeviceId:" + jsonObject.getString("DeviceId"));
        if (userid != null) {
            logger.info("企业微信免登 uuid:" + userid);
        } else {
            logger.info("企业微信免登 errmsg:" + jsonObject.getString("errmsg"));
        }

        // 按userid + Tid 通第三方登录表，返回loginUser对象
        LoginUser loginUser = this.justAuthService.getLoginUserByJust(userid, "wxe", tid);
        if (loginUser == null) {
            response.sendRedirect("");
            return;
        }

        // 设置租户信息
        // loginUser.setTenantid(state);
        // loginUser.setIsadmin(this.sysLoginService.getIsAdmin(loginUser.getUserid(), state));
        //加入租户信息
        TenantInfo tenantInfo = this.sysLoginService.getTenantInfo(state);
        tenantInfo.setIsdeptadmin(0);
        // 加入架构信息
        List<DeptinfoPojo> lstdept = null;
        R<List<DeptinfoPojo>> rinfo = this.systemFeignService.getDeptinfolistByUser(loginUser.getUserid(), loginUser.getTenantid());
        if (rinfo.getCode() == 200) lstdept = rinfo.getData();
        tenantInfo.setLstdept(lstdept);
        if (lstdept != null && lstdept.size() > 0) {
            tenantInfo.setDeptid(lstdept.get(0).getDeptid());
            tenantInfo.setDeptcode(lstdept.get(0).getDeptcode());
            tenantInfo.setDeptname(lstdept.get(0).getDeptname());
            tenantInfo.setIsdeptadmin(lstdept.get(0).getIsdeptadmin());
        }
        loginUser.setTenantinfo(tenantInfo);

        // 将logUser存入redis  设置5分钟后过期
        String tokenuuid = inksSnowflake.getSnowflake().nextIdStr();
        redisService.setCacheObject("login_tokens:" + tokenuuid, loginUser, 300L, TimeUnit.SECONDS);
        logger.info("企业微信免登 临时login_tokens:" + tokenuuid);
        // 参数中获得前端登录页 + tokenuuid
        loginurl = loginurl.replace("{key}", tokenuuid);
        // 抛出网址
        // response.addCookie(new Cookie("inks_token",tokenuuid));
        response.sendRedirect(loginurl);
    }

    @GetMapping("/redirectApp")
    @ApiOperation(value = "企业微信扫码登录", notes = "企业微信扫码登录", produces = "application/json")
    public void redirectApp(String tid, String code, String state, HttpServletRequest request, HttpServletResponse response) throws IOException {
        logger.info("------开始企业微信免费登,获得参数-------");
        String loginurl = "";
        R<Map<String, String>> rcfg = systemFeignService.getConfigTenAll(0, tid, "");
        if (rcfg.getCode() == 200) {
            Map<String, String> mapcfg = rcfg.getData();
            QyWeChat.corpId = mapcfg.get(ConfigConstant.WXE_CORPID);
            logger.info("企业微信免登 corpId:" + QyWeChat.corpId);
            QyWeChat.agentSecret = mapcfg.get(ConfigConstant.WXE_APPSECRET);
            logger.info("企业微信免登 agentSecret:" + QyWeChat.agentSecret);
            loginurl = mapcfg.get(ConfigConstant.WXE_APPLOGINURL);
        } else {
            logger.info("获取Config参数失败" + rcfg.getMsg());
            R.fail("获取Config参数失败");
        }

        // 通过 corpid + agentSecret 获得企业微信Token
        // https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}
        String token = QyWeChatUtils.refreshToken("agentToken");
        logger.info("企业微信免登 token:" + token);

        // 通过 Token + Authorization Code 获得用户信息
        // https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token={access_token}&code={code}
        String url = QyWeChat.getUser.replace("{access_token}", token).replace("{code}", code);
        logger.info("企业微信免登 url:" + url);
        JSONObject jsonObject = SendRequest.sendGet(url);
        logger.info("企业微信免登返回:" + jsonObject.toString());

        // 提取json中的企业微信userid
        String userid = jsonObject.getString("UserId"); // jsonObject.get("UserId").toString(); 如null时会报错

        logger.info("企业微信免登 DeviceId:" + jsonObject.getString("DeviceId"));
        if (userid != null) {
            logger.info("企业微信免登 uuid:" + userid);
        } else {
            logger.info("企业微信免登 errmsg:" + jsonObject.getString("errmsg"));
        }

        // 按userid + Tid 通第三方登录表，返回loginUser对象
        LoginUser loginUser = this.justAuthService.getLoginUserByJust(userid, "wxe", tid);
        if (loginUser == null) {
            response.sendRedirect("");
            return;
        }

        // 设置租户信息
        // loginUser.setTenantid(state);
        // loginUser.setIsadmin(this.sysLoginService.getIsAdmin(loginUser.getUserid(), state));
        //加入租户信息
        TenantInfo tenantInfo = loginUser.getTenantinfo(); // this.sysLoginService.getTenantInfo(state);
        tenantInfo.setIsdeptadmin(0);
        // 加入架构信息
        List<DeptinfoPojo> lstdept = null;
        R<List<DeptinfoPojo>> rinfo = this.systemFeignService.getDeptinfolistByUser(loginUser.getUserid(), loginUser.getTenantid());
        if (rinfo.getCode() == 200) lstdept = rinfo.getData();
        tenantInfo.setLstdept(lstdept);
        if (lstdept != null && !lstdept.isEmpty()) {
            tenantInfo.setDeptid(lstdept.get(0).getDeptid());
            tenantInfo.setDeptcode(lstdept.get(0).getDeptcode());
            tenantInfo.setDeptname(lstdept.get(0).getDeptname());
            tenantInfo.setIsdeptadmin(lstdept.get(0).getIsdeptadmin());
        }
        loginUser.setTenantinfo(tenantInfo);

        // 将logUser存入redis  设置5分钟后过期
        String tokenuuid = inksSnowflake.getSnowflake().nextIdStr();
        redisService.setCacheObject("login_tokens:" + tokenuuid, loginUser, 300L, TimeUnit.SECONDS);
        logger.info("企业微信免登 临时token:" + tokenuuid);
        // 参数中获得前端登录页 + tokenuuid
        loginurl = loginurl.replace("{key}", tokenuuid);
        logger.info("企业微信免登 loginurl:" + loginurl);
        // 抛出网址
        // response.addCookie(new Cookie("inks_token",tokenuuid));
        response.sendRedirect(loginurl);
    }

    @GetMapping("/redirectDai")
    @ApiOperation(value = "服务商代开发：企业微信扫码登录", notes = "企业微信扫码登录", produces = "application/json")
    public void redirectDai(String tid, String code, String state, HttpServletRequest request, HttpServletResponse response) throws IOException, JSONException {
        logger.info("------开始企业微信免费登,获得参数-------");
        String loginurl = "";
        R<Map<String, String>> rcfg = systemFeignService.getConfigTenAll(0, tid, "");
        if (rcfg.getCode() == 200) {
            Map<String, String> mapcfg = rcfg.getData();
            QyWeChat.corpId = mapcfg.get(ConfigConstant.WXE_CORPID);
            logger.info("企业微信免登 corpId:" + QyWeChat.corpId);
            QyWeChat.agentSecret = mapcfg.get(ConfigConstant.WXE_APPSECRET);
            logger.info("企业微信免登 agentSecret:" + QyWeChat.agentSecret);
            loginurl = mapcfg.get(ConfigConstant.WXE_APPLOGINURL);
        } else {
            logger.info("获取Config参数失败" + rcfg.getMsg());
            R.fail("获取Config参数失败");
        }

        // 通过 corpid + agentSecret 获得企业微信Token
        // https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}
        String token = QyWeChatUtils.refreshToken("agentToken");
        logger.info("企业微信免登 token:" + token);

        // 通过 Token + Authorization Code 获得用户信息
        // https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token={access_token}&code={code}
        String url = QyWeChat.getUser.replace("{access_token}", token).replace("{code}", code);
        logger.info("企业微信免登 url:" + url);
        JSONObject jsonObject = SendRequest.sendGet(url);
        logger.info("企业微信免登返回:" + jsonObject.toString());

        // 提取json中的企业微信userid 代开发是【加密的userid】,需要转换
        String userid = jsonObject.getString("UserId"); // jsonObject.get("UserId").toString(); 如null时会报错
        logger.info("企业微信免登 DeviceId:" + jsonObject.getString("DeviceId"));
        if (userid != null) {
            logger.info("企业微信免登 uuid:" + userid);
        } else {
            logger.info("企业微信免登 errmsg:" + jsonObject.getString("errmsg"));
        }

        //// 对【加密的userid】进行解密
        //userid = justAuthService.getUseridByEncrypt(userid, tid);
        //if (StringUtils.isBlank(userid)) {
        //    logger.info("对【加密的userid】进行解密失败，PiJustAuth未关联企微明文和密文Userid");
        //}
        //logger.info("企业微信免登 解密后的 uuid:" + userid);
        // 按【加密的userid】 + Tid 通第三方登录表，返回loginUser对象
        LoginUser loginUser = this.justAuthService.getLoginUserByJustEncrypt(userid, "wxe", tid);
        if (loginUser == null) {
            response.sendRedirect("");
            return;
        }

        // 设置租户信息
        // loginUser.setTenantid(state);
        // loginUser.setIsadmin(this.sysLoginService.getIsAdmin(loginUser.getUserid(), state));
        //加入租户信息
        TenantInfo tenantInfo = loginUser.getTenantinfo(); // this.sysLoginService.getTenantInfo(state);
        tenantInfo.setIsdeptadmin(0);
        // 加入架构信息
        List<DeptinfoPojo> lstdept = null;
        R<List<DeptinfoPojo>> rinfo = this.systemFeignService.getDeptinfolistByUser(loginUser.getUserid(), loginUser.getTenantid());
        if (rinfo.getCode() == 200) lstdept = rinfo.getData();
        tenantInfo.setLstdept(lstdept);
        if (lstdept != null && !lstdept.isEmpty()) {
            tenantInfo.setDeptid(lstdept.get(0).getDeptid());
            tenantInfo.setDeptcode(lstdept.get(0).getDeptcode());
            tenantInfo.setDeptname(lstdept.get(0).getDeptname());
            tenantInfo.setIsdeptadmin(lstdept.get(0).getIsdeptadmin());
        }
        loginUser.setTenantinfo(tenantInfo);

        // 将logUser存入redis  设置5分钟后过期
        String tokenuuid = inksSnowflake.getSnowflake().nextIdStr();
        redisService.setCacheObject("login_tokens:" + tokenuuid, loginUser, 300L, TimeUnit.SECONDS);
        logger.info("企业微信免登 临时token:" + tokenuuid);
        // 参数中获得前端登录页 + tokenuuid
        loginurl = loginurl.replace("{key}", tokenuuid);
        logger.info("企业微信免登 loginurl:" + loginurl);
        // 抛出网址
        // response.addCookie(new Cookie("inks_token",tokenuuid));
        response.sendRedirect(loginurl);
    }

    @ApiOperation(value = "读取LoginUser用户信息", notes = "读取LoginUser解析用户信息", produces = "application/json")
    @GetMapping("/getLoginUser")
    @ResponseBody
    public R<Map<String, Object>> getLoginUser(String key) {
        //解析用户信息
        LoginUser loginUser = tokenService.getLoginUser(key);
        try {
            if (loginUser == null) {
                logger.info("key已过期失效：" + key);
                return R.fail("key已过期失效");
            }
            logger.info("Loginuser：" + loginUser);
            //删除缓存用户信息
            // redisService.deleteObject("login_tokens:"+key);
            R<Map<String, Object>> rmap = this.systemFeignService.updateToken(key);
            if (rmap.getCode() == 200) {
                logger.info("免登最终token：" + rmap.getData().get("access_token").toString());
                //重新存入redis
                Map<String, Object> map = rmap.getData();   //tokenService.createToken(loginUser);
                return R.ok(map);
            } else {
                return R.fail("key更新Token失效");
            }
        } catch (Exception e) {
            logger.warn(e.getMessage());
            return R.fail(e.getMessage());
        }
    }
}
