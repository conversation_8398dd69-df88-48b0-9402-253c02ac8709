package inks.auth.controller;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiSnsGetuserinfoBycodeRequest;
import com.dingtalk.api.request.OapiUserGetbyunionidRequest;
import com.dingtalk.api.response.OapiSnsGetuserinfoBycodeResponse;
import com.dingtalk.api.response.OapiUserGetbyunionidResponse;
import inks.api.feign.SystemFeignService;
import inks.auth.service.JustAuthService;
import inks.auth.service.SysLoginService;
import inks.auth.utils.PrintColor;
import inks.auth.utils.ding.AccessTokenUtil;
import inks.common.core.constant.ConfigConstant;
import inks.common.core.domain.DeptinfoPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.domain.TenantInfo;
import inks.common.core.text.inksSnowflake;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Controller
@RequestMapping("/dinglogin")
public class DingLoginController {

    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(DingLoginController.class);
    @Resource
    private JustAuthService justAuthService;
    @Resource
    private TokenService tokenService;
    @Resource
    private RedisService redisService;
    @Resource
    private SystemFeignService systemFeignService;
    @Resource
    private SysLoginService sysLoginService;

    // 根据索引获取跳转地址 http://dev.inksyun.com:31202/#/?key={key};http://dev.inksyun.com:31203/#/?key={key};
    public static String getJumpAddressByNo(String addresses, Integer no) {
        String[] addressArray = addresses.trim().split(";");
        if (no == null || no < 0 || no >= addressArray.length) {
            no = 0;
        }
        return addressArray[no];
    }

    public static void main(String[] args) {
        String addresses = "http://dev.inksyun.com:31202/#/?key={key}";
        String jumpAddress = getJumpAddressByNo(addresses, 1);
        System.out.println(jumpAddress);
    }

    /**
     * 获取授权用户的个人信息 openapi@dingtalk
     * code 临时授权码
     * state 租户ID
     * no 跳转地址的索引, 跳转地址格式为:http://dev.inksyun.com:31202/#/?key={key};http://dev.inksyun.com:31203/#/?key={key}
     * 以分号分隔多个跳转地址,no相当于索引,确定是第几个跳转地址 (no为0或不传时默认第一个跳转地址, 若只有一个跳转地址,则忽略no)
     *
     * @return
     * @throws Exception ServiceResult<Map<String,Object>> 2020-11-4
     */
    @RequestMapping(value = "/redirectApp", method = RequestMethod.GET)
    public void redirectApp(
            @RequestParam(required = false, defaultValue = "0") Integer no,
            @RequestParam("code") String code,
            @RequestParam("state") String state,
            HttpServletRequest request,
            HttpServletResponse response) throws Exception {
        PrintColor.red("/redirectApp开始钉钉免登 code：" + code + " state：" + state + " no:" + no);
        logger.info("开始App免登 code：" + code);
        logger.info("开始App免登 state：" + state);
        logger.info("开始App免登 no：" + no);

        String loginurl = "";
        R<Map<String, String>> rcfg = systemFeignService.getConfigTenAll(0, state, "");
        if (rcfg.getCode() == 200) {
            Map<String, String> mapcfg = rcfg.getData();
            AccessTokenUtil.AppKey = mapcfg.get(ConfigConstant.DING_APPKEY);//"system.ding.appkey"
            logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
            AccessTokenUtil.AppSecret = mapcfg.get(ConfigConstant.DING_APPSECRET);//"system.ding.appsecret"
            logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);
            loginurl = mapcfg.get(ConfigConstant.DING_APPLOGINURL);//"system.ding.apploginurl"
            logger.info("钉钉免登 完整AppLoginUrl:" + loginurl);
            loginurl = getJumpAddressByNo(loginurl, no);
            logger.info("钉钉免登 通过no实际跳转AppLoginUrl:" + loginurl);
        } else {
            logger.info("获取Config参数失败" + rcfg.getMsg());
            R.fail("获取Config参数失败");
        }


        // 获取access_token，注意正式代码要有异常流处理
        String access_token = AccessTokenUtil.getToken();
        logger.info("钉钉免登 Token:" + access_token);

        // 通过临时授权码获取授权用户的个人信息
        DefaultDingTalkClient client2 = new DefaultDingTalkClient("https://oapi.dingtalk.com/sns/getuserinfo_bycode");
        OapiSnsGetuserinfoBycodeRequest reqBycodeRequest = new OapiSnsGetuserinfoBycodeRequest();

        reqBycodeRequest.setTmpAuthCode(code);
        OapiSnsGetuserinfoBycodeResponse bycodeResponse = client2.execute(reqBycodeRequest, AccessTokenUtil.AppKey, AccessTokenUtil.AppSecret);

        // 根据unionid获取钉钉的userId
        String unionid = bycodeResponse.getUserInfo().getUnionid();
        DingTalkClient clientDingTalkClient = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/user/getbyunionid");
        OapiUserGetbyunionidRequest reqGetbyunionidRequest = new OapiUserGetbyunionidRequest();
        reqGetbyunionidRequest.setUnionid(unionid);
        OapiUserGetbyunionidResponse oapiUserGetbyunionidResponse = clientDingTalkClient.execute(reqGetbyunionidRequest, access_token);

        // 根据获取用户钉钉的userId,即PiJustAuth.AuthUuid
        String userid = oapiUserGetbyunionidResponse.getResult().getUserid();

        // 按userid + Tid 通第三方登录表，返回loginUser对象
        LoginUser loginUser = this.justAuthService.getLoginUserByJust(userid, "ding", state);
        if (loginUser == null) {
            logger.info("钉钉免登: 未找到第三方登录对应用户");
            response.sendRedirect("");
            return;
        }

        // 设置租户信息
        // loginUser.setTenantid(state);
        // loginUser.setIsadmin(this.sysLoginService.getIsAdmin(loginUser.getUserid(), state));
        //加入租户信息
        TenantInfo tenantInfo = this.sysLoginService.getTenantInfo(state);
        tenantInfo.setIsdeptadmin(0);
        // 加入架构信息
        List<DeptinfoPojo> lstdept = null;
        R<List<DeptinfoPojo>> rinfo = this.systemFeignService.getDeptinfolistByUser(loginUser.getUserid(), loginUser.getTenantid());
        if (rinfo.getCode() == 200) lstdept = rinfo.getData();
        tenantInfo.setLstdept(lstdept);
        if (CollectionUtils.isNotEmpty(lstdept)) {
            tenantInfo.setDeptid(lstdept.get(0).getDeptid());
            tenantInfo.setDeptcode(lstdept.get(0).getDeptcode());
            tenantInfo.setDeptname(lstdept.get(0).getDeptname());
            tenantInfo.setIsdeptadmin(lstdept.get(0).getIsdeptadmin());
        }
        loginUser.setTenantinfo(tenantInfo);


        // 将logUser存入redis  设置5分钟后过期
        String tokenuuid = inksSnowflake.getSnowflake().nextIdStr();
        redisService.setCacheObject("login_tokens:" + tokenuuid, loginUser, 300L, TimeUnit.SECONDS);
        System.out.println("钉钉免登 临时token:" + tokenuuid);
        // 参数中获得前端登录页 + tokenuuid
        loginurl = loginurl.replace("{key}", tokenuuid);
        PrintColor.red("跳转免登 AppLoginUrl:" + loginurl);
        response.sendRedirect(loginurl);
    }

    /**
     * 获取授权用户的个人信息 openapi@dingtalk
     *
     * @return
     * @throws Exception ServiceResult<Map<String,Object>> 2020-11-4
     */
    @RequestMapping(value = "/redirectWeb", method = RequestMethod.GET)
    public void redirectWeb(
            @RequestParam(required = false, defaultValue = "0") Integer no,
            @RequestParam("code") String code,
            @RequestParam("state") String state,
            HttpServletRequest request,
            HttpServletResponse response) throws Exception {

        logger.info("开始Web免登 code：" + code);
        logger.info("开始Web免登 state：" + state);

        logger.info("------开始Web钉钉免登-------");

        String loginurl = "";
        R<Map<String, String>> rcfg = systemFeignService.getConfigTenAll(0, state, "");
        if (rcfg.getCode() == 200) {
            Map<String, String> mapcfg = rcfg.getData();
            AccessTokenUtil.AppKey = mapcfg.get(ConfigConstant.DING_APPKEY);
            logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
            AccessTokenUtil.AppSecret = mapcfg.get(ConfigConstant.DING_APPSECRET);
            logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);
            loginurl = mapcfg.get(ConfigConstant.DING_WEBLOGINURL);
            logger.info("钉钉免登 完整WebLoginUrl:" + loginurl);
            loginurl = getJumpAddressByNo(loginurl, no);
            logger.info("钉钉免登 通过no实际跳转WebLoginUrl:" + loginurl);
        } else {
            logger.info("获取Config参数失败" + rcfg.getMsg());
            R.fail("获取Config参数失败");
        }


        // 获取access_token，注意正式代码要有异常流处理
        String access_token = AccessTokenUtil.getToken();
        logger.info("钉钉免登 Token:" + access_token);

        // 通过临时授权码获取授权用户的个人信息
        DefaultDingTalkClient client2 = new DefaultDingTalkClient("https://oapi.dingtalk.com/sns/getuserinfo_bycode");
        OapiSnsGetuserinfoBycodeRequest reqBycodeRequest = new OapiSnsGetuserinfoBycodeRequest();

        reqBycodeRequest.setTmpAuthCode(code);
        OapiSnsGetuserinfoBycodeResponse bycodeResponse = client2.execute(reqBycodeRequest, AccessTokenUtil.AppKey, AccessTokenUtil.AppSecret);

        // 根据unionid获取钉钉的userId
        String unionid = bycodeResponse.getUserInfo().getUnionid();
        DingTalkClient clientDingTalkClient = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/user/getbyunionid");
        OapiUserGetbyunionidRequest reqGetbyunionidRequest = new OapiUserGetbyunionidRequest();
        reqGetbyunionidRequest.setUnionid(unionid);
        OapiUserGetbyunionidResponse oapiUserGetbyunionidResponse = clientDingTalkClient.execute(reqGetbyunionidRequest, access_token);

        // 根据获取用户钉钉的userId,即PiJustAuth.AuthUuid
        String userid = oapiUserGetbyunionidResponse.getResult().getUserid();

        // 按userid + Tid 通第三方登录表，返回loginUser对象
        LoginUser loginUser = this.justAuthService.getLoginUserByJust(userid, "ding", state);
        if (loginUser == null) {
            logger.info("钉钉免登: 未到第三方登录对应用户");
            response.sendRedirect("");
            return;
        }

        // 设置租户信息
        // loginUser.setTenantid(state);
        // loginUser.setIsadmin(this.sysLoginService.getIsAdmin(loginUser.getUserid(), state));
        //加入租户信息
        TenantInfo tenantInfo = this.sysLoginService.getTenantInfo(state);
        tenantInfo.setIsdeptadmin(0);
        // 加入架构信息
        List<DeptinfoPojo> lstdept = null;
        R<List<DeptinfoPojo>> rinfo = this.systemFeignService.getDeptinfolistByUser(loginUser.getUserid(), loginUser.getTenantid());
        if (rinfo.getCode() == 200) lstdept = rinfo.getData();
        tenantInfo.setLstdept(lstdept);
        if (CollectionUtils.isNotEmpty(lstdept)) {
            tenantInfo.setDeptid(lstdept.get(0).getDeptid());
            tenantInfo.setDeptcode(lstdept.get(0).getDeptcode());
            tenantInfo.setDeptname(lstdept.get(0).getDeptname());
            tenantInfo.setIsdeptadmin(lstdept.get(0).getIsdeptadmin());
        }
        loginUser.setTenantinfo(tenantInfo);

        // 将logUser存入redis  设置5分钟后过期
        String tokenuuid = inksSnowflake.getSnowflake().nextIdStr();
        redisService.setCacheObject("login_tokens:" + tokenuuid, loginUser, 300L, TimeUnit.SECONDS);
        System.out.println("钉钉免登 login_tokens:" + tokenuuid);
        // 参数中获得前端登录页 + tokenuuid
        loginurl = loginurl.replace("{key}", tokenuuid);
        PrintColor.red("跳转免登 WebLoginUrl:" + loginurl);
        response.sendRedirect(loginurl);
    }

    @ApiOperation(value = "读取LoginUser用户信息", notes = "读取LoginUser解析用户信息", produces = "application/json")
    @GetMapping("/getLoginUser")
    @ResponseBody
    public R<Map<String, Object>> getLoginUser(String key) {
            LoginUser loginUser = this.tokenService.getLoginUser(key);
        try {
            //解析用户信息
            if (loginUser == null) {
                logger.info("key已过期失效：" + key);
                return R.fail("key已过期失效");
            }
            logger.info("Loginuser：" + loginUser);
            //删除缓存用户信息
            //  redisService.deleteObject("login_tokens:" + key);
            R<Map<String, Object>> rmap = this.systemFeignService.updateToken(key);
            if (rmap.getCode() == 200) {
                logger.info("免登最终token：" + rmap.getData().get("access_token").toString());
                //重新存入redis
                Map<String, Object> map = rmap.getData();   //tokenService.createToken(loginUser);
                return R.ok(map);
            } else {
                return R.fail("key更新Token失效");
            }
        } catch (Exception e) {
            logger.warn(e.getMessage());
            return R.fail(e.getMessage());
        }
    }


}
