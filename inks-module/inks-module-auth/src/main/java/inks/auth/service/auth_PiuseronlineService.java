package inks.auth.service;

import inks.auth.domain.PiuseronlinePojo;


/**
 * 用户在线(Piuseronline)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-13 13:14:43
 */
public interface auth_PiuseronlineService {

    PiuseronlinePojo getEntity(String key, String tid);


    PiuseronlinePojo insert(PiuseronlinePojo piuseronlinePojo);

    PiuseronlinePojo update(PiuseronlinePojo piuseronlinepojo);

    int delete(String key, String tid);

    PiuseronlinePojo getEntityByUserid(String userid, int terminalType, String tenantid);
}
