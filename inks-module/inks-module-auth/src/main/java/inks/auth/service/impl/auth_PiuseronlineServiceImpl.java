package inks.auth.service.impl;

import inks.auth.domain.PiuseronlinePojo;
import inks.auth.domain.PiuseronlineEntity;
import inks.auth.mapper.auth_PiuseronlineMapper;
import inks.auth.service.auth_PiuseronlineService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;

import java.util.Date;

import inks.common.core.text.inksSnowflake;

/**
 * 用户在线(Piuseronline)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-13 13:14:43
 */
@Service("auth_piuseronlineService")
public class auth_PiuseronlineServiceImpl implements auth_PiuseronlineService {
    @Resource
    private auth_PiuseronlineMapper authPiuseronlineMapper;

    @Override
    public PiuseronlinePojo getEntity(String key, String tid) {
        return this.authPiuseronlineMapper.getEntity(key, tid);
    }

    @Override
    public PiuseronlinePojo insert(PiuseronlinePojo piuseronlinePojo) {
        //初始化NULL字段
        if (piuseronlinePojo.getUserid() == null) piuseronlinePojo.setUserid("");
        if (piuseronlinePojo.getUsername() == null) piuseronlinePojo.setUsername("");
        if (piuseronlinePojo.getRealname() == null) piuseronlinePojo.setRealname("");
        if (piuseronlinePojo.getNickname() == null) piuseronlinePojo.setNickname("");
        if (piuseronlinePojo.getTenantid() == null) piuseronlinePojo.setTenantid("");
        if (piuseronlinePojo.getTenantname() == null) piuseronlinePojo.setTenantname("");
        if (piuseronlinePojo.getIpaddress() == null) piuseronlinePojo.setIpaddress("");
        if (piuseronlinePojo.getIplocation() == null) piuseronlinePojo.setIplocation("");
        if (piuseronlinePojo.getMacaddress() == null) piuseronlinePojo.setMacaddress("");
        if (piuseronlinePojo.getBrowsername() == null) piuseronlinePojo.setBrowsername("");
        if (piuseronlinePojo.getHostsystem() == null) piuseronlinePojo.setHostsystem("");
        if (piuseronlinePojo.getTerminaltype() == null) piuseronlinePojo.setTerminaltype(0);
        if (piuseronlinePojo.getToken() == null) piuseronlinePojo.setToken("");
        if (piuseronlinePojo.getLogindate() == null) piuseronlinePojo.setLogindate(new Date());
//     if(piuseronlinePojo.getLastactivitydate()==null) piuseronlinePojo.setLastactivitydate(new Date());
//     if(piuseronlinePojo.getTokenexpirydate()==null) piuseronlinePojo.setTokenexpirydate(new Date());
        PiuseronlineEntity piuseronlineEntity = new PiuseronlineEntity();
        BeanUtils.copyProperties(piuseronlinePojo, piuseronlineEntity);
        //生成雪花id
        piuseronlineEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.authPiuseronlineMapper.insert(piuseronlineEntity);
        return this.getEntity(piuseronlineEntity.getId(), piuseronlineEntity.getTenantid());

    }


    @Override
    public PiuseronlinePojo update(PiuseronlinePojo piuseronlinePojo) {
        PiuseronlineEntity piuseronlineEntity = new PiuseronlineEntity();
        BeanUtils.copyProperties(piuseronlinePojo, piuseronlineEntity);
        this.authPiuseronlineMapper.update(piuseronlineEntity);
        return this.getEntity(piuseronlineEntity.getId(), piuseronlineEntity.getTenantid());
    }

    @Override
    public int delete(String key, String tid) {
        return this.authPiuseronlineMapper.delete(key, tid);
    }

    @Override
    public PiuseronlinePojo getEntityByUserid(String userid, int terminalType, String tenantid) {
        return this.authPiuseronlineMapper.getEntityByUserid(userid, terminalType, tenantid);
    }
}
