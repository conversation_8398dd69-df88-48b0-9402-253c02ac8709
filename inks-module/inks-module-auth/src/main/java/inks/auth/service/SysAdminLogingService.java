package inks.auth.service;



import inks.auth.domain.Piadmin;
import inks.common.core.domain.LoginUser;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.Map;

public interface SysAdminLogingService {
    public LoginUser login(String username, String password, HttpServletRequest request) throws ParseException, Exception;
    public void record(LoginUser loginUser, HttpServletRequest request) throws ParseException;
    //小程序扫码登录
    LoginUser scanLogin(String code, String key, HttpServletRequest request) throws ParseException;

    Piadmin bindOpenid(String userid, String openid);

    String checkHard() throws Exception;

}
