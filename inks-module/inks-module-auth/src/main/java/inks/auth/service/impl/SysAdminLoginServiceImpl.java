package inks.auth.service.impl;

import inks.auth.domain.CiLoginLogEntity;
import inks.auth.domain.Piadmin;
import inks.auth.mapper.CiLoginLogMapper;
import inks.auth.mapper.auth_PiuserMapper;
import inks.auth.mapper.SysAdminLogingMapper;
import inks.auth.service.SysAdminLogingService;
import inks.common.core.constant.UserConstants;
import inks.common.core.domain.LoginUser;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.*;
import inks.common.redis.service.RedisService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.HashMap;
import java.util.Map;

@Service
public class SysAdminLoginServiceImpl implements SysAdminLogingService {
    @Resource
    private SysAdminLogingMapper sysAdminLogingMapper;
    @Resource
    private CiLoginLogMapper ciLoginLogMapper;
    @Resource
    private RedisService redisService;

    private final String SCANLOGIN_CODE = "scanlogin_code:";

    @Resource
    private auth_PiuserMapper authPiuserMapper;


    @Override
    public LoginUser login(String username, String password, HttpServletRequest request) throws Exception {
        //构建登陆日志对象
        CiLoginLogEntity ciLoginLogEntity = new CiLoginLogEntity();
        ciLoginLogEntity.setLoginTime(BillCodeUtil.newDate());
        ciLoginLogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        Map<String, String> map = UserAgentUtil.getOs(request.getHeader("User-Agent"));
        ciLoginLogEntity.setBrowserName(map.get("browser"));
        ciLoginLogEntity.setHostSystem(map.get("os"));
        String ip = UserAgentUtil.getIpAddr(request);
        String address = UserAgentUtil.getRealAddressByIP(ip);
        ciLoginLogEntity.setIpAddr(ip);
        ciLoginLogEntity.setLoginLocation(address);
//        if(address.equals("内网IP")){
//            ciLoginLogEntity.setIpAddr("内网IP");
//            ciLoginLogEntity.setLoginLocation(InksConstants.UNKNOWN);
//        }else{
//            ciLoginLogEntity.setIpAddr(UserAgentUtil.getIpAddr(request));
//            ciLoginLogEntity.setLoginLocation(AddressUtils.getAddress(UserAgentUtil.getIpAddr(request)));
//        }
        if (StringUtils.isAnyBlank(username, password)) {
            throw new BaseBusinessException("用户/密码必须填写");
        }
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            throw new BaseBusinessException("用户密码不在指定范围");
        }
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            throw new BaseBusinessException("用户名不在指定范围");
        }
        password = AESUtil.Encrypt(password);
        int count = sysAdminLogingMapper.count(username);
        if (count < 1) {
            ciLoginLogEntity.setUserid("");
            ciLoginLogEntity.setUserName(username);
            ciLoginLogEntity.setRealName("");
            ciLoginLogEntity.setLoginStatus(1L);
            ciLoginLogEntity.setDirection("登录");
            ciLoginLogEntity.setLoginMsg("管理员：" + username + "登录失败,登录时间："
                    + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",原因：账号不存在" + ",登录系统："
                    + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
                    + ciLoginLogEntity.getBrowserName() + ",登录地址："
                    + ciLoginLogEntity.getLoginLocation());
            ciLoginLogMapper.insert(ciLoginLogEntity);
            throw new BaseBusinessException("账号不存在");
        }
        LoginUser loginUser = sysAdminLogingMapper.login(username, password);
        if (loginUser == null) {
            ciLoginLogEntity.setUserid("");
            ciLoginLogEntity.setUserName(username);
            ciLoginLogEntity.setRealName("");
            ciLoginLogEntity.setLoginStatus(1L);
            ciLoginLogEntity.setDirection("登录");
            ciLoginLogEntity.setLoginMsg("管理员：" + username + "登录失败,登录时间："
                    + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",原因：密码错误" + ",登录系统："
                    + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
                    + ciLoginLogEntity.getBrowserName() + ",登录地址："
                    + ciLoginLogEntity.getLoginLocation());
            ciLoginLogMapper.insert(ciLoginLogEntity);
            throw new BaseBusinessException("账号密码错误");
        }
        ciLoginLogEntity.setUserid(loginUser.getUserid());
        ciLoginLogEntity.setUserName(username);
        ciLoginLogEntity.setRealName(loginUser.getRealname());
        ciLoginLogEntity.setDirection("登录");
        ciLoginLogEntity.setLoginStatus(0L);
        ciLoginLogEntity.setLoginMsg("管理员：" + username + "登录成功,登录时间："
                + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",登录系统："
                + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
                + ciLoginLogEntity.getBrowserName() + ",登录地址："
                + ciLoginLogEntity.getLoginLocation());
        ciLoginLogMapper.insert(ciLoginLogEntity);
        return loginUser;
    }

    //记录用户退出日志
    @Override
    public void record(LoginUser loginUser, HttpServletRequest request) throws ParseException {
        CiLoginLogEntity ciLoginLogEntity = new CiLoginLogEntity();
        ciLoginLogEntity.setLoginTime(BillCodeUtil.newDate());
        ciLoginLogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        Map<String, String> map = UserAgentUtil.getOs(request.getHeader("User-Agent"));
        ciLoginLogEntity.setBrowserName(map.get("browser"));
        ciLoginLogEntity.setHostSystem(map.get("os"));
        ciLoginLogEntity.setIpAddr(UserAgentUtil.getIpAddr(request));
        ciLoginLogEntity.setLoginLocation(AddressUtils.getRealAddressByIP(UserAgentUtil.getIpAddr(request)));
        ciLoginLogEntity.setUserid(loginUser.getUserid());
        ciLoginLogEntity.setUserName(loginUser.getUsername());
        ciLoginLogEntity.setRealName(loginUser.getRealname());
        ciLoginLogEntity.setDirection("登出");
        ciLoginLogEntity.setLoginStatus(1L);
        ciLoginLogEntity.setLoginMsg("管理员：" + loginUser.getUsername() + "退出系统,退出时间："
                + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",操作系统："
                + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
                + ciLoginLogEntity.getBrowserName() + ",操作地址："
                + ciLoginLogEntity.getLoginLocation());
        ciLoginLogMapper.insert(ciLoginLogEntity);
    }

    /**
     * @return Map<Object>
     * @Description
     * <AUTHOR>
     * @param[1] openid  前端传过来的openid (20240520 pms废弃,改为来自OEM公众号的openid)
     * @param[2] key pms扫码登录时传来的redis中的key,oem不需要传
     * @param[3] request 读取ip
     * @time 2023/3/21 14:13
     */
    @Override
    public LoginUser scanLogin(String openid, String key, HttpServletRequest request) throws ParseException {
        //构建登陆日志对象
        CiLoginLogEntity ciLoginLogEntity = new CiLoginLogEntity();
        ciLoginLogEntity.setLoginTime(BillCodeUtil.newDate());
        ciLoginLogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        Map<String, String> map = UserAgentUtil.getOs(request.getHeader("User-Agent"));
        ciLoginLogEntity.setBrowserName(map.get("browser"));
        ciLoginLogEntity.setHostSystem(map.get("os"));
        String ip = UserAgentUtil.getIpAddr(request);
        String address = UserAgentUtil.getRealAddressByIP(ip);
        ciLoginLogEntity.setIpAddr(ip);
        ciLoginLogEntity.setLoginLocation(address);

        //开始扫码登录(pms直接传入了openid) 20240520 pms废弃,改为来自OEM公众号的openid
        //通过openid获取是否有用户信息
        Piadmin piadmin = authPiuserMapper.getEntityByOpenid(openid);
        if (piadmin != null) {
            //登录成功-日志
            ciLoginLogEntity.setUserid(piadmin.getAdminid());
            ciLoginLogEntity.setUserName(piadmin.getUsername());
            ciLoginLogEntity.setRealName(piadmin.getRealname());
            ciLoginLogEntity.setDirection("登录");
            ciLoginLogEntity.setLoginStatus(0L);
            ciLoginLogEntity.setLoginMsg("管理员：" + piadmin.getUsername() + ",openid登录成功,登录时间："
                    + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",登录系统："
                    + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
                    + ciLoginLogEntity.getBrowserName() + ",登录地址："
                    + ciLoginLogEntity.getLoginLocation());
            ciLoginLogMapper.insert(ciLoginLogEntity);
        } else {
            if (key != null) {
                //piadmin为空，说明用户openid不在PiAdmin表中
                Map<String, Object> missionMsg = new HashMap<>();
                missionMsg.put("code", "403");
                missionMsg.put("msg", "用户无权限");
                this.redisService.setCacheMapValue(SCANLOGIN_CODE, key, missionMsg);
                redisService.expire(SCANLOGIN_CODE, 60 * 6);
            }
            //登录失败-日志
            ciLoginLogEntity.setUserid("");
            //ciLoginLogEntity.setUserName(username);
            ciLoginLogEntity.setRealName("");
            ciLoginLogEntity.setLoginStatus(1L);
            ciLoginLogEntity.setDirection("登录");
            ciLoginLogEntity.setLoginMsg("openid:" + openid + "登录失败,登录时间："
                    + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",原因：openid不存在" + ",登录系统："
                    + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
                    + ciLoginLogEntity.getBrowserName() + ",登录地址："
                    + ciLoginLogEntity.getLoginLocation());
            ciLoginLogMapper.insert(ciLoginLogEntity);
            throw new BaseBusinessException("openid不存在");
        }
        LoginUser loginUser = new LoginUser();
        loginUser.setUserid(piadmin.getAdminid());
        loginUser.setUsername(piadmin.getUsername());
        loginUser.setRealname(piadmin.getRealname());
        return loginUser;
    }

    @Override
    public Piadmin bindOpenid(String userid, String openid) {
        authPiuserMapper.bindOpenid(userid, openid);
        return authPiuserMapper.getEntityByOpenid(openid);
    }

    @Override
    public String checkHard() throws Exception {
//        String nowSN = SN.getSN();//当前设备SN码
//        // 获取加密的密文 原文为:{"code":"oms","sn":"123456","key":"xxxx-xxxx-xxxx","uc":10,"ex":1704038400}
//        String systemRegistrkey = sysAdminLogingMapper.getSystemRegistrkey(InksConstants.DEFAULT_TENANT);
//        if (isBlank(systemRegistrkey)) {
//            //HashMap<String, Object> ciConfigMap = new HashMap<>();
//            //ciConfigMap.put("code", "403");
//            throw new BaseBusinessException("未注册! default: system.registrkey为空");
//        }
//        // RSA私钥解密为原文
//        String decrypt = RSADecryptor.decrypt(systemRegistrkey, MyRSA.PRIVATE_KEY);
//        Map<String, String> map = JSON.parseObject(decrypt, new TypeReference<Map<String, String>>() {
//        });
//        String sn = map.get("sn");
//        if (sn.equals(nowSN)) {
//            return decrypt;
//        } else {
//            map.put("sn", nowSN);
//            map.put("checkFail", "1");
////            return R.info(402, "硬件校验失败", JSON.toJSONString(map));
//            return JSON.toJSONString(map);
//        }
        return null;
    }
}
