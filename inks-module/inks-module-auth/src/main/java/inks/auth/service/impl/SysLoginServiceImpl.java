package inks.auth.service.impl;

import cn.hutool.extra.servlet.ServletUtil;
import eu.bitwalker.useragentutils.UserAgent;
import inks.auth.domain.CiLoginLogEntity;
import inks.auth.domain.PiTenant;
import inks.auth.domain.Piuser;
import inks.auth.domain.PiuserLogin;
import inks.auth.form.RegisterBody;
import inks.auth.mapper.CiLoginLogMapper;
import inks.auth.mapper.PiTenantMapper;
import inks.auth.mapper.PiuserLoginMapper;
import inks.auth.mapper.auth_PiuserMapper;
import inks.auth.service.SysLoginService;
import inks.common.core.constant.UserConstants;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.TenantInfo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: song
 * @Date: 2021/09/04/9:36
 * @Description:
 */
@Service
public class SysLoginServiceImpl implements SysLoginService {
    @Resource
    private auth_PiuserMapper authPiuserMapper;
    @Resource
    private PiTenantMapper piTenantMapper;
    @Resource
    private PiuserLoginMapper piuserLoginMapper;
    @Resource
    private CiLoginLogMapper ciLoginLogMapper;

    public LoginUser login(String username, String password, HttpServletRequest request) throws Exception {
        //构建登陆日志对象
        CiLoginLogEntity ciLoginLogEntity = new CiLoginLogEntity();
        ciLoginLogEntity.setLoginTime(BillCodeUtil.newDate());
        ciLoginLogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());

        //获取请求浏览器操作系统
        UserAgent userAgent = UserAgent.parseUserAgentString(request.getHeader("User-Agent"));
        String os = userAgent.getOperatingSystem().getName();
        String browser = userAgent.getBrowser().getName();
        ciLoginLogEntity.setBrowserName(browser);
        ciLoginLogEntity.setHostSystem(os);
        String ip = ServletUtil.getClientIP(request);
        // 取真实地址
        String address = UserAgentUtil.getRealAddressByIP(ip);
        //PrintColor.red("=========>>>ip:" + ip + ",address:" + address);
        //// 打印各个头字段的值
        //String[] headers = {"X-Forwarded-For", "X-Real-IP", "Proxy-Client-IP", "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR"};
        //for (String header : headers) {
        //    String headerValue = request.getHeader(header);
        //    PrintColor.red("Header: " + header + ", Value: " + headerValue);
        //}
        ciLoginLogEntity.setIpAddr(ip);
        ciLoginLogEntity.setLoginLocation(address);

        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            throw new BaseBusinessException("用户/密码必须填写");
        }
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            throw new BaseBusinessException("用户密码不在指定范围");
        }
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            throw new BaseBusinessException("用户名不在指定范围");
        }
        //密码MD5加密
        password = AESUtil.Encrypt(password);
        //查询账号是否存在
        int count = authPiuserMapper.getPruserCount(username);
        if (count < 1) {
            ciLoginLogEntity.setUserid("");
            ciLoginLogEntity.setUserName(username);
            ciLoginLogEntity.setRealName("");
            ciLoginLogEntity.setDirection("登录");
            ciLoginLogEntity.setLoginStatus(1L);
            ciLoginLogEntity.setLoginMsg("用户：" + username + "登录失败,登录时间："
                    + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",原因：账号不存在" + ",登录系统："
                    + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
                    + ciLoginLogEntity.getBrowserName() + ",登录地址："
                    + ciLoginLogEntity.getLoginLocation());
            ciLoginLogMapper.insert(ciLoginLogEntity);
            throw new BaseBusinessException("账号不存在");
        }
        //查询账号密码是否正确正确返回登录对象错误返回空
        LoginUser loginUser = authPiuserMapper.login(username, password);
        if (loginUser == null) {
            ciLoginLogEntity.setUserid("");
            ciLoginLogEntity.setUserName(username);
            ciLoginLogEntity.setRealName("");
            ciLoginLogEntity.setDirection("登录");
            ciLoginLogEntity.setLoginStatus(1L);
            ciLoginLogEntity.setLoginMsg("用户：" + username + "登录失败,登录时间："
                    + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",原因：密码错误" + ",登录系统："
                    + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
                    + ciLoginLogEntity.getBrowserName() + ",登录地址："
                    + ciLoginLogEntity.getLoginLocation());
            ciLoginLogMapper.insert(ciLoginLogEntity);
            throw new BaseBusinessException("账号密码错误");
        }
        ciLoginLogEntity.setUserid(loginUser.getUserid());
        ciLoginLogEntity.setUserName(username);
        ciLoginLogEntity.setRealName(loginUser.getRealname());
        ciLoginLogEntity.setDirection("登录");
        ciLoginLogEntity.setLoginStatus(0L);
        ciLoginLogEntity.setLoginMsg("用户：" + username + "登录成功,登录时间："
                + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",登录系统："
                + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
                + ciLoginLogEntity.getBrowserName() + ",登录地址："
                + ciLoginLogEntity.getLoginLocation());
        ciLoginLogMapper.insert(ciLoginLogEntity);
        loginUser.setLogid(ciLoginLogEntity.getId());
        return loginUser;
    }

    @Override
    public LoginUser loginByOpenid(String openid, HttpServletRequest request) throws ParseException {

        LoginUser loginUser = authPiuserMapper.loginByOpenid(openid);
        if (loginUser == null) {
            throw new BaseBusinessException("当前微信未绑定系统账号");
        }
        //构建登陆日志对象
        CiLoginLogEntity ciLoginLogEntity = new CiLoginLogEntity();
        ciLoginLogEntity.setLoginTime(BillCodeUtil.newDate());
        ciLoginLogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());

        //获取请求浏览器操作系统
        UserAgent userAgent = UserAgent.parseUserAgentString(request.getHeader("User-Agent"));
        String os = userAgent.getOperatingSystem().getName();
        String browser = userAgent.getBrowser().getName();
        ciLoginLogEntity.setBrowserName(browser);
        ciLoginLogEntity.setHostSystem(os);

        String ip = UserAgentUtil.getIpAddr(request);
        String address = UserAgentUtil.getRealAddressByIP(ip);
        ciLoginLogEntity.setIpAddr(ip);
        ciLoginLogEntity.setLoginLocation(address);
        ciLoginLogEntity.setUserid(loginUser.getUserid());
        ciLoginLogEntity.setUserName(loginUser.getUsername());
        ciLoginLogEntity.setRealName(loginUser.getRealname());
        ciLoginLogEntity.setDirection("微信登录");
        ciLoginLogEntity.setLoginStatus(0L);
        ciLoginLogEntity.setLoginMsg("微信用户：" + loginUser.getUsername() + "登录成功,登录时间："
                + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",登录系统："
                + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
                + ciLoginLogEntity.getBrowserName() + ",登录地址："
                + ciLoginLogEntity.getLoginLocation());
        ciLoginLogMapper.insert(ciLoginLogEntity);
        loginUser.setLogid(ciLoginLogEntity.getId());
        return loginUser;
    }

    @Override
    @Transactional
    public LoginUser register(RegisterBody registerBody, HttpServletRequest request) throws Exception {
        String userName = registerBody.getUserName();
        String password = AESUtil.Encrypt(registerBody.getPassword());
        String id = inksSnowflake.getSnowflake().nextIdStr();
        try {
            if (authPiuserMapper.getUserByName(userName) != null) {
                throw new BaseBusinessException("");
            }
            Piuser piuser = new Piuser();
            piuser.setUserid(id);
            piuser.setUserName(userName);
            piuser.setRealName(userName);
            piuser.setLister(id);
            piuser.setCreateby(id);
            piuser.setCreateDate(BillCodeUtil.newDate());
            piuser.setModifyData(BillCodeUtil.newDate());
            authPiuserMapper.inserPiuser(piuser);
            PiuserLogin piuserLogin = new PiuserLogin();
            piuserLogin.setId(inksSnowflake.getSnowflake().nextIdStr());
            piuserLogin.setUserId(id);
            piuserLogin.setUserPassword(password);
            piuserLogin.setLister(id);
            piuserLogin.setCreateDate(BillCodeUtil.newDate());
            piuserLogin.setModifyDate(BillCodeUtil.newDate());
            piuserLogin.setCheckipaddr(0);
            piuserLoginMapper.insertPiuserLogin(piuserLogin);
            return login(userName, registerBody.getPassword(), request);
        } catch (BaseBusinessException e) {
            throw new BaseBusinessException(e.getMessage());
        } catch (Exception ex) {
            throw new BaseBusinessException("注册失败,请联系系统管理人员" + ex.getMessage());
        }
    }

    @Override
    public void inspect(String userName) {
        if (authPiuserMapper.getUserByName(userName) != null) {
            throw new BaseBusinessException("用户名已存在,请重新输入");
        }
    }

    @Override
    public List<PiTenant> getPiTenant(String id) {
        List<PiTenant> list = new ArrayList<>();
        try {
            list = piTenantMapper.getPageList(id);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
        return list;
    }

    @Override
    public void record(LoginUser loginUser, HttpServletRequest request) throws ParseException {
        CiLoginLogEntity ciLoginLogEntity = new CiLoginLogEntity();
        ciLoginLogEntity.setLoginTime(BillCodeUtil.newDate());
        ciLoginLogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        Map<String, String> map = UserAgentUtil.getOs(request.getHeader("User-Agent"));
        ciLoginLogEntity.setBrowserName(map.get("browser"));
        ciLoginLogEntity.setHostSystem(map.get("os"));
        ciLoginLogEntity.setIpAddr(UserAgentUtil.getIpAddr(request));
        ciLoginLogEntity.setLoginLocation(AddressUtils.getRealAddressByIP(UserAgentUtil.getIpAddr(request)));
        ciLoginLogEntity.setUserid(loginUser.getUserid());
        ciLoginLogEntity.setUserName(loginUser.getUsername());
        ciLoginLogEntity.setRealName(loginUser.getRealname());
        ciLoginLogEntity.setDirection("登出");
        ciLoginLogEntity.setLoginStatus(0L);
        ciLoginLogEntity.setLoginMsg("用户：" + loginUser.getUsername() + "退出系统,退出时间："
                + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",操作系统："
                + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
                + ciLoginLogEntity.getBrowserName() + ",操作地址："
                + ciLoginLogEntity.getLoginLocation());
        ciLoginLogMapper.insert(ciLoginLogEntity);
    }

    @Override
    public Integer getIsAdmin(String key, String tid) {
        try {
            return piTenantMapper.getIsAdmin(key, tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    //忘记密码
    @Override
    public void updatePwd(String key, String pwd) {
        try {
            String userid = piTenantMapper.getUserId(key);
            String password = AESUtil.Encrypt(pwd);
            piuserLoginMapper.updatePwd(userid, password);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    // 获得租户信息
    @Override
    public TenantInfo getTenantInfo(String key) {
        return this.piTenantMapper.getTenantInfo(key);
    }


}
