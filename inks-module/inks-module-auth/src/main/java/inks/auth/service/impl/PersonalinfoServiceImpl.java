package inks.auth.service.impl;

import inks.auth.mapper.PersonalinfoMapper;
import inks.auth.service.PersonalinfoService;
import inks.common.core.exception.BaseBusinessException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
@Service
public class PersonalinfoServiceImpl implements PersonalinfoService {
    @Resource
    private PersonalinfoMapper personalinfoMapper;
    @Override
    public Map<String, Object> getInfo(String key) {
        try {
            Map<String, Object> map = personalinfoMapper.getInfo(key);
            return map;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }
}
