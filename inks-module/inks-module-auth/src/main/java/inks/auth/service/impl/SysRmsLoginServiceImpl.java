package inks.auth.service.impl;

import com.alibaba.fastjson.JSONArray;
import inks.auth.domain.CiLoginLogEntity;
import inks.auth.domain.PirmsuserPojo;
import inks.auth.mapper.CiLoginLogMapper;
import inks.auth.mapper.SysRmsLogingMapper;
import inks.auth.service.SysRmsLogingService;
import inks.common.core.constant.UserConstants;
import inks.common.core.domain.JustauthPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.*;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class SysRmsLoginServiceImpl implements SysRmsLogingService {
    @Resource
    private CiLoginLogMapper ciLoginLogMapper;
    @Resource
    private SysRmsLogingMapper sysRmsLogingMapper;
    @Resource
    private RedisService redisService;
    private final String SCANLOGIN_CODE = "scanlogin_code:";//扫码登录

    @Override
    public LoginUser login(String username, String password, HttpServletRequest request) throws ParseException, Exception {
        //构建登陆日志对象
        CiLoginLogEntity ciLoginLogEntity = new CiLoginLogEntity();
        ciLoginLogEntity.setLoginTime(BillCodeUtil.newDate());
        ciLoginLogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        Map<String, String> map = UserAgentUtil.getOs(request.getHeader("User-Agent"));
        ciLoginLogEntity.setBrowserName(map.get("browser"));
        ciLoginLogEntity.setHostSystem(map.get("os"));
        String ip = UserAgentUtil.getIpAddr(request);
        String address = UserAgentUtil.getRealAddressByIP(ip);
        ciLoginLogEntity.setIpAddr(ip);
        ciLoginLogEntity.setLoginLocation(address);

        if (StringUtils.isAnyBlank(username, password)) {
            throw new BaseBusinessException("用户/密码必须填写");
        }
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            throw new BaseBusinessException("用户密码不在指定范围");
        }
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            throw new BaseBusinessException("用户名不在指定范围");
        }
        password = AESUtil.Encrypt(password);
        Integer count = sysRmsLogingMapper.count(username);
        if (count < 1) {
            ciLoginLogEntity.setUserid("");
            ciLoginLogEntity.setUserName(username);
            ciLoginLogEntity.setRealName("");
            ciLoginLogEntity.setLoginStatus(1L);
            ciLoginLogEntity.setDirection("登录");
            ciLoginLogEntity.setLoginMsg("Rms用户：" + username + "登录失败,登录时间："
                    + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",原因：账号不存在" + ",登录系统："
                    + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
                    + ciLoginLogEntity.getBrowserName() + ",登录地址："
                    + ciLoginLogEntity.getLoginLocation());
            ciLoginLogMapper.insert(ciLoginLogEntity);
            throw new BaseBusinessException("账号不存在");
        }
        LoginUser loginUser = sysRmsLogingMapper.login(username, password);// 20240607 loginUser加入数据标签DataLabel
        if (loginUser == null) {
            ciLoginLogEntity.setUserid("");
            ciLoginLogEntity.setUserName(username);
            ciLoginLogEntity.setRealName("");
            ciLoginLogEntity.setLoginStatus(1L);
            ciLoginLogEntity.setDirection("登录");
            ciLoginLogEntity.setLoginMsg("Rms用户：" + username + "登录失败,登录时间："
                    + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",原因：密码错误" + ",登录系统："
                    + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
                    + ciLoginLogEntity.getBrowserName() + ",登录地址："
                    + ciLoginLogEntity.getLoginLocation());
            ciLoginLogMapper.insert(ciLoginLogEntity);
            throw new BaseBusinessException("账号密码错误");
        }
        ciLoginLogEntity.setUserid(loginUser.getUserid());
        ciLoginLogEntity.setUserName(username);
        ciLoginLogEntity.setRealName(loginUser.getRealname());
        ciLoginLogEntity.setDirection("登录");
        ciLoginLogEntity.setLoginStatus(0L);
        ciLoginLogEntity.setLoginMsg("Rms用户：" + username + "登录成功,登录时间："
                + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",登录系统："
                + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
                + ciLoginLogEntity.getBrowserName() + ",登录地址："
                + ciLoginLogEntity.getLoginLocation());
        ciLoginLogMapper.insert(ciLoginLogEntity);
        loginUser.setLogid(ciLoginLogEntity.getId());
        return loginUser;
    }

    @Override
    public void record(LoginUser loginUser, HttpServletRequest request) throws ParseException {
        CiLoginLogEntity ciLoginLogEntity = new CiLoginLogEntity();
        ciLoginLogEntity.setLoginTime(BillCodeUtil.newDate());
        ciLoginLogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        Map<String, String> map = UserAgentUtil.getOs(request.getHeader("User-Agent"));
        ciLoginLogEntity.setBrowserName(map.get("browser"));
        ciLoginLogEntity.setHostSystem(map.get("os"));
        ciLoginLogEntity.setIpAddr(UserAgentUtil.getIpAddr(request));
        ciLoginLogEntity.setLoginLocation(AddressUtils.getRealAddressByIP(UserAgentUtil.getIpAddr(request)));
        ciLoginLogEntity.setUserid(loginUser.getUserid());
        ciLoginLogEntity.setUserName(loginUser.getUsername());
        ciLoginLogEntity.setRealName(loginUser.getRealname());
        ciLoginLogEntity.setDirection("登出");
        ciLoginLogEntity.setLoginStatus(1L);
        ciLoginLogEntity.setLoginMsg("Rms用户：" + loginUser.getUsername() + "退出系统,退出时间："
                + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",操作系统："
                + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
                + ciLoginLogEntity.getBrowserName() + ",操作地址："
                + ciLoginLogEntity.getLoginLocation());
        ciLoginLogMapper.insert(ciLoginLogEntity);
    }

    /**
     * @return Map<Object>
     * @Description
     * <AUTHOR>
     * @param[1] openid  前端传过来的openid
     * @param[2] key pms扫码登录时传来的redis中的key,oem不需要传
     * @param[3] request 读取ip
     * @time 2023/4/21 22:08
     */
    @Override
    public LoginUser scanLogin(String openid, String key, HttpServletRequest request) throws ParseException {
        //构建登陆日志对象
        CiLoginLogEntity ciLoginLogEntity = new CiLoginLogEntity();
        ciLoginLogEntity.setLoginTime(BillCodeUtil.newDate());
        ciLoginLogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        Map<String, String> map = UserAgentUtil.getOs(request.getHeader("User-Agent"));
        ciLoginLogEntity.setBrowserName(map.get("browser"));
        ciLoginLogEntity.setHostSystem(map.get("os"));
        String ip = UserAgentUtil.getIpAddr(request);
        String address = UserAgentUtil.getRealAddressByIP(ip);
        ciLoginLogEntity.setIpAddr(ip);
        ciLoginLogEntity.setLoginLocation(address);


       //开始扫码登录(rms直接传入了openid)
      //通过openid获取是否有用户信息
        List<PirmsuserPojo> pirmsuserPojoList = sysRmsLogingMapper.getListByOpenid(openid);
//        List<String> tidList = pirmsuserPojoList.stream()
//                .map(PirmsuserPojo::getTenantid)
//                .collect(Collectors.toList());
        if (pirmsuserPojoList != null) {
//            登录成功-日志
            ciLoginLogEntity.setUserid(pirmsuserPojoList.get(0).getUserid());
            ciLoginLogEntity.setUserName(pirmsuserPojoList.get(0).getUsername());
            ciLoginLogEntity.setRealName(pirmsuserPojoList.get(0).getRealname());
            ciLoginLogEntity.setDirection("登录");
            ciLoginLogEntity.setLoginStatus(0L);
            ciLoginLogEntity.setLoginMsg("管理员：" + pirmsuserPojoList.get(0).getUsername() + ",openid登录成功,登录时间："
                    + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",登录系统："
                    + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
                    + ciLoginLogEntity.getBrowserName() + ",登录地址："
                    + ciLoginLogEntity.getLoginLocation());
            ciLoginLogMapper.insert(ciLoginLogEntity);
        } else {
            if (key!=null) {
                //piadmin为空，说明用户openid不在PiAdmin表中
                Map<String, Object> missionMsg = new HashMap<>();
                missionMsg.put("code", "403");
                missionMsg.put("msg", "openid下无绑定租户");
                this.redisService.setCacheMapValue(SCANLOGIN_CODE, key, missionMsg);
                redisService.expire(SCANLOGIN_CODE, 60 * 6);
            }
            //登录失败-日志
            ciLoginLogEntity.setUserid("");
            //ciLoginLogEntity.setUserName(username);
            ciLoginLogEntity.setRealName("");
            ciLoginLogEntity.setLoginStatus(1L);
            ciLoginLogEntity.setDirection("登录");
            ciLoginLogEntity.setLoginMsg("openid:" + openid + "登录失败,登录时间："
                    + BillCodeUtil.forMatDate(ciLoginLogEntity.getLoginTime()) + ",原因：openid下无绑定租户" + ",登录系统："
                    + ciLoginLogEntity.getHostSystem() + ",操作浏览器："
                    + ciLoginLogEntity.getBrowserName() + ",登录地址："
                    + ciLoginLogEntity.getLoginLocation());
            ciLoginLogMapper.insert(ciLoginLogEntity);
            throw new BaseBusinessException("openid不存在");
        }
        LoginUser loginUser = new LoginUser();
        loginUser.setUserid(pirmsuserPojoList.get(0).getUserid());
        loginUser.setUsername(pirmsuserPojoList.get(0).getUsername());
        loginUser.setRealname(pirmsuserPojoList.get(0).getRealname());
        return loginUser;
    }

    @Override
    public PirmsuserPojo bindOpenid(LoginUser loginUser, String openidFromOam) {
        String userid = loginUser.getUserid();
        String tid = loginUser.getTenantid();
        // 如果PiRmsJustAuth(第三方授权表)没有该用户且AuthType为'openid'，则先创建该用户PiRmsJustAuth
        JustauthPojo piRmsJustAuth = sysRmsLogingMapper.getPiRmsJustAuthByUseridAndAuthType(userid, "openid");
        if (piRmsJustAuth==null) {
            JustauthPojo justauthPojo = new JustauthPojo();
            justauthPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
            justauthPojo.setUserid(userid);
            justauthPojo.setRealname(loginUser.getRealname());
            justauthPojo.setUsername(loginUser.getUsername());
            justauthPojo.setAuthtype("openid");
            justauthPojo.setAuthuuid(openidFromOam);
            // 通用字段
            justauthPojo.setCreatedate(new Date());
            justauthPojo.setCreateby(loginUser.getRealName());
            justauthPojo.setCreatebyid(userid);
            justauthPojo.setModifydate(new Date());
            justauthPojo.setLister(loginUser.getRealName());
            justauthPojo.setListerid(userid);
            justauthPojo.setTenantid(tid);
            // mapper.xml仅插入以上字段
            sysRmsLogingMapper.insertPiRmsJustAuth(justauthPojo);
        }
        sysRmsLogingMapper.bindOpenid(userid, openidFromOam,tid);
        return sysRmsLogingMapper.getListByOpenid(openidFromOam).get(0);
    }



}
