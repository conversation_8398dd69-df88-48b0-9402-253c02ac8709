package inks.auth.service.impl;

import inks.auth.service.PhoneSmsService;
import inks.auth.utils.SmsUtis;
import inks.common.core.constant.Constants;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.CaptchaUtils;
import inks.common.redis.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
public class PhoneSmsServiceImpl implements PhoneSmsService {
    @Autowired
    private RedisService redisService;

    /* 注册验证 */
    @Override
    public void PhoneRegister(String phone, String accessKeyId, String accessKeySecret) {
        SmsUtis msgUtis = new SmsUtis();
        String verifyKey = Constants.EMAIL_CODE_KEY + phone;
        //查询邮箱验证码
        String captcha = redisService.getCacheObject(verifyKey);
        try {
            if (captcha != null) {
                throw new BaseBusinessException("有效期10分钟内请勿重复获取");
            }
            String Captcha = CaptchaUtils.getCaptcha();
            msgUtis.sendRegister(phone, Captcha, accessKeyId, accessKeySecret);
            verifyKey = Constants.EMAIL_CODE_KEY + phone;
            redisService.setCacheObject(verifyKey, Captcha, 10L, TimeUnit.MINUTES);
        } catch (BaseBusinessException e) {
            throw new BaseBusinessException(e.getMessage());
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }

    }

    /* 通用验证,忘记密码 */
    @Override
    public void PhoneCaptcha(String phone, String accessKeyId, String accessKeySecret) {
        SmsUtis msgUtis = new SmsUtis();
        String verifyKey = Constants.CAPTCHA_CODE_KEY + phone;
        //查询邮箱验证码
        String captcha = redisService.getCacheObject(verifyKey);
        try {
            if (captcha != null) {
                throw new BaseBusinessException("有效期10分钟内请勿重复获取");
            }
            String Captcha = CaptchaUtils.getCaptcha();
            msgUtis.sendCaptcha(phone, Captcha, accessKeyId, accessKeySecret);
            verifyKey = Constants.CAPTCHA_CODE_KEY + phone;
            redisService.setCacheObject(verifyKey, Captcha, 10L, TimeUnit.MINUTES);
        } catch (BaseBusinessException e) {
            throw new BaseBusinessException(e.getMessage());
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }

    }
}
