package inks.auth.service;

import inks.auth.domain.PiTenant;
import inks.common.core.domain.LoginUser;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年04月06日 16:54
 */
public interface SysScmLogingService {
    public LoginUser login(String username, String password, HttpServletRequest request) throws ParseException, Exception;

    void record(LoginUser loginUser, HttpServletRequest request) throws ParseException;

    LoginUser scanLogin(String openid, String key, HttpServletRequest request) throws ParseException;
}
