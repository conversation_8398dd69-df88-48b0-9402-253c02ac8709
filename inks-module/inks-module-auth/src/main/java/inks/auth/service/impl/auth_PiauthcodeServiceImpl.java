package inks.auth.service.impl;


import inks.auth.domain.PiauthcodePojo;
import inks.auth.mapper.auth_PiauthcodeMapper;
import inks.auth.service.auth_PiauthcodeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 授权码(Piauthcode)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-22 12:44:38
 */
@Service("auth_piauthcodeService")
public class auth_PiauthcodeServiceImpl implements auth_PiauthcodeService {
    @Resource
    private auth_PiauthcodeMapper authPiauthcodeMapper;


    /**
     * 通过ID查询单条数据
     *
     * @param code 主键
     * @return 实例对象
     */
    @Override
    public PiauthcodePojo getEntityByEnabled(String code) {
        return this.authPiauthcodeMapper.getEntityByEnabled(code);
    }

}
