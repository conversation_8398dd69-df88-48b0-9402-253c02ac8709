package inks.auth.service;

import inks.auth.domain.PiTenant;
import inks.auth.form.RegisterBody;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.TenantInfo;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: song
 * @Date: 2021/09/04/10:40
 * @Description:
 */
public interface SysLoginService {
    public LoginUser login(String username, String password, HttpServletRequest request) throws ParseException, Exception;
    LoginUser loginByOpenid(String openid, HttpServletRequest request) throws ParseException;
    public LoginUser register(RegisterBody registerBody, HttpServletRequest request) throws Exception;
    public void inspect(String userName);
    public List<PiTenant> getPiTenant(String id);
    public void record(LoginUser loginUser, HttpServletRequest request) throws ParseException;
    Integer getIsAdmin(String key,String tid);
    void updatePwd(String key,String pwd);
    // 获得租户信息 Eric 20220220
    TenantInfo getTenantInfo( String key);

}
