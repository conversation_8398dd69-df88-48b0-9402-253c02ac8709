package inks.auth.service.impl;

import inks.auth.mapper.JustAuthMapper;
import inks.auth.service.JustAuthService;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.TenantInfo;
import inks.common.core.exception.BaseBusinessException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class JustAuthServiceImpl implements JustAuthService {
    @Resource
    private JustAuthMapper wxeLoginMapper;
    @Override
    public LoginUser getLoginUserByJust(String authuid,String type,String tid) {
        try {
            LoginUser loginUser = wxeLoginMapper.getLoginUserByJust(authuid,type,tid);
            TenantInfo tenantInfo = wxeLoginMapper.getTenantInfo(tid);
            loginUser.setTenantinfo(tenantInfo);
            return loginUser;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public LoginUser getLoginUserByJustEncrypt(String useridEncrypt, String type, String tid) {
        try {
            LoginUser loginUser = wxeLoginMapper.getLoginUserByJustEncrypt(useridEncrypt,type,tid);
            TenantInfo tenantInfo = wxeLoginMapper.getTenantInfo(tid);
            loginUser.setTenantinfo(tenantInfo);
            return loginUser;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public String getUseridByEncrypt(String useridEncrypt, String tid) {
        return wxeLoginMapper.getUseridByEncrypt(useridEncrypt,"wxe",tid);
    }
}
