package inks.auth.service;

import inks.auth.domain.Piadmin;
import inks.auth.domain.PirmsuserPojo;
import inks.common.core.domain.LoginUser;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;

/**
 * <AUTHOR>
 * @date 2023年04月06日 16:54
 */
public interface SysRmsLogingService {
    public LoginUser login(String username, String password, HttpServletRequest request) throws ParseException, Exception;

    void record(LoginUser loginUser, HttpServletRequest request) throws ParseException;

    LoginUser scanLogin(String openid, String key, HttpServletRequest request) throws ParseException;

    PirmsuserPojo bindOpenid(LoginUser loginUser, String openidFromOam);
}
