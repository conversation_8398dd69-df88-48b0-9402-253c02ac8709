package inks.auth.utils;

import com.aliyun.tea.*;
import com.aliyun.dysmsapi20170525.models.*;
import com.aliyun.teaopenapi.models.*;
import org.springframework.stereotype.Component;

@Component
public class SmsUtis {
    /**
     * 使用AK&SK初始化账号Client
     * @param
     * @param
     * @return Client
     * @throws Exception
     */
    public static com.aliyun.dysmsapi20170525.Client createClient(String accessKeyId,String accessKeySecret) throws Exception {
        Config config = new Config()
                // 您的AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 您的AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // 访问的域名
        config.endpoint = "dysmsapi.aliyuncs.com";
        return new com.aliyun.dysmsapi20170525.Client(config);
    }

    public void sendRegister(String phone,String Captcha,String accessKeyId,String accessKeySecret) throws Exception {
        com.aliyun.dysmsapi20170525.Client client = SmsUtis.createClient(accessKeyId,accessKeySecret);
        SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setPhoneNumbers(phone)
                .setSignName("应凯软件")
                .setTemplateCode("SMS_223541873")
                .setTemplateParam("{\"code\":\""+Captcha+"\"}");
        SendSmsResponse resp = client.sendSms(sendSmsRequest);
        com.aliyun.teaconsole.Client.log(com.aliyun.teautil.Common.toJSONString(TeaModel.buildMap(resp)));
    }

    public void sendCaptcha(String phone,String Captcha,String accessKeyId,String accessKeySecret) throws Exception {
        com.aliyun.dysmsapi20170525.Client client = SmsUtis.createClient(accessKeyId,accessKeySecret);
        SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setPhoneNumbers(phone)
                .setSignName("应凯软件")
                .setTemplateCode("SMS_223561904")
                .setTemplateParam("{\"code\":\""+Captcha+"\"}");
        SendSmsResponse resp = client.sendSms(sendSmsRequest);
        com.aliyun.teaconsole.Client.log(com.aliyun.teautil.Common.toJSONString(TeaModel.buildMap(resp)));
    }

    public void sendRegisterNanno(SendSmsRequest sendSmsRequest,String accessKeyId,String accessKeySecret) throws Exception {
        com.aliyun.dysmsapi20170525.Client client = SmsUtis.createClient(accessKeyId,accessKeySecret);
//        SendSmsRequest sendSmsRequest = new SendSmsRequest()
//                .setPhoneNumbers(phone)
//                .setSignName("应凯软件")
//                .setTemplateCode("SMS_223541873")
//                .setTemplateParam("{\"code\":\""+Captcha+"\"}");
        SendSmsResponse resp = client.sendSms(sendSmsRequest);
        com.aliyun.teaconsole.Client.log(com.aliyun.teautil.Common.toJSONString(TeaModel.buildMap(resp)));
    }
}
