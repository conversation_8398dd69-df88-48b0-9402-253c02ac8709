package inks.auth.utils.wxe;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import okhttp3.*;

import java.io.IOException;
import java.util.*;

public class WeComUtil {

    private static final MediaType JSON_TYPE = MediaType.get("application/json; charset=utf-8");
    private static final OkHttpClient client = new OkHttpClient();

    /**
     * 批量将明文userid转换为密文open_userid
     *
     * @param userIdList 明文userid列表
     * @param accessToken 企业微信access_token
     * @return Map<userid, open_userid>
     * @throws IOException 网络异常
     */
    public static Map<String, String> batchUserIdToOpenUserId(List<String> userIdList, String accessToken) throws IOException {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/batch/userid_to_openuserid?access_token=" + accessToken;

        JSONObject req = new JSONObject();
        req.put("userid_list", userIdList);

        RequestBody body = RequestBody.create(req.toJSONString(), JSON_TYPE);
        Request request = new Request.Builder().url(url).post(body).build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) throw new IOException("请求失败: " + response);

            JSONObject respJson = JSON.parseObject(Objects.requireNonNull(response.body()).string());
            if (respJson.getIntValue("errcode") != 0) {
                throw new RuntimeException("企业微信接口错误: " + respJson.getString("errmsg"));
            }

            Map<String, String> resultMap = new LinkedHashMap<>();
            JSONArray arr = respJson.getJSONArray("open_userid_list");
            if (arr != null) {
                for (int i = 0; i < arr.size(); i++) {
                    JSONObject obj = arr.getJSONObject(i);
                    resultMap.put(obj.getString("userid"), obj.getString("open_userid"));
                }
            }
            // 可根据业务处理invalid_userid_list
            return resultMap;
        }
    }

    /**
     * 单个明文userid转换为密文open_userid
     *
     * @param userId 明文userid
     * @param accessToken 企业微信access_token
     * @return open_userid
     * @throws IOException 网络异常
     */
    public static String userIdToOpenUserId(String userId, String accessToken) throws IOException {
        List<String> ids = Collections.singletonList(userId);
        Map<String, String> map = batchUserIdToOpenUserId(ids, accessToken);
        String result = map.get(userId);
        if (result == null || result.isEmpty()) {
            throw new RuntimeException("未找到对应密文open_userid");
        }
        return result;
    }
}
