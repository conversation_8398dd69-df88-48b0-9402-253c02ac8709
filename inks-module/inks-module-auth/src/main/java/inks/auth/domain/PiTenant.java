package inks.auth.domain;


import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: song
 * @Date: 2021/09/16/9:11
 * @Description:
 */

public class PiTenant {
    /** 租户id */
    private String Tenantid;

    public String getTenantid() {
        return Tenantid;
    }

    public void setTenantid(String tenantid) {
        Tenantid = tenantid;
    }

    public String getTenantCode() {
        return TenantCode;
    }

    public void setTenantCode(String tenantCode) {
        TenantCode = tenantCode;
    }

    public String getTenantName() {
        return TenantName;
    }

    public void setTenantName(String tenantName) {
        TenantName = tenantName;
    }

    public String getCompany() {
        return Company;
    }

    public void setCompany(String company) {
        Company = company;
    }

    public String getCompanyAdd() {
        return CompanyAdd;
    }

    public void setCompanyAdd(String companyAdd) {
        CompanyAdd = companyAdd;
    }

    public String getCompanyTel() {
        return CompanyTel;
    }

    public void setCompanyTel(String companyTel) {
        CompanyTel = companyTel;
    }

    public String getContactor() {
        return Contactor;
    }

    public void setContactor(String contactor) {
        Contactor = contactor;
    }

    public Long getTenantState() {
        return TenantState;
    }

    public void setTenantState(Long tenantState) {
        TenantState = tenantState;
    }

    public String getSellerid() {
        return Sellerid;
    }

    public void setSellerid(String sellerid) {
        Sellerid = sellerid;
    }

    public String getSellerCode() {
        return SellerCode;
    }

    public void setSellerCode(String sellerCode) {
        SellerCode = sellerCode;
    }

    public String getLister() {
        return Lister;
    }

    public void setLister(String lister) {
        Lister = lister;
    }

    public Date getCreateDate() {
        return CreateDate;
    }

    public void setCreateDate(Date createDate) {
        CreateDate = createDate;
    }

    public Date getModifyDate() {
        return ModifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        ModifyDate = modifyDate;
    }

    /** 租户号 */
    private String TenantCode;

    /** 租户名称 */
    private String TenantName;

    /** 租户公司 */
    private String Company;

    /** 公司地址 */
    private String CompanyAdd;

    /** 公司电话 */
    private String CompanyTel;

    /** 联系人 */
    private String Contactor;

    /** 状态 */
    private Long TenantState;

    /** 销售id */
    private String Sellerid;

    /** 销售编码 */
    private String SellerCode;

    /** 制表 */
    private String Lister;

    /** 新建日期 */
    private Date CreateDate;

    /** 修改日期 */
    private Date ModifyDate;


}
