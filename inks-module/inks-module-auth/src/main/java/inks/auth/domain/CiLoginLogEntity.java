package inks.auth.domain;

import com.fasterxml.jackson.annotation.JsonProperty;


import java.util.Date;

/**
 * 登录日志对象 CiLoginLog
 *
 * <AUTHOR>
 * @date 2021-10-06
 */
public class CiLoginLogEntity {
    /** id */
    private String id;

    /** 用户ID */
    private String Userid;

    /** 登录号 */
    private String UserName;

    /** 中文名 */
    private String RealName;

    /** 主机IP */
    private String IpAddr;

    /** 主机地址 */
    private String LoginLocation;

    /** 浏览器名称 */
    private String BrowserName;

    /** 操作系统 */
    private String HostSystem;

    /** 登录/登出 */
    private String Direction;

    /** 登录状态0成功 1失败 */
    private Long LoginStatus;

    /** 操作信息 */
    private String LoginMsg;

    /** 访问时间 */
    private Date LoginTime;

    /** 租户ID */
    private String Tenantid;
    /** 租户名称 */
    private String TenantName;

    @JsonProperty("id")
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    @JsonProperty("Userid")
    public String getUserid() {
        return Userid;
    }

    public void setUserid(String userid) {
        Userid = userid;
    }
    @JsonProperty("UserName")
    public String getUserName() {
        return UserName;
    }

    public void setUserName(String userName) {
        UserName = userName;
    }
    @JsonProperty("RealName")
    public String getRealName() {
        return RealName;
    }

    public void setRealName(String realName) {
        RealName = realName;
    }
    @JsonProperty("IpAddr")
    public String getIpAddr() {
        return IpAddr;
    }

    public void setIpAddr(String ipAddr) {
        IpAddr = ipAddr;
    }
    @JsonProperty("LoginLocation")
    public String getLoginLocation() {
        return LoginLocation;
    }

    public void setLoginLocation(String loginLocation) {
        LoginLocation = loginLocation;
    }
    @JsonProperty("BrowserName")
    public String getBrowserName() {
        return BrowserName;
    }

    public void setBrowserName(String browserName) {
        BrowserName = browserName;
    }
    @JsonProperty("HostSystem")
    public String getHostSystem() {
        return HostSystem;
    }

    public void setHostSystem(String hostSystem) {
        HostSystem = hostSystem;
    }
    @JsonProperty("Direction")
    public String getDirection() {
        return Direction;
    }

    public void setDirection(String direction) {
        Direction = direction;
    }
    @JsonProperty("LoginStatus")
    public Long getLoginStatus() {
        return LoginStatus;
    }

    public void setLoginStatus(Long loginStatus) {
        LoginStatus = loginStatus;
    }
    @JsonProperty("LoginMsg")
    public String getLoginMsg() {
        return LoginMsg;
    }

    public void setLoginMsg(String loginMsg) {
        LoginMsg = loginMsg;
    }
    @JsonProperty("LoginTime")
    public Date getLoginTime() {
        return LoginTime;
    }

    public void setLoginTime(Date loginTime) {
        LoginTime = loginTime;
    }

    public String getTenantid() {
        return Tenantid;
    }

    public void setTenantid(String tenantid) {
        Tenantid = tenantid;
    }

    public String getTenantName() {
        return TenantName;
    }

    public void setTenantName(String tenantName) {
        TenantName = tenantName;
    }
}
