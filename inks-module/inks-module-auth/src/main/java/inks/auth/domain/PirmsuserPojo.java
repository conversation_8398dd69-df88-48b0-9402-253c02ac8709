package inks.auth.domain;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * RMS用户(Pirmsuser)实体类
 *
 * <AUTHOR>
 * @since 2023-04-24 10:39:12
 */
public class PirmsuserPojo implements Serializable {
    private static final long serialVersionUID = 772701365004337312L;
         // 用户id
         @Excel(name = "用户id") 
    private String userid;
         // 登录名
         @Excel(name = "登录名") 
    private String username;
         // 姓名
         @Excel(name = "姓名") 
    private String realname;
         // 昵称
         @Excel(name = "昵称") 
    private String nickname;
         // 密码
         @Excel(name = "密码") 
    private String userpassword;
         // 手机
         @Excel(name = "手机") 
    private String mobile;
         // 用户邮箱
         @Excel(name = "用户邮箱") 
    private String email;
         // 性别
         @Excel(name = "性别") 
    private Integer sex;
         // 系统语言选择
         @Excel(name = "系统语言选择") 
    private String langcode;
         // 头像
         @Excel(name = "头像") 
    private String avatar;
         // 类型 1客户2供应商3加工厂商
         @Excel(name = "类型 1客户2供应商3加工厂商") 
    private Integer usertype;
         // 是否管理员
         @Excel(name = "是否管理员") 
    private Integer isadmin;
         // 组织id
         @Excel(name = "组织id") 
    private String deptid;
         // 组织编码
         @Excel(name = "组织编码") 
    private String deptcode;
         // 组织名称
         @Excel(name = "组织名称") 
    private String deptname;
         // 是否部门主管
         @Excel(name = "是否部门主管") 
    private Integer isdeptadmin;
         // 部门内行号
         @Excel(name = "部门内行号") 
    private Integer deptrownum;
         // 行号
         @Excel(name = "行号") 
    private Integer rownum;
         // 状态:0正常,1禁用
         @Excel(name = "状态:0正常,1禁用") 
    private Integer userstatus;
         // 编码(备用)
         @Excel(name = "编码(备用)") 
    private String usercode;
         // 往来单位id
         @Excel(name = "往来单位id") 
    private String groupids;
         // 往来单位
         @Excel(name = "往来单位") 
    private String groupnames;
         // RMS服务ids
         @Excel(name = "RMS服务ids") 
    private String rmsfunctids;
         // RMS服务
         @Excel(name = "RMS服务") 
    private String rmsfunctnames;
         // 备注
         @Excel(name = "备注") 
    private String remark;
    // 数据标签
    @Excel(name = "数据标签")
    private String datalabel;
         // 创建者
         @Excel(name = "创建者") 
    private String createby;
         // 创建者id
         @Excel(name = "创建者id") 
    private String createbyid;
         // 新建日期
         @Excel(name = "新建日期") 
    private Date createdate;
         // 制表
         @Excel(name = "制表") 
    private String lister;
         // 制表id
         @Excel(name = "制表id") 
    private String listerid;
         // 修改日期
         @Excel(name = "修改日期") 
    private Date modifydate;
         // 租户id
         @Excel(name = "租户id") 
    private String tenantid;
         // 租户名称
         @Excel(name = "租户名称") 
    private String tenantname;
         // 乐观锁
         @Excel(name = "乐观锁") 
    private Integer revision;

     // 用户id
       public String getUserid() {
        return userid;
    }
    
    public void setUserid(String userid) {
        this.userid = userid;
    }
        
     // 登录名
       public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
        
     // 姓名
       public String getRealname() {
        return realname;
    }
    
    public void setRealname(String realname) {
        this.realname = realname;
    }
        
     // 昵称
       public String getNickname() {
        return nickname;
    }
    
    public void setNickname(String nickname) {
        this.nickname = nickname;
    }
        
     // 密码
       public String getUserpassword() {
        return userpassword;
    }
    
    public void setUserpassword(String userpassword) {
        this.userpassword = userpassword;
    }
        
     // 手机
       public String getMobile() {
        return mobile;
    }
    
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
        
     // 用户邮箱
       public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }

    public String getDatalabel() {
        return datalabel;
    }

    public void setDatalabel(String datalabel) {
        this.datalabel = datalabel;
    }

    // 性别
       public Integer getSex() {
        return sex;
    }
    
    public void setSex(Integer sex) {
        this.sex = sex;
    }
        
     // 系统语言选择
       public String getLangcode() {
        return langcode;
    }
    
    public void setLangcode(String langcode) {
        this.langcode = langcode;
    }
        
     // 头像
       public String getAvatar() {
        return avatar;
    }
    
    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }
        
     // 类型 1客户2供应商3加工厂商
       public Integer getUsertype() {
        return usertype;
    }
    
    public void setUsertype(Integer usertype) {
        this.usertype = usertype;
    }
        
     // 是否管理员
       public Integer getIsadmin() {
        return isadmin;
    }
    
    public void setIsadmin(Integer isadmin) {
        this.isadmin = isadmin;
    }
        
     // 组织id
       public String getDeptid() {
        return deptid;
    }
    
    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }
        
     // 组织编码
       public String getDeptcode() {
        return deptcode;
    }
    
    public void setDeptcode(String deptcode) {
        this.deptcode = deptcode;
    }
        
     // 组织名称
       public String getDeptname() {
        return deptname;
    }
    
    public void setDeptname(String deptname) {
        this.deptname = deptname;
    }
        
     // 是否部门主管
       public Integer getIsdeptadmin() {
        return isdeptadmin;
    }
    
    public void setIsdeptadmin(Integer isdeptadmin) {
        this.isdeptadmin = isdeptadmin;
    }
        
     // 部门内行号
       public Integer getDeptrownum() {
        return deptrownum;
    }
    
    public void setDeptrownum(Integer deptrownum) {
        this.deptrownum = deptrownum;
    }
        
     // 行号
       public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
     // 状态:0正常,1禁用
       public Integer getUserstatus() {
        return userstatus;
    }
    
    public void setUserstatus(Integer userstatus) {
        this.userstatus = userstatus;
    }
        
     // 编码(备用)
       public String getUsercode() {
        return usercode;
    }
    
    public void setUsercode(String usercode) {
        this.usercode = usercode;
    }
        
     // 往来单位id
       public String getGroupids() {
        return groupids;
    }
    
    public void setGroupids(String groupids) {
        this.groupids = groupids;
    }
        
     // 往来单位
       public String getGroupnames() {
        return groupnames;
    }
    
    public void setGroupnames(String groupnames) {
        this.groupnames = groupnames;
    }
        
     // RMS服务ids
       public String getRmsfunctids() {
        return rmsfunctids;
    }
    
    public void setRmsfunctids(String rmsfunctids) {
        this.rmsfunctids = rmsfunctids;
    }
        
     // RMS服务
       public String getRmsfunctnames() {
        return rmsfunctnames;
    }
    
    public void setRmsfunctnames(String rmsfunctnames) {
        this.rmsfunctnames = rmsfunctnames;
    }
        
     // 备注
       public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
     // 创建者
       public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
     // 创建者id
       public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
     // 新建日期
       public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
     // 制表
       public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
     // 制表id
       public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
     // 修改日期
       public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
     // 租户id
       public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
     // 租户名称
       public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
     // 乐观锁
       public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

