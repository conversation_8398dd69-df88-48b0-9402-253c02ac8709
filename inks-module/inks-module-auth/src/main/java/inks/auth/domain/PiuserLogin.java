package inks.auth.domain;



import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: song
 * @Date: 2021/09/15/15:29
 * @Description:
 */
public class PiuserLogin {
    private String id;
    private String userId;
    private String userPassword;
    private String lister;
    private Date createDate;
    private Date modifyDate;

    public Integer getCheckipaddr() {
        return checkipaddr;
    }

    public void setCheckipaddr(Integer checkipaddr) {
        this.checkipaddr = checkipaddr;
    }

    private Integer checkipaddr;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserPassword() {
        return userPassword;
    }

    public void setUserPassword(String userPassword) {
        this.userPassword = userPassword;
    }

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }
}
