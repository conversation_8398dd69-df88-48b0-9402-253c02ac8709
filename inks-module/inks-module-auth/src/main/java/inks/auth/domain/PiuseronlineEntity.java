package inks.auth.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 用户在线(Piuseronline)实体类
 *
 * <AUTHOR>
 * @since 2024-03-13 13:14:29
 */
public class PiuseronlineEntity implements Serializable {
    private static final long serialVersionUID = -27919624578863266L;
     // id
    private String id;
     // 用户id
    private String userid;
     // 登录名
    private String username;
     // 姓名
    private String realname;
     // 昵称
    private String nickname;
     // 租户id
    private String tenantid;
     // 租户Name
    private String tenantname;
     // IP地址
    private String ipaddress;
     // IP位置
    private String iplocation;
     // MAC地址
    private String macaddress;
     // 浏览器名称
    private String browsername;
     // 操作系统
    private String hostsystem;
     // 0其他/1web/2APP/3h5/4ipad/5winform
    private Integer terminaltype;
     // 当前Token
    private String token;
     // 登录日期
    private Date logindate;
     // 最后活动日期
    private Date lastactivitydate;
     // Token过期日期
    private Date tokenexpirydate;

// id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 用户id
    public String getUserid() {
        return userid;
    }
    
    public void setUserid(String userid) {
        this.userid = userid;
    }
        
// 登录名
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
        
// 姓名
    public String getRealname() {
        return realname;
    }
    
    public void setRealname(String realname) {
        this.realname = realname;
    }
        
// 昵称
    public String getNickname() {
        return nickname;
    }
    
    public void setNickname(String nickname) {
        this.nickname = nickname;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 租户Name
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
// IP地址
    public String getIpaddress() {
        return ipaddress;
    }
    
    public void setIpaddress(String ipaddress) {
        this.ipaddress = ipaddress;
    }
        
// IP位置
    public String getIplocation() {
        return iplocation;
    }
    
    public void setIplocation(String iplocation) {
        this.iplocation = iplocation;
    }
        
// MAC地址
    public String getMacaddress() {
        return macaddress;
    }
    
    public void setMacaddress(String macaddress) {
        this.macaddress = macaddress;
    }
        
// 浏览器名称
    public String getBrowsername() {
        return browsername;
    }
    
    public void setBrowsername(String browsername) {
        this.browsername = browsername;
    }
        
// 操作系统
    public String getHostsystem() {
        return hostsystem;
    }
    
    public void setHostsystem(String hostsystem) {
        this.hostsystem = hostsystem;
    }
        
// 0其他/1web/2APP/3h5/4ipad/5winform
    public Integer getTerminaltype() {
        return terminaltype;
    }
    
    public void setTerminaltype(Integer terminaltype) {
        this.terminaltype = terminaltype;
    }
        
// 当前Token
    public String getToken() {
        return token;
    }
    
    public void setToken(String token) {
        this.token = token;
    }
        
// 登录日期
    public Date getLogindate() {
        return logindate;
    }
    
    public void setLogindate(Date logindate) {
        this.logindate = logindate;
    }
        
// 最后活动日期
    public Date getLastactivitydate() {
        return lastactivitydate;
    }
    
    public void setLastactivitydate(Date lastactivitydate) {
        this.lastactivitydate = lastactivitydate;
    }
        
// Token过期日期
    public Date getTokenexpirydate() {
        return tokenexpirydate;
    }
    
    public void setTokenexpirydate(Date tokenexpirydate) {
        this.tokenexpirydate = tokenexpirydate;
    }
        

}

