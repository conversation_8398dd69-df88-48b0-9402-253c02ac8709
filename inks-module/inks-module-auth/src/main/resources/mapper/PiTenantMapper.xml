<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="inks.auth.mapper.PiTenantMapper">
    <select id="getPageList" parameterType="java.lang.String" resultType="inks.auth.domain.PiTenant">
        select *
        from PiTenant
                 LEFT JOIN PiTenantUser ON PiTenant.Tenantid = PiTenantUser.Tenantid
        where PiTenantUser.Userid = #{id}
    </select>

<!--    <select id="getIsAdmin" parameterType="java.lang.String" resultType="java.lang.Integer">-->
<!--        SELECT IsAdmin-->
<!--        FROM PiTenant-->
<!--                 LEFT JOIN PiTenantUser ON PiTenant.Tenantid = PiTenantUser.Tenantid-->
<!--        WHERE PiTenant.Tenantid = #{key} -->
<!--    </select>-->
    <select id="getIsAdmin" resultType="java.lang.Integer">
        SELECT IsAdmin
        FROM  PiTenantUser
        WHERE PiTenantUser.Userid = #{key} and PiTenantUser.Tenantid = #{tid}
    </select>
    <select id="getUserId" resultType="java.lang.String">
        SELECT Userid FROM PiUser WHERE UserName=#{key}
    </select>

    <!--查询单个-->
    <select id="getTenantInfo" resultType="inks.common.core.domain.TenantInfo">
        select Tenantid,
               TenantCode,
               TenantName,
               Company,
               CompanyAdd,
               CompanyTel,
               Contactor,
               PreviouVisit
        from PiTenant
        where PiTenant.Tenantid = #{key}
    </select>
    <select id="getTenantName" resultType="java.lang.String">
        SELECT TenantName FROM PiTenant WHERE Tenantid=#{key}
    </select>

    <!--通过FunctionCode和tid查询可设置的所有权限-->
    <select id="getFunctionPermsByFunctionCode" resultType="java.lang.String">
        SELECT PiPermCode.PermCode
        FROM PiFunctionPerm
                 LEFT JOIN PiPermCode ON PiPermCode.Permid = PiFunctionPerm.Permid
                 INNER JOIN PiSubscriber ON PiFunctionPerm.Functionid = PiSubscriber.Functionid
        WHERE PiSubscriber.FunctionCode = #{functionCode}
          AND PiSubscriber.Tenantid = #{tid}
        ORDER BY PiPermCode.PermCode
    </select>
</mapper>
