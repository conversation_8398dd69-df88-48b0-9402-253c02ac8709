<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.auth.mapper.SysScmLogingMapper">
    <select id="login" resultType="inks.common.core.domain.LoginUser">
        SELECT Userid AS userid, UserName AS username, UserPassword AS password, RealName AS realName,DataLabel AS dataLabel
        FROM PiScmUser
        WHERE username = #{userName}
          and UserPassword = #{passWord} LIMIT 1
    </select>
    <select id="count" resultType="java.lang.Integer">
        SELECT count(1)
        FROM PiScmUser
        WHERE username = #{userName}
    </select>
    <select id="getListByOpenid" resultType="inks.auth.domain.PiscmuserPojo">
        select PiScmUser.Userid,
               PiScmUser.UserName,
               PiScmUser.RealName,
               PiScmUser.NickName,
               PiScmUser.UserPassword,
               PiScmUser.Mobile,
               PiScmUser.Email,
               PiScmUser.Sex,
               PiScmUser.LangCode,
               PiScmUser.Avatar,
               PiScmUser.UserType,
               PiScmUser.IsAdmin,
               PiScmUser.Deptid,
               PiScmUser.DeptCode,
               PiScmUser.DeptName,
               PiScmUser.IsDeptAdmin,
               PiScmUser.DeptRowNum,
               PiScmUser.RowNum,
               PiScmUser.UserStatus,
               PiScmUser.UserCode,
               PiScmUser.Groupids,
               PiScmUser.GroupNames,
               PiScmUser.ScmFunctids,
               PiScmUser.ScmFunctNames,
               PiScmUser.Remark,
               PiScmUser.CreateBy,
               PiScmUser.CreateByid,
               PiScmUser.CreateDate,
               PiScmUser.Lister,
               PiScmUser.Listerid,
               PiScmUser.ModifyDate,
               PiScmUser.Tenantid,
               PiScmUser.TenantName,
               PiScmUser.Revision
        from PiScmUser
                 Left Join inkssaas.PiScmJustAuth on PiScmJustAuth.Userid = PiScmUser.Userid
        where PiScmJustAuth.AuthUuid = #{openid}
    </select>

    <select id="getListInTids" resultType="inks.auth.domain.PiTenant">
        SELECT * FROM PiTenant
        WHERE PiTenant.Tenantid IN
        <foreach item="tid" collection="tidList" open="(" separator="," close=")">
            #{tid}
        </foreach>
    </select>
    <select id="getListByUserid" resultType="inks.auth.domain.PiscmuserPojo">
        select * from PiScmUser where Userid = #{userid}
    </select>
    <select id="getScmUserByUserid" resultType="inks.auth.domain.PiscmuserPojo">
        select * from PiScmUser where Userid = #{userid} and Tenantid = #{tid}
    </select>


</mapper>
