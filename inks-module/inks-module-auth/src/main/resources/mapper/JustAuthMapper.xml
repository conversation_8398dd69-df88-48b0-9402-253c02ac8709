<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.auth.mapper.JustAuthMapper">
    <select id="getLoginUserByJust" resultType="inks.common.core.domain.LoginUser">
        SELECT PiUser.Userid        AS userid,
               PiUser.UserName      AS username,
               ''                   AS password,
               PiUser.RealName      AS realname,
               PiJustAuth.Tenantid  AS tenantid,
               PiTenantUser.IsAdmin AS isadmin,
               PiJustAuth.authuuid
        FROM PiJustAuth
                 LEFT JOIN PiUser ON PiJustAuth.Userid = PiUser.Userid
                 LEFT JOIN PiTenantUser ON PiJustAuth.Tenantid = PiTenantUser.Tenantid AND PiTenantUser.Userid = PiJustAuth.Userid
        WHERE PiJustAuth.Tenantid = #{tid}
          and PiJustAuth.AuthUuid = #{userid}
          and PiJustAuth.AuthType = #{type}
       LIMIT 1
    </select>

    <select id="getTenantInfo" resultType="inks.common.core.domain.TenantInfo">
        SELECT PiTenant.Tenantid,
               PiTenant.TenantCode,
               PiTenant.TenantName,
               PiTenant.Company,
               PiTenant.CompanyAdd,
               PiTenant.CompanyTel,
               PiTenant.Contactor,
               PiTenant.PreviouVisit
        FROM PiTenant
        WHERE PiTenant.Tenantid = #{tid}
    </select>


    <select id="getUseridByEncrypt" resultType="java.lang.String">
        SELECT PiJustAuth.AuthUuid
        FROM PiJustAuth
        WHERE PiJustAuth.Tenantid = #{tid}
          and PiJustAuth.AuthUuidEnc= #{useridEncrypt}
    </select>

    <select id="getLoginUserByJustEncrypt" resultType="inks.common.core.domain.LoginUser">
        SELECT PiUser.Userid        AS userid,
               PiUser.UserName      AS username,
               ''                   AS password,
               PiUser.RealName      AS realname,
               PiJustAuth.Tenantid  AS tenantid,
               PiTenantUser.IsAdmin AS isadmin,
               PiJustAuth.authuuid
        FROM PiJustAuth
                 LEFT JOIN PiUser ON PiJustAuth.Userid = PiUser.Userid
                 LEFT JOIN PiTenantUser ON PiJustAuth.Tenantid = PiTenantUser.Tenantid AND PiTenantUser.Userid = PiJustAuth.Userid
        WHERE PiJustAuth.Tenantid = #{tid}
          and PiJustAuth.AuthUuidEnc = #{useridEncrypt}
          and PiJustAuth.AuthType = #{type}
    </select>
</mapper>