<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="inks.auth.mapper.auth_PiuserMapper" >
<select id="getPruserCount" resultType="java.lang.Integer">
  SELECT
    count( 1 )
  FROM
    PiUser
      LEFT JOIN PiUserLogin ON PiUser.Userid = PiUserLogin.Userid
  WHERE
    PiUser.UserName = #{userName}
</select>
  <select id="getUserByName" resultType="inks.common.core.domain.LoginUser">
    SELECT
      PiUser.Userid,PiUser.UserName,PiUserLogin.UserPassword,PiUser.RealName,PiUser.Avatar
    FROM
      PiUser
        LEFT JOIN PiUserLogin ON PiUser.Userid = PiUserLogin.Userid
    WHERE
      PiUser.UserName = #{userName}
  </select>
  <select id="login" resultType="inks.common.core.domain.LoginUser">
    SELECT
      PiUser.Userid as userid,PiUser.UserName as username,PiUserLogin.UserPassword as password,PiUser.RealName as realName
    FROM
      PiUser
        LEFT JOIN PiUserLogin ON PiUser.Userid = PiUserLogin.Userid
    WHERE PiUser.UserName = #{userName} and PiUserLogin.UserPassword = #{passWord}
  </select>

    <select id="loginByOpenid" resultType="inks.common.core.domain.LoginUser">
        SELECT PiUser.Userid   as userid,
               PiUser.UserName as username,
               PiUser.RealName as realName
        FROM PiJustAuth
                 join PiUser on PiJustAuth.Userid = PiUser.Userid
        where AuthType = 'openid'
          and AuthUuid = #{openid}
        limit 1
    </select>

    <insert id="inserPiuser" parameterType="inks.auth.domain.Piuser">
    insert into PiUser (Userid,UserCode,UserName,RealName,Mobile,CreateBy,Lister,CreateDate,ModifyDate)
     VALUES(#{userid},#{userCode},#{userName},#{realName},#{mobile},#{createby},#{lister},#{createDate},#{modifyData})
  </insert>

  <select id="getEntityByOpenid" resultType="inks.auth.domain.Piadmin">
    select Adminid,
           UserName,
           RealName,
           Mobile,
           Email,
           LangCode,
           Avatar,
           UserStatus,
           AdminCode,
           Remark,
           WxOpenid,
           RoleType,
           Lister,
           CreateDate,
           ModifyDate
    from PiAdmin
    where PiAdmin.WxOpenid = #{openid}
  </select>

  <update id="bindOpenid">
    update PiAdmin set WxOpenid = #{openid} where Adminid = #{userid}
  </update>
</mapper>