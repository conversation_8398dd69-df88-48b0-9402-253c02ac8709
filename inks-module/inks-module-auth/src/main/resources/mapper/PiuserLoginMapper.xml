<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="inks.auth.mapper.PiuserLoginMapper">
    <insert id="insertPiuserLogin" parameterType="inks.auth.domain.PiuserLogin">
        insert into PiUserLogin (id,Userid,UserPassword,CheckIpAddr,Lister,CreateDate,ModifyDate)
         VALUES (#{id},#{userId},#{userPassword},#{checkipaddr},#{lister},#{createDate},#{modifyDate})
    </insert>
    <update id="updatePwd">
        update PiUserLogin set UserPassword=#{pwd} where Userid=#{key}
    </update>
</mapper>
