<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.auth.mapper.PersonalinfoMapper">
    <select id="getInfo" resultType="java.util.Map">
        SELECT
            PiUser.Userid,
            PiUser.UserName,
            PiUser.RealName,
            PiUser.NickName,
            PiUser.Mobile,
            PiUser.Email,
            PiUser.Sex,
            PiUser.LangCode,
            PiUser.Avatar,
            PiUser.UserStatus,
            PiUser.UserCode,
            PiUser.Remark,
            PiUser.Lister,
            PiTenantUser.IsAdmin,
            PiTenant.TenantCode,
            PiTenant.TenantName,
            PiTenant.Company,
            PiTenant.CompanyAdd,
            PiTenant.CompanyTel,
            PiTenant.Contactor,
            PiTenant.TenantState,
            PiTenant.<PERSON>ller<PERSON>,
            PiTenant.Lister AS PiTenantLister
        FROM
            PiUser
                LEFT JOIN PiTenantUser ON PiUser.Userid = PiTenantUser.Userid
                LEFT JOIN PiTenant ON PiTenant.Tenantid = PiTenantUser.Tenantid
        WHERE PiUser.Userid = #{key}
    </select>
</mapper>
