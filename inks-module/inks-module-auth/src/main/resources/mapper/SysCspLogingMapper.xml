<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.auth.mapper.SysRmsLogingMapper">
    <select id="login" resultType="inks.common.core.domain.LoginUser">
        SELECT Userid AS userid, UserName AS username, UserPassword AS password, RealName AS realName,DataLabel AS dataLabel
        FROM PiRmsUser
        WHERE username = #{userName}
          and UserPassword = #{passWord} LIMIT 1
    </select>
    <select id="count" resultType="java.lang.Integer">
        SELECT count(1)
        FROM PiRmsUser
        WHERE username = #{userName}
    </select>
    <select id="getListByOpenid" resultType="inks.auth.domain.PirmsuserPojo">
        select PiRmsUser.Userid,
               PiRmsUser.UserName,
               PiRmsUser.RealName,
               PiRmsUser.NickName,
               PiRmsUser.UserPassword,
               PiRmsUser.Mobile,
               PiRmsUser.Email,
               PiRmsUser.Sex,
               PiRmsUser.LangCode,
               PiRmsUser.Avatar,
               PiRmsUser.UserType,
               PiRmsUser.IsAdmin,
               PiRmsUser.Deptid,
               PiRmsUser.DeptCode,
               PiRmsUser.DeptName,
               PiRmsUser.IsDeptAdmin,
               PiRmsUser.DeptRowNum,
               PiRmsUser.RowNum,
               PiRmsUser.UserStatus,
               PiRmsUser.UserCode,
               PiRmsUser.Groupids,
               PiRmsUser.GroupNames,
               PiRmsUser.RmsFunctids,
               PiRmsUser.RmsFunctNames,
               PiRmsUser.Remark,
               PiRmsUser.CreateBy,
               PiRmsUser.CreateByid,
               PiRmsUser.CreateDate,
               PiRmsUser.Lister,
               PiRmsUser.Listerid,
               PiRmsUser.ModifyDate,
               PiRmsUser.Tenantid,
               PiRmsUser.TenantName,
               PiRmsUser.Revision
        from PiRmsUser
                 Left Join inkssaas.PiRmsJustAuth on PiRmsJustAuth.Userid = PiRmsUser.Userid
        where PiRmsJustAuth.AuthUuid = #{openid}
    </select>

    <select id="getListInTids" resultType="inks.auth.domain.PiTenant">
        SELECT * FROM PiTenant
        WHERE PiTenant.Tenantid IN
        <foreach item="tid" collection="tidList" open="(" separator="," close=")">
            #{tid}
        </foreach>
    </select>
    <select id="getListByUserid" resultType="inks.auth.domain.PirmsuserPojo">
        select * from PiRmsUser where Userid = #{userid}
    </select>
    <select id="getRmsUserByUserid" resultType="inks.auth.domain.PirmsuserPojo">
        select * from PiRmsUser where Userid = #{userid} and Tenantid = #{tid}
    </select>

    <update id="bindOpenid">
        UPDATE PiRmsUser Left Join PiRmsJustAuth on PiRmsJustAuth.Userid = PiRmsUser.Userid
        SET PiRmsJustAuth.AuthUuid = #{openid}
        WHERE PiRmsUser.Userid = #{userid}
          and PiRmsJustAuth.AuthType = 'openid'
          and PiRmsJustAuth.Tenantid = #{tid}
    </update>

    <select id="getPiRmsJustAuthByUseridAndAuthType" resultType="inks.common.core.domain.JustauthPojo">
        SELECT * FROM PiRmsJustAuth
        WHERE Userid = #{userid} AND AuthType = #{authType}
    </select>

    <insert id="insertPiRmsJustAuth">
        insert into PiRmsJustAuth(id, Userid, UserName, RealName, NickName, AuthType, AuthUuid, Unionid, AuthAvatar,
                                  CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName,
                                  Revision)
        values (#{id}, #{userid}, #{username}, #{realname}, '', #{authtype}, #{authuuid}, '', '', #{createby},
                #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{tenantid}, '', 1)
    </insert>

</mapper>
