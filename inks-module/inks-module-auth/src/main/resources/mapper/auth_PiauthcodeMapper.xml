<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.auth.mapper.auth_PiauthcodeMapper">

    <!--查询单个-->
    <select id="getEntityByEnabled" resultType="inks.auth.domain.PiauthcodePojo">
        select id,
               AuthCode,
               AuthDesc,
               UserName,
               UserPassword,
               RowNum,
               EnabledMark,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from PiAuthCode
        where PiAuthCode.AuthCode = #{code} and PiAuthCode.EnabledMark=1
    </select>
</mapper>

