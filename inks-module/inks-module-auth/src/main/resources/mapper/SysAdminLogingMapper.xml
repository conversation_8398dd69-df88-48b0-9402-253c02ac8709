<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.auth.mapper.SysAdminLogingMapper">
    <select id="login" resultType="inks.common.core.domain.LoginUser">
        SELECT PiAdmin.Adminid AS userid, UserName AS username, UserPassword AS password,RealName AS realName
        FROM PiAdmin
                 LEFT JOIN PiAdminLogin
                           ON PiAdmin.Adminid = PiAdminLogin.Adminid
        WHERE username = #{userName} and UserPassword = #{passWord}
    </select>
    <select id="count" resultType="java.lang.Integer">
        SELECT count(1)
        FROM PiAdmin
                 LEFT JOIN PiAdminLogin
                           ON PiAdmin.Adminid = PiAdminLogin.Adminid
        WHERE username = #{userName}
    </select>

    <select id="getSystemRegistrkey" resultType="java.lang.String">
        select CfgValue
        from CiConfig
        where CfgKey = 'system.registrkey' and Tenantid= #{tid}
    </select>
</mapper>
