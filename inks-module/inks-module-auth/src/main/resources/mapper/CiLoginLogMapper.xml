<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.auth.mapper.CiLoginLogMapper">

    <resultMap type="inks.auth.domain.CiLoginLogEntity" id="CiLoginLogResult">
        <result property="id"    column="id"    />
        <result property="Userid"    column="Userid"    />
        <result property="UserName"    column="UserName"    />
        <result property="RealName"    column="RealName"    />
        <result property="IpAddr"    column="IpAddr"    />
        <result property="LoginLocation"    column="LoginLocation"    />
        <result property="BrowserName"    column="BrowserName"    />
        <result property="HostSystem"    column="HostSystem"    />
        <result property="Direction"    column="Direction"    />
        <result property="LoginStatus"    column="LoginStatus"    />
        <result property="LoginMsg"    column="LoginMsg"    />
        <result property="LoginTime"    column="LoginTime"    />
    </resultMap>

    <sql id="selectCiLoginLogVo">
        select id, Userid, UserName, RealName, IpAddr, LoginLocation, BrowserName, HostSystem, Direction, LoginStatus, LoginMsg, LoginTime from CiLoginLog
    </sql>

<!--    <select id="getPageList" parameterType="CiLoginLogEntity" resultMap="CiLoginLogResult">-->
<!--        <include refid="selectCiLoginLogVo"/>-->
<!--        where 1 = 1-->
<!--        <if test="SearchType==0">-->
<!--            <if test="SearchPojo != null ">-->
<!--                <include refid="and"></include>-->
<!--            </if>-->
<!--            <if test="DateRange != null ">-->
<!--                <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">-->
<!--                    and CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}-->
<!--                </if>-->
<!--                <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">-->
<!--                    and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}-->
<!--                </if>-->
<!--            </if>-->
<!--        </if>-->
<!--        <if test="SearchType==1">-->
<!--            <if test="SearchPojo != null ">-->
<!--                <include refid="or"></include>-->
<!--            </if>-->
<!--            <if test="DateRange != null ">-->
<!--                <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">-->
<!--                    or CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}-->
<!--                </if>-->
<!--                <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">-->
<!--                    or ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}-->
<!--                </if>-->
<!--            </if>-->
<!--        </if>-->
<!--        order by ${OrderBy} <if test="OrderType==0">asc</if>-->
<!--        <if test="OrderType==1">desc</if>-->
<!--    </select>-->
    <sql id="and">
        <if test="SearchPojo.Userid != null  and SearchPojo.Userid != ''"> Userid like concat('%', #{Userid}, '%')   </if>
        <if test="SearchPojo.UserName != null  and SearchPojo.UserName != ''"> UserName like concat('%', #{UserName}, '%')   </if>
        <if test="SearchPojo.RealName != null  and SearchPojo.RealName != ''"> RealName like concat('%', #{RealName}, '%')   </if>
        <if test="SearchPojo.IpAddr != null  and SearchPojo.IpAddr != ''"> IpAddr like concat('%', #{IpAddr}, '%')   </if>
        <if test="SearchPojo.LoginLocation != null  and SearchPojo.LoginLocation != ''"> LoginLocation like concat('%', #{LoginLocation}, '%')   </if>
        <if test="SearchPojo.BrowserName != null  and SearchPojo.BrowserName != ''"> BrowserName like concat('%', #{BrowserName}, '%')   </if>
        <if test="SearchPojo.HostSystem != null  and SearchPojo.HostSystem != ''"> HostSystem like concat('%', #{HostSystem}, '%')   </if>
        <if test="SearchPojo.Direction != null  and SearchPojo.Direction != ''"> Direction like concat('%', #{Direction}, '%')   </if>
        <if test="SearchPojo.LoginStatus != null ">  and LoginStatus = #{SearchPojo.LoginStatus} </if>
        <if test="SearchPojo.LoginMsg != null  and SearchPojo.LoginMsg != ''"> LoginMsg like concat('%', #{LoginMsg}, '%')   </if>
        <if test="SearchPojo.LoginTime != null ">  and LoginTime = #{SearchPojo.LoginTime} </if>
    </sql>
    <sql id="or">
        <if test="Userid != null  and Userid != ''"> or Userid = #{Userid}</if>
        <if test="UserName != null  and UserName != ''"> or UserName like concat('%', #{UserName}, '%')</if>
        <if test="RealName != null  and RealName != ''"> or RealName like concat('%', #{RealName}, '%')</if>
        <if test="IpAddr != null  and IpAddr != ''"> or IpAddr = #{IpAddr}</if>
        <if test="LoginLocation != null  and LoginLocation != ''"> or LoginLocation = #{LoginLocation}</if>
        <if test="BrowserName != null  and BrowserName != ''"> or BrowserName like concat('%', #{BrowserName}, '%')</if>
        <if test="HostSystem != null  and HostSystem != ''"> or HostSystem = #{HostSystem}</if>
        <if test="Direction != null  and Direction != ''"> or Direction = #{Direction}</if>
        <if test="LoginStatus != null "> or LoginStatus = #{LoginStatus}</if>
        <if test="LoginMsg != null  and LoginMsg != ''"> or LoginMsg = #{LoginMsg}</if>
        <if test="LoginTime != null "> or LoginTime = #{LoginTime}</if>
    </sql>
    <select id="getEntity" parameterType="String" resultMap="CiLoginLogResult">
        <include refid="selectCiLoginLogVo"/>
        where id = #{id}
    </select>

    <insert id="insert" parameterType="inks.auth.domain.CiLoginLogEntity">
        insert into CiLoginLog
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="Userid != null ">Userid,</if>
            <if test="UserName != null ">UserName,</if>
            <if test="RealName != null ">RealName,</if>
            <if test="IpAddr != null ">IpAddr,</if>
            <if test="LoginLocation != null ">LoginLocation,</if>
            <if test="BrowserName != null ">BrowserName,</if>
            <if test="HostSystem != null ">HostSystem,</if>
            <if test="Direction != null ">Direction,</if>
            <if test="LoginStatus != null">LoginStatus,</if>
            <if test="LoginMsg != null ">LoginMsg,</if>
            <if test="LoginTime != null">LoginTime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="Userid != null ">#{Userid},</if>
            <if test="UserName != null ">#{UserName},</if>
            <if test="RealName != null ">#{RealName},</if>
            <if test="IpAddr != null ">#{IpAddr},</if>
            <if test="LoginLocation != null ">#{LoginLocation},</if>
            <if test="BrowserName != null ">#{BrowserName},</if>
            <if test="HostSystem != null ">#{HostSystem},</if>
            <if test="Direction != null ">#{Direction},</if>
            <if test="LoginStatus != null">#{LoginStatus},</if>
            <if test="LoginMsg != null ">#{LoginMsg},</if>
            <if test="LoginTime != null">#{LoginTime},</if>
        </trim>
    </insert>

    <update id="update" parameterType="inks.auth.domain.CiLoginLogEntity">
        update CiLoginLog
        <trim prefix="SET" suffixOverrides=",">
            <if test="Userid != null and Userid != ''">Userid = #{Userid},</if>
            <if test="UserName != null and UserName != ''">UserName = #{UserName},</if>
            <if test="RealName != null and RealName != ''">RealName = #{RealName},</if>
            <if test="IpAddr != null and IpAddr != ''">IpAddr = #{IpAddr},</if>
            <if test="LoginLocation != null and LoginLocation != ''">LoginLocation = #{LoginLocation},</if>
            <if test="BrowserName != null and BrowserName != ''">BrowserName = #{BrowserName},</if>
            <if test="HostSystem != null and HostSystem != ''">HostSystem = #{HostSystem},</if>
            <if test="Direction != null and Direction != ''">Direction = #{Direction},</if>
            <if test="LoginStatus != null">LoginStatus = #{LoginStatus},</if>
            <if test="LoginMsg != null and LoginMsg != ''">LoginMsg = #{LoginMsg},</if>
            <if test="LoginTime != null">LoginTime = #{LoginTime},</if>
            <if test="Tenantid != null and Tenantid != ''">Tenantid = #{Tenantid},</if>
            <if test="TenantName != null and TenantName != ''">TenantName = #{TenantName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="delete" parameterType="String">
        delete from CiLoginLog where id = #{id}
    </delete>

    <delete id="deleteCiLoginLogByIds" parameterType="String">
        delete from CiLoginLog where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>