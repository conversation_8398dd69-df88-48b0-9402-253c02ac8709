<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.auth.mapper.SysDmsLogingMapper">
    <select id="login" resultType="inks.common.core.domain.LoginUser">
        SELECT Userid AS userid, UserName AS username, UserPassword AS password, RealName AS realName,DataLabel AS dataLabel
        FROM PiDmsUser
        WHERE username = #{userName}
          and UserPassword = #{passWord} LIMIT 1
    </select>
    <select id="count" resultType="java.lang.Integer">
        SELECT count(1)
        FROM PiDmsUser
        WHERE username = #{userName}
    </select>
    <select id="getListByOpenid" resultType="inks.auth.domain.PidmsuserPojo">
        select PiDmsUser.Userid,
               PiDmsUser.UserName,
               PiDmsUser.RealName,
               PiDmsUser.NickName,
               PiDmsUser.UserPassword,
               PiDmsUser.Mobile,
               PiDmsUser.Email,
               PiDmsUser.Sex,
               PiDmsUser.LangCode,
               PiDmsUser.Avatar,
               PiDmsUser.UserType,
               PiDmsUser.IsAdmin,
               PiDmsUser.Deptid,
               PiDmsUser.DeptCode,
               PiDmsUser.DeptName,
               PiDmsUser.IsDeptAdmin,
               PiDmsUser.DeptRowNum,
               PiDmsUser.RowNum,
               PiDmsUser.UserStatus,
               PiDmsUser.UserCode,
               PiDmsUser.Groupids,
               PiDmsUser.GroupNames,
               PiDmsUser.DmsFunctids,
               PiDmsUser.DmsFunctNames,
               PiDmsUser.Remark,
               PiDmsUser.CreateBy,
               PiDmsUser.CreateByid,
               PiDmsUser.CreateDate,
               PiDmsUser.Lister,
               PiDmsUser.Listerid,
               PiDmsUser.ModifyDate,
               PiDmsUser.Tenantid,
               PiDmsUser.TenantName,
               PiDmsUser.Revision
        from PiDmsUser
                 Left Join inkssaas.PiDmsJustAuth on PiDmsJustAuth.Userid = PiDmsUser.Userid
        where PiDmsJustAuth.AuthUuid = #{openid}
    </select>

    <select id="getListInTids" resultType="inks.auth.domain.PiTenant">
        SELECT * FROM PiTenant
        WHERE PiTenant.Tenantid IN
        <foreach item="tid" collection="tidList" open="(" separator="," close=")">
            #{tid}
        </foreach>
    </select>
    <select id="getListByUserid" resultType="inks.auth.domain.PidmsuserPojo">
        select * from PiDmsUser where Userid = #{userid}
    </select>
    <select id="getDmsUserByUserid" resultType="inks.auth.domain.PidmsuserPojo">
        select * from PiDmsUser where Userid = #{userid} and Tenantid = #{tid}
    </select>


</mapper>
