server:
  port: 9030 #服务端口
spring:
  cloud:
    nacos:
      discovery:
        server-addr: **************:8848
#        server-addr: **************:8848
        username: nacos
        password: inks0820
        ip: *************
  datasource:
    #MYsql连接字符串
    url: ***************************************************************************************************************************************************
    username: root
    password: asd@123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    maximum-pool-size: 5
    hikari:
      connection-test-query: SELECT 1
    #redis配置
  redis:
    database: 0
    # Redis服务器地址 写你的ip
    host: **************
    # Redis服务器连接端口
    port: 56379
    # Redis服务器连接密码（默认为空）
    password: asd@123456
    # 连接池最大连接数（使用负值表示没有限制  类似于mysql的连接池
    jedis:
      pool:
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 表示连接池的链接拿完了 现在去申请需要等待的时间
        max-wait: -1
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
    # 连接超时时间（毫秒） 去链接redis服务端
    timeout: 6000
feign:
  sentinel:
    enabled: true

mybatis:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  type-aliases-package: inls.auth.domian
wechat:
  miniapp:
    config:
      #微信小程序(PMS小程序)的appid
      appid: wx3866e1b5f1524d0f
      #微信小程序的Secret
      secret: 27158463ac80520f28ebca7c79a9ae57
      #微信小程序消息服务器配置的token
      token: 123456
      #微信小程序消息服务器配置的EncodingAESKey
      aseKey: 123456
      msgDataFormat: JSON
#雪花算法:  数据中心id,工作机器id
snowflake:
  dataCenterId: 1
  workerId: 1
inks:
  justauth:
    api: http://inks.tpddns.net:31080
    dingtalk:
      id: dingoa43wwh1kvqgpvangn
      secret: 1eGSxvhF6kCTruk66q4bzC3zZiDz7eVGBCCD9AMwHU_8Ja0NdrNiRcWDrwOOkpEN
  oam:
    api: http://oam.inksyun.com
    appid: wx7850d75f765d0dce