server:
  port: 9010 #服务端口
  max-threads: 1000
  min-spare-threads: 30
  tomcat:
    max-http-form-post-size: -1
spring:
  application:
    name: file
  profiles:
    active: dev
oss:
  type: aliyun
  bucket: inksdemo
  urlprefix: http://oss.demo.inksyun.com/
  minio:
    access-key: minioadmin
    secret-key: minioadmin
    endpoint: http://192.168.99.242:9000
  aliyun:
    access-key-id: LTAI5tL76QGhNx5eSkzkLnbv
    access-key-secret: ******************************
    endpoint: http://oss.demo.inksyun.com
