server:
  port: 9010 #服务端口
  max-threads: 1000
  min-spare-threads: 30
  tomcat:
    max-http-form-post-size: -1
spring:
  application:
    name: file
  profiles:
    active: dev
  main:
    allow-bean-definition-overriding: true #当遇到同样名字的时候，是否允许覆盖注册
  cloud:
    nacos:
      discovery:
        server-addr: **************:31848
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 200MB
  redis:
    database: 0
    # Redis服务器地址 写你的ip
    host: **************
    # Redis服务器连接端口
    port: 31379
    # Redis服务器连接密码（默认为空）
    password: asd@123456
    # 连接池最大连接数（使用负值表示没有限制  类似于mysql的连接池
    jedis:
      pool:
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 表示连接池的链接拿完了 现在去申请需要等待的时间
        max-wait: -1
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
    # 连接超时时间（毫秒） 去链接redis服务端
    timeout: 6000
oss:
  type: aliyun
  bucket: inksdemo
  urlprefix: http://oss.demo.inksyun.com/
  minio:
    access-key: minioadmin
    secret-key: minioadmin
    endpoint: http://**************:9000
  aliyun:
    access-key-id: LTAI5tL76QGhNx5eSkzkLnbv
    access-key-secret: ******************************
    endpoint: http://oss.demo.inksyun.com
