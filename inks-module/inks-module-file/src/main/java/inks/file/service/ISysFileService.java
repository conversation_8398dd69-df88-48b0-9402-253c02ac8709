package inks.file.service;

import inks.common.core.domain.LoginUser;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: song
 * @Date: 2021/08/28/9:31
 * @Description:
 */
public interface ISysFileService {
//    /**
//     * 文件上传接口
//     *
//     * @param file 上传的文件
//     * @return 访问地址
//     * @throws Exception
//     */
//     String uploadFile(MultipartFile file,String moduleCode,String prefix, LoginUser loginUser) throws Exception;
//    /**
//     * 文件上传接口
//     *
//     * @param file 上传的文件Base64
//     * @return 访问地址
//     * @throws Exception
//     */
//     String uploadBase64(String file,String moduleCode,String prefix, String tid) throws Exception;
//
//
//    void downWebOssFile(String fileName, HttpServletResponse response) throws IOException;
}
