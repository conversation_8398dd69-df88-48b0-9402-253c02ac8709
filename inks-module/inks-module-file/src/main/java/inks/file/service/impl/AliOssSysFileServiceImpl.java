package inks.file.service.impl;

import inks.file.service.ISysFileService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: song
 * @Date: 2021/08/28/10:49
 * @Description:
 */
@Primary
@Service
public class AliOssSysFileServiceImpl implements ISysFileService {
//    /**
//     * @Description: 阿里云连接配置
//     * @Param:
//     * @return:
//     * @Author: song
//     * @Date: 2021/8/28
//     */
//    @Value("${spring.alioss.accessKeyId}")
//    private String accessKeyId;
//    @Value("${spring.alioss.accessKeySecret}")
//    private String accessKeySecret;
//    @Value("${spring.alioss.endpoint}")
//    private String endpoint;
//    @Value("${spring.alioss.bucket}")
//    private String bucket;
//    @Value("${spring.alioss.ossrefixp}")
//    private String ossrefixp;
//    private static final Logger logger = LoggerFactory.getLogger(AliOssSysFileServiceImpl.class);
//
//
//    @Override
//    public String uploadFile(MultipartFile file, String moduleCode, String prefix,  LoginUser loginUser) throws Exception {
//        //生成UUID将文件命名成UUID以防重复覆盖
//        String tid=loginUser.getTenantid();
//        String dateStr = DateUtils.getTimestamp(new Date()).toString();
//        String suffix = file.getOriginalFilename().substring(
//                file.getOriginalFilename().lastIndexOf('.'));
//        if (!this.isFolder(moduleCode)) {
//            throw new BaseBusinessException("文件夹不存在请输入正确的文件夹");
//        }
//
//        //创建阿里云OSS连接
//        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
//        try {
//            //容器不存在，就创建
//            if (!ossClient.doesBucketExist(bucket)) {
//                ossClient.createBucket(bucket);
//                CreateBucketRequest createBucketRequest = new CreateBucketRequest(bucket);
//                createBucketRequest.setCannedACL(CannedAccessControlList.Default);
//                ossClient.createBucket(createBucketRequest);
//            }
//            String fileUrl = dateStr + suffix;
//            if (prefix != null)
//                fileUrl = prefix + "-" + fileUrl;
//            fileUrl = tid + "-" + fileUrl;  // 租户id开头
//            //上传文件至阿里云OSS服务器
//            PutObjectResult result = ossClient.putObject(new PutObjectRequest(bucket, moduleCode + "/" + fileUrl, file.getInputStream()));
//            if (null != result) {
//                return ossrefixp + moduleCode + "/" + fileUrl;
//            }
//        } catch (OSSException oe) {
//            logger.error(oe.getMessage());
//        } catch (ClientException ce) {
//            logger.error(ce.getMessage());
//        } catch (Exception e) {
//            logger.error(e.getMessage());
//        } finally {
//            ossClient.shutdown();
//        }
//        return null;
//    }
//
//    public Boolean isFolder(String moduleCode) {
//        moduleCode = moduleCode + "/";
//        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
//        if (ossClient.doesObjectExist(bucket, moduleCode)) {
//            return true;
//        }
//        return false;
//
//    }
//
//    @Override
//    public String uploadBase64(String img, String moduleCode, String prefix, String tid) throws Exception {
//        if (!this.isFolder(moduleCode)) {
//            throw new BaseBusinessException("文件夹不存在请输入正确的文件夹");
//        }
//      //  img ="data:image/png;base64,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";
//        // 使用前端插件时可能有前有（"data:image/xxxx;base64,"）
//        // 获取图片格式
//        String imgsuffix = img.substring(11,img.indexOf(";"));
//        // 使用插件传输产生的前缀
//        String imgprefix = img.substring(0,img.indexOf(",") + 1);
//        // 替换前缀为空
//        img= img.replace(imgprefix,"");
//
//       // System.out.println("img2:"+img);
//
//        BASE64Decoder decoder = new BASE64Decoder();
//        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
//        try {
//
//            // Base64解码
//            byte[] imgbytes = decoder.decodeBuffer(img);
////            for (int i = 0; i < bytes.length; ++i) {
////                if (bytes[i] < 0) {// 调整异常数据
////                    bytes[i] += 256;
////                }
////            }
////            ObjectMetadata objectMeta = new ObjectMetadata();
////            objectMeta.setContentLength(imgbytes.length);
//           //将字节码转换成流
//            //InputStream input = new ByteArrayInputStream(bytes);
//            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(imgbytes);
//
//            String fileName = inksSnowflake.getSnowflake().nextIdStr();
//            if (prefix != null)
//                fileName = prefix + "_" + fileName;
//
//            ossClient.putObject(bucket, moduleCode + "/" + fileName + "."+imgsuffix, byteArrayInputStream);
//
////            if (!ossClient.doesBucketExist(bucket)) {
////                ossClient.createBucket(bucket);
////                CreateBucketRequest createBucketRequest = new CreateBucketRequest(bucket);
////                createBucketRequest.setCannedACL(CannedAccessControlList.Default);
////                ossClient.createBucket(createBucketRequest);
////            }
//            //PutObjectResult result = ossClient.putObject(new PutObjectRequest(bucket, moduleCode + "/" + fileName + "."+imgsuffix, input, objectMeta));
//        //    ossClient.putObject(bucket, moduleCode + "/" + fileName + ".jpg", input, objectMeta);
//            return ossrefixp + moduleCode + "/" + fileName + "."+imgsuffix;
//        } catch (Exception e) {
//            logger.error(e.getMessage());
//            return null;
//        } finally {
//            ossClient.shutdown();
//        }
//    }
//
//    @Override
//    public void downWebOssFile(String fileName, HttpServletResponse response) throws IOException {
//        String objectName = fileName;
//        // 创建OSSClient实例。
//        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
//        // ossObject包含文件所在的存储空间名称、文件名称、文件元信息以及一个输入流。
//        OSSObject ossObject = ossClient.getObject(bucket, objectName);
//        int i = fileName.lastIndexOf("/");
//        fileName = fileName.substring(i + 1);
//        //将流上传到网页
//        BufferedOutputStream out = new BufferedOutputStream(response.getOutputStream());
//        //通知浏览器以附件形式下载
//        response.setContentType("application/octet-stream;charset=ISO8859-1");
//        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
//        response.addHeader("Pargam", "no-cache");
//        response.addHeader("Cache-Control", "no-cache");
//        // 读去Object内容  返回
//        BufferedInputStream in = new BufferedInputStream(ossObject.getObjectContent());
//        byte[] car = new byte[1024];
//        int L = 0;
//        while ((L = in.read(car)) != -1) {
//            out.write(car, 0, L);
//        }
//        if (out != null) {
//            out.flush();
//            out.close();
//        }
//        if (in != null) {
//            in.close();
//        }
//        // 关闭OSSClient。
//        ossClient.shutdown();
//    }
}
