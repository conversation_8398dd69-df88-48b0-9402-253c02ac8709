package inks.file.controller;


import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.FileInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.security.service.TokenService;
import inks.file.service.FileInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: song
 * @Date: 2021/08/28/9:26
 * @Description:
 */
@RestController
@Api(tags = "file中心:文件API")
public class FileController {
    private static final Logger log = LoggerFactory.getLogger(FileController.class);
//    @Autowired
//    private ISysFileService iSysFileService;

    @Resource
    private FileInfoService fileInfoService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "上传单个文件", notes = "上传单个文件")
    @PostMapping(value = "upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<FileInfo> upload(MultipartFile file, String code) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (loginUser == null) {
            throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
        }
        try {
            return R.ok(this.fileInfoService.putFile(file, code), "上传成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "上传文本", notes = "上传文本")
    @PostMapping(value = "saveContent")
    public R<FileInfo> saveContent(@RequestBody String json) {
        FileInfo fileInfo = JSONArray.parseObject(json, FileInfo.class);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (loginUser == null) {
            throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
        }
        try {
            return R.ok(this.fileInfoService.putContent(fileInfo), "上传成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "上传ImageBase64", notes = "上传ImageBase64")
    @PostMapping(value = "saveImage")
    public R<FileInfo> saveImage(@RequestBody String json) {
        FileInfo fileInfo = JSONArray.parseObject(json, FileInfo.class);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (loginUser == null) {
            throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
        }
        try {
            return R.ok(this.fileInfoService.putImage(fileInfo), "上传成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除单个文件", notes = "删除单个文件")
    @PostMapping(value = "remove")
    public R remove(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (loginUser == null) {
            throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
        }
        FileInfo fileInfo = JSONArray.parseObject(json, FileInfo.class);
        try {
            return R.ok(this.fileInfoService.removeFile(fileInfo), "删除成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

//    @ApiOperation(value = "上传BASE64", notes = "上传BASE64")
//    @RequestMapping(value = "/uploadBase64", method = RequestMethod.POST)
//    public R uploadBase64(@RequestBody String json, String code, String prefix) {
//        try {
//            JSONObject jsonObject = JSON.parseObject(json);
//            //  String imgStr = URLDecoder.decode(img, "utf-8");
//            String imgStr = jsonObject.getString("img");
//            System.out.println("img:" + imgStr);
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            if (loginUser == null) {
//                throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
//            }
//            return R.ok(iSysFileService.uploadBase64(imgStr, code, prefix, loginUser.getTenantid()), "上传成功");
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    @ApiOperation(value = "下载文件", notes = "下载文件")
//    @GetMapping("/down")
//    public void downFile(String fileName, HttpServletResponse response) {
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        try {
//            if (loginUser == null) {
//                throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
//            }
//            iSysFileService.downWebOssFile(fileName, response);
//        } catch (BaseBusinessException be) {
//            throw new BaseBusinessException(be.getMessage());
//        } catch (Exception e) {
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }

//    @ApiOperation(value = "上传文件", notes = "上传文件")
//    @PostMapping("upload")
//    public R upload(MultipartFile file, String code, String prefix) {
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        if (loginUser == null) {
//            throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
//        }
//        try {
//            return R.ok(iSysFileService.uploadFile(file, code, prefix, loginUser), "上传成功");
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    @ApiOperation(value = "上传BASE64", notes = "上传BASE64")
//    @RequestMapping(value = "/uploadBase64", method = RequestMethod.POST)
//    public R uploadBase64(@RequestBody String json, String code, String prefix) {
//        try {
//            JSONObject jsonObject = JSON.parseObject(json);
//            //  String imgStr = URLDecoder.decode(img, "utf-8");
//            String imgStr = jsonObject.getString("img");
//            System.out.println("img:" + imgStr);
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            if (loginUser == null) {
//                throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
//            }
//            return R.ok(iSysFileService.uploadBase64(imgStr, code, prefix, loginUser.getTenantid()), "上传成功");
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    @ApiOperation(value = "下载文件", notes = "下载文件")
//    @GetMapping("/down")
//    public void downFile(String fileName, HttpServletResponse response) {
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        try {
//            if (loginUser == null) {
//                throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
//            }
//            iSysFileService.downWebOssFile(fileName, response);
//        } catch (BaseBusinessException be) {
//            throw new BaseBusinessException(be.getMessage());
//        } catch (Exception e) {
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }
}
