package inks.file.config;

import com.aliyun.oss.OSSClient;
import inks.file.oss.AliyunStorage;
import inks.file.oss.Storage;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * aliyun oss配置类
 *
 * <AUTHOR> Yvon
 * @version : 1.0
 * @since : 2020-11-03
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass({OSSClient.class})
public class AliyunOSSConfiguration {

    AliyunOSSConfiguration() {
    }

    @Bean
    @ConditionalOnProperty(prefix = "oss", name = {"type"}, havingValue = "aliyun")
    Storage aliyunStorage(inks.file.config.OSSProperties ossProperties) {
        inks.file.config.OSSProperties.Aliyun aliyun = ossProperties.getAliyun();
        OSSClient ossClient = new OSSClient(aliyun.getEndpoint(), aliyun.getAccessKeyId(), aliyun.getAccessKeySecret());
        return new AliyunStorage(ossClient);
    }
}
