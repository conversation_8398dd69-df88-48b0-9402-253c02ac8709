package inks.file.config;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * oss 自动配置
 *
 * <AUTHOR> Yvon
 * @version : 1.0
 * @since : 2020-11-03
 */
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties({inks.file.config.OSSProperties.class})
@Import({inks.file.config.MinioOSSConfiguration.class, inks.file.config.AliyunOSSConfiguration.class})
public class OSSAutoConfiguration {
    public OSSAutoConfiguration() {
    }
}