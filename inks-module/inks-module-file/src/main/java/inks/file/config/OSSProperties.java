package inks.file.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * oss 属性
 *
 * <AUTHOR> Yvon
 * @version : 1.0
 * @since : 2020-11-03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode()
@ConfigurationProperties(prefix = "oss")
public class OSSProperties {

    private Aliyun aliyun;
    private Minio minio;

    public <PERSON>yun getAliyun() {
        return this.aliyun;
    }

    public Minio getMinio() {
        return this.minio;
    }

    public void setAliyun(<PERSON><PERSON> aliyun) {
        this.aliyun = aliyun;
    }

    public void setMinio(Minio minio) {
        this.minio = minio;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode()
    public static class Aliyun {
        private boolean active;
        private String endpoint;
        private String accessKeyId;
        private String accessKeySecret;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode()
    public static class Minio {
        private boolean active;
        private String endpoint;
        private String accessKey;
        private String secretKey;
    }


}
