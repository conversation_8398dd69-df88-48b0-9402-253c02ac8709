package inks.backup.controller;


import com.alibaba.fastjson.JSONArray;
import inks.backup.service.FileInfoService;
import inks.common.core.domain.FileInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.security.service.TokenService;
import io.minio.MinioClient;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: song
 * @Date: 2021/08/28/9:26
 * @Description:
 */

@RestController
public class FileController {
    private static final Logger log = LoggerFactory.getLogger(FileController.class);

    @Autowired
    @Qualifier("fileInfoAliyunServiceImpl")
    private FileInfoService fileInfoAliyunService; //aliyun上传

    @Autowired
    @Qualifier("fileInfoMinioServiceImpl")
    private FileInfoService fileInfoMinioService; //minio上传

    @Value("${oss.type}")
    private String osstype;
    // 通过路径上传（备份数据库时的文件夹后缀）
    @Value("${oss.minio.dirsuffix}")
    private String dirsuffix;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    private MinioClient minioClient;

    /**
     * @return R<FileInfo>
     * @Description Minio上传单个文件, 自定义桶名bucket
     * <AUTHOR>
     * @param[1] file
     * @param[2] bucket 桶名
     * @param[3] dir 桶名后跟的文件夹名
     * @time 2023/5/31 10:31
     */
    @ApiOperation(value = "上传单个文件ByOssType", notes = "上传单个文件")
    @PostMapping(value = "upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<FileInfo> upload(MultipartFile file, String bucket, String dir, String osstype) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (loginUser == null) throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
        if (StringUtils.isBlank(osstype)) {
            osstype = this.osstype;
        }
        //加一层日期文件夹
        dir = dir + "/" + DateUtils.parseDateToStr("yyyyMMdd", new Date());
        try {
            if (osstype.equals("minio")) {
                return R.ok(this.fileInfoMinioService.putFile(file, bucket, dir), "上传成功");
            } else if (osstype.equals("aliyun")) {
                if (StringUtils.isBlank(bucket)) bucket = "inksoms";
                return R.ok(this.fileInfoAliyunService.putFile(file, bucket, dir), "上传成功");
            } else {
                return R.fail("osstype:oss类型错误");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @return R
     * @Description 删除单个文件
     * <AUTHOR>
     * @param[1] json 只接收bucketname和filename(例如：{"bucketName":"pic","fileName":"test/20210531/xx.jpg"})
     * @time 2023/5/31 10:58
     */
    @ApiOperation(value = "删除单个文件ByOssType", notes = "删除单个文件")
    @PostMapping(value = "remove")
    public R remove(@RequestBody String json, String osstype) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (loginUser == null) {
            throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
        }
        if (StringUtils.isBlank(osstype)) {
            osstype = this.osstype;
        }
        FileInfo fileInfo = JSONArray.parseObject(json, FileInfo.class);
        try {
            if (osstype.equals("minio")) {
                return R.ok(this.fileInfoMinioService.removeFile(fileInfo), "删除成功");
            } else if (osstype.equals("aliyun")) {
                return R.ok(this.fileInfoAliyunService.removeFile(fileInfo), "删除成功");
            } else {
                return R.fail("osstype:oss类型错误");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
       http://inks.tpddns.net:9080/utils/mysql/20230705/4e8098fc30143b3906ff.zip
       minio地址改为，子目录yaml参数，W1.zip为周一，这样确保一个服务，有7天备份
       http://inks.tpddns.net:9080/utils/mysql/{yaml参数}/W1.zip
       W1+HH.zip 这个格式，一样一天备份多次也可以
       W101.ZIP 周一，1点备份*/
    @ApiOperation(value = "通过文件路径上传单个文件ByOssType", notes = "通过文件路径上传单个文件")
    @PostMapping(value = "uploadByPath")
    public R<FileInfo> uploadByPath(String filepath, String bucket, String dir, String osstype) throws IOException {
        if (StringUtils.isBlank(osstype)) {
            osstype = this.osstype;
        }
        try {
            //加一层日期文件夹  备份数据库不要加日期文件夹，改为加yml的参数
//            dir = dir + "/" + DateUtils.parseDateToStr("yyyyMMdd", new Date());
            dir = dir + "/" + dirsuffix;
            log.info("-----------------------------开始Minio通过文件路径上传单个文件-----------------------------");
            if (osstype.equals("minio")) {
                return R.ok(this.fileInfoMinioService.putFile(filepath, bucket, dir), "上传成功");
            } else if (osstype.equals("aliyun")) {
                return R.ok(this.fileInfoAliyunService.putFile(filepath, bucket, dir), "上传成功");
            } else {
                return R.fail("osstype:oss类型错误");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "上传文本", notes = "Aliyun上传文本")
    @PostMapping(value = "saveContent")
    public R<FileInfo> saveContent(@RequestBody String json) {
        FileInfo fileInfo = JSONArray.parseObject(json, FileInfo.class);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (loginUser == null) {
            throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
        }
        try {
            return R.ok(this.fileInfoAliyunService.putContent(fileInfo), "上传成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


//    @ApiOperation(value = "上传ImageBase64", notes = "Aliyun上传ImageBase64")
//    @PostMapping(value = "saveImage")
//    public R<FileInfo> saveImage(@RequestBody String json) {
//        FileInfo fileInfo = JSONArray.parseObject(json, FileInfo.class);
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        if (loginUser == null) {
//            throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
//        }
//        try {
//            return R.ok(this.fileInfoAliyunService.putImage(fileInfo), "上传成功");
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }


    @ApiOperation(value = "String字符串转html并上传", notes = "上传文件")
    @PostMapping(value = "uploadHtml")
    public R<FileInfo> uploadHtml(String content, String bucket, String dir) throws IOException {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (loginUser == null) {
            throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
        }
        String fileName = "example.html"; // 临时文件名
        String fileType = "text/html"; // 文件类型
        // 将字符串内容转换为字节数组
        byte[] contentBytes = content.getBytes(StandardCharsets.UTF_8);
        // 创建MultipartFile对象
        MultipartFile file = new MockMultipartFile(fileName, fileName, fileType, contentBytes);
        try {
            //加一层日期文件夹
            dir = dir + "/" + DateUtils.parseDateToStr("yyyyMMdd", new Date());
            log.info("-----------------------------开始上传html文件-----------------------------");
            return R.ok(this.fileInfoMinioService.putFile(file, bucket, dir), "上传成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


//    @ApiOperation(value = "上传BASE64", notes = "上传BASE64")
//    @RequestMapping(value = "/uploadBase64", method = RequestMethod.POST)
//    public R uploadBase64(@RequestBody String json, String code, String prefix) {
//        try {
//            JSONObject jsonObject = JSON.parseObject(json);
//            //  String imgStr = URLDecoder.decode(img, "utf-8");
//            String imgStr = jsonObject.getString("img");
//            System.out.println("img:" + imgStr);
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            if (loginUser == null) {
//                throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
//            }
//            return R.ok(iSysFileService.uploadBase64(imgStr, code, prefix, loginUser.getTenantid()), "上传成功");
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    @ApiOperation(value = "下载文件", notes = "下载文件")
//    @GetMapping("/down")
//    public void downFile(String fileName, HttpServletResponse response) {
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        try {
//            if (loginUser == null) {
//                throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
//            }
//            iSysFileService.downWebOssFile(fileName, response);
//        } catch (BaseBusinessException be) {
//            throw new BaseBusinessException(be.getMessage());
//        } catch (Exception e) {
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }

//    @ApiOperation(value = "上传文件", notes = "上传文件")
//    @PostMapping("upload")
//    public R upload(MultipartFile file, String code, String prefix) {
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        if (loginUser == null) {
//            throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
//        }
//        try {
//            return R.ok(iSysFileService.uploadFile(file, code, prefix, loginUser), "上传成功");
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    @ApiOperation(value = "上传BASE64", notes = "上传BASE64")
//    @RequestMapping(value = "/uploadBase64", method = RequestMethod.POST)
//    public R uploadBase64(@RequestBody String json, String code, String prefix) {
//        try {
//            JSONObject jsonObject = JSON.parseObject(json);
//            //  String imgStr = URLDecoder.decode(img, "utf-8");
//            String imgStr = jsonObject.getString("img");
//            System.out.println("img:" + imgStr);
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            if (loginUser == null) {
//                throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
//            }
//            return R.ok(iSysFileService.uploadBase64(imgStr, code, prefix, loginUser.getTenantid()), "上传成功");
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    @ApiOperation(value = "下载文件", notes = "下载文件")
//    @GetMapping("/down")
//    public void downFile(String fileName, HttpServletResponse response) {
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        try {
//            if (loginUser == null) {
//                throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
//            }
//            iSysFileService.downWebOssFile(fileName, response);
//        } catch (BaseBusinessException be) {
//            throw new BaseBusinessException(be.getMessage());
//        } catch (Exception e) {
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }
}
