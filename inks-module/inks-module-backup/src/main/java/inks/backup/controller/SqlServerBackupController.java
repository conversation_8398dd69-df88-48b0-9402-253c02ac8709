package inks.backup.controller;

import inks.backup.pojo.LoggerQueue;
import inks.backup.service.OssService;
import inks.backup.utils.mysqldump.CompressZip;
import inks.backup.utils.mysqldump.DatabaseUtil;
import inks.backup.utils.PrintColor;
import inks.backup.utils.mysqldump.FileHelper;
import inks.common.core.domain.FileInfo;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.Charset;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;

import static org.apache.commons.lang3.StringUtils.*;

@RestController
@Api(tags = "Sql Server备份")
public class SqlServerBackupController {

    @Resource
    private FileController fileController;
    @Resource
    private MailController mailController;
    private final static String BACKUP_SUFFIX = ".bak";


    @Value("${spring.mail.ipAddress}")
    private String ipAddress;
    @Value("${spring.mail.toEmail}")
    private String toEmail;
    // 默认备份的数据库名字
    @Value("${backup.sqlserver.dbname}")
    private String ymlDbName;
    @Value("${backup.sqlserver.scheduling.cron}")
    private String cron;
    @Resource
    private OssService ossService;

    @Value("${oss.bucket}")
    private String bucket;
    @Value("${backup.sqlserver.password}")//解压密码
    private String password;
    @Value("${backup.sqlserver.directory}")//临时备份文件存放目录
    private String directory;

    private final static Logger logger = LoggerFactory.getLogger(SqlServerBackupController.class);


    public static void main(String[] args) throws UnknownHostException {
        System.out.println("本机IP地址: " + InetAddress.getLocalHost().getHostAddress());
    }


    @Scheduled(cron = "${backup.sqlserver.scheduling.cron}")//每天凌晨1点执行
    @ApiOperation(value = "SqlServer备份,默认备份yml数据库", notes = "", produces = "application/json")
//    @RequestMapping(value = "/backup", method = RequestMethod.GET)
    public R<String> backup() {
        try {
            System.out.println("========开始定时任务备份数据库:"+ymlDbName+"\ncron:"+cron);
            //如果没有填写数据库名字,则使用yml配置文件中的数据库名字
            // 开始备份+压缩+上传minio+发送邮件
            String path = backupMinio(ymlDbName);
            return R.ok("备份成功,备份路径为:" + path);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * sqlserver备份
     *
     * @param dbname 数据库名称
     * @return
     */
//    @Scheduled(cron = "${backup.sqlserver.scheduling.cron}")//每天凌晨1点执行
    @ApiOperation(value = "通过数据库名进行SqlServer备份,不填默认备份yml数据库", notes = "", produces = "application/json")
    @RequestMapping(value = "/backupByDbName", method = RequestMethod.GET)
    public R<String> backupTest(@RequestParam(required = false)String dbname) {
        try {
            //如果没有填写数据库名字,则使用yml配置文件中的数据库名字
            if (isBlank(dbname)) {
                dbname = ymlDbName;
            }
            // 开始备份+压缩+上传minio+发送邮件
            String path = backupMinio(dbname);
            return R.ok("备份成功,备份路径为:" + path);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * sqlserver备份  开始备份+压缩+上传minio+删除+发送邮件
     * @param dbname  数据库名称
     * @return
     */
    public String backupMinio(String dbname) {
        //默认备份的路径:各系统下工作目录 即jar包所在目录
        String backupPath = System.getProperty("user.dir");
        //如果配置文件中有备份路径,则使用配置文件中的备份路径
        if (StringUtils.isNotBlank(directory)) {
            backupPath = directory;
        }
        //备份文件的名字,使用雪花算法
        String backupName = inksSnowflake.getSnowflake().nextIdStr();
        File file = new File(backupPath);
        if (!file.exists()) {
            file.mkdirs();
        }
        // 备份存储路径
        String path = backupPath + File.separator + backupName + BACKUP_SUFFIX;
        //备份sql
        String bakSQL = "backup database " + dbname + " to disk=? with init";
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        try {
            //获取数据库连接
            connection = new DatabaseUtil().getSqlServerConnection();
            if (connection != null) {
                LoggerQueue.toLogPojo(Thread.currentThread(), "获取数据源成功", "message", "info");
            }
            if (connection != null) {
                preparedStatement = connection.prepareStatement(bakSQL);
            }
            if (preparedStatement != null) {
                preparedStatement.setString(1, path); //这一步会把 disk=? 的问号替换成path
            }
            LoggerQueue.toLogPojo(Thread.currentThread(), "开始进行备份", "process", "info");
            //执行sql
            if (preparedStatement != null) {
                preparedStatement.execute();
            }
            LoggerQueue.toLogPojo(Thread.currentThread(), "获取备份文件成功，存储至-->" + path, "message", "info");
            logger.info("{},获取备份文件成功", dbname);

            // bak压缩成zip,并删除bak文件
            String zipPath = path.replace(".bak", ".zip");
//            ZipHelper.zip(path, zipPath);
            CompressZip.zip(new File(path) , new File(zipPath) , Charset.forName("GBK") , password , "密码为在yml文件中配置");
            FileHelper.delete(path);
            // 上传zip到minio 先设置上传响应等待时间为5分钟
            PrintColor.green("------------------开始上传:" + zipPath + "fileController:" + fileController + "-------------------");
            R<FileInfo> fileInfoR = fileController.uploadByPath(zipPath, "utils", "sqlserver_backup", "minio");
            String fileUrl = fileInfoR.getData().getFileurl();
            // .zip也删除 上传后再删除！！
            FileHelper.delete(zipPath);
            PrintColor.green("------------------.zip也删除:" + zipPath + "-------------------");
            PrintColor.color("备份成功,文件名为:" + zipPath + "\nminio地址为：" + fileUrl);

            // 发送邮件
            String hostAddress = InetAddress.getLocalHost().getHostAddress();//获取本机ip
            String subject = "Sql Server备份--" + hostAddress + "/" + dbname;
            String content = "备份服务器ip为:<br>" + hostAddress
                    + "<br><br>备份数据库为：<br>" + dbname
                    + "<br><br>Sql Server备份成功，文件名(已删除)为：<br>" + zipPath
                    + "<br><br>minio地址为：<br>" + fileUrl;
            mailController.sendEmail(toEmail, subject, content);
            PrintColor.zi("邮件发送成功");
            return "备份成功,文件名为:" + zipPath + "\nminio地址为：" + fileUrl + "备份服务器ip为:" + hostAddress;
        } catch (SQLException e) {
            LoggerQueue.toLogPojo(Thread.currentThread(), "备份失败", "message", "error");
            throw new BaseBusinessException("备份失败");
        } catch (IOException | MessagingException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (preparedStatement != null) {
                    preparedStatement.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }







//    @PostMapping("/download")
    public void download(@RequestParam("name") String name, HttpServletResponse response) {
        InputStream inputStream = null;
        ServletOutputStream outputStream = null;
        try {
            inputStream = ossService.download(bucket, name);
            outputStream = response.getOutputStream();
            response.setHeader("Content-Disposition", "attachment;filename=" + name);
            int len = 0;
            byte[] buffer = new byte[1024];
            while ((len = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, len);
            }
            outputStream.flush();
        } catch (Exception e) {
            throw new BaseBusinessException("下载失败");
        } finally {
            try {
                inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

}
