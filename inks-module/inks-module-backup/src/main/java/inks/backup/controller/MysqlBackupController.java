package inks.backup.controller;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import inks.backup.utils.PrintColor;
import inks.backup.utils.mysqldump.FileHelper;
import inks.backup.utils.mysqldump.JdbcConnection;
import inks.backup.utils.mysqldump.MysqlExport;
import inks.common.core.domain.FileInfo;
import inks.common.core.domain.R;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;
import java.sql.Connection;
import java.time.Duration;
import java.util.concurrent.TimeUnit;


/**
 * 数据备份
 *
 * <AUTHOR>
 * @since 2021-11-08 13:55:37
 */
@RestController
@Api(tags = "Mysql备份")
public class MysqlBackupController {
    @Resource
    private TokenService tokenService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Value("${spring.datasource.hikari.backup.url}")
    private String dbUrl;

    @Value("${spring.datasource.hikari.backup.username}")
    private String dbUsername;

    @Value("${spring.datasource.hikari.backup.password}")
    private String dbPassword;
    @Value("${spring.mail.toEmail}")
    private String toEmail;
    @Value("${spring.mail.ipAddress}")
    private String ipAddress;

    @Resource
    private FileController fileController;
    @Resource
    private MailController mailController;

    public static void main(String[] args) {
        String currentWorkingDirectory = System.getProperty("user.dir");
        System.out.println("===================System.getProperty(\"user.dir\")"+currentWorkingDirectory);
    }

    //16:46执行
    //    @Scheduled(cron = "${backup.scheduling.cron}")//每天凌晨1点执行
    // 最终备份文件格式为: CREATE TABLE IF NOT EXISTS `SaScene` (...   加上 INSERT INTO `SaScene`(...语句
    @ApiOperation(value = "Mysql备份", notes = "Mysql备份", produces = "application/json")
    @RequestMapping(value = "/mysqlBackup", method = RequestMethod.GET)
    public R<String> mysqlBackup() {
        // 定义锁的名称和锁过期时间3000秒
        String lockName = "mysql_backup_lock";
        long expireTime = 3000000L; //
        PrintColor.green("------------------停在这里说明redis锁死-------------------");
        // 尝试获取锁
        Boolean success = redisTemplate.opsForValue().setIfAbsent(lockName, "locked", Duration.ofMillis(expireTime));
        if (!success) {
            return R.fail("Redis锁死：有其他备份任务正在进行，请稍后再试。");
        }
        PrintColor.green("------------------准备建立连接,停在这里说明连接失败-------------------");
        try {
            // 建立(要备份)数据库连接
//            Connection connection = JdbcConnection.getConnection("192.168.99.111:53308", "inkssaas", "root", "asd@123456");
//            String jdbcUrl = "******************************************************************************************************************************************************************************************************************************";
            String jdbcUrl = dbUrl + "&user=" + dbUsername + "&password=" + dbPassword;
            Connection connection = JdbcConnection.getConnection(jdbcUrl);
            // 创建导出对象，传入参数：数据库连接对象，要导出的数据库名，导出的路径
            // 保存到当前工作目录
            String currentWorkingDirectory = System.getProperty("user.dir");
            PrintColor.green("------------------成功建立连接,开始备份-------------------");
            MysqlExport mysqlExport = new MysqlExport(connection, currentWorkingDirectory);
            // 导出数据库到文件 exportFilePath是zip文件的全路径
            String exportFilePath = mysqlExport.export();
            // 上传.zip压缩文件到minio 先设置上传响应等待时间为5分钟
            R<FileInfo> fileInfoR = fileController.uploadByPath(exportFilePath, "utils", "mysql", "minio");
            String fileUrl = fileInfoR.getData().getFileurl();
            // .zip也删除 上传后再删除！！
            FileHelper.delete(exportFilePath);
            PrintColor.green("------------------.zip也删除:"+exportFilePath+"-------------------");
            PrintColor.color("备份成功,文件名为:" + exportFilePath + "\nminio地址为：" + fileUrl);
            // 发送邮件
            String[] urlParts = dbUrl.split("//"); // 拆分URL
            String dbInfo = urlParts[1].split("\\?")[0]; // 提取主机名、端口号和数据库名部分
            String subject = ipAddress+": Mysql备份--" + dbInfo;
            String content = "Mysql备份成功，文件名为：" + exportFilePath + "<br><br>minio地址为：" + fileUrl;
            mailController.sendEmail(toEmail, subject, content);
//            mailController.sendEmail("<EMAIL>", subject, content);
            PrintColor.zi("邮件发送成功");
            return R.ok("备份成功,文件名为:" + exportFilePath + "\nminio地址为：" + fileUrl);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        } finally {
            // 释放锁
            redisTemplate.delete(lockName);
        }
    }


    @ApiOperation(value = "Mysql备份By连接名和密码", notes = "Mysql备份", produces = "application/json")
    @RequestMapping(value = "/mysqlBackupByUrl", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "CiInitLog.List")
    public R<String> mysqlBackupByUrl(String dbUrl, String dbUsername, String dbPassword) {
        // 定义锁的名称和锁过期时间3000秒
        String lockName = "mysql_backup_lock";
        long expireTime = 3000000L; //
        PrintColor.green("------------------停在这里说明redis锁死-------------------");
        // 尝试获取锁
        Boolean success = redisTemplate.opsForValue().setIfAbsent(lockName, "locked", Duration.ofMillis(expireTime));
        if (!success) {
            return R.fail("Redis锁死：有其他备份任务正在进行，请稍后再试。");
        }
        PrintColor.green("------------------准备建立连接,停在这里说明连接失败-------------------");
        try {
            // 建立(要备份)数据库连接
//            Connection connection = JdbcConnection.getConnection("192.168.99.111:53308", "inkssaas", "root", "asd@123456");
//            String jdbcUrl = "******************************************************************************************************************************************************************************************************************************";
            String jdbcUrl = dbUrl + "&user=" + dbUsername + "&password=" + dbPassword;
            Connection connection = JdbcConnection.getConnection(jdbcUrl);
            // 创建导出对象，传入参数：数据库连接对象，要导出的数据库名，导出的路径
            // 保存到当前工作目录
            String currentWorkingDirectory = System.getProperty("user.dir");
            PrintColor.green("------------------成功建立连接,开始备份-------------------");
            MysqlExport mysqlExport = new MysqlExport(connection, currentWorkingDirectory);
            // 导出数据库到文件
            String exportFilePath = mysqlExport.export();
            // 上传.zip压缩文件到minio
            R<FileInfo> fileInfoR = fileController.uploadByPath(exportFilePath, "utils", "mysql", "minio");
            String fileUrl = fileInfoR.getData().getFileurl();
            // .zip也删除 上传后再删除！！
            FileHelper.delete(exportFilePath);
//            // 上传.zip压缩文件到minio 设置响应等待时间为5分钟
//            OkHttpClient client = new OkHttpClient.Builder()
//                    .readTimeout(5, TimeUnit.MINUTES)
//                    .build();
            PrintColor.color("备份成功,文件名为:" + exportFilePath + "\nminio地址为：" + fileUrl);
            // 发送邮件
            String[] urlParts = dbUrl.split("//"); // 拆分URL
            String dbInfo = urlParts[1].split("\\?")[0]; // 提取主机名、端口号和数据库名部分
            String subject = "Mysql备份--" + dbInfo;
            String content = "Mysql备份成功，文件名为：" + exportFilePath + "<br><br>minio地址为：" + fileUrl;
            mailController.sendEmail(toEmail, subject, content);
//            mailController.sendEmail("<EMAIL>", subject, content);
            PrintColor.zi("邮件发送成功");
            return R.ok("备份成功,文件名为:" + exportFilePath + "      minio地址为：" + fileUrl);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        } finally {
            // 释放锁
            redisTemplate.delete(lockName);
        }
    }


    @ApiOperation(value = "上传Mysql备份文件By路径", notes = "Mysql备份", produces = "application/json")
    @RequestMapping(value = "/uploadMysql200MOkHttp", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiInitLog.List")
    public R<String> uploadMysql200MOkHttp(String path) throws IOException {

        // 上传.zip压缩文件到minio 设置响应等待时间为5分钟
        OkHttpClient client = new OkHttpClient.Builder()
                .readTimeout(5, TimeUnit.MINUTES)
                .build();
        // 创建请求体
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("filepath", path)
                .addFormDataPart("bucket", "utils")
                .addFormDataPart("dir", "mysql")
                .addFormDataPart("osstype", "minio")
                .build();
        // 构建请求
        Request request = new Request.Builder()
                .url("http://dev.inksyun.com:31080/utils/D96M16B1/uploadByPath")
                .headers(Headers.of("Authorization", "bcdb"))
                .post(requestBody)
                .build();
        // 发送请求并获取响应
        Response response = client.newCall(request).execute();
        // 处理响应结果
        String fileUrl = "";
        if (response.isSuccessful()) {
            String responseBody = response.body().string();
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode responseJson = objectMapper.readTree(responseBody);
            fileUrl = responseJson.get("data").get("fileurl").asText();
            System.out.println("fileUrl = " + fileUrl);
        } else {
            throw new IOException("Unexpected code " + response);
        }
        System.out.println("备份成功,文件名为:" + path + "\n\nminio地址为：" + fileUrl);
        return R.ok("备份成功,文件名为:" + path + "\n\nminio地址为：" + fileUrl);
    }

    @ApiOperation(value = "上传Mysql备份文件By路径", notes = "Mysql备份", produces = "application/json")
    @RequestMapping(value = "/uploadMysql200M", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiInitLog.List")
    public R<String> uploadMysql200M(String path) throws IOException {
        // 上传.zip压缩文件到minio
        R<FileInfo> fileInfoR = fileController.uploadByPath(path, "utils", "mysql", "minio");
        String fileUrl = fileInfoR.getData().getFileurl();
        System.out.println("备份成功,文件名为:" + path + "\n\nminio地址为：" + fileUrl);
        return R.ok("备份成功,文件名为:" + path + "\n\nminio地址为：" + fileUrl);
    }


}
