package inks.backup.service.impl;

import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.IdUtil;
import com.google.common.base.Strings;
import inks.backup.oss.Storage;
import inks.backup.service.FileInfoService;
import inks.common.core.domain.FileInfo;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;

/**
 * 文件信息 service impl
 *
 * <AUTHOR> Yvon
 * @version : 1.0
 * @since : 2020-11-04
 */
@Service
public class FileInfoAliyunServiceImpl implements FileInfoService {

    @Value("${oss.aliyun.bucket}")
    private String BUCKET_NAME;

    @Value("${oss.aliyun.urlprefix}")
    private String URL_PREFIX;

    @Resource
    @Qualifier("aliyunStorage") // 或者使用 @Qualifier("minioStorage")
    Storage storage;

    @Value("aliyun")
    private String storageType;

    /**
     * 构建文件信息
     *
     * @param file    文件
     * @param dirname 关联编号
     * @return 文件信息
     * <AUTHOR> Yvon / 2020-02-17
     */
    private FileInfo of(MultipartFile file,  String bucket, String dirname) {
        final String oriFileName = file.getOriginalFilename();
        String objectName = IdUtil.simpleUUID().substring(1, 21);
        String fileName = objectName;
        String fileSuffix = oriFileName.substring(oriFileName.lastIndexOf(".") + 1);
        if (!Strings.isNullOrEmpty(fileSuffix)) {
            fileName = objectName.concat(String.valueOf(CharUtil.DOT)).concat(fileSuffix);
        }
        FileInfo fileInfo = new FileInfo();
        fileInfo.setBucketname(bucket.toLowerCase());
        fileInfo.setFilename(fileName);
        fileInfo.setFileoriname(oriFileName);
        fileInfo.setFilesize(file.getSize());
        fileInfo.setContenttype(file.getContentType());
        fileInfo.setDirname(dirname);
        fileInfo.setFilesuffix(fileSuffix);
        fileInfo.setStorage(storageType);
        fileInfo.setFileurl(URL_PREFIX + dirname + "/" + fileName);
        return fileInfo;
    }

    // 通过文件绝对路径构建文件信息
    private FileInfo of(String filePath, String bucket, String dirname) {
        File inputFile = new File(filePath);
        String oriFileName = inputFile.getName();
        String objectName = IdUtil.simpleUUID().substring(1, 21);
        String fileName = objectName;
        String fileSuffix = StringUtils.getFilenameExtension(oriFileName);
        if (!StringUtils.isEmpty(fileSuffix)) {
            fileName = objectName.concat(".").concat(fileSuffix);
        }
        FileInfo fileInfo = new FileInfo();
        fileInfo.setBucketname(bucket.toLowerCase());
        fileInfo.setFilename(fileName);
        fileInfo.setFileoriname(oriFileName);
        fileInfo.setFilesize(inputFile.length());
        fileInfo.setContenttype(getContentType(fileSuffix)); // 根据文件后缀获取内容类型，调用之前提供的 getContentType 方法
        fileInfo.setDirname(dirname);
        fileInfo.setFilesuffix(fileSuffix);
        fileInfo.setStorage(storageType);
        fileInfo.setFileurl(URL_PREFIX + dirname + "/" + fileName);
        return fileInfo;
    }
    /**
     * 构建文件信息
     *
     * @param fileInfo
     * @return 文件信息
     * <AUTHOR> Yvon / 2020-02-17
     */
    private FileInfo ofByContent(FileInfo fileInfo) {
        String fileSuffix = fileInfo.getFilesuffix() == null ? "txt" : fileInfo.getFilesuffix();
        String objectName = IdUtil.simpleUUID().substring(1, 21);
        if (fileInfo.getFileoriname() == null) fileInfo.setFileoriname(objectName);
        String oriFileName = fileInfo.getFileoriname().concat(String.valueOf(CharUtil.DOT)).concat(fileSuffix);
        String fileName = objectName.concat(String.valueOf(CharUtil.DOT)).concat(fileSuffix);
        fileInfo.setBucketname(BUCKET_NAME.toLowerCase());
        if (fileInfo.getFilename() == null || fileInfo.getFilename().isEmpty())
            fileInfo.setFilename(fileName);
        fileInfo.setFileoriname(oriFileName);
        fileInfo.setFilesize(fileInfo.getContent().getBytes().length);
        fileInfo.setContenttype("text");
        fileInfo.setFilesuffix(fileSuffix);
        fileInfo.setStorage(storageType);
        fileInfo.setFileurl(URL_PREFIX + fileInfo.getDirname() + "/" + fileInfo.getFilename());
        return fileInfo;
    }

    /**
     * 构建文件信息
     *
     * @param fileInfo
     * @return 文件信息
     * <AUTHOR> Yvon / 2020-02-17
     */
    private FileInfo ofByImage(FileInfo fileInfo) {
        String[] shartimg = fileInfo.getImg().split(",");   //拆分 base64
        int index = shartimg[0].indexOf("/");
        int endindex = shartimg[0].indexOf(";");
        String fileSuffix = shartimg[0].substring(index + 1, endindex);//获取文件后缀
        String oriFileName = fileInfo.getFileoriname().concat(String.valueOf(CharUtil.DOT)).concat(fileSuffix);
        String objectName = IdUtil.simpleUUID().substring(1, 21);
        String fileName = objectName.concat(String.valueOf(CharUtil.DOT)).concat(fileSuffix);
        fileInfo.setBucketname(BUCKET_NAME.toLowerCase());
        fileInfo.setFilename(fileName);
        fileInfo.setFileoriname(oriFileName);
        fileInfo.setFilesize(fileInfo.getImg().getBytes().length);
        fileInfo.setContenttype(fileSuffix);
        fileInfo.setFilesuffix(fileSuffix);
        fileInfo.setStorage(storageType);
        fileInfo.setFileurl(URL_PREFIX + fileInfo.getDirname() + "/" + fileName);
        return fileInfo;
    }



    // aliyun返回连接没有桶名，但bucket必须已经存在才能上传
    @Override
    public FileInfo putFile(MultipartFile file, String bucket, String dirname) {
        FileInfo fileInfo = of(file, bucket, dirname);
        // 上传文件
        try {
            storage.putObject(fileInfo.getBucketname(), fileInfo.getDirname(), fileInfo.getFilename(), file.getInputStream(), fileInfo.getContenttype());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return fileInfo;
    }

    @Override
    public FileInfo putContent(FileInfo fileInfo) {
        ofByContent(fileInfo);
        // 上传文件
        try {
//            Storage storage = storageFactory.createStorage(storageType); // 使用 StorageFactory 创建对应的 Storage 实例

            storage.putObject(fileInfo.getBucketname(), fileInfo.getDirname(), fileInfo.getFilename(), new ByteArrayInputStream(fileInfo.getContent().getBytes()), fileInfo.getContenttype());

        } catch (Exception e) {
            e.printStackTrace();
        }
        return fileInfo;
    }

//    @Override
//    public FileInfo putImage(FileInfo fileInfo) {
//
//        // 上传文件
//        try {
//            String[] shartimg = fileInfo.getImg().split(",");   //拆分 base64
//            int index = shartimg[0].indexOf("/");
//            int endindex = shartimg[0].indexOf(";");
//            String fileSuffix = shartimg[0].substring(index + 1, endindex);//获取文件后缀
//            String oriFileName = "";
//            String objectName = IdUtil.simpleUUID().substring(1, 21);
//            if (fileInfo.getFileoriname() != null && fileInfo.getFileoriname().equals("")) {
//                oriFileName = fileInfo.getFileoriname().concat(String.valueOf(CharUtil.DOT)).concat(fileSuffix);
//            } else {
//                oriFileName = objectName.concat(String.valueOf(CharUtil.DOT)).concat(fileSuffix);
//            }
//
//            String fileName = objectName.concat(String.valueOf(CharUtil.DOT)).concat(fileSuffix);
//            fileInfo.setBucketname(BUCKET_NAME.toLowerCase());
//            fileInfo.setFilename(fileName);
//            fileInfo.setFileoriname(oriFileName);
//            fileInfo.setFilesize(fileInfo.getImg().getBytes().length);
//            fileInfo.setContenttype(fileSuffix);
//            fileInfo.setFilesuffix(fileSuffix);
//            fileInfo.setStorage(storageType);
//            fileInfo.setFileurl(URL_PREFIX + fileInfo.getDirname() + "/" + fileName);
//
//            byte[] bytes = new BASE64Decoder().decodeBuffer(shartimg[1]);  //将字符串转换为byte数组
//            InputStream inputStream = new ByteArrayInputStream(bytes);
////            ObjectMetadata metadata = new ObjectMetadata();
////            metadata.setContentLength(inputStream.available());
//            storage.putObject(fileInfo.getBucketname(), fileInfo.getDirname(), fileInfo.getFilename(), inputStream, fileInfo.getContenttype());
//
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return fileInfo;
//    }

    @Override
    public boolean removeFile(FileInfo fileInfo) {
        String fileName = fileInfo.getFilename();
        String bucketName = null;
        if (fileInfo.getDirname() != null && !fileInfo.getDirname().equals("")) {
            fileName = fileInfo.getDirname() + "/" + fileName;
        }
        if (fileInfo.getBucketname() != null && !fileInfo.getBucketname().trim().isEmpty()) {
            bucketName = fileInfo.getBucketname();
        }else {
            bucketName = BUCKET_NAME.toLowerCase();
        }
        try {
            storage.removeObject(bucketName.toLowerCase(), fileName);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    @Override
    public FileInfo putFile(String filePath, String bucket, String dirname) {
        FileInfo fileInfo = null;
        try {
            File file = new File(filePath);
            String fileName = file.getName();
            String contentType = getContentType(fileName); // 根据文件名获取内容类型，自行实现 getContentType() 方法
            fileInfo = of(filePath, bucket, dirname);
            FileInputStream inputStream = new FileInputStream(file);
            storage.putObject(fileInfo.getBucketname(), fileInfo.getDirname(), fileInfo.getFilename(), inputStream, contentType);
            inputStream.close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return fileInfo;
    }
    // 获取文件路径下文件的类型
    private String getContentType(String fileExtension) {
        String contentType = "";

        if (fileExtension.endsWith(".txt")) {
            contentType = "text/plain";
        } else if (fileExtension.endsWith(".jpg") || fileExtension.endsWith(".jpeg")) {
            contentType = "image/jpeg";
        } else if (fileExtension.endsWith(".png")) {
            contentType = "image/png";
        } else if (fileExtension.endsWith(".pdf")) {
            contentType = "application/pdf";
        } else if (fileExtension.endsWith(".zip")) {
            contentType = "application/zip";
        } else if (fileExtension.endsWith(".sql")) {
            contentType = "application/sql";
        }else {
            contentType = "application/octet-stream"; // 默认的内容类型
        }
        // 可else if添加其他文件类型的判断条件
        return contentType;
    }
//    @Override
//    public List<String> putFiles(List<MultipartFile> files, String relateId) {
//        List<FileInfo> fileInfos = new ArrayList<>();
//        files.forEach(file -> {
//            FileInfo fileInfo = of(file, relateId);
//            save(fileInfo);
//            // 上传文件
//            try {
//                storage.putObject(fileInfo.getBucketName(), fileInfo.getFileName(), file.getInputStream(), fileInfo.getContentType());
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            fileInfos.add(fileInfo);
//        });
//        return  fileInfos;
//    }
//
//
//
//    @Override
//    public Set<String> getUrlByRelateId(String relateId) {
//        Set<String> urls = new HashSet<>();
//        List<FileInfo> fileInfos = getByRelateId(relateId);
//        fileInfos.forEach(fileInfo -> {
//            try {
//                String objectUrl = storage.getObjectUrl(fileInfo.getBucketName(), fileInfo.getFileName());
//                urls.add(objectUrl);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        });
//        return urls;
//    }
//
//    @Override
//    public InputStream getFileStream(FileInfo fileInfo) throws Exception {
//        return storage.getObject(fileInfo.getBucketName(), fileInfo.getFileName());
//    }
//
//    @Override
//    public List<FileInfo> findFileInfoByRelateId(String relateId) {
//        return lambdaQuery()
//                .eq(FileInfo::getRelateId, relateId)
//                .list();
//    }
//
//    @Override
//    public FileInfo getFileInfoById(String id) {
//        return lambdaQuery()
//                .eq(FileInfo::getId, id)
//                .eq(FileInfo::getStorage, storageType)
//                .one();
//    }
//
//    @Override
//    public boolean removeById(Serializable id) {
//        FileInfo fileInfo = baseMapper.selectById(id);
//        baseMapper.deleteById(id);
//        try {
//            storage.removeObject(BUCKET_NAME.toLowerCase(), fileInfo.getFileName());
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return true;
//    }
//
//    @Override
//    public boolean removeByIds(Collection<? extends Serializable> idList) {
//        this.baseMapper.deleteBatchIds(idList);
//        List<FileInfo> fileInfos = baseMapper.selectBatchIds(idList);
//        try {
//            storage.removeObjects(BUCKET_NAME.toLowerCase(), fileInfos.stream().map(FileInfo::getFileName).collect(Collectors.toList()));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return true;
//    }
//
//    @Override
//    public boolean removeByRelateIds(List<String> relateId) {
//        List<FileInfo> fileInfoList = lambdaQuery().
//                in(FileInfo::getRelateId, relateId)
//                .list();
//        List<String> idList = fileInfoList.stream().map(FileInfo::getFileName).collect(Collectors.toList());
//        this.baseMapper.deleteBatchIds(idList);
//        try {
//            storage.removeObjects(BUCKET_NAME.toLowerCase(), idList);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return true;
//    }
//
//    @Override
//    public String getUrlById(String id) {
//        FileInfo fileInfo = baseMapper.selectById(id);
//        try {
//            return storage.getObjectUrl(fileInfo.getBucketName(), fileInfo.getFileName());
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return null;
//    }
//
//    /**
//     * 构建文件信息
//     *
//     * @param file     文件
//     * @param relateId 关联编号
//     * @return 文件信息
//     * <AUTHOR> Yvon / 2020-02-17
//     */
//    private FileInfo of(MultipartFile file, String relateId) {
//        final String oriFileName = file.getOriginalFilename();
//
//
//
//        String objectName = IdUtil.simpleUUID().substring(1,21);
//        String fileName = objectName;
//        String fileSuffix = oriFileName.substring(oriFileName.lastIndexOf(".") + 1);
//        if (!Strings.isNullOrEmpty(fileSuffix)) {
//            fileName = objectName.concat(String.valueOf(CharUtil.DOT)).concat(fileSuffix);
//        }
//
//        return FileInfo.builder()
//                .bucketName(BUCKET_NAME.toLowerCase())
//                .fileName(fileName)
//                .fileOriName(oriFileName)
//                .fileSize(file.getSize())
//                .contentType(file.getContentType())
//                .relateId(relateId)
//                .storage(storageType)
//                .build();
//    }
//
//
//    /**
//     * 通过关联编号查询
//     *
//     * @param relateId 关联编号
//     * @return : List<FileInfo>
//     * <AUTHOR> Yvon / 2020-11-04
//     */
//    private List<FileInfo> getByRelateId(String relateId) {
//        return lambdaQuery().eq(FileInfo::getRelateId, relateId)
//                .eq(FileInfo::getStorage, storageType)
//                .list();
//    }

}
