package inks.backup.service;

import inks.common.core.domain.FileInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件信息 service
 *
 * <AUTHOR> Yvon
 * @version : 1.0
 * @since : 2020-11-04
 */

public interface FileInfoService {

    /**
     * 上传文件
     *
     * @param file 文件
     * @param dir  目录
     * @return : FileInfo
     * <AUTHOR> Yvon / 2020-11-03
     */

    // 上传文件，且自定义桶
    FileInfo putFile(MultipartFile file, String bucket, String dir);

    // 根据文件路径上传文件
    FileInfo putFile(String filePath, String bucket, String dirname);
    /**
     * 上传文本
     *
     * @param fileInfo 文件
     * @return : FileInfo
     * <AUTHOR> Yvon / 2020-11-03
     */
    FileInfo putContent(FileInfo fileInfo);

    /**
     * 上传Base64
     *
     * @param fileInfo 文件
     * @return : FileInfo
     * <AUTHOR> Yvon / 2020-11-03
     */
//    FileInfo putImage(FileInfo fileInfo);

    /**
     * 通过关联编号删除
     *
     * @param fileInfo 文件名
     * @return : boolean
     * <AUTHOR> Yvon / 2020-11-05
     */
    boolean removeFile(FileInfo fileInfo);

//    /**
//     * 通过关联编号查询url
//     *
//     * @param relateId 关联编号
//     * @return : Set<String>
//     * <AUTHOR> Yvon / 2020-11-04
//     */
//    Set<String> getUrlByRelateId(String relateId);
//
//    /**
//     * 查询文件流
//     *
//     * @param fileInfo 文件信息
//     * @return : InputStream
//     * <AUTHOR> Yvon / 2020-11-04
//     */
//    InputStream getFileStream(FileInfo fileInfo) throws Exception;
//
//    /**
//     * 通过关联编号查询文件信息
//     *
//     * @param relateId 关联编号
//     * @return : List<FileInfo>
//     * <AUTHOR> Yvon / 2020-11-05
//     */
//    List<FileInfo> findFileInfoByRelateId(String relateId);
//
//    /**
//     * 通过编号查询文件信息(下载使用)
//     *
//     * @param id 编号
//     * @return : FileInfo
//     * <AUTHOR> Yvon / 2020-11-04
//     */
//    FileInfo getFileInfoById(String id);
//
//    /**
//     * 批量上传文件
//     *
//     * @param files    文件
//     * @param relateId 关联编号
//     * @return : List<FileInfo>
//     * <AUTHOR> Yvon / 2020-11-05
//     */
//    List<FileInfo> putFiles(List<MultipartFile> files, String relateId);
//

//
//    /**
//     * 通过编号查询url
//     *
//     * @param id 编号
//     * @return : String
//     * <AUTHOR> Yvon / 2020-11-17
//     */
//    String getUrlById(String id);
}
