package inks.backup.service;

import inks.common.core.domain.FileInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

public interface OssService {

    FileInfo upload(MultipartFile multipartFile, String dirname);
    FileInfo uploadPic(MultipartFile multipartFile, String dirname);

    InputStream download(String bucketName, String objectName);

    FileInfo of(MultipartFile file, String dirname);
}
