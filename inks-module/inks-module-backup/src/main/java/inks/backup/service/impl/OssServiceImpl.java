package inks.backup.service.impl;

import inks.backup.oss.Storage;
import inks.backup.service.OssService;
import inks.common.core.domain.FileInfo;
import inks.common.core.exception.BaseBusinessException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;

@Service
public class OssServiceImpl implements OssService {
    @Value("${oss.aliyun.bucket}")
    private String BUCKET_NAME;

    @Value("${oss.aliyun.bucket}")
    private String PIC_BUCKET;

    @Value("${oss.aliyun.endpoint}")
    private String END_POINT;

    @Resource
    @Qualifier("aliyunStorage") // 或者使用 @Qualifier("aliyunStorage")
    private Storage storage;


    /**
     * 构建文件信息
     *
     * @param file    文件
     * @param dirname 关联编号
     * @return 文件信息
     * <AUTHOR> Yvon / 2020-02-17
     */
    public FileInfo of(MultipartFile file, String dirname) {
        final String oriFileName = file.getOriginalFilename();
//        String objectName = IdUtil.simpleUUID().substring(1, 21);
        String fileName = file.getOriginalFilename();
        String fileSuffix = oriFileName.substring(oriFileName.lastIndexOf(".") + 1);
//        if (!Strings.isNullOrEmpty(fileSuffix)) {
//            fileName = fileName.concat(String.valueOf(CharUtil.DOT)).concat(fileSuffix);
//        }
        FileInfo fileInfo = new FileInfo();
        fileInfo.setBucketname(BUCKET_NAME.toLowerCase());
        fileInfo.setFilename(fileName);
        fileInfo.setFileoriname(oriFileName);
        fileInfo.setFilesize(file.getSize());
        fileInfo.setContenttype(file.getContentType());
        fileInfo.setDirname(dirname);
        fileInfo.setFilesuffix(fileSuffix);
        fileInfo.setStorage("minio");
        fileInfo.setFileurl(END_POINT+"/"+BUCKET_NAME+"/"+dirname + "/" + fileName);
        return fileInfo;
    }

    private FileInfo of(MultipartFile file, String dirname, String bucketName) {
        final String oriFileName = file.getOriginalFilename();
//        String objectName = IdUtil.simpleUUID().substring(1, 21);
        String fileName = file.getOriginalFilename();
        String fileSuffix = oriFileName.substring(oriFileName.lastIndexOf(".") + 1);
//        if (!Strings.isNullOrEmpty(fileSuffix)) {
//            fileName = fileName.concat(String.valueOf(CharUtil.DOT)).concat(fileSuffix);
//        }
        FileInfo fileInfo = new FileInfo();
        fileInfo.setBucketname(bucketName.toLowerCase());
        fileInfo.setFilename(fileName);
        fileInfo.setFileoriname(oriFileName);
        fileInfo.setFilesize(file.getSize());
        fileInfo.setContenttype(file.getContentType());
        fileInfo.setDirname(dirname);
        fileInfo.setFilesuffix(fileSuffix);
        fileInfo.setStorage("minio");
        fileInfo.setFileurl(END_POINT+"/"+dirname + "/" + fileName);
        return fileInfo;
    }


    @Override
    public FileInfo upload(MultipartFile file, String dirname) {
        FileInfo fileInfo = of(file, dirname);
        // 上传文件
        try {
            storage.putObject(
                    fileInfo.getBucketname(),
                    fileInfo.getDirname(),
                    fileInfo.getFilename(),
                    file.getInputStream(),
                    fileInfo.getContenttype());
        } catch (Exception e) {
            e.printStackTrace();
            throw new BaseBusinessException("文件上传失败");
        }
        return fileInfo;
    }

    @Override
    public FileInfo uploadPic(MultipartFile multipartFile, String dirname) {
        FileInfo fileInfo = of(multipartFile, dirname, PIC_BUCKET);
        try{
            storage.putObject(fileInfo.getBucketname(), dirname, fileInfo.getFilename(), multipartFile.getInputStream(), fileInfo.getContenttype());
            return fileInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public InputStream download(String bucketName, String objectName) {
        try {
            InputStream inputStream = storage.getObject(bucketName, objectName);
            return inputStream;
        } catch (Exception e) {
            throw new BaseBusinessException("下载失败");
        }
    }


}
