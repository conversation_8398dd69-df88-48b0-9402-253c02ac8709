package inks.backup.oss;

import cn.hutool.core.util.CharUtil;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.ObjectMetadata;
import io.minio.ComposeSource;
import io.minio.ObjectStat;

import javax.crypto.SecretKey;
import java.io.InputStream;
import java.util.List;

/**
 * aliyun 存储
 *
 * <AUTHOR> Yvon
 * @version : 1.0
 * @since : 2020-11-03
 */
public class AliyunStorage implements Storage {
    private final OSSClient ossClient;

    public AliyunStorage(OSSClient ossClient) {
        this.ossClient = ossClient;
    }

    @Override
    public void putObject(String bucketName, String dirName, String objectName, InputStream inputStream, String contentType) {
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType(contentType);
        if (dirName != null && !dirName.equals("")) {
            objectName = dirName + "/" + objectName;
        }
        this.ossClient.putObject(bucketName, objectName, inputStream, objectMetadata);
    }

    @Override
    public void putObject(String bucketName, String dirName, String objectName, InputStream inputStream, String contentType, SecretKey secretKey) throws Exception {

    }


    @Override
    public InputStream getObject(String bucketName, String objectName) {
        return this.ossClient.getObject(bucketName, objectName).getObjectContent();
    }

    @Override
    public InputStream getObject(String bucketName, String objectName, Long offset, Long length) throws Exception {
        return null;
    }

    @Override
    public String getObjectUrl(String bucketName, String objectName) {
        return "https://".concat(bucketName).concat(String.valueOf(CharUtil.DOT)).concat(this.ossClient.getEndpoint().getHost()).concat(String.valueOf(CharUtil.SLASH)).concat(objectName);
    }

    @Override
    public void removeObject(String bucketName, String objectName) {
        this.ossClient.deleteObject(bucketName, objectName);
    }

    @Override
    public void removeObjects(String bucketName, List<String> objectNames) throws Exception {

    }

    @Override
    public ObjectStat statObject(String bucketName, String objectName) throws Exception {
        return null;
    }

    @Override
    public List<Object> listObjects(String bucketName, String objectName) throws Exception {
        return null;
    }

    @Override
    public void composeObject(String bucketName, String objectName, List<ComposeSource> sourceList) throws Exception {

    }


}