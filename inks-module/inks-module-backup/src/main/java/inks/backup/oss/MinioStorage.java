package inks.backup.oss;

import com.alibaba.fastjson.JSON;
import io.minio.*;
import io.minio.messages.DeleteObject;
import io.minio.messages.Item;

import javax.crypto.SecretKey;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

/**
 * minio 存储
 *
 * <AUTHOR> lhm
 * @version : 1.0
 * @since : 2022-10-10
 */
public class MinioStorage implements Storage {
    private final MinioClient minioClient;

    public MinioStorage(MinioClient minioClient) {
        this.minioClient = minioClient;
    }

    @Override
    public void putObject(String bucketName, String dirName, String objectName, InputStream inputStream, String contentType) throws Exception {
        boolean isExist = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
        if (!isExist) {
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
        }
        if (dirName != null && !dirName.equals("")) {
            objectName = dirName + "/" + objectName;
        }
        minioClient.putObject(
                PutObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .stream(inputStream, inputStream.available(), -1)
                        .contentType(contentType)
                        .build());
    }

    @Override
    public void putObject(String bucketName, String dirName, String objectName, InputStream inputStream, String contentType, SecretKey secretKey) throws Exception {
        boolean isExist = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
        if (!isExist) {
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
        }
        if (dirName != null && !dirName.equals("")) {
            objectName = dirName + "/" + objectName;
        }
        minioClient.putObject(
                PutObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .stream(inputStream, inputStream.available(), -1)
                        .contentType("application/octet-stream")
                        .sse(new ServerSideEncryptionCustomerKey(secretKey))
                        .build());
    }

    @Override
    public InputStream getObject(String bucketName, String objectName) throws Exception {
        return minioClient.getObject(
                GetObjectArgs.builder().bucket(bucketName).object(objectName).build());
    }

    @Override
    public InputStream getObject(String bucketName, String objectName, Long offset, Long length) throws Exception {
        return minioClient.getObject(GetObjectArgs.builder().bucket(bucketName).object(objectName).offset(offset).length(length).build());
    }

    @Override
    public String getObjectUrl(String bucketName, String objectName) throws Exception {
        return this.minioClient.getObjectUrl(bucketName, objectName);
    }

    @Override
    public void removeObject(String bucketName, String objectName) throws Exception {
        minioClient.removeObject(
                RemoveObjectArgs.builder().bucket(bucketName).object(objectName).build());
    }

    @Override
    public void removeObjects(String bucketName, List<String> objectNames) throws Exception {
        List<DeleteObject> objects = new LinkedList<>();
        objectNames.forEach(objectName -> objects.add(new DeleteObject(objectName)));
        minioClient.removeObjects(
                RemoveObjectsArgs.builder()
                        .bucket(bucketName)
                        .objects(objects)
                        .build());
    }

    @Override
    public ObjectStat statObject(String bucketName, String objectName) throws Exception {
        return minioClient.statObject(StatObjectArgs.builder().bucket(bucketName).object(objectName).build());
    }

    @Override
    public List<Object> listObjects(String bucketName, String objectName) throws Exception {
        Iterable<Result<Item>> iterable = minioClient.listObjects(ListObjectsArgs.builder().bucket(bucketName).prefix(objectName).recursive(true).build());
        Iterator<Result<Item>> iterator = iterable.iterator();
        List<Object> items = new ArrayList<>();
        String format = "{'fileName':'%s','fileSize':'%s'}";
        while (iterator.hasNext()) {
            Item item = iterator.next().get();
            items.add(JSON.parse((String.format(format, item.objectName(), item.size()))));
        }
        return items;
    }

    @Override
    public void composeObject(String bucketName, String objectName, List<ComposeSource> sourceList) throws Exception {
        minioClient.composeObject(
                ComposeObjectArgs
                        .builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .sources(sourceList)
                        .build());
    }
}