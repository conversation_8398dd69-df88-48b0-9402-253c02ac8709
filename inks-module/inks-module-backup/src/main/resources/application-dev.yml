spring:
  datasource:
    hikari:
      sqlserver:
        jdbc-url: ****************************************************************************************
        username: sa
        password: Aa@123456!
        connection-test-query: SELECT 1
      backup:
        url: **************************************************************************************************************************************************
#        url: **************************************************************************************************************************************************
        username: root
        password: asd@123456
        driver-class-name: com.mysql.cj.jdbc.Driver
  #redis配置
  redis:
    database: 0
    # Redis服务器地址 写你的ip
    host: **************
    # Redis服务器连接端口
    port: 56379
    # Redis服务器连接密码（默认为空）
    password: asd@123456
    # 连接池最大连接数（使用负值表示没有限制  类似于mysql的连接池
    jedis:
      pool:
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 表示连接池的链接拿完了 现在去申请需要等待的时间
        max-wait: -1
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
    # 连接超时时间（毫秒） 去链接redis服务端
    timeout: 6000
  mail:
    username: <EMAIL>
    password: ASDqwe@!@#
    host: smtp.qiye.aliyun.com
    default-encoding: UTF-8
    properties:
      mail.smtp.socketFactory.fallback: true
      mail.smtp.starttls.enable: true
#    toEmail: <EMAIL>  #收件人
    toEmail: <EMAIL>  #登录日志的收件人
    ipAddress: 96  #ip地址
tenant:
  name: inks

backup:
  sqlserver:
    dbname: OemBrPlc
    scheduling:
      cron: 0 0 1 * * *   # 每天凌晨1点执行备份
    password: 123456      # 下载后的备份文件解压密码
    directory: D:\backup\sqlserver  # 临时备份文件存放目录()
#      cron: 0 1 * * * *  # 在线Cron表达式生成器:https://cron.qqe2.com/





oss:
  type: minio
  minio:
    bucket: utils
    dirsuffix: ymlsuffix
    access-key: minioadmin
    secret-key: minioadmin
    endpoint: http://*************:9080
    #    urlprefix: http://inks.tpddns.net:9080/${oss.minio.bucket}/
    urlprefix: http://dev.inksyun.com:9080/
  aliyun:
    bucket: inksoms
    access-key-id: LTAI5tL76QGhNx5eSkzkLnbv
    access-key-secret: ******************************
    endpoint: http://oss.oms.inksyun.com
    urlprefix: http://oss.oms.inksyun.com/