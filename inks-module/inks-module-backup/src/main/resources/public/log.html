<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Hello World</title>
    <script src="https://cdn.bootcss.com/jquery/2.1.4/jquery.js"></script>
    <script src="https://cdn.bootcss.com/sockjs-client/1.1.4/sockjs.min.js"></script>
    <script src="https://cdn.bootcss.com/stomp.js/2.3.3/stomp.min.js"></script>
    <style type="text/css">
        * {
            padding: 0;
            margin: 0;
        }
        body {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: #333;
            background-color: #f5f5f5;
        }

        input[type="text"], input[type="password"], textarea {
            display: block;
            width: 100%;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ccc;
            box-sizing: border-box;
        }

        button {
            display: inline-block;
            padding: 10px 20px;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            text-decoration: none;
            color: #fff;
            background-color: rgba(0, 123, 255, 0.55);
            border-radius: 4px;
            border: none;
        }

        button:hover {
            background-color: rgba(26, 61, 28, 0.78);
        }

        h1 {
            font-size: 24px;
        }

        label {
            display: block;
        }
        #dbUrl {
            width: 1200px;
        }
        #dbUsername, #dbPassword {
            width: 150px;
        }
    </style>
</head>
<body>
<h1>数据库备份</h1>
<h1 id="clock">1970-01-01 00:00:00</h1>

<button onclick="backup()">Sql server备份</button>
<button onclick="mysqlBackup()">Mysql备份</button>
<h1>数据库备份2</h1>
<label for="dbUrl">数据库URL：</label>
<input type="text" id="dbUrl" name="dbUrl">

<label for="dbUsername">用户名：</label>
<input type="text" id="dbUsername" name="dbUsername">

<label for="dbPassword">密码：</label>
<input type="password" id="dbPassword" name="dbPassword">


<button onclick="mysqlBackupByUrl()">Mysql备份</button>

<div id="log-container2">
    <div id="log-content2"></div>
</div>
<div id="log-container" style="height: 450px; overflow-y: scroll; background: #333; color: #aaa; padding: 10px;">
    <div id="log-content"></div>
</div>

<div>version:V20221018</div>
<div style="display: flex; justify-content: center;">Copyright @ 应凯软件 2022</div>
</body>
<script>
    var stompClient = null;
    var clock = null;
    var port = 0;

    $(document).ready(function () {
        new Promise(function (resolve, reject) {
            $.get("getPort", function (data) {
                if (data.code == 200) {
                    port = data.data
                    resolve()
                }else{
                    alert("连接至服务器失败")
                    reject()
                }
            })
        }).then(() => {
            openSocket();
            var socket = new SockJS('http://localhost:' + port + '/websocket');
            clock = Stomp.over(socket);
            clock.connect({}, function (frame) {
                clock.subscribe('/topic/clock', function (event) {
                    $("#clock").html(event.body)
                }, {});
            });
        })


    });

    function backup() {
        $.get("http://localhost:9990/backupTest?backupName=OemBrPlc7&dbname=OemBrPlc", function (data) {
        // $.get("/backup", function (data) {
            console.log(data)
            if (data.code == 200) {
                // download(data.data)
                var logContent = document.getElementById("log-content");
                logContent.innerHTML = data.data;
            }

        })
    }


    function mysqlBackup() {
        $.get("http://localhost:9990/mysqlBackup", function (data) {
            // $.get("/backup", function (data) {
            console.log(data)
            if (data.code == 200) {
                var logContent = document.getElementById("log-content");
                logContent.innerHTML = data.data;
            }
        })
    }

    function mysqlBackupByUrl() {
        var dbUrl = document.getElementById("dbUrl").value;
        var dbUsername = document.getElementById("dbUsername").value;
        var dbPassword = document.getElementById("dbPassword").value;

        $.get("http://localhost:9990/mysqlBackupByUrl", {
            dbUrl: dbUrl,
            dbUsername: dbUsername,
            dbPassword: dbPassword
        }, function (data) {
            console.log(data);
            if (data.code == 200) {
                var logContent = document.getElementById("log-content");
                logContent.innerHTML = data.data;
            }
        });
    }

    function download(name) {
        let xhr = new XMLHttpRequest();
        xhr.open("get", "download?name=" + name, true);
        xhr.setRequestHeader('Content-Type', 'application/json;charset=UTF-8');
        xhr.withCredentials = true;
        xhr.responseType = "blob";
        xhr.onload = function () {
            var filename;
            let contentDisposition = xhr.getResponseHeader("Content-Disposition");
            if (contentDisposition) {
                filename = window.decodeURI(xhr.getResponseHeader("Content-Disposition").split('=')[1], "UTF-8");
                // filename = filename.substring(0, filename.indexOf(";"));
            }
            if (!contentDisposition) {
                alert("备份失败")
                return
            }
            var blob = new Blob([xhr.response], {type: "zip"});
            var csvUrl = URL.createObjectURL(blob);
            console.log(csvUrl)
            console.log(filename)
            var link = document.createElement('a');
            link.href = csvUrl;
            link.download = filename;
            link.click();
        };
        // 发送ajax请求
        xhr.send(JSON.stringify());
    }

    function clearLog() {
        $("#log-container div").empty();
    }

    function openSocket() {
        if (stompClient == null) {
            var socket = new SockJS('http://localhost:' + port + '/websocket');
            stompClient = Stomp.over(socket);
            stompClient.connect({}, function (frame) {
                stompClient.subscribe('/topic/pullLogger', function (event) {
                    var content = JSON.parse(event.body);
                    if (content.level == 'info') {
                        $("#log-container div").append(content.timestamp + "&nbsp&nbsp&nbsp" + "<span style=\"color:green;\">" + content.level + "</span>" + " --- [" + content.threadName + "] " + "<span style=\"color:#128fa2;\">" + content.className + "</span>" + "   :" + content.message).append("<br/>");
                    } else {
                        $("#log-container div").append(content.timestamp + "&nbsp&nbsp&nbsp" + "<span style=\"color:red;\">" + content.level + "</span>" + " --- [" + content.threadName + "] " + "<span style=\"color:#128fa2;\">" + content.className + "</span>" + "   :" + content.message).append("<br/>");
                    }
                    $("#log-container").scrollTop($("#log-container div").height() - $("#log-container").height());
                }, {});
            });
        }
    }

    function closeSocket() {
        if (stompClient != null) {
            stompClient.disconnect();
            stompClient = null;
        }
    }
</script>
</html>