server:
  port: 9990
spring:
  application:
    name: backup
  datasource:
    hikari:
      db:
        jdbc-url: *************************************************************************************************************
        username: sa
        password: Aa@123456!
        connection-test-query: SELECT 1

backup:
  path: c:\\backup   #临时存储位置
  name: inks         #租户名
  type: sqlserver    #数据库类型
  dbname: bz_data    #数据库名
  dir: sqlserverbackup    #oss存储目录
  scheduling:
    cron: 0/10 * * * * *   #自动备份周期


mysql:
  dbname: test
  backup:
    dir: mySqlBackup
    scheduling:
      cron: 0 0 0/1 * * ?

logging:
  level:
    org:
      springframework:
        security: info

feign:
  sentinel:
    enabled: true
oss:
  bucket: inksdemo
  minio:
    access-key: minioadmin
    secret-key: minioadmin
    endpoint: http://192.168.99.95:9080