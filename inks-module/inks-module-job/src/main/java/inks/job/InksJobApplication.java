package inks.job;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@SpringBootApplication(scanBasePackages={"inks.*","inks.common.core.*",
        "inks.common.redis.*","inks.common.security.*"})
@EnableSwagger2
@EnableDiscoveryClient
@EnableFeignClients
public class InksJobApplication {

    public static void main(String[] args) {
        SpringApplication.run(InksJobApplication.class, args);
    }

}
