package inks.job.mapper;

import inks.common.core.domain.QueryParam;
import inks.job.domain.CiconfigEntity;
import inks.job.domain.pojo.CiconfigPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CiconfigMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiconfigPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CiconfigPojo> getPageList(QueryParam queryParam);



    /**
     * 新增数据
     *
     * @param ciconfigEntity 实例对象
     * @return 影响行数
     */
    int insert(CiconfigEntity ciconfigEntity);


    /**
     * 修改数据
     *
     * @param ciconfigEntity 实例对象
     * @return 影响行数
     */
    int update(CiconfigEntity ciconfigEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}
