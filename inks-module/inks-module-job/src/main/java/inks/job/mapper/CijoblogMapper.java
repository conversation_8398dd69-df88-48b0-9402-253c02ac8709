package inks.job.mapper;

import inks.common.core.domain.QueryParam;
import inks.job.domain.pojo.CijoblogPojo;
import inks.job.domain.CijoblogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 任务日志(Cijoblog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-01-08 15:42:07
 */
@Mapper
public interface CijoblogMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CijoblogPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CijoblogPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param cijoblogEntity 实例对象
     * @return 影响行数
     */
    int insert(CijoblogEntity cijoblogEntity);

    
    /**
     * 修改数据
     *
     * @param cijoblogEntity 实例对象
     * @return 影响行数
     */
    int update(CijoblogEntity cijoblogEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
                                                                                                         }

