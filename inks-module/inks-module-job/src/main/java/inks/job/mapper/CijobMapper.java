package inks.job.mapper;

import inks.common.core.domain.QueryParam;
import inks.job.domain.pojo.CijobPojo;
import inks.job.domain.CijobEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 定时任务(Cijob)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-01-08 15:38:33
 */
@Mapper
public interface CijobMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CijobPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CijobPojo> getPageList(QueryParam queryParam);



    /**
     * 新增数据
     *
     * @param cijobEntity 实例对象
     * @return 影响行数
     */
    int insert(CijobEntity cijobEntity);


    /**
     * 修改数据
     *
     * @param cijobEntity 实例对象
     * @return 影响行数
     */
    int update(CijobEntity cijobEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
     /*
      *
      * <AUTHOR>
      * @description 初始化绑定所有任务
      * @date 2022/1/8
      * @param * @param null
      * @return
      */
    List<CijobEntity> getList();

                                                                                                                                  }

