package inks.job.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 任务日志(Cijoblog)实体类
 *
 * <AUTHOR>
 * @since 2022-01-08 15:42:07
 */
public class CijoblogEntity implements Serializable {
    private static final long serialVersionUID = 164008794090313399L;
         // 任务日志ID
         private String id;
         // 任务名称
         private String jobname;
         // 任务组名
         private String jobgroup;
         // 任务方法
         private String methodname;
         // 方法参数
         private String methodparams;
         // 日志信息
         private String jobmessage;
         // 1为异常
         private Integer exceptionmark;
         // 异常信息
         private String exceptioninfo;
         // 创建者
         private String createby;
         // 创建者id
         private String createbyid;
         // 新建日期
         private Date createdate;
         // 制表
         private String lister;
         // 制表id
         private String listerid;
         // 修改日期
         private Date modifydate;
         // 自定义1
         private String custom1;
         // 自定义2
         private String custom2;
         // 自定义3
         private String custom3;
         // 自定义4
         private String custom4;
         // 自定义5
         private String custom5;
         // 租户id
         private String tenantid;
         // 乐观锁
         private Integer revision;

// 任务日志ID
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 任务名称
    public String getJobname() {
        return jobname;
    }
    
    public void setJobname(String jobname) {
        this.jobname = jobname;
    }
        
// 任务组名
    public String getJobgroup() {
        return jobgroup;
    }
    
    public void setJobgroup(String jobgroup) {
        this.jobgroup = jobgroup;
    }
        
// 任务方法
    public String getMethodname() {
        return methodname;
    }
    
    public void setMethodname(String methodname) {
        this.methodname = methodname;
    }
        
// 方法参数
    public String getMethodparams() {
        return methodparams;
    }
    
    public void setMethodparams(String methodparams) {
        this.methodparams = methodparams;
    }
        
// 日志信息
    public String getJobmessage() {
        return jobmessage;
    }
    
    public void setJobmessage(String jobmessage) {
        this.jobmessage = jobmessage;
    }
        
// 1为异常
    public Integer getExceptionmark() {
        return exceptionmark;
    }
    
    public void setExceptionmark(Integer exceptionmark) {
        this.exceptionmark = exceptionmark;
    }
        
// 异常信息
    public String getExceptioninfo() {
        return exceptioninfo;
    }
    
    public void setExceptioninfo(String exceptioninfo) {
        this.exceptioninfo = exceptioninfo;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
// 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
// 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
// 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
// 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

