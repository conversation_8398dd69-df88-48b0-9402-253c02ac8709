package inks.job.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.job.domain.CiconfigEntity;
import inks.job.domain.pojo.CiconfigPojo;
import inks.job.mapper.CiconfigMapper;
import inks.job.service.CiconfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 系统参数(Ciconfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-27 18:30:51
 */
@Service("ciconfigService")
public class CiconfigServiceImpl implements CiconfigService {
    @Resource
    private CiconfigMapper ciconfigMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CiconfigPojo getEntity(String key, String tid) {
        return this.ciconfigMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CiconfigPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiconfigPojo> lst = ciconfigMapper.getPageList(queryParam);
            PageInfo<CiconfigPojo> pageInfo = new PageInfo<CiconfigPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param ciconfigPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiconfigPojo insert(CiconfigPojo ciconfigPojo) {
    //初始化NULL字段
     if(ciconfigPojo.getParentid()==null) ciconfigPojo.setParentid("");
     if(ciconfigPojo.getCfgname()==null) ciconfigPojo.setCfgname("");
     if(ciconfigPojo.getCfgkey()==null) ciconfigPojo.setCfgkey("");
     if(ciconfigPojo.getCfgvalue()==null) ciconfigPojo.setCfgvalue("");
     if(ciconfigPojo.getCfgtype()==null) ciconfigPojo.setCfgtype(0);
     if(ciconfigPojo.getCfglevel()==null) ciconfigPojo.setCfglevel(0);
     if(ciconfigPojo.getCfgoption()==null) ciconfigPojo.setCfgoption("");
     if(ciconfigPojo.getRownum()==null) ciconfigPojo.setRownum(0);
     if(ciconfigPojo.getEnabledmark()==null) ciconfigPojo.setEnabledmark(0);
     if(ciconfigPojo.getAllowdelete()==null) ciconfigPojo.setAllowdelete(0);
     if(ciconfigPojo.getRemark()==null) ciconfigPojo.setRemark("");
     if(ciconfigPojo.getCreateby()==null) ciconfigPojo.setCreateby("");
     if(ciconfigPojo.getCreatebyid()==null) ciconfigPojo.setCreatebyid("");
     if(ciconfigPojo.getCreatedate()==null) ciconfigPojo.setCreatedate(new Date());
     if(ciconfigPojo.getLister()==null) ciconfigPojo.setLister("");
     if(ciconfigPojo.getListerid()==null) ciconfigPojo.setListerid("");
     if(ciconfigPojo.getModifydate()==null) ciconfigPojo.setModifydate(new Date());
     if(ciconfigPojo.getCustom1()==null) ciconfigPojo.setCustom1("");
     if(ciconfigPojo.getCustom2()==null) ciconfigPojo.setCustom2("");
     if(ciconfigPojo.getCustom3()==null) ciconfigPojo.setCustom3("");
     if(ciconfigPojo.getCustom4()==null) ciconfigPojo.setCustom4("");
     if(ciconfigPojo.getCustom5()==null) ciconfigPojo.setCustom5("");
     if(ciconfigPojo.getUserid()==null) ciconfigPojo.setUserid("");
     if(ciconfigPojo.getTenantid()==null) ciconfigPojo.setTenantid("");
     if(ciconfigPojo.getRevision()==null) ciconfigPojo.setRevision(0);
        CiconfigEntity ciconfigEntity = new CiconfigEntity();
        BeanUtils.copyProperties(ciconfigPojo,ciconfigEntity);
        
          ciconfigEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          ciconfigEntity.setRevision(1);  //乐观锁
          this.ciconfigMapper.insert(ciconfigEntity);
        return this.getEntity(ciconfigEntity.getId(),ciconfigEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param ciconfigPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiconfigPojo update(CiconfigPojo ciconfigPojo) {
        CiconfigEntity ciconfigEntity = new CiconfigEntity(); 
        BeanUtils.copyProperties(ciconfigPojo,ciconfigEntity);
        this.ciconfigMapper.update(ciconfigEntity);
        return this.getEntity(ciconfigEntity.getId(),ciconfigEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.ciconfigMapper.delete(key,tid) ;
    }
}
