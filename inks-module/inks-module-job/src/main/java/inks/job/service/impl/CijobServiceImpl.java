package inks.job.service.impl;

import inks.common.core.constant.ScheduleConstants;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.exception.job.TaskException;
import inks.common.core.text.inksSnowflake;
import inks.job.domain.CijobEntity;
import inks.job.domain.SysJob;
import inks.job.domain.pojo.CijobPojo;
import inks.job.mapper.CijobMapper;
import inks.job.service.CijobService;
import inks.job.util.CronUtils;
import inks.job.util.ScheduleUtils;
import org.quartz.JobDataMap;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 定时任务(Cijob)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-08 15:38:36
 */
@Service("cijobService")
public class CijobServiceImpl implements CijobService {
    @Resource
    private CijobMapper cijobMapper;
    @Autowired
    private Scheduler scheduler;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CijobPojo getEntity(String key, String tid) {
        return this.cijobMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CijobPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CijobPojo> lst = cijobMapper.getPageList(queryParam);
            PageInfo<CijobPojo> pageInfo = new PageInfo<CijobPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param cijobPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CijobPojo insert(CijobPojo cijobPojo) throws SchedulerException, TaskException {
    //初始化NULL字段
     if(cijobPojo.getJobname()==null) cijobPojo.setJobname("");
     if(cijobPojo.getJobgroup()==null) cijobPojo.setJobgroup("");
     if(cijobPojo.getMethodname()==null) cijobPojo.setMethodname("");
     if(cijobPojo.getMethodparams()==null) cijobPojo.setMethodparams("");
     if(cijobPojo.getCronexpression()==null) cijobPojo.setCronexpression("");
     if(cijobPojo.getMisfirepolicy()==null) cijobPojo.setMisfirepolicy("");
     if(cijobPojo.getStatus()==null) cijobPojo.setStatus(0);
     if(cijobPojo.getUserid()==null) cijobPojo.setUserid("");
     if(cijobPojo.getUsername()==null) cijobPojo.setUsername("");
     if(cijobPojo.getRealname()==null) cijobPojo.setRealname("");
     if(cijobPojo.getRemark()==null) cijobPojo.setRemark("");
     if(cijobPojo.getRownum()==null) cijobPojo.setRownum(0);
     if(cijobPojo.getCreateby()==null) cijobPojo.setCreateby("");
     if(cijobPojo.getCreatebyid()==null) cijobPojo.setCreatebyid("");
     if(cijobPojo.getCreatedate()==null) cijobPojo.setCreatedate(new Date());
     if(cijobPojo.getLister()==null) cijobPojo.setLister("");
     if(cijobPojo.getListerid()==null) cijobPojo.setListerid("");
     if(cijobPojo.getModifydate()==null) cijobPojo.setModifydate(new Date());
     if(cijobPojo.getCustom1()==null) cijobPojo.setCustom1("");
     if(cijobPojo.getCustom2()==null) cijobPojo.setCustom2("");
     if(cijobPojo.getCustom3()==null) cijobPojo.setCustom3("");
     if(cijobPojo.getCustom4()==null) cijobPojo.setCustom4("");
     if(cijobPojo.getCustom5()==null) cijobPojo.setCustom5("");
     if(cijobPojo.getTenantid()==null) cijobPojo.setTenantid("");
     if(cijobPojo.getRevision()==null) cijobPojo.setRevision(0);
        CijobEntity cijobEntity = new CijobEntity();
        BeanUtils.copyProperties(cijobPojo,cijobEntity);

          cijobEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          cijobEntity.setRevision(1);  //乐观锁
          this.cijobMapper.insert(cijobEntity);
        ScheduleUtils.createScheduleJob(scheduler, cijobEntity);
        return this.getEntity(cijobEntity.getId(),cijobEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param cijobPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CijobPojo update(CijobPojo cijobPojo) throws SchedulerException, TaskException {
        CijobEntity cijobEntity = new CijobEntity();
        BeanUtils.copyProperties(cijobPojo,cijobEntity);
        this.cijobMapper.update(cijobEntity);
        String jobId = cijobEntity.getId();
        JobKey jobKey = ScheduleUtils.getJobKey(jobId, cijobEntity.getJobgroup());
        if (scheduler.checkExists(jobKey))
        {
            // 防止创建时存在数据问题 先移除，然后在执行创建操作
            scheduler.deleteJob(jobKey);
        }
        ScheduleUtils.createScheduleJob(scheduler, cijobEntity);
        return this.getEntity(cijobEntity.getId(),cijobEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) throws SchedulerException {
        CijobPojo cijobPojo = cijobMapper.getEntity(key,tid);
        String id = cijobPojo.getId();
        String jobGroup = cijobPojo.getJobgroup();
        scheduler.deleteJob(ScheduleUtils.getJobKey(id, jobGroup));
        return this.cijobMapper.delete(key,tid) ;
    }
     /*
      *
      * <AUTHOR>
      * @description 启动任务
      * @date 2022/1/8
      * @param * @param null
      * @return
      */
    @Override
    public void run(String key,String tid) throws SchedulerException {
        CijobPojo cijobPojo = cijobMapper.getEntity(key,tid);
        String id = cijobPojo.getId();
        String jobGroup = cijobPojo.getJobgroup();
        // 参数
        JobDataMap dataMap = new JobDataMap();
        dataMap.put(ScheduleConstants.TASK_PROPERTIES, cijobPojo);
        scheduler.triggerJob(ScheduleUtils.getJobKey(id, jobGroup), dataMap);
    }

     /*
      *
      * <AUTHOR>
      * @description 初始化任务
      * @date 2022/1/8
      * @param * @param null
      * @return
      */
    @PostConstruct
    public void init() throws SchedulerException, TaskException
    {
        scheduler.clear();
        List<CijobEntity> jobList = cijobMapper.getList();
        for (CijobEntity job : jobList)
        {
            ScheduleUtils.createScheduleJob(scheduler, job);
        }
    }
    /**
     * 校验cron表达式是否有效
     *
     * @param cronExpression 表达式
     * @return 结果
     */
    public boolean checkCronExpressionIsValid(String cronExpression)
    {
        return CronUtils.isValid(cronExpression);
    }

}
