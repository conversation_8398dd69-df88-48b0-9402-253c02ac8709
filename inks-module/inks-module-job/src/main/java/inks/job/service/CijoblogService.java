package inks.job.service;

import inks.common.core.domain.QueryParam;
import inks.job.domain.pojo.CijoblogPojo;
import inks.job.domain.CijoblogEntity;

import com.github.pagehelper.PageInfo;

/**
 * 任务日志(Cijoblog)表服务接口
 *
 * <AUTHOR>
 * @since 2022-01-08 15:42:07
 */
public interface CijoblogService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CijoblogPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CijoblogPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param cijoblogPojo 实例对象
     * @return 实例对象
     */
    CijoblogPojo insert(CijoblogPojo cijoblogPojo);

    /**
     * 修改数据
     *
     * @param cijoblogpojo 实例对象
     * @return 实例对象
     */
    CijoblogPojo update(CijoblogPojo cijoblogpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);
                                                                                                         }
