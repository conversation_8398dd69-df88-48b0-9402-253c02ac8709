package inks.job.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.job.domain.pojo.CijoblogPojo;
import inks.job.domain.CijoblogEntity;
import inks.job.mapper.CijoblogMapper;
import inks.job.service.CijoblogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 任务日志(Cijoblog)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-08 15:42:07
 */
@Service("cijoblogService")
public class CijoblogServiceImpl implements CijoblogService {
    @Resource
    private CijoblogMapper cijoblogMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CijoblogPojo getEntity(String key, String tid) {
        return this.cijoblogMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CijoblogPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CijoblogPojo> lst = cijoblogMapper.getPageList(queryParam);
            PageInfo<CijoblogPojo> pageInfo = new PageInfo<CijoblogPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param cijoblogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CijoblogPojo insert(CijoblogPojo cijoblogPojo) {
    //初始化NULL字段
     if(cijoblogPojo.getJobname()==null) cijoblogPojo.setJobname("");
     if(cijoblogPojo.getJobgroup()==null) cijoblogPojo.setJobgroup("");
     if(cijoblogPojo.getMethodname()==null) cijoblogPojo.setMethodname("");
     if(cijoblogPojo.getMethodparams()==null) cijoblogPojo.setMethodparams("");
     if(cijoblogPojo.getJobmessage()==null) cijoblogPojo.setJobmessage("");
     if(cijoblogPojo.getExceptionmark()==null) cijoblogPojo.setExceptionmark(0);
     if(cijoblogPojo.getExceptioninfo()==null) cijoblogPojo.setExceptioninfo("");
     if(cijoblogPojo.getCreateby()==null) cijoblogPojo.setCreateby("");
     if(cijoblogPojo.getCreatebyid()==null) cijoblogPojo.setCreatebyid("");
     if(cijoblogPojo.getCreatedate()==null) cijoblogPojo.setCreatedate(new Date());
     if(cijoblogPojo.getLister()==null) cijoblogPojo.setLister("");
     if(cijoblogPojo.getListerid()==null) cijoblogPojo.setListerid("");
     if(cijoblogPojo.getModifydate()==null) cijoblogPojo.setModifydate(new Date());
     if(cijoblogPojo.getCustom1()==null) cijoblogPojo.setCustom1("");
     if(cijoblogPojo.getCustom2()==null) cijoblogPojo.setCustom2("");
     if(cijoblogPojo.getCustom3()==null) cijoblogPojo.setCustom3("");
     if(cijoblogPojo.getCustom4()==null) cijoblogPojo.setCustom4("");
     if(cijoblogPojo.getCustom5()==null) cijoblogPojo.setCustom5("");
     if(cijoblogPojo.getTenantid()==null) cijoblogPojo.setTenantid("");
     if(cijoblogPojo.getRevision()==null) cijoblogPojo.setRevision(0);
        CijoblogEntity cijoblogEntity = new CijoblogEntity(); 
        BeanUtils.copyProperties(cijoblogPojo,cijoblogEntity);
        
          cijoblogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          cijoblogEntity.setRevision(1);  //乐观锁
          this.cijoblogMapper.insert(cijoblogEntity);
        return this.getEntity(cijoblogEntity.getId(),cijoblogEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param cijoblogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CijoblogPojo update(CijoblogPojo cijoblogPojo) {
        CijoblogEntity cijoblogEntity = new CijoblogEntity(); 
        BeanUtils.copyProperties(cijoblogPojo,cijoblogEntity);
        this.cijoblogMapper.update(cijoblogEntity);
        return this.getEntity(cijoblogEntity.getId(),cijoblogEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.cijoblogMapper.delete(key,tid) ;
    }
    
                                                                                                             
}
