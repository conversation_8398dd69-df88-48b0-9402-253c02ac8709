package inks.job.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.job.domain.pojo.CiconfigPojo;

public interface CiconfigService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiconfigPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CiconfigPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param ciconfigPojo 实例对象
     * @return 实例对象
     */
    CiconfigPojo insert(CiconfigPojo ciconfigPojo);

    /**
     * 修改数据
     *
     * @param ciconfigpojo 实例对象
     * @return 实例对象
     */
    CiconfigPojo update(CiconfigPojo ciconfigpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

}
