package inks.job.service.impl; /*
 *功能描述
 * <AUTHOR>
 * @date  2022/1/10
 * @param 发送邮箱服务实现
 */

import inks.common.core.exception.BaseBusinessException;
import inks.job.service.RegisterCaptchaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;

@Service
public class RegisterCaptchaServiceImpl implements RegisterCaptchaService {
    @Autowired
    private JavaMailSenderImpl mailSender;
    @Override
    public void emaiRegisterCaptcha(String email) {
        try {
            SimpleMailMessage mailMessage = new SimpleMailMessage();
            //设置邮件标题
            mailMessage.setSubject("text邮件测试");
            //设置邮件内容
            mailMessage.setText("text邮件测试");
            //收件人邮箱
            mailMessage.setTo(email);
            //发件人邮箱
            mailMessage.setFrom("<EMAIL>");
            mailSender.send(mailMessage);
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }
}
