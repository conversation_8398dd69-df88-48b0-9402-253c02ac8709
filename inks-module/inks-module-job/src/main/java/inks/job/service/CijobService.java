package inks.job.service;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.job.TaskException;
import inks.job.domain.SysJob;
import inks.job.domain.pojo.CijobPojo;
import inks.job.domain.CijobEntity;

import com.github.pagehelper.PageInfo;
import org.quartz.SchedulerException;

/**
 * 定时任务(Cijob)表服务接口
 *
 * <AUTHOR>
 * @since 2022-01-08 15:38:36
 */
public interface CijobService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CijobPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CijobPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param cijobPojo 实例对象
     * @return 实例对象
     */
    CijobPojo insert(CijobPojo cijobPojo) throws SchedulerException, TaskException;

    /**
     * 修改数据
     *
     * @param cijobpojo 实例对象
     * @return 实例对象
     */
    CijobPojo update(CijobPojo cijobpojo) throws SchedulerException, TaskException;

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid) throws SchedulerException;

    /*
     *
     * <AUTHOR>
     * @description
     * @date 2022/1/8
     * @param * @param null
     * @return
     */

    public void run(String key, String tid) throws SchedulerException;
}

