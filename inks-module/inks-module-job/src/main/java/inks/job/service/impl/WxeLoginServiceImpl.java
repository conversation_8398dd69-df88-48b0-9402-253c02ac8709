package inks.job.service.impl;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.TenantInfo;
import inks.common.core.exception.BaseBusinessException;
import inks.job.mapper.WxeLoginMapper;
import inks.job.service.WxeLoginService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class WxeLoginServiceImpl implements WxeLoginService {
    @Resource
    private WxeLoginMapper wxeLoginMapper;
    @Override
    public LoginUser wxeLogin(String tid, String authuid) {
        try {
            LoginUser loginUser = wxeLoginMapper.wxeLogin(tid,authuid);
            TenantInfo tenantInfo = wxeLoginMapper.getTenantInfo(tid);
            loginUser.setTenantinfo(tenantInfo);
            return loginUser;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }
}
