package inks.job.task;
import inks.job.service.RegisterCaptchaService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import java.util.Date;
/*
 *功能描述
 * <AUTHOR>
 * @date  2022/1/8
 * @param 定时任务类
 */
@Component("inksTask")
public class inksTask {

    @Resource
    private RegisterCaptchaService registerCaptchaService;

    public void test() throws MessagingException {
        registerCaptchaService.emaiRegisterCaptcha("<EMAIL>");
        System.out.println(new Date());
    }
     /*
      *
      * <AUTHOR>
      * @description 定时发送邮箱任务
      * @date 2022/1/10
      * @param * @param null
      * @return
      */
    public void sendEmai(String email,String tid){
        registerCaptchaService.emaiRegisterCaptcha(email);
    }
}
