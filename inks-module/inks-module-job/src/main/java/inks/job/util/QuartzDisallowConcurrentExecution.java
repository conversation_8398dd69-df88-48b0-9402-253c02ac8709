package inks.job.util;

import inks.job.domain.CijobEntity;
import inks.job.domain.SysJob;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;

/**
 * 定时任务处理（禁止并发执行）
 *
 * <AUTHOR>
 *
 */
@DisallowConcurrentExecution
public class QuartzDisallowConcurrentExecution extends AbstractQuartzJob
{
    @Override
    protected void doExecute(JobExecutionContext context, CijobEntity sysJob) throws Exception
    {
        JobInvokeUtil.invokeMethod(sysJob);
    }
}
