package inks.job.controller;

import com.alibaba.fastjson.JSONObject;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.job.domain.pojo.WxeMessagePojo;
import inks.job.domain.pojo.WxeTextPojo;
import inks.job.feign.SystemFeignService;
import inks.job.service.WxeLoginService;
import inks.job.util.QyWeChat;
import inks.job.util.QyWeChatUtils;
import inks.job.util.SendRequest;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("message")
public class MessageController {
    @Resource
    private SystemFeignService systemFeignService;
    @ApiOperation(value=" 企业微信第三方应用发送信息", notes="企业微信第三方应用发送信息", produces="application/json")
    @RequestMapping(value="/wxemsg",method= RequestMethod.GET)
    public R<String> wxemsg(String uuid,String msg,String type){
        try {

            R r = new R();
            //获取企业ID
            r = systemFeignService.getConfigValue("D08M11.corpid","5a7f9f3a-6dba-4bb6-8057-935cd7878c50","");
            if(r.getCode()==200){
                QyWeChat.corpId = r.getData().toString();
            }
            //获取应用密钥
            r = systemFeignService.getConfigValue("D08M11.agentsecret","5a7f9f3a-6dba-4bb6-8057-935cd7878c50","");
            if(r.getCode()==200){
                QyWeChat.agentSecret = r.getData().toString();
            }
            r = systemFeignService.getConfigValue("D08M11.agentId","5a7f9f3a-6dba-4bb6-8057-935cd7878c50","");
            if(r.getCode()==200){
                QyWeChat.agentId = Integer.valueOf(r.getData().toString());
            }
            String token = QyWeChatUtils.refreshToken("agentToken");
            String url = QyWeChat.message.replace("{access_token}",token);
            WxeMessagePojo wxeMessagePojo = new WxeMessagePojo();
            wxeMessagePojo.setAgentid(QyWeChat.agentId);
            wxeMessagePojo.setTouser(uuid.replace(",","|"));
            wxeMessagePojo.setSafe(0);
            wxeMessagePojo.setMsgtype("text");
            WxeTextPojo wxeTextPojo = new WxeTextPojo();
            wxeTextPojo.setContent(msg);
            wxeMessagePojo.setText(wxeTextPojo);
            JSONObject jsonObject = SendRequest.sendPost(url,JSONObject.toJSONString(wxeMessagePojo));
            return R.ok("信息发送成功");
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
}
