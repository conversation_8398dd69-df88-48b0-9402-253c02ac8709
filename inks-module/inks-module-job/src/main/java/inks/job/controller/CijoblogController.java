package inks.job.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.common.redis.service.RedisService;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.job.domain.pojo.CijoblogPojo;
import inks.job.domain.CijoblogEntity;
import inks.job.service.CijoblogService;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 任务日志(CiJobLog)表控制层
 *
 * <AUTHOR>
 * @since 2022-01-08 15:42:07
 */
@RestController
@RequestMapping("cijoblog")
public class CijoblogController {
    /**
     * 服务对象
     */
    @Resource
    private CijoblogService cijoblogService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;



    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value=" 获取任务日志详细信息", notes="获取任务日志详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiJobLog.List")
    public R<CijoblogPojo> getEntity(String key) {
    try {
           // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cijoblogService.getEntity(key, loginUser.getTenantid()));
         }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiJobLog.List")
    public R<PageInfo<CijoblogPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || queryParam.getOrderBy() =="") queryParam.setOrderBy("CiJobLog.CreateDate");
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.cijoblogService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }



    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value=" 新增任务日志", notes="新增任务日志", produces="application/json")
    @RequestMapping(value="/create",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiJobLog.Add")
    public R<CijoblogPojo> create(@RequestBody String json) {
       try {
       CijoblogPojo cijoblogPojo = JSONArray.parseObject(json,CijoblogPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
                                                                                                                     cijoblogPojo.setCreateby(loginUser.getRealname());   // 创建者
            cijoblogPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            cijoblogPojo.setCreatedate(new Date());   // 创建时间
            cijoblogPojo.setLister(loginUser.getRealname());   // 制表
            cijoblogPojo.setListerid(loginUser.getUserid());    // 制表id
            cijoblogPojo.setModifydate(new Date());   //修改时间
            cijoblogPojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.cijoblogService.insert(cijoblogPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
   }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value="修改任务日志", notes="修改任务日志", produces="application/json")
    @RequestMapping(value="/update",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiJobLog.Edit")
    public R<CijoblogPojo> update(@RequestBody String json) {
       try {
         CijoblogPojo cijoblogPojo = JSONArray.parseObject(json,CijoblogPojo.class);
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            cijoblogPojo.setLister(loginUser.getRealname());   // 制表
            cijoblogPojo.setListerid(loginUser.getUserid());    // 制表id
            cijoblogPojo.setTenantid(loginUser.getTenantid());   //租户id
            cijoblogPojo.setModifydate(new Date());   //修改时间
//            cijoblogPojo.setAssessor(""); // 审核员
//            cijoblogPojo.setAssessorid(""); // 审核员id
//            cijoblogPojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.cijoblogService.update(cijoblogPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value="删除任务日志", notes="删除任务日志", produces="application/json")
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiJobLog.Delete")
    public R<Integer> delete(String key) {
    try {
            // 获得用户数据
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cijoblogService.delete(key, loginUser.getTenantid()));
     }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

//    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
//    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "CiJobLog.Print")
//    public void printBill(String key, String ptid) throws IOException, JRException {
//        // 获得用户数据
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        //获取单据信息
//        CijoblogPojo cijoblogPojo = this.cijoblogService.getEntity(key,loginUser.getTenantid());
//        //表头转MAP
//        Map<String, Object> map = BeanUtils.beanToMap(cijoblogPojo);
//
//      //从redis中获取Reprot内容
//     ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
//     String content = "";
//     if (reportsPojo != null ) {
//         content = reportsPojo.getRptdata();
//     } else {
//         throw new BaseBusinessException("未找到报表");
//     }
//        //报表生成
//        InputStream stream = new ByteArrayInputStream(content.getBytes());
//        HttpServletResponse response= ServletUtils.getResponse();
//        ServletOutputStream os = response.getOutputStream();
//        try {
//            //编译报表
//            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
//            //数据填充
//            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
//            //打印PDF数据流
//            JasperExportManager.exportReportToPdfStream(print, os);
//        } catch (JRException e) {
//            e.printStackTrace();
//        } catch (BaseBusinessException base) {
//            base.getMessage();
//        } finally {
//            os.flush();
//        }
//    }
}

