package inks.job.controller;

import com.alibaba.fastjson.JSONObject;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.text.inksSnowflake;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.job.feign.SystemFeignService;
import inks.job.service.WxeLoginService;
import inks.job.util.QyWeChat;
import inks.job.util.QyWeChatUtils;
import inks.job.util.SendRequest;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Controller
@RequestMapping("/wxelogin")
public class WxeLoginController {

    @Resource
    private WxeLoginService wxeLoginService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private RedisService redisService;
    @Resource
    private SystemFeignService systemFeignService;

    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(WxeLoginController.class);

    @GetMapping("/redirect")
    @ApiOperation(value = "企业微信扫码登录", notes = "企业微信扫码登录", produces = "application/json")
    public void doLogin(String tid, String code, String state, HttpServletRequest request, HttpServletResponse response) throws IOException {
        R r = new R();
        logger.info("------开始企业微信免费登-------");
        //获取企业ID
        r = systemFeignService.getConfigValue("D08M11.corpid", state, "");
        if (r.getCode() == 200) {
            QyWeChat.corpId = r.getData().toString();
            logger.info("企业微信免登 corpId:" + QyWeChat.corpId);
        } else {
            logger.info("获取应用corpId失败" + r.toString());
        }
        //获取应用密钥
        r = systemFeignService.getConfigValue("D08M11.agentsecret", state, "");
        if (r.getCode() == 200) {
            QyWeChat.agentSecret = r.getData().toString();
            logger.info("企业微信免登 agentSecret:" + QyWeChat.agentSecret);
        } else {
            logger.info("获取应用agentSecret失败" + r.toString());
        }
        // 通过 corpid + agentSecret 获得企业微信Token
        // https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}
        String token = QyWeChatUtils.refreshToken("agentToken");
        logger.info("企业微信免登 token:" + token);

        // 通过 Token + Authorization Code 获得用户信息
        // https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token={access_token}&code={code}
        String url = QyWeChat.getUser.replace("{access_token}", token).replace("{code}", code);
        logger.info("企业微信免登 url:" + url);
        JSONObject jsonObject = SendRequest.sendGet(url);
        logger.info("企业微信免登返回:" + jsonObject.toString());

        // 提取json中的企业微信userid
        String userid = jsonObject.getString("UserId"); // jsonObject.get("UserId").toString(); 如null时会报错
        if (userid != null) {
            logger.info("企业微信免登 uuid:" + userid);
        } else {
            logger.info("企业微信免登 errmsg:" + jsonObject.getString("errmsg"));
        }

        // 按userid + Tid 通第三方登录表，返回loginUser对象
        LoginUser loginUser = wxeLoginService.wxeLogin(tid, userid);
        if (loginUser == null) {
            response.sendRedirect("");
            return;
        }

        // 将logUser存入redis  设置5分钟后过期
        String tokenuuid = inksSnowflake.getSnowflake().nextIdStr();
        redisService.setCacheObject("login_tokens:" + tokenuuid, loginUser, 300L, TimeUnit.SECONDS);
        logger.info("企业微信免登 临时login_tokens:" + tokenuuid);
        // 参数中获得前端登录页 + tokenuuid
        String loginurl = "";
        r = systemFeignService.getConfigValue("D08M11.loginurl", state, "");
        if (r.getCode() == 200) {
            loginurl = r.getData().toString().replace("{key}", tokenuuid);
            logger.info("企业微信免登 loginurl:" + r.getData().toString());
        } else {
            logger.info("企业微信免登 loginurl 失败" + r.toString());
        }
        // 抛出网址
        response.sendRedirect(loginurl);
    }

    @ApiOperation(value = "读取LoginUser用户信息", notes = "读取LoginUser解析用户信息", produces = "application/json")
    @GetMapping("/getLoginUser")
    @ResponseBody
    public R<Map<String, Object>> getLoginUser(String key) {
        try {
            //解析是用户信息
            LoginUser loginUser = tokenService.getLoginUser(key);
            if (loginUser==null){
                logger.info("key已过期失效："+key);
              return   R.fail("key已过期失效");
            }
            //删除缓存用户信息
            redisService.deleteObject(key);

            //重新存入redis
            Map<String, Object> map = tokenService.createToken(loginUser);
            return R.ok(map);
        } catch (Exception e) {
            logger.warn(e.getMessage());
            return R.fail(e.getMessage());
        }
    }
}
