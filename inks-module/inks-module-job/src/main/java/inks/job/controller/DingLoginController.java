package inks.job.controller;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiSnsGetuserinfoBycodeRequest;
import com.dingtalk.api.request.OapiUserGetbyunionidRequest;
import com.dingtalk.api.response.OapiSnsGetuserinfoBycodeResponse;
import com.dingtalk.api.response.OapiUserGetbyunionidResponse;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.text.inksSnowflake;
import inks.common.redis.service.RedisService;
import inks.job.feign.SystemFeignService;
import inks.job.service.WxeLoginService;
import inks.job.util.ding.AccessTokenUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Controller
@RequestMapping("/dinglogin")
public class DingLoginController {

    @Resource
    private WxeLoginService wxeLoginService;

    @Resource
    private RedisService redisService;

    @Resource
    private SystemFeignService systemFeignService;

    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(DingLoginController.class);


//    @ApiOperation(value="解析用户信息", notes="解析用户信息", produces="application/json")
//    @GetMapping("/getUser")
//    @ResponseBody
//    public void getUser(String key){
//
//    }


    /**
     * 获取授权用户的个人信息 openapi@dingtalk
     *
     * @return
     * @throws Exception ServiceResult<Map<String,Object>> 2020-11-4
     */
    @RequestMapping(value = "/redirect", method = RequestMethod.GET)
    public void getDdScan(@RequestParam("code") String code, @RequestParam("state") String state, HttpServletRequest request, HttpServletResponse response) throws Exception {

        logger.info( "开始免登 code：" + code);
        logger.info( "开始免登 state：" + state);

        R r = new R();
        logger.info("------开始钉钉免登-------");
        //获取企业ID
        r = systemFeignService.getConfigValue("D08M12.appkey", state, "");
        if (r.getCode() == 200) {
            AccessTokenUtil.AppKey = r.getData().toString();
            System.out.println("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
        } else {
            System.out.println("获取应用AppKey失败" + r.toString());
        }
        //获取应用密钥
        r = systemFeignService.getConfigValue("D08M12.agentsecret", state, "");
        if (r.getCode() == 200) {
            AccessTokenUtil.AppSecret = r.getData().toString();
            System.out.println("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);
        } else {
            System.out.println("获取应用AppSecret失败" + r.toString());
        }


        // 获取access_token，注意正式代码要有异常流处理
        String access_token = AccessTokenUtil.getToken();
        logger.info("钉钉免登 Token:" + access_token);

        // 通过临时授权码获取授权用户的个人信息
        DefaultDingTalkClient client2 = new DefaultDingTalkClient("https://oapi.dingtalk.com/sns/getuserinfo_bycode");
        OapiSnsGetuserinfoBycodeRequest reqBycodeRequest = new OapiSnsGetuserinfoBycodeRequest();

        reqBycodeRequest.setTmpAuthCode(code);
        OapiSnsGetuserinfoBycodeResponse bycodeResponse = client2.execute(reqBycodeRequest,  AccessTokenUtil.AppKey, AccessTokenUtil.AppSecret);

        // 根据unionid获取userid
        String unionid = bycodeResponse.getUserInfo().getUnionid();
        DingTalkClient clientDingTalkClient = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/user/getbyunionid");
        OapiUserGetbyunionidRequest reqGetbyunionidRequest = new OapiUserGetbyunionidRequest();
        reqGetbyunionidRequest.setUnionid(unionid);
        OapiUserGetbyunionidResponse oapiUserGetbyunionidResponse = clientDingTalkClient.execute(reqGetbyunionidRequest, access_token);

        // 根据获取用户userId
        String userid = oapiUserGetbyunionidResponse.getResult().getUserid();

        // 按userid + Tid 通第三方登录表，返回loginUser对象
        LoginUser loginUser = wxeLoginService.wxeLogin(state, userid);
        if (loginUser == null) {
            logger.info("钉钉免登: 未到第三方登录对应用户");
            response.sendRedirect("");
            return;
        }

        // 将logUser存入redis  设置5分钟后过期
        String tokenuuid = inksSnowflake.getSnowflake().nextIdStr();
        redisService.setCacheObject("login_tokens:" + tokenuuid, loginUser, 300L, TimeUnit.SECONDS);
        System.out.println("钉钉免登 login_tokens:" + tokenuuid);
        // 参数中获得前端登录页 + tokenuuid
        String loginurl = "";
        r = systemFeignService.getConfigValue("D08M12.loginurl", state, "");
        if (r.getCode() == 200) {
            loginurl = r.getData().toString().replace("{key}", tokenuuid);
            System.out.println("钉钉免登 loginurl:" + r.getData().toString());
        } else {
            System.out.println("钉钉免登 loginurl 失败" + r.toString());
        }
        response.sendRedirect(loginurl);


//        // 根据userId获取用户信息
//        DingTalkClient clientDingTalkClient2 = new DefaultDingTalkClient(
//                "https://oapi.dingtalk.com/topapi/v2/user/get");
//        OapiV2UserGetRequest reqGetRequest = new OapiV2UserGetRequest();
//        reqGetRequest.setUserid(userid);
//        reqGetRequest.setLanguage("zh_CN");
//        OapiV2UserGetResponse rspGetResponse = clientDingTalkClient2.execute(reqGetRequest, access_token);
//        System.out.println(rspGetResponse.getBody());
//        return rspGetResponse.getBody();
    }
}
