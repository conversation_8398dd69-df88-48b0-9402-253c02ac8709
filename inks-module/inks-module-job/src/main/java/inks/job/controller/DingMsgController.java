package inks.job.controller;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.request.OapiMessageCorpconversationGetsendresultRequest;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import com.dingtalk.api.response.OapiMessageCorpconversationGetsendresultResponse;
import com.taobao.api.ApiException;
import inks.common.core.domain.R;
import inks.common.redis.service.RedisService;
import inks.job.feign.SystemFeignService;
import inks.job.util.ding.AccessTokenUtil;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;

/**
 * 定时任务(dingmsg)表控制层
 *
 * <AUTHOR>
 * @since 2022-01-08 15:38:31
 */
@Controller
@RequestMapping("/dingmsg")
public class DingMsgController {

    @Resource
    private RedisService redisService;

    @Resource
    private SystemFeignService systemFeignService;

    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(DingLoginController.class);

    @ApiOperation(value = " 企业微信第三方应用发送信息", notes = "企业微信第三方应用发送信息", produces = "application/json")
    @RequestMapping(value = "/sendCardMsg", method = RequestMethod.GET)
    public R<String> sendCardMsg(String uuid, String msg, String tid) {

        logger.info("开始免登 uuid：" + uuid);
        logger.info("开始免登 tid：" + tid);
        Long agentId = 0L;

        R r = new R();
        logger.info("------开始钉钉免登-------");
        //获取企业ID
        r = systemFeignService.getConfigValue("D08M12.appkey", tid, "");
        if (r.getCode() == 200) {
            AccessTokenUtil.AppKey = r.getData().toString();
            logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
        } else {
            logger.info("获取应用AppKey失败" + r.toString());
        }
        //获取应用密钥
        r = systemFeignService.getConfigValue("D08M12.agentsecret", tid, "");
        if (r.getCode() == 200) {
            AccessTokenUtil.AppSecret = r.getData().toString();
            logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);
        } else {
            logger.info("获取应用AppSecret失败" + r.toString());
        }

        //获取应用密钥
        r = systemFeignService.getConfigValue("D08M12.agentid", tid, "");
        if (r.getCode() == 200) {
            agentId = Long.parseLong(r.getData().toString());
            logger.info("钉钉免登 agentId:" +   agentId);
        } else {
            logger.info("获取应用agentId失败" + r.toString());
        }


        // 获取access_token，注意正式代码要有异常流处理
        String access_token = AccessTokenUtil.getToken();
        logger.info("钉钉免登 Token:" + access_token);

        try {

            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
            OapiMessageCorpconversationAsyncsendV2Request req = new OapiMessageCorpconversationAsyncsendV2Request();
            req.setAgentId(1454349674L);
            req.setUseridList("manager3081");

            OapiMessageCorpconversationAsyncsendV2Request.Msg obj1 = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            obj1.setMsgtype("action_card");

            OapiMessageCorpconversationAsyncsendV2Request.ActionCard obj5 = new OapiMessageCorpconversationAsyncsendV2Request.ActionCard();
//            List<OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList> list7 = new ArrayList<OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList>();
//            OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList obj8 = new OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList();
//
//            obj8.setActionUrl("https://www.taobao.com");
//            obj8.setTitle("一个按钮");
//
//            list7.add(obj8);
//
//            obj5.setBtnJsonList(list7);
            //obj5.setBtnOrientation("1");
            obj5.setSingleUrl("https://open.dingtalk.com");
            obj5.setSingleTitle("查看详情");
            obj5.setMarkdown("支持markdown格式的正文内容");
            obj5.setTitle("是透出到会话列表和通知的文案");
            obj1.setActionCard(obj5);

            req.setMsg(obj1);
            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(req, access_token);
            logger.info(rsp.getBody());
            return  R.ok(rsp.getBody());
        } catch (ApiException e) {
            e.printStackTrace();
            return  R.fail(e.getErrMsg());
        }
    }

    @ApiOperation(value = " 企业微信第三方应用发送信息", notes = "企业微信第三方应用发送信息", produces = "application/json")
    @RequestMapping(value = "/sendTextMsg", method = RequestMethod.GET)
    public R<String> sendTextMsg(String uuid, String msg, String tid) {

        logger.info("开始免登 uuid：" + uuid);
        logger.info("开始免登 tid：" + tid);
        Long agentId = 0L;

        R r = new R();
        logger.info("------开始钉钉免登-------");
        //获取企业ID
        r = systemFeignService.getConfigValue("D08M12.appkey", tid, "");
        if (r.getCode() == 200) {
            AccessTokenUtil.AppKey = r.getData().toString();
            logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
        } else {
            logger.info("获取应用AppKey失败" + r.toString());
        }
        //获取应用密钥
        r = systemFeignService.getConfigValue("D08M12.agentsecret", tid, "");
        if (r.getCode() == 200) {
            AccessTokenUtil.AppSecret = r.getData().toString();
            logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);
        } else {
            logger.info("获取应用AppSecret失败" + r.toString());
        }

        //获取应用密钥
        r = systemFeignService.getConfigValue("D08M12.agentid", tid, "");
        if (r.getCode() == 200) {
            agentId = Long.parseLong(r.getData().toString());
            logger.info("钉钉免登 agentId:" +   agentId);
        } else {
            logger.info("获取应用agentId失败" + r.toString());
        }


        // 获取access_token，注意正式代码要有异常流处理
        String access_token = AccessTokenUtil.getToken();
        logger.info("钉钉免登 Token:" + access_token);

        try {

            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
            OapiMessageCorpconversationAsyncsendV2Request req = new OapiMessageCorpconversationAsyncsendV2Request();
            req.setAgentId(agentId);
            req.setUseridList(uuid);
            OapiMessageCorpconversationAsyncsendV2Request.Msg obj1 = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            obj1.setMsgtype("text");
            OapiMessageCorpconversationAsyncsendV2Request.Text obj2 = new OapiMessageCorpconversationAsyncsendV2Request.Text();
            obj2.setContent(msg);
            obj1.setText(obj2);
            req.setMsg(obj1);
            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(req, access_token);
            logger.info(rsp.getBody());
            return  R.ok(rsp.getBody());
        } catch (ApiException e) {
            e.printStackTrace();
            return  R.fail(e.getErrMsg());
        }
    }


    @ApiOperation(value = " 企业微信第三方应用发送信息", notes = "企业微信第三方应用发送信息", produces = "application/json")
    @RequestMapping(value = "/chkMsgState", method = RequestMethod.GET)
    public R<String> chkMsgState(Long taskid,  String tid) {

        logger.info("开始免登 taskid：" +taskid);
        logger.info("开始免登 tid：" + tid);
        Long agentId = 0L;

        R r = new R();
        logger.info("------开始钉钉免登-------");
        //获取企业ID
        r = systemFeignService.getConfigValue("D08M12.appkey", tid, "");
        if (r.getCode() == 200) {
            AccessTokenUtil.AppKey = r.getData().toString();
            logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
        } else {
            logger.info("获取应用AppKey失败" + r.toString());
        }
        //获取应用密钥
        r = systemFeignService.getConfigValue("D08M12.agentsecret", tid, "");
        if (r.getCode() == 200) {
            AccessTokenUtil.AppSecret = r.getData().toString();
            logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);
        } else {
            logger.info("获取应用AppSecret失败" + r.toString());
        }

        //获取应用密钥
        r = systemFeignService.getConfigValue("D08M12.agentid", tid, "");
        if (r.getCode() == 200) {
            agentId = Long.parseLong(r.getData().toString());
            logger.info("钉钉免登 agentId:" +   agentId);
        } else {
            logger.info("获取应用agentId失败" + r.toString());
        }


        // 获取access_token，注意正式代码要有异常流处理
        String access_token = AccessTokenUtil.getToken();
        logger.info("钉钉免登 Token:" + access_token);

        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/getsendresult");
            OapiMessageCorpconversationGetsendresultRequest req = new OapiMessageCorpconversationGetsendresultRequest();
            req.setAgentId(agentId);
            req.setTaskId(taskid);
            OapiMessageCorpconversationGetsendresultResponse rsp = client.execute(req, access_token);
            logger.info(rsp.getBody());
            return  R.ok(rsp.getBody());
        } catch (ApiException e) {
            e.printStackTrace();
            return  R.fail(e.getErrMsg());
        }
    }
}
