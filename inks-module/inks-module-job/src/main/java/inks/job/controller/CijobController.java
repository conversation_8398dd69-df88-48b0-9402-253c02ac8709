package inks.job.controller;

import inks.common.core.constant.Constants;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.job.TaskException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.StringUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.job.domain.SysJob;
import inks.job.domain.pojo.CijobPojo;
import inks.job.domain.CijobEntity;
import inks.job.service.CijobService;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.job.util.CronUtils;
import org.quartz.SchedulerException;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 定时任务(CiJob)表控制层
 *
 * <AUTHOR>
 * @since 2022-01-08 15:38:31
 */
@RestController
@RequestMapping("cijob")
public class CijobController {
    /**
     * 服务对象
     */
    @Resource
    private CijobService cijobService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;



    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value=" 获取定时任务详细信息", notes="获取定时任务详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiJob.List")
    public R<CijobPojo> getEntity(String key) {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    try {
           // 获得用户数据
            return R.ok(this.cijobService.getEntity(key, loginUser.getTenantid()));
         }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiJob.List")
    public R<PageInfo<CijobPojo>> getPageList(@RequestBody String json) {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || queryParam.getOrderBy() =="") queryParam.setOrderBy("CiJob.CreateDate");
            // 获得用户数据
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.cijobService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }



    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value=" 新增定时任务", notes="新增定时任务", produces="application/json")
    @RequestMapping(value="/create",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiJob.Add")
    public R<CijobPojo> create(@RequestBody String json) {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
       try {
       CijobPojo cijobPojo = JSONArray.parseObject(json,CijobPojo.class);
            // 获得用户数据
            cijobPojo.setCreateby(loginUser.getRealname());   // 创建者
            cijobPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            cijobPojo.setCreatedate(new Date());   // 创建时间
            cijobPojo.setLister(loginUser.getRealname());   // 制表
            cijobPojo.setListerid(loginUser.getUserid());    // 制表id
            cijobPojo.setModifydate(new Date());   //修改时间
            cijobPojo.setTenantid(loginUser.getTenantid());   //租户id
           if (!CronUtils.isValid(cijobPojo.getCronexpression()))
           {
               return R.fail("新增任务'" + cijobPojo.getJobname() + "'失败，Cron表达式不正确");
           }
           else if (StringUtils.containsIgnoreCase(cijobPojo.getInvoketarget(), Constants.LOOKUP_RMI))
           {
               return R.fail("新增任务'" + cijobPojo.getJobname() + "'失败，目标字符串不允许'rmi://'调用");
           }
           else if (StringUtils.containsIgnoreCase(cijobPojo.getInvoketarget(), "ldap://"))
           {
               return R.fail("新增任务'" + cijobPojo.getJobname() + "'失败，目标字符串不允许'ldap://'调用");
           }
           else if (StringUtils.containsAnyIgnoreCase(cijobPojo.getInvoketarget(), new String[] { Constants.HTTP, Constants.HTTPS }))
           {
               return R.fail("新增任务'" + cijobPojo.getJobname() + "'失败，目标字符串不允许'http(s)//'调用");
           }
        return R.ok(this.cijobService.insert(cijobPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
   }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value="修改定时任务", notes="修改定时任务", produces="application/json")
    @RequestMapping(value="/update",method= RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiJob.Edit")
    public R<CijobPojo> update(@RequestBody String json) {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
       try {
         CijobPojo cijobPojo = JSONArray.parseObject(json,CijobPojo.class);
            // 获得用户数据
            cijobPojo.setLister(loginUser.getRealname());   // 制表
            cijobPojo.setListerid(loginUser.getUserid());    // 制表id
            cijobPojo.setTenantid(loginUser.getTenantid());   //租户id
            cijobPojo.setModifydate(new Date());   //修改时间
           if (!CronUtils.isValid(cijobPojo.getCronexpression()))
           {
               return R.fail("修改任务'" + cijobPojo.getJobname() + "'失败，Cron表达式不正确");
           }
           else if (StringUtils.containsIgnoreCase(cijobPojo.getInvoketarget(), Constants.LOOKUP_RMI))
           {
               return R.fail("修改任务'" + cijobPojo.getJobname() + "'失败，目标字符串不允许'rmi://'调用");
           }
           else if (StringUtils.containsIgnoreCase(cijobPojo.getInvoketarget(), "ldap://"))
           {
               return R.fail("修改任务'" + cijobPojo.getJobname() + "'失败，目标字符串不允许'ldap://'调用");
           }
           else if (StringUtils.containsAnyIgnoreCase(cijobPojo.getInvoketarget(), new String[] { Constants.HTTP, Constants.HTTPS }))
           {
               return R.fail("修改任务'" + cijobPojo.getJobname() + "'失败，目标字符串不允许'http(s)//'调用");
           }
//            cijobPojo.setAssessor(""); // 审核员
//            cijobPojo.setAssessorid(""); // 审核员id
//            cijobPojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.cijobService.update(cijobPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value="删除定时任务", notes="删除定时任务", produces="application/json")
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiJob.Delete")
    public R<Integer> delete(String key) {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
    try {
            // 获得用户数据
            return R.ok(this.cijobService.delete(key, loginUser.getTenantid()));
     }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    @RequestMapping(value = "/run",method = RequestMethod.PUT)
    @ApiOperation(value = "定时任务立即执行一次", notes = "定时任务立即执行一次", produces = "application/json")
    public R run(String key) throws SchedulerException
    {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        cijobService.run(key,loginUser.getTenantid());
        return R.ok();
    }

    @PutMapping("/changeStatus")
    @ApiOperation(value = "定时任务状态修改", notes = "定时任务状态修改", produces = "application/json")
    public R changeStatus(@RequestBody String json) throws SchedulerException, TaskException {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        CijobPojo cijobPojo = JSONArray.parseObject(json,CijobPojo.class);
        CijobPojo cijobPojo1 = cijobService.getEntity(cijobPojo.getId(),loginUser.getTenantid());
        cijobPojo1.setStatus(cijobPojo.getStatus());
        return R.ok(cijobService.update(cijobPojo1));
    }

//    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
//    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "CiJob.Print")
//    public void printBill(String key, String ptid) throws IOException, JRException {
//        // 获得用户数据
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        //获取单据信息
//        CijobPojo cijobPojo = this.cijobService.getEntity(key,loginUser.getTenantid());
//        //表头转MAP
//        Map<String, Object> map = BeanUtils.beanToMap(cijobPojo);
//
//      //从redis中获取Reprot内容
//     ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
//     String content = "";
//     if (reportsPojo != null ) {
//         content = reportsPojo.getRptdata();
//     } else {
//         throw new BaseBusinessException("未找到报表");
//     }
//        //报表生成
//        InputStream stream = new ByteArrayInputStream(content.getBytes());
//        HttpServletResponse response= ServletUtils.getResponse();
//        ServletOutputStream os = response.getOutputStream();
//        try {
//            //编译报表
//            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
//            //数据填充
//            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
//            //打印PDF数据流
//            JasperExportManager.exportReportToPdfStream(print, os);
//        } catch (JRException e) {
//            e.printStackTrace();
//        } catch (BaseBusinessException base) {
//            base.getMessage();
//        } finally {
//            os.flush();
//        }
//    }
}

