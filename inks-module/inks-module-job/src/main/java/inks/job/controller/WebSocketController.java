package inks.job.controller;

import com.alibaba.fastjson.JSONObject;
import inks.common.core.domain.R;
import inks.job.domain.pojo.GoodsPojo;
import inks.job.domain.pojo.WsPrintPojo;
import inks.job.service.OneWebSocket;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/websocket")
public class WebSocketController {
    @Resource
    private OneWebSocket oneWebSocket;

    //查看所以在线用户
    @GetMapping("/online")
    @ApiOperation(value = "查看所以在线用户", notes = "查看所以在线用户", produces = "application/json")
    public R<List<String>> online() {
        try {
            return R.ok(oneWebSocket.online());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //点对点发送
    @ApiOperation(value = "点对点发送", notes = "点对点发送", produces = "application/json")
    @GetMapping("/sending")
    public R<String> sending(String key, String msg) {
        try {
            if (msg == null) {
                GoodsPojo goodsPojo = new GoodsPojo("2h0001a","Disp","200*4500*12",10000D);
                List<GoodsPojo> lst = new ArrayList<>();
                lst.add(goodsPojo);
                WsPrintPojo wsPrintPojo = new WsPrintPojo("print", "打印", JSONObject.toJSONString(lst));
                msg = JSONObject.toJSONString(wsPrintPojo);
            }
            oneWebSocket.AppointSending(key, msg);
            return R.ok("信息发送成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //发送所以在线用户
    @GetMapping("/sendingAll")
    @ApiOperation(value = "发送所以在线用户", notes = "发送所以在线用户", produces = "application/json")
    public R<String> sendingAll(String message) {
        try {
            oneWebSocket.GroupSending(message);
            return R.ok("信息发送成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
