package inks.job.feign;

import inks.common.core.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "system")
public interface SystemFeignService {
    /*
    单据编码服务
     */
    @GetMapping("/SYSM07B02/getBillCode")
    R getBillCode(@RequestParam("code") String code, @RequestHeader("Authorization") String token);

    /*
    系统参数服务
    */
    @GetMapping("/SYSM06B1/getConfigValue")
    R getConfigValue(@RequestParam("key") String key, @RequestParam("tid") String tid, @RequestHeader("Authorization") String token);
}
