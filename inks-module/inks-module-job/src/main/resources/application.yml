server:
  port: 9050 #服务端口
spring:
  mail:
    username: <EMAIL>
    password: ASDqwe@!@#
    host: smtp.qiye.aliyun.com
    default-encoding: UTF-8
    properties:
      mail.smtp.socketFactory.fallback: true
      mail.smtp.starttls.enable: true
  application:
    name: job
  main:
    allow-bean-definition-overriding: true #当遇到同样名字的时候，是否允许覆盖注册
  cloud:
    nacos:
      discovery:
        server-addr: **************:8848
  datasource:
    #MYsql连接字符串
    url: ***************************************************************************************************************************************************
    username: root
    password: asd@123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    maximum-pool-size: 5
    hikari:
      connection-test-query: SELECT 1
  #redis配置
  redis:
    database: 0
    # Redis服务器地址 写你的ip
    host: **************
    # Redis服务器连接端口
    port: 56379
    # Redis服务器连接密码（默认为空）
    password: asd@123456
    # 连接池最大连接数（使用负值表示没有限制  类似于mysql的连接池
    jedis:
      pool:
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 表示连接池的链接拿完了 现在去申请需要等待的时间
        max-wait: -1
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
    # 连接超时时间（毫秒） 去链接redis服务端
    timeout: 6000
  alipay:
    #    appid
    app_id: 2021002193654512
    #    商户私钥
    merchant_private_key: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCp5GUiy/0XAOva05toOXMCH7GGxeNh+5O58bO8SxlGPFhDvLvt6zwPM7celRXaR1xM24MurFd8u5KHIqX+Dtik8kdG7vruXrGNr2oBGVelB9Qqo8IcgjxMEr+Zk+A3RToXv//+b44C3Hxc/F2VqfTrLU/jK54AvG4ekz+YWAE65/Y0U7JTHXRzf+epV5Z57O1fVNQXE6oY3mWGO9xrhwELH6Y/R1QnnUhJA6EDJp3HNs1cKDR4VdoPfC0jR/+LgDNL5xzp9C47EvWCbFXI2BRBmMzRLGlHe9iwUKQ24baX1gChc1iefuiYcZR5UyxENRzH1Kx6ZtJSxbn+PS69vfLvAgMBAAECggEALmmm94qi6dXmmTGWEzMeqEXgSeFl7S69fN77K0WY8gcqVdcJwEWzcrO+Iyy3e5pjLNwLSoTqobjcnu5oSq/jn6xQrKA9DUHxX7O8UfCKcRtiawOx0/gAYQf+MAamCnNvG23okaoIMd/qWbzYFDsKHfWTDYys7aaMru2rQgNI0r54PRzvKfT11bdOGoJpuxof9KXkSs52FeumyTqicDrb/TMJnqcZ2mSyROuFx1fffsF5Gy/m/GT1j/hbxmc/7PIEJjHFn2Y6uQc3/IgXugOBW/VvCI6nZ5d0dtkGhsuh3GTllHIUQMHidCkSd5Kt0SZUVivL1DeXnHAFPj3HB2BjAQKBgQD6L6S4/6DUsUNtPxc7fAowVSckvMgMyFVWfzSdFOyIw6YWZnto6nm+DHM3YPHD8q4ZapEjoIVLGty05a1y+wN3G36NpQhq2WilvdKso/XJlguHfh+6Ne/+XAsarONzSs8Aw923w0iN1VRGuCuIQyJcdEJKt1oMBPdDN3bKae47eQKBgQCt1xVKNtS0p0gXVEgSu/FzZDuDMGPaGzSwBAiumMIaIx0J/Sld1YYwpdUfAwMb2gLBt89QRE/1YGfwIwXBH3G8/J7W2/jjMSMXNO2PfbeTqEB5SQQ4PqGkl1a7pLqmeYq+/QvH77bhbLODvIF48IrHsoMGeu/HnnSH9aF70U2fpwKBgQD4M3XulqP+/hEPe4zX1ZniE0hvGN46WDDZS/9tawmyMK8//9Gk5yF6Gq3fHc0cAVEZI/DzcFdsiCy3RjiKHl50tpEZgsVUA3XfH++2mD7KC5JKjCR7vvDl3nihsS1A/tFYR/hZS2JaW25tbl65oyieojP2Heo2jGKli4vNCcRvuQKBgBAjW3KcTHrI3MRtqpMyp2OS9oNjRqHEzMtaVIQA4mQSxmta+TAg1mhFvNc4LF21i3P4YGKPNO0OmheM6MfY53In8Yb4vM46jYtl+nfLr/MhN3sK8D1YcyEYe5DH5hv2RI8eZq2U72FV+gRMx6g2jGulgsxjNUdOynqkru0TrSUZAoGBAJ3xVSnilF0ro+Q+f4UpUcyyoGD4Tz2RhpW1EzEg9nJn1Iemcz5GP3yhZ4/wryzqFDhG2Cdt4AR41kmCSdQt12o0K7c0jKN+0GDD/QyOlhYnnTfHCq/LRJQVOdeXiaUuIsH8s1FhnizwgTRylZLBaa1nED1X2IrlRQZWLim4vOec
    #    支付宝公钥
    alipay_public_key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAly1cHQo5mMudSB2Ty0ioP8DXdA/RwMLGd0dvC6s6u8POmqv/K7mpATRSmy67Byp3dPfYH29ZAlWqsnlz+PARC7XbKDrSimVI1EpFeC2M79oX9AgkuMTJP6al+ULGvXeRZyF9CkAtWx2yaGpODKrzbhqJxFKRqSp8fWhejLZpNhmIK7i9FVKaM1zNMH2umGF/4uOp1WHxNcKCev1phq4Qn9511cVA3XH/zoJuAcenjfymifcflyfbSqtZpNSS2RohRhrhewav4cIPu3tLzdd6kOlhoFDHMJUT+HIVzI5nxvsTFyAR2Cts4KqvuO1FDCXyeJ54XQw4y0x8Rm0UrASRcQIDAQAB
    #    支付宝网关
    gatewayUrl: https://openapi.alipay.com/gateway.do
    #    字符编码
    charset: utf-8
    #    签名方式
    sign_type: RSA2
    #    异步验签
    notify_url: http://inks.tpddns.net:8081/system/SYSM10B2/notifyPayResult
    #    同步跳转
    return_url: http://www.inkstech.com
mybatis:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  type-aliases-package: inls.system.domian

