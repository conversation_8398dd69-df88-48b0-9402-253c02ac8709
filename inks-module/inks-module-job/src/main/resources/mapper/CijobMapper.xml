<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.job.mapper.CijobMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.job.domain.pojo.CijobPojo">
        select id,
               JobName,
               JobGroup,
               MethodName,
               MethodParams,
               CronExpression,
               MisfirePolicy,
               Status,
               Userid,
               UserName,
               RealName,
               Remark,
               RowNum,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               Revision,
               InvokeTarget
        from CiJob
        where CiJob.id = #{key}
          and CiJob.Tenantid = #{tid}
    </select>
    <sql id="selectCijobVo">
        select id,
               JobName,
               JobGroup,
               MethodName,
               MethodParams,
               CronExpression,
               MisfirePolicy,
               Status,
               Userid,
               UserName,
               RealName,
               Remark,
               RowNum,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               Revision,
               InvokeTarget
        from CiJob
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.job.domain.pojo.CijobPojo">
        <include refid="selectCijobVo"/>
        where 1 = 1 and CiJob.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiJob.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.jobname != null and SearchPojo.jobname  != ''">
            and CiJob.JobName like concat('%', #{SearchPojo.jobname}, '%')
        </if>
        <if test="SearchPojo.jobgroup != null and SearchPojo.jobgroup  != ''">
            and CiJob.JobGroup like concat('%', #{SearchPojo.jobgroup}, '%')
        </if>
        <if test="SearchPojo.methodname != null and SearchPojo.methodname  != ''">
            and CiJob.MethodName like concat('%', #{SearchPojo.methodname}, '%')
        </if>
        <if test="SearchPojo.methodparams != null and SearchPojo.methodparams  != ''">
            and CiJob.MethodParams like concat('%', #{SearchPojo.methodparams}, '%')
        </if>
        <if test="SearchPojo.cronexpression != null and SearchPojo.cronexpression  != ''">
            and CiJob.CronExpression like concat('%', #{SearchPojo.cronexpression}, '%')
        </if>
        <if test="SearchPojo.misfirepolicy != null and SearchPojo.misfirepolicy  != ''">
            and CiJob.MisfirePolicy like concat('%', #{SearchPojo.misfirepolicy}, '%')
        </if>
        <if test="SearchPojo.userid != null and SearchPojo.userid  != ''">
            and CiJob.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.username != null and SearchPojo.username  != ''">
            and CiJob.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null and SearchPojo.realname  != ''">
            and CiJob.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark  != ''">
            and CiJob.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby  != ''">
            and CiJob.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid  != ''">
            and CiJob.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and CiJob.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid  != ''">
            and CiJob.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1  != ''">
            and CiJob.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2  != ''">
            and CiJob.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3  != ''">
            and CiJob.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4  != ''">
            and CiJob.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5  != ''">
            and CiJob.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.jobname != null and SearchPojo.jobname != ''">
            or CiJob.JobName like concat('%', #{SearchPojo.jobname}, '%')
        </if>
        <if test="SearchPojo.jobgroup != null and SearchPojo.jobgroup != ''">
            or CiJob.JobGroup like concat('%', #{SearchPojo.jobgroup}, '%')
        </if>
        <if test="SearchPojo.methodname != null and SearchPojo.methodname != ''">
            or CiJob.MethodName like concat('%', #{SearchPojo.methodname}, '%')
        </if>
        <if test="SearchPojo.methodparams != null and SearchPojo.methodparams != ''">
            or CiJob.MethodParams like concat('%', #{SearchPojo.methodparams}, '%')
        </if>
        <if test="SearchPojo.cronexpression != null and SearchPojo.cronexpression != ''">
            or CiJob.CronExpression like concat('%', #{SearchPojo.cronexpression}, '%')
        </if>
        <if test="SearchPojo.misfirepolicy != null and SearchPojo.misfirepolicy != ''">
            or CiJob.MisfirePolicy like concat('%', #{SearchPojo.misfirepolicy}, '%')
        </if>
        <if test="SearchPojo.userid != null and SearchPojo.userid != ''">
            or CiJob.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.username != null and SearchPojo.username != ''">
            or CiJob.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null and SearchPojo.realname != ''">
            or CiJob.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or CiJob.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or CiJob.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            or CiJob.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or CiJob.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            or CiJob.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            or CiJob.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            or CiJob.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            or CiJob.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            or CiJob.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            or CiJob.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into CiJob(id, JobName, JobGroup, MethodName, MethodParams, CronExpression, MisfirePolicy, Status,
                          Userid, UserName, RealName, Remark, RowNum, CreateBy, CreateByid, CreateDate, Lister,
                          Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision,InvokeTarget)
        values (#{id}, #{jobname}, #{jobgroup}, #{methodname}, #{methodparams}, #{cronexpression}, #{misfirepolicy},
                #{status}, #{userid}, #{username}, #{realname}, #{remark}, #{rownum}, #{createby}, #{createbyid},
                #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
                #{custom5}, #{tenantid}, #{revision},#{invoketarget})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiJob
        <set>
            <if test="jobname != null ">
                JobName =#{jobname},
            </if>
            <if test="jobgroup != null ">
                JobGroup =#{jobgroup},
            </if>
            <if test="methodname != null ">
                MethodName =#{methodname},
            </if>
            <if test="methodparams != null ">
                MethodParams =#{methodparams},
            </if>
            <if test="cronexpression != null ">
                CronExpression =#{cronexpression},
            </if>
            <if test="misfirepolicy != null ">
                MisfirePolicy =#{misfirepolicy},
            </if>
            <if test="status != null">
                Status =#{status},
            </if>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="invoketarget != null ">
                InvokeTarget =#{invoketarget},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiJob
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <select id="getList" resultType="inks.job.domain.CijobEntity">
        select * from CiJob
    </select>
</mapper>

