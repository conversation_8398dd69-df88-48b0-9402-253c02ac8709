<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.job.mapper.CiconfigMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.job.domain.pojo.CiconfigPojo">
        select id,
               Parentid,
               CfgName,
               CfgKey,
               CfgValue,
               CfgType,
               CfgLevel,
               CfgOption,
               RowNum,
               EnabledMark,
               AllowDelete,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Userid,
               Tenantid,
               Revision
        from CiConfig
        where CiConfig.id = #{key}
          and CiConfig.Tenantid = #{tid}
    </select>
    <sql id="selectCiconfigVo">
        select id,
               <PERSON>rent<PERSON>,
               <PERSON>fgName,
               Cfg<PERSON><PERSON>,
               CfgValue,
               CfgType,
               CfgLevel,
               CfgOption,
               RowNum,
               EnabledMark,
               AllowDelete,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Userid,
               Tenantid,
               Revision
        from CiConfig
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.job.domain.pojo.CiconfigPojo">
        <include refid="selectCiconfigVo"/>
        where 1 = 1
        <if test="Tenantid != 'default' ">
            and CiConfig.Tenantid =#{tenantid}
        </if>
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiConfig.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.parentid != null and SearchPojo.parentid  != ''">
            and CiConfig.Parentid like concat('%', #{SearchPojo.parentid}, '%')
        </if>
        <if test="SearchPojo.cfgname != null and SearchPojo.cfgname  != ''">
            and CiConfig.CfgName like concat('%', #{SearchPojo.cfgname}, '%')
        </if>
        <if test="SearchPojo.cfgkey != null and SearchPojo.cfgkey  != ''">
            and CiConfig.CfgKey like concat('%', #{SearchPojo.cfgkey}, '%')
        </if>
        <if test="SearchPojo.cfgvalue != null and SearchPojo.cfgvalue  != ''">
            and CiConfig.CfgValue like concat('%', #{SearchPojo.cfgvalue}, '%')
        </if>
        <if test="SearchPojo.cfgoption != null and SearchPojo.cfgoption  != ''">
            and CiConfig.CfgOption like concat('%', #{SearchPojo.cfgoption}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark  != ''">
            and CiConfig.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby  != ''">
            and CiConfig.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid  != ''">
            and CiConfig.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and CiConfig.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid  != ''">
            and CiConfig.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1  != ''">
            and CiConfig.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2  != ''">
            and CiConfig.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3  != ''">
            and CiConfig.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4  != ''">
            and CiConfig.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5  != ''">
            and CiConfig.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.userid != null and SearchPojo.userid  != ''">
            and CiConfig.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.parentid != null and SearchPojo.parentid != ''">
            or CiConfig.Parentid like concat('%', #{SearchPojo.parentid}, '%')
        </if>
        <if test="SearchPojo.cfgname != null and SearchPojo.cfgname != ''">
            or CiConfig.CfgName like concat('%', #{SearchPojo.cfgname}, '%')
        </if>
        <if test="SearchPojo.cfgkey != null and SearchPojo.cfgkey != ''">
            or CiConfig.CfgKey like concat('%', #{SearchPojo.cfgkey}, '%')
        </if>
        <if test="SearchPojo.cfgvalue != null and SearchPojo.cfgvalue != ''">
            or CiConfig.CfgValue like concat('%', #{SearchPojo.cfgvalue}, '%')
        </if>
        <if test="SearchPojo.cfgoption != null and SearchPojo.cfgoption != ''">
            or CiConfig.CfgOption like concat('%', #{SearchPojo.cfgoption}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or CiConfig.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or CiConfig.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            or CiConfig.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or CiConfig.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            or CiConfig.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            or CiConfig.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            or CiConfig.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            or CiConfig.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            or CiConfig.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            or CiConfig.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.userid != null and SearchPojo.userid != ''">
            or CiConfig.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into CiConfig(id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CfgOption, RowNum, EnabledMark,
                             AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate,
                             Custom1, Custom2, Custom3, Custom4, Custom5, Userid, Tenantid, Revision)
        values (#{id}, #{parentid}, #{cfgname}, #{cfgkey}, #{cfgvalue}, #{cfgtype}, #{cfglevel}, #{cfgoption},
                #{rownum}, #{enabledmark}, #{allowdelete}, #{remark}, #{createby}, #{createbyid}, #{createdate},
                #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5},
                #{userid}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiConfig
        <set>
            <if test="parentid != null ">
                Parentid =#{parentid},
            </if>
            <if test="cfgname != null ">
                CfgName =#{cfgname},
            </if>
            <if test="cfgkey != null ">
                CfgKey =#{cfgkey},
            </if>
            <if test="cfgvalue != null ">
                CfgValue =#{cfgvalue},
            </if>
            <if test="cfgtype != null">
                CfgType =#{cfgtype},
            </if>
            <if test="cfglevel != null">
                CfgLevel =#{cfglevel},
            </if>
            <if test="cfgoption != null ">
                CfgOption =#{cfgoption},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="allowdelete != null">
                AllowDelete =#{allowdelete},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiConfig
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

