<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.job.mapper.CijoblogMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.job.domain.pojo.CijoblogPojo">
        select
          id, JobName, JobGroup, MethodName, MethodParams, JobMessage, ExceptionMark, ExceptionInfo, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from CiJobLog
        where CiJobLog.id = #{key} and CiJobLog.Tenantid=#{tid}
    </select>
    <sql id="selectCijoblogVo">
         select
          id, JobName, JobGroup, MethodName, MethodParams, JobMessage, ExceptionMark, ExceptionInfo, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>er, <PERSON>erid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from CiJobLog
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.job.domain.pojo.CijoblogPojo">
        <include refid="selectCijoblogVo"/>
         where 1 = 1 and CiJobLog.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and CiJobLog.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>

             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">
           <include refid="and"></include>
         </if>
         <if test="SearchType==1">
           <include refid="or"></include>
         </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.jobname != null and SearchPojo.jobname  != ''">
   and CiJobLog.JobName like concat('%', #{SearchPojo.jobname}, '%')
</if>
<if test="SearchPojo.jobgroup != null and SearchPojo.jobgroup  != ''">
   and CiJobLog.JobGroup like concat('%', #{SearchPojo.jobgroup}, '%')
</if>
<if test="SearchPojo.methodname != null and SearchPojo.methodname  != ''">
   and CiJobLog.MethodName like concat('%', #{SearchPojo.methodname}, '%')
</if>
<if test="SearchPojo.methodparams != null and SearchPojo.methodparams  != ''">
   and CiJobLog.MethodParams like concat('%', #{SearchPojo.methodparams}, '%')
</if>
<if test="SearchPojo.jobmessage != null and SearchPojo.jobmessage  != ''">
   and CiJobLog.JobMessage like concat('%', #{SearchPojo.jobmessage}, '%')
</if>
<if test="SearchPojo.exceptioninfo != null and SearchPojo.exceptioninfo  != ''">
   and CiJobLog.ExceptionInfo like concat('%', #{SearchPojo.exceptioninfo}, '%')
</if>
<if test="SearchPojo.createby != null and SearchPojo.createby  != ''">
   and CiJobLog.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null and SearchPojo.createbyid  != ''">
   and CiJobLog.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
   and CiJobLog.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid  != ''">
   and CiJobLog.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1  != ''">
   and CiJobLog.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2  != ''">
   and CiJobLog.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3  != ''">
   and CiJobLog.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4  != ''">
   and CiJobLog.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5  != ''">
   and CiJobLog.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>
     <sql id="or">
     and (1=0
<if test="SearchPojo.jobname != null and SearchPojo.jobname != ''">
   or CiJobLog.JobName like concat('%', #{SearchPojo.jobname}, '%')
</if>
<if test="SearchPojo.jobgroup != null and SearchPojo.jobgroup != ''">
   or CiJobLog.JobGroup like concat('%', #{SearchPojo.jobgroup}, '%')
</if>
<if test="SearchPojo.methodname != null and SearchPojo.methodname != ''">
   or CiJobLog.MethodName like concat('%', #{SearchPojo.methodname}, '%')
</if>
<if test="SearchPojo.methodparams != null and SearchPojo.methodparams != ''">
   or CiJobLog.MethodParams like concat('%', #{SearchPojo.methodparams}, '%')
</if>
<if test="SearchPojo.jobmessage != null and SearchPojo.jobmessage != ''">
   or CiJobLog.JobMessage like concat('%', #{SearchPojo.jobmessage}, '%')
</if>
<if test="SearchPojo.exceptioninfo != null and SearchPojo.exceptioninfo != ''">
   or CiJobLog.ExceptionInfo like concat('%', #{SearchPojo.exceptioninfo}, '%')
</if>
<if test="SearchPojo.createby != null and SearchPojo.createby != ''">
   or CiJobLog.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
   or CiJobLog.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   or CiJobLog.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
   or CiJobLog.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or CiJobLog.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or CiJobLog.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or CiJobLog.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or CiJobLog.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or CiJobLog.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
)
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into CiJobLog(id, JobName, JobGroup, MethodName, MethodParams, JobMessage, ExceptionMark, ExceptionInfo, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision,InvokeTarget)
        values (#{id}, #{jobname}, #{jobgroup}, #{methodname}, #{methodparams}, #{jobmessage}, #{exceptionmark}, #{exceptioninfo}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision},#{invoketarget})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiJobLog
        <set>
            <if test="jobname != null ">
                JobName =#{jobname},
            </if>
            <if test="jobgroup != null ">
                JobGroup =#{jobgroup},
            </if>
            <if test="methodname != null ">
                MethodName =#{methodname},
            </if>
            <if test="methodparams != null ">
                MethodParams =#{methodparams},
            </if>
            <if test="jobmessage != null ">
                JobMessage =#{jobmessage},
            </if>
            <if test="exceptionmark != null">
                ExceptionMark =#{exceptionmark},
            </if>
            <if test="exceptioninfo != null ">
                ExceptionInfo =#{exceptioninfo},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from CiJobLog where id = #{key} and Tenantid=#{tid}
    </delete>
                                                                                    </mapper>

