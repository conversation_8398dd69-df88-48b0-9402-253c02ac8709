server:
  tomcat:
    uri-encoding: UTF-8
#spring数据源
spring:
  cloud:
    nacos:
      discovery:
        server-addr: **************:8848
        username: nacos
        password: inks0820
        ip: *************
  datasource:
    #MYsql连接字符串
    url: jdbc:mysql://**************:53308/inkssaas?useUnicode=true&characterEncoding=utf-8&allowMutilQueries=true&serverTimezone=Asia/Shanghai&useSSL=false
    username: inksoms
    password: Aa@13579
    driver-class-name: com.mysql.cj.jdbc.Driver
    # druid相关配置
    druid:
      # 初始化时建立物理连接的个数
      initial-size: 5
      # 最大连接池数量
      max-active: 20
      # 最小连接池数量
      min-idle: 10
      # 获取连接时最大等待时间，单位毫秒
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 连接保持空闲而不被驱逐的最小时间
      min-evictable-idle-time-millis: 300000
      # 用来检测连接是否有效的sql，要求是一个查询语句
      validation-query: SELECT 1 FROM DUAL
      # 建议配置为true，不影响性能，并且保证安全性。申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效
      test-while-idle: true
      # 申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      test-on-borrow: false
      # 归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
      test-on-return: false
      # 是否缓存preparedStatement，也就是PSCache。PSCache对支持游标的数据库性能提升巨大，比如说oracle。在mysql下建议关闭
      pool-prepared-statements: false
      # 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true
      max-pool-prepared-statement-per-connection-size: 50
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connect-properties:
        druid.stat.mergeSql: true
        druid.stat.slowSqlMillis: 10
      # 合并多个DruidDataSource的监控数据
      use-global-data-source-stat: true
      # Spring 监控，利用aop 对指定接口的执行时间，jdbc数进行记录
      aop-patterns: "inks.service.std.store.mapper.*"
      ########### 启用内置过滤器（第一个 stat必须，否则监控不到SQL）##########
      filters: stat,wall,log4j2
      # 自己配置监控统计拦截的filter
      filter:
        # 开启druiddatasource的状态监控
        stat:
          enabled: true
          db-type: mysql
          # 开启慢sql监控，默认3000毫秒,超过0.5s 就认为是慢sql，记录到日志中
          log-slow-sql: true
          slow-sql-millis: 10
        # 日志监控，使用slf4j 进行日志输出
      #        slf4j:
      #          enabled: true
      #          statement-log-error-enabled: true
      #          statement-create-after-log-enabled: false
      #          statement-close-after-log-enabled: false
      #          result-set-open-after-log-enabled: false
      #          result-set-close-after-log-enabled: false
      ########## 配置WebStatFilter，用于采集web关联监控的数据 ##########
      web-stat-filter:
        enabled: true                    # 启动 StatFilter
        url-pattern: /*                  # 过滤所有url
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*" # 排除一些不必要的url
        session-stat-enable: true        # 开启session统计功能
        session-stat-max-count: 1000     # session的最大个数,默认100
      ########## 配置StatViewServlet（监控页面），用于展示Druid的统计信息 ##########
      stat-view-servlet:
        enabled: true                    # 启用StatViewServlet
        url-pattern: /druid/*            # 访问内置监控页面的路径，内置监控页面的首页是/druid/index.html
        reset-enable: false              # 不允许清空统计数据,重新计算
        login-username: admin            # 配置监控页面访问密码
        login-password: 123456
        allow:                           # 允许访问的地址，如果allow没有配置或者为空，则允许所有访问127.0.0.1
        deny:                            # 拒绝访问的地址，deny优先于allow，如果在deny列表中，就算在allow列表中，也会被拒绝
  #redis配置
  redis:
    database: 0
    # Redis服务器地址 写你的ip
    host: **************
    # Redis服务器连接端口
    port: 56379
    # Redis服务器连接密码（默认为空）
    password: asd@123456
    # 连接池最大连接数（使用负值表示没有限制  类似于mysql的连接池
    jedis:
      pool:
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 表示连接池的链接拿完了 现在去申请需要等待的时间
        max-wait: -1
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
    # 连接超时时间（毫秒） 去链接redis服务端
    timeout: 6000
#  alipay:
#    #    appid
#    app_id: 2021002193654512
#    #    商户私钥
#    merchant_private_key: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCp5GUiy/0XAOva05toOXMCH7GGxeNh+5O58bO8SxlGPFhDvLvt6zwPM7celRXaR1xM24MurFd8u5KHIqX+Dtik8kdG7vruXrGNr2oBGVelB9Qqo8IcgjxMEr+Zk+A3RToXv//+b44C3Hxc/F2VqfTrLU/jK54AvG4ekz+YWAE65/Y0U7JTHXRzf+epV5Z57O1fVNQXE6oY3mWGO9xrhwELH6Y/R1QnnUhJA6EDJp3HNs1cKDR4VdoPfC0jR/+LgDNL5xzp9C47EvWCbFXI2BRBmMzRLGlHe9iwUKQ24baX1gChc1iefuiYcZR5UyxENRzH1Kx6ZtJSxbn+PS69vfLvAgMBAAECggEALmmm94qi6dXmmTGWEzMeqEXgSeFl7S69fN77K0WY8gcqVdcJwEWzcrO+Iyy3e5pjLNwLSoTqobjcnu5oSq/jn6xQrKA9DUHxX7O8UfCKcRtiawOx0/gAYQf+MAamCnNvG23okaoIMd/qWbzYFDsKHfWTDYys7aaMru2rQgNI0r54PRzvKfT11bdOGoJpuxof9KXkSs52FeumyTqicDrb/TMJnqcZ2mSyROuFx1fffsF5Gy/m/GT1j/hbxmc/7PIEJjHFn2Y6uQc3/IgXugOBW/VvCI6nZ5d0dtkGhsuh3GTllHIUQMHidCkSd5Kt0SZUVivL1DeXnHAFPj3HB2BjAQKBgQD6L6S4/6DUsUNtPxc7fAowVSckvMgMyFVWfzSdFOyIw6YWZnto6nm+DHM3YPHD8q4ZapEjoIVLGty05a1y+wN3G36NpQhq2WilvdKso/XJlguHfh+6Ne/+XAsarONzSs8Aw923w0iN1VRGuCuIQyJcdEJKt1oMBPdDN3bKae47eQKBgQCt1xVKNtS0p0gXVEgSu/FzZDuDMGPaGzSwBAiumMIaIx0J/Sld1YYwpdUfAwMb2gLBt89QRE/1YGfwIwXBH3G8/J7W2/jjMSMXNO2PfbeTqEB5SQQ4PqGkl1a7pLqmeYq+/QvH77bhbLODvIF48IrHsoMGeu/HnnSH9aF70U2fpwKBgQD4M3XulqP+/hEPe4zX1ZniE0hvGN46WDDZS/9tawmyMK8//9Gk5yF6Gq3fHc0cAVEZI/DzcFdsiCy3RjiKHl50tpEZgsVUA3XfH++2mD7KC5JKjCR7vvDl3nihsS1A/tFYR/hZS2JaW25tbl65oyieojP2Heo2jGKli4vNCcRvuQKBgBAjW3KcTHrI3MRtqpMyp2OS9oNjRqHEzMtaVIQA4mQSxmta+TAg1mhFvNc4LF21i3P4YGKPNO0OmheM6MfY53In8Yb4vM46jYtl+nfLr/MhN3sK8D1YcyEYe5DH5hv2RI8eZq2U72FV+gRMx6g2jGulgsxjNUdOynqkru0TrSUZAoGBAJ3xVSnilF0ro+Q+f4UpUcyyoGD4Tz2RhpW1EzEg9nJn1Iemcz5GP3yhZ4/wryzqFDhG2Cdt4AR41kmCSdQt12o0K7c0jKN+0GDD/QyOlhYnnTfHCq/LRJQVOdeXiaUuIsH8s1FhnizwgTRylZLBaa1nED1X2IrlRQZWLim4vOec
#    #    支付宝公钥
#    alipay_public_key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAly1cHQo5mMudSB2Ty0ioP8DXdA/RwMLGd0dvC6s6u8POmqv/K7mpATRSmy67Byp3dPfYH29ZAlWqsnlz+PARC7XbKDrSimVI1EpFeC2M79oX9AgkuMTJP6al+ULGvXeRZyF9CkAtWx2yaGpODKrzbhqJxFKRqSp8fWhejLZpNhmIK7i9FVKaM1zNMH2umGF/4uOp1WHxNcKCev1phq4Qn9511cVA3XH/zoJuAcenjfymifcflyfbSqtZpNSS2RohRhrhewav4cIPu3tLzdd6kOlhoFDHMJUT+HIVzI5nxvsTFyAR2Cts4KqvuO1FDCXyeJ54XQw4y0x8Rm0UrASRcQIDAQAB
#    #    支付宝网关
#    gatewayUrl: https://openapi.alipay.com/gateway.do
#    #    字符编码
#    charset: utf-8
#    #    签名方式
#    sign_type: RSA2
#    #    异步验签
#    notify_url: http://inks.tpddns.net:8081/system/SYSM10B2/notifyPayResult
#    #    同步跳转
#    return_url: http://www.inkstech.com
  mail:
    username: <EMAIL>
    password: ASDqwe@!@#
    host: smtp.qiye.aliyun.com
    default-encoding: UTF-8
    properties:
      mail.smtp.socketFactory.fallback: true
      mail.smtp.starttls.enable: true
#    toEmail: <EMAIL>  #登录日志的收件人
    toEmail: <EMAIL>  #登录日志的收件人
    ipAddress: oms96  #ip地址

mybatis:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  type-aliases-package: inks.system.**.domian
  #配置打印SQL语句到控制台
#雪花算法:  数据中心id,工作机器id
snowflake:
  dataCenterId: 1
  workerId: 1
logging:
  file:
    name: ./slow_sql.log # 相对路径指向resource下的logs文件夹
  level:
    # 确保慢SQL的日志级别高于其他日志级别
    root: INFO
    # 设置Druid连接池的日志级别
    com.alibaba.druid: trace

seetaface6:
  # 对象池，这里没有做配置，用的common-pool默认的配置
  pool:
  #模型配置
  model:
    base: D:\face\models #基础路径
    face_recognizer: ${seetaface6.model.base}/face_recognizer.csta
    face_recognizer_mask: ${seetaface6.model.base}/face_recognizer_mask.csta
    face_recognizer_light: ${seetaface6.model.base}/face_recognizer_light.csta
    age_predictor: ${seetaface6.model.base}/age_predictor.csta
    face_landmarker_pts5: ${seetaface6.model.base}/face_landmarker_pts5.csta
    face_landmarker_pts68: ${seetaface6.model.base}/face_landmarker_pts68.csta
    pose_estimation: ${seetaface6.model.base}/pose_estimation.csta
    eye_state: ${seetaface6.model.base}/eye_state.csta
    face_detector: ${seetaface6.model.base}/face_detector.csta
    face_landmarker_mask_pts5: ${seetaface6.model.base}/face_landmarker_mask_pts5.csta
    mask_detector: ${seetaface6.model.base}/mask_detector.csta
    gender_predictor: ${seetaface6.model.base}/gender_predictor.csta
    quality_lbn: ${seetaface6.model.base}/quality_lbn.csta
    fas_first: ${seetaface6.model.base}/fas_first.csta
    fas_second: ${seetaface6.model.base}/fas_second.csta

