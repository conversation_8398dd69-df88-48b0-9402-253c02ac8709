<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CiloginlogMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CiloginlogPojo">
        select id,
               Userid,
               UserName,
               RealName,
               IpAddr,
               LoginLocation,
               BrowserName,
               HostSystem,
               Direction,
               LoginStatus,
               LoginMsg,
               LoginTime,
               Tenantid,
               TenantName
        from CiLoginLog
        where CiLoginLog.id = #{key}
    </select>
    <sql id="selectCiloginlogVo">
        select id,
               Userid,
               UserName,
               RealName,
               IpAddr,
               LoginLocation,
               BrowserName,
               HostSystem,
               Direction,
               LoginStatus,
               LoginMsg,
               LoginTime,
               Tenantid,
               TenantName
        from CiLoginLog
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CiloginlogPojo">
        <include refid="selectCiloginlogVo"/>
        where 1 = 1
        <if test="tenantid != 'default' ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiLoginLog.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.id != null and SearchPojo.id  != ''">
            and CiLoginLog.id like concat('%', #{SearchPojo.id}, '%')
        </if>
        <if test="SearchPojo.userid != null and SearchPojo.userid  != ''">
            and CiLoginLog.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.username != null and SearchPojo.username  != ''">
            and CiLoginLog.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null and SearchPojo.realname  != ''">
            and CiLoginLog.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.ipaddr != null and SearchPojo.ipaddr  != ''">
            and CiLoginLog.IpAddr like concat('%', #{SearchPojo.ipaddr}, '%')
        </if>
        <if test="SearchPojo.loginlocation != null and SearchPojo.loginlocation  != ''">
            and CiLoginLog.LoginLocation like concat('%', #{SearchPojo.loginlocation}, '%')
        </if>
        <if test="SearchPojo.browsername != null and SearchPojo.browsername  != ''">
            and CiLoginLog.BrowserName like concat('%', #{SearchPojo.browsername}, '%')
        </if>
        <if test="SearchPojo.hostsystem != null and SearchPojo.hostsystem  != ''">
            and CiLoginLog.HostSystem like concat('%', #{SearchPojo.hostsystem}, '%')
        </if>
        <if test="SearchPojo.direction != null and SearchPojo.direction  != ''">
            and CiLoginLog.Direction like concat('%', #{SearchPojo.direction}, '%')
        </if>
        <if test="SearchPojo.loginmsg != null and SearchPojo.loginmsg  != ''">
            and CiLoginLog.LoginMsg like concat('%', #{SearchPojo.loginmsg}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.id != null and SearchPojo.id != ''">
            or CiLoginLog.id like concat('%', #{SearchPojo.id}, '%')
        </if>
        <if test="SearchPojo.userid != null and SearchPojo.userid != ''">
            or CiLoginLog.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.username != null and SearchPojo.username != ''">
            or CiLoginLog.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null and SearchPojo.realname != ''">
            or CiLoginLog.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.ipaddr != null and SearchPojo.ipaddr != ''">
            or CiLoginLog.IpAddr like concat('%', #{SearchPojo.ipaddr}, '%')
        </if>
        <if test="SearchPojo.loginlocation != null and SearchPojo.loginlocation != ''">
            or CiLoginLog.LoginLocation like concat('%', #{SearchPojo.loginlocation}, '%')
        </if>
        <if test="SearchPojo.browsername != null and SearchPojo.browsername != ''">
            or CiLoginLog.BrowserName like concat('%', #{SearchPojo.browsername}, '%')
        </if>
        <if test="SearchPojo.hostsystem != null and SearchPojo.hostsystem != ''">
            or CiLoginLog.HostSystem like concat('%', #{SearchPojo.hostsystem}, '%')
        </if>
        <if test="SearchPojo.direction != null and SearchPojo.direction != ''">
            or CiLoginLog.Direction like concat('%', #{SearchPojo.direction}, '%')
        </if>
        <if test="SearchPojo.loginmsg != null and SearchPojo.loginmsg != ''">
            or CiLoginLog.LoginMsg like concat('%', #{SearchPojo.loginmsg}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into CiLoginLog(id, Userid, UserName, RealName, IpAddr, LoginLocation, BrowserName, HostSystem,
                               Direction, LoginStatus, LoginMsg, LoginTime,Tenantid,TenantName)
        values (#{id}, #{userid}, #{username}, #{realname}, #{ipaddr}, #{loginlocation}, #{browsername}, #{hostsystem},
                #{direction}, #{loginstatus}, #{loginmsg}, #{logintime},#{tenantid},#{tenantname})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiLoginLog
        <set>
            <if test="id != null ">
                id =#{id},
            </if>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="ipaddr != null ">
                IpAddr =#{ipaddr},
            </if>
            <if test="loginlocation != null ">
                LoginLocation =#{loginlocation},
            </if>
            <if test="browsername != null ">
                BrowserName =#{browsername},
            </if>
            <if test="hostsystem != null ">
                HostSystem =#{hostsystem},
            </if>
            <if test="direction != null ">
                Direction =#{direction},
            </if>
            <if test="loginstatus != null">
                LoginStatus =#{loginstatus},
            </if>
            <if test="loginmsg != null ">
                LoginMsg =#{loginmsg},
            </if>
            <if test="logintime != null">
                LoginTime =#{logintime},
            </if>
            <if test="tenantid != null ">
                Tenantid =#{tenantid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
        </set>
        where id= #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiLoginLog
        where id= #{key}
    </delete>

    <!--查询指定行数据-->
    <select id="getCountListByPro" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT left(LoginLocation,3) as name, Count(0) as value
        FROM CiLoginLog
        where LoginLocation is not null and LoginLocation<![CDATA[ <> ]]>''
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and (CiLoginLog.LoginTime BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and (${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
            </if>
        </if>
        Group by left(LoginLocation,3)
        order by left(LoginLocation,3)
    </select>

    <!--查询指定行数据-->
    <select id="getCountListByCity" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.common.core.domain.ChartPojo">
        SELECT right(LoginLocation,3) as name, Count(0) as value
        FROM CiLoginLog
        where LoginLocation is not null and LoginLocation<![CDATA[ <> ]]>''
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and (CiLoginLog.LoginTime BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and (${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
            </if>
        </if>
        <if test="SearchPojo.loginlocation != null and SearchPojo.loginlocation  != ''">
            and CiLoginLog.LoginLocation like concat('%', #{SearchPojo.loginlocation}, '%')
        </if>
        Group by right(LoginLocation,3)
        order by right(LoginLocation,3)
    </select>
    <!--    通过租户id查询租户下登录日志-->
    <select id="getCountPageListByTen" parameterType="inks.common.core.domain.QueryParam" resultType="java.util.Map">
        SELECT
            RealName AS 用户名,
            UserName AS 登录账号,
            SUM(CASE WHEN LoginStatus = 0 THEN 1 ELSE 0 END) AS 登录成功次数,
            SUM(CASE WHEN LoginStatus = 1 THEN 1 ELSE 0 END) AS 登录失败次数,
            MAX(LoginLocation) AS 登录地点,
            MAX(BrowserName) AS 浏览器名称,
            MAX(HostSystem) AS 操作系统,
            MAX(LoginTime) AS 最近访问时间,
            MAX(TenantName) AS 租户名称
        FROM CiLoginLog
        WHERE (LoginMsg LIKE '用户%' or LoginMsg LIKE 'Scm%' or LoginMsg LIKE 'Dms%')
          and (CiLoginLog.LoginTime BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
          and Tenantid = #{tenantid}
        GROUP BY
            Userid,RealName,UserName
        ORDER BY
            MAX(LoginTime) DESC
    </select>
<!--    统计时间段内每个admin用户登录情况-->
    <select id="getEveryAdminLogin" resultType="java.util.Map">
        SELECT
            RealName AS 用户名,
            UserName AS 登录账号,
            SUM(CASE WHEN LoginStatus = 0 THEN 1 ELSE 0 END) AS 登录成功次数,
            SUM(CASE WHEN LoginStatus = 1 THEN 1 ELSE 0 END) AS 登录失败次数,
            MAX(LoginLocation) AS 登录地点,
            MAX(BrowserName) AS 浏览器名称,
            MAX(HostSystem) AS 操作系统,
            MAX(LoginTime) AS 最近访问时间,
            MAX(TenantName) AS 租户名称
        FROM CiLoginLog
        WHERE LoginMsg LIKE '管理员%'
          and (CiLoginLog.LoginTime BETWEEN #{startdate} AND #{enddate})
        GROUP BY
            Userid,RealName,UserName
        ORDER BY
            MAX(LoginTime) DESC
    </select>
    <!--    统计时间段内每个普通用户登录情况-->
    <select id="getEveryUserLogin" resultType="java.util.Map">
        SELECT
            RealName AS 用户名,
            UserName AS 登录账号,
            SUM(CASE WHEN LoginStatus = 0 THEN 1 ELSE 0 END) AS 登录成功次数,
            SUM(CASE WHEN LoginStatus = 1 THEN 1 ELSE 0 END) AS 登录失败次数,
            MAX(LoginLocation) AS 登录地点,
            MAX(BrowserName) AS 浏览器名称,
            MAX(HostSystem) AS 操作系统,
            MAX(LoginTime) AS 最近访问时间,
            MAX(TenantName) AS 租户名称
        FROM CiLoginLog
        WHERE (LoginMsg LIKE '用户%' or LoginMsg LIKE 'Scm%' or LoginMsg LIKE 'Dms%')
          and (CiLoginLog.LoginTime BETWEEN #{startdate} AND #{enddate})
        GROUP BY
            Userid,RealName,UserName
        ORDER BY
            MAX(LoginTime) DESC
    </select>

    <select id="getEveryTenantLogin" resultType="java.util.Map">
        SELECT
            Tenantid AS 租户id,
            TenantName AS 租户名称,
            SUM(CASE WHEN LoginStatus = 0 THEN 1 ELSE 0 END) AS 登录成功次数,
            SUM(CASE WHEN LoginStatus = 1 THEN 1 ELSE 0 END) AS 登录失败次数,
            MAX(LoginLocation) AS 登录地点,
            MAX(BrowserName) AS 浏览器名称,
            MAX(HostSystem) AS 操作系统,
            MAX(LoginTime) AS 最近访问时间
        FROM CiLoginLog
          WHERE (CiLoginLog.LoginTime BETWEEN #{startdate} AND #{enddate})
        GROUP BY
            Tenantid,TenantName
        ORDER BY
            MAX(LoginTime) DESC
    </select>


    <select id="getAllAdminLogin" resultType="inks.system.domain.pojo.CiloginlogPojo">
        select id,
               Userid,
               UserName,
               RealName,
               IpAddr,
               LoginLocation,
               BrowserName,
               HostSystem,
               Direction,
               LoginStatus,
               LoginMsg,
               LoginTime,
               Tenantid,
               TenantName
        from CiLoginLog
        WHERE LoginMsg LIKE '管理员%'
          and (CiLoginLog.LoginTime BETWEEN #{startdate} AND #{enddate})
        ORDER BY LoginTime desc, Userid ASC
    </select>

    <select id="getAllUserLogin" resultType="inks.system.domain.pojo.CiloginlogPojo">
        select id,
               Userid,
               UserName,
               RealName,
               IpAddr,
               LoginLocation,
               BrowserName,
               HostSystem,
               Direction,
               LoginStatus,
               LoginMsg,
               LoginTime,
               Tenantid,
               TenantName
        from CiLoginLog
        WHERE (LoginMsg LIKE '用户%' or LoginMsg LIKE 'Scm%' or LoginMsg LIKE 'Dms%')
          and (CiLoginLog.LoginTime BETWEEN #{startdate} AND #{enddate})
        ORDER BY
            LoginTime desc,Userid ASC
    </select>

    <delete id="deleteByTime">
        delete
        from CiLoginLog
        where 1 = 1
        <if test="tenantid != null and tenantid != ''">
            and Tenantid = #{tenantid}
        </if>
        <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
            and CiLoginLog.LoginTime BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
        </if>
        <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
            and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
        </if>
    </delete>

    <select id="getCountSuccessByUser" resultType="java.util.Map">
        SELECT realname,
               SUM(IF(LoginStatus = 0, 1, 0)) AS success
        FROM CiLoginLog
        WHERE Tenantid = #{tenantid}
          and (CiLoginLog.LoginTime BETWEEN #{startDate} AND #{endDate})
        GROUP BY Userid, RealName
        ORDER BY success DESC
    </select>
</mapper>

