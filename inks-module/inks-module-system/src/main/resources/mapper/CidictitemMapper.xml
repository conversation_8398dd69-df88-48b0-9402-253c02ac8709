<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CidictitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CidictitemPojo">
        select id,
        Pid,
        DictCode,
        DictValue,
        Essential,
        CssClass,
        RowNum,
        DefaultMark,
        EnabledMark,
        Remark,
        BackgroundColor,
        FontColor,
        Icon,
        Custom1,
        Custom2,
        Custom3,
        Custom4,
        Custom5,
        Tenantid,
        Revision
        from CiDictItem
        where CiDictItem.id = #{key}
        and CiDictItem.Tenantid = #{tid}
    </select>
    <sql id="selectCidictitemVo">
        select id,
               Pid,
               DictCode,
               DictValue,
               Essential,
               CssClass,
               <PERSON><PERSON><PERSON>,
               DefaultMark,
               EnabledMark,
               Remark,
               BackgroundColor,
               FontColor,
               Icon,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               Revision
        from CiDictItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.CidictitemPojo">
        <include refid="selectCidictitemVo"/>
        where 1 = 1 and CiDictItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiDictItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by #{orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and CiDictItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.dictcode != null and SearchPojo.dictcode != ''">
            and CiDictItem.dictcode like concat('%', #{SearchPojo.dictcode}, '%')
        </if>
        <if test="SearchPojo.dictvalue != null and SearchPojo.dictvalue != ''">
            and CiDictItem.dictvalue like concat('%', #{SearchPojo.dictvalue}, '%')
        </if>
        <if test="SearchPojo.cssclass != null and SearchPojo.cssclass != ''">
            and CiDictItem.cssclass like concat('%', #{SearchPojo.cssclass}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and CiDictItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and CiDictItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and CiDictItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and CiDictItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and CiDictItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and CiDictItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or CiDictItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.dictcode != null and SearchPojo.dictcode != ''">
                or CiDictItem.DictCode like concat('%', #{SearchPojo.dictcode}, '%')
            </if>
            <if test="SearchPojo.dictvalue != null and SearchPojo.dictvalue != ''">
                or CiDictItem.DictValue like concat('%', #{SearchPojo.dictvalue}, '%')
            </if>
            <if test="SearchPojo.cssclass != null and SearchPojo.cssclass != ''">
                or CiDictItem.CssClass like concat('%', #{SearchPojo.cssclass}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or CiDictItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or CiDictItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or CiDictItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or CiDictItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or CiDictItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or CiDictItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.system.domain.pojo.CidictitemPojo">
        select id,
        Pid,
        DictCode,
        DictValue,
        Essential,
        CssClass,
        RowNum,
        DefaultMark,
        EnabledMark,
        Remark,
        BackgroundColor,
        FontColor,
        Icon,
        Custom1,
        Custom2,
        Custom3,
        Custom4,
        Custom5,
        Tenantid,
        Revision
        from CiDictItem
        where CiDictItem.Pid = #{Pid}
        and CiDictItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert" >
        insert into CiDictItem(id, Pid, DictCode, DictValue, Essential, CssClass, RowNum, DefaultMark, EnabledMark,
        Remark, BackgroundColor,
        FontColor,
        Icon, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{pid}, #{dictcode}, #{dictvalue}, #{essential}, #{cssclass}, #{rownum}, #{defaultmark},
        #{enabledmark}, #{remark}, #{backgroundcolor}, #{fontcolor}, #{icon},
        #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid},
        #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiDictItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="dictcode != null ">
                DictCode = #{dictcode},
            </if>
            <if test="dictvalue != null ">
                DictValue = #{dictvalue},
            </if>
            <if test="essential != null">
                Essential = #{essential},
            </if>
            <if test="cssclass != null ">
                CssClass = #{cssclass},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="defaultmark != null">
                DefaultMark = #{defaultmark},
            </if>
            <if test="enabledmark != null">
                EnabledMark = #{enabledmark},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="backgroundcolor != null ">
                BackgroundColor = #{backgroundcolor},
            </if>
            <if test="fontcolor != null ">
                FontColor = #{fontcolor},
            </if>
            <if test="icon != null ">
                Icon = #{icon},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from CiDictItem where id = #{key} and Tenantid=#{tid}
    </delete>

</mapper>

