<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PifunctionwarnMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PifunctionwarnPojo">
        select id,
               Functionid,
               FunctionCode,
               FunctionName,
               Warnid,
               WarnCode,
               WarnName,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Revision
        from PiFunctionWarn
        where PiFunctionWarn.id = #{key}
    </select>
    <sql id="selectPifunctionwarnVo">
        select id,
               Functionid,
               FunctionCode,
               FunctionName,
               Warnid,
               WarnCode,
               WarnName,
               Remark,
               CreateBy,
               CreateByid,
               <PERSON>reate<PERSON><PERSON>,
               <PERSON><PERSON>,
               <PERSON><PERSON><PERSON>,
               ModifyDate,
               Revision
        from PiFunctionWarn
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PifunctionwarnPojo">
        <include refid="selectPifunctionwarnVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiFunctionWarn.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.functionid != null ">
            and PiFunctionWarn.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null ">
            and PiFunctionWarn.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null ">
            and PiFunctionWarn.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.warnid != null ">
            and PiFunctionWarn.Warnid like concat('%', #{SearchPojo.warnid}, '%')
        </if>
        <if test="SearchPojo.warncode != null ">
            and PiFunctionWarn.WarnCode like concat('%', #{SearchPojo.warncode}, '%')
        </if>
        <if test="SearchPojo.warnname != null ">
            and PiFunctionWarn.WarnName like concat('%', #{SearchPojo.warnname}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and PiFunctionWarn.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and PiFunctionWarn.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and PiFunctionWarn.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and PiFunctionWarn.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and PiFunctionWarn.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.functionid != null ">
                or PiFunctionWarn.Functionid like concat('%', #{SearchPojo.functionid}, '%')
            </if>
            <if test="SearchPojo.functioncode != null ">
                or PiFunctionWarn.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
            </if>
            <if test="SearchPojo.functionname != null ">
                or PiFunctionWarn.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
            </if>
            <if test="SearchPojo.warnid != null ">
                or PiFunctionWarn.Warnid like concat('%', #{SearchPojo.warnid}, '%')
            </if>
            <if test="SearchPojo.warncode != null ">
                or PiFunctionWarn.WarnCode like concat('%', #{SearchPojo.warncode}, '%')
            </if>
            <if test="SearchPojo.warnname != null ">
                or PiFunctionWarn.WarnName like concat('%', #{SearchPojo.warnname}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or PiFunctionWarn.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or PiFunctionWarn.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or PiFunctionWarn.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or PiFunctionWarn.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or PiFunctionWarn.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiFunctionWarn(id, Functionid, FunctionCode, FunctionName, Warnid, WarnCode, WarnName, Remark,
                                   CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision)
        values (#{id}, #{functionid}, #{functioncode}, #{functionname}, #{warnid}, #{warncode}, #{warnname}, #{remark},
                #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiFunctionWarn
        <set>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="warnid != null ">
                Warnid =#{warnid},
            </if>
            <if test="warncode != null ">
                WarnCode =#{warncode},
            </if>
            <if test="warnname != null ">
                WarnName =#{warnname},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiFunctionWarn
        where id = #{key}
    </delete>

    <!--查询List-->
    <select id="getListByFunction" resultType="inks.system.domain.pojo.PifunctionwarnPojo">
        <include refid="selectPifunctionwarnVo"/>
        where PiFunctionWarn.Functionid =#{key}
        order by PiFunctionWarn.WarnCode
    </select>
</mapper>

