<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PiuserroleMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PiuserrolePojo">
        SELECT
            PiUser.UserName,
            PiUser.RealName,
            PiUser.NickName,
            PiUserRole.id,
            PiUserRole.Roleid,
            PiUserRole.Userid,
            PiUserRole.Lister,
            PiUserRole.CreateDate,
            PiUserRole.ModifyDate,
            PiUserRole.Tenantid,
            PiRole.RoleCode,
            PiRole.RoleName,
            PiUserRole.RowNum,
            PiUserRole.CreateBy,
            PiUserRole.CreateByid,
            PiUserRole.Listerid,
            PiUserRole.TenantName,
            PiUserRole.Revision
        FROM
            PiUser
                RIGHT JOIN PiUserRole ON PiUserRole.Userid = PiUser.Userid
                LEFT JOIN PiRole ON PiRole.Roleid = PiUserRole.Roleid
        where PiUserRole.id = #{key}
          and PiUserRole.Tenantid = #{tid}
    </select>
    <sql id="selectPiuserroleVo">
        SELECT
            PiUser.UserName,
            PiUser.RealName,
            PiUser.NickName,
            PiUserRole.id,
            PiUserRole.Roleid,
            PiUserRole.Userid,
            PiUserRole.Lister,
            PiUserRole.CreateDate,
            PiUserRole.ModifyDate,
            PiUserRole.Tenantid,
            PiRole.RoleCode,
            PiRole.RoleName,
            PiUserRole.RowNum,
            PiUserRole.CreateBy,
            PiUserRole.CreateByid,
            PiUserRole.Listerid,
            PiUserRole.TenantName,
            PiUserRole.Revision
        FROM
            PiUser
                RIGHT JOIN PiUserRole ON PiUserRole.Userid = PiUser.Userid
                LEFT JOIN PiRole ON PiRole.Roleid = PiUserRole.Roleid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PiuserrolePojo">
        <include refid="selectPiuserroleVo"/>
        where 1 = 1 and PiUserRole.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiUserRole.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.roleid != null ">
            and PiUserRole.Roleid like concat('%', #{SearchPojo.roleid}, '%')
        </if>
        <if test="SearchPojo.userid != null ">
            and PiUserRole.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and PiUserRole.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and PiUserRole.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and PiUserRole.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and PiUserRole.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and PiUserRole.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.roleid != null ">
                or PiUserRole.Roleid like concat('%', #{SearchPojo.roleid}, '%')
            </if>
            <if test="SearchPojo.userid != null ">
                or PiUserRole.Userid like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or PiUserRole.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or PiUserRole.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or PiUserRole.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or PiUserRole.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or PiUserRole.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiUserRole(id, Roleid, Userid, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid,
                               ModifyDate, Tenantid, TenantName, Revision)
        values (#{id}, #{roleid}, #{userid}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister},
                #{listerid}, #{modifydate}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiUserRole
        <set>
            <if test="roleid != null ">
                Roleid =#{roleid},
            </if>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiUserRole
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--查询List-->
    <select id="getListByRole" resultType="inks.system.domain.pojo.PiuserrolePojo">
        <include refid="selectPiuserroleVo"/>
        where 1 = 1 and PiUserRole.Roleid =#{key}
        order by PiRole.RoleCode
    </select>

    <!--查询List-->
    <select id="getListByUser" resultType="inks.system.domain.pojo.PiuserrolePojo">
        <include refid="selectPiuserroleVo"/>
        where 1 = 1 and PiUserRole.Userid =#{key}
        order by PiUser.UserName
    </select>

    <!--查询List-->
    <select id="getPermByUser" resultType="inks.system.domain.pojo.PipermcodePojo">
        SELECT DISTINCT
            PiPermCode.Permid,
            PiPermCode.Parentid,
            PiPermCode.PermType,
            PiPermCode.PermCode,
            PiPermCode.PermName,
            PiPermCode.RowNum,
            PiPermCode.IsPublic,
            PiPermCode.EnabledMark,
            PiPermCode.AllowDelete,
            PiPermCode.Remark,
            PiPermCode.CreateBy,
            PiPermCode.CreateByid,
            PiPermCode.CreateDate,
            PiPermCode.Lister,
            PiPermCode.Listerid,
            PiPermCode.ModifyDate,
            PiPermCode.Revision
        FROM
            PiUserRole
                RIGHT JOIN PiRole ON PiUserRole.Roleid = PiRole.Roleid
                RIGHT JOIN PiPermission ON PiRole.Roleid = PiPermission.Resourceid
                RIGHT JOIN PiPermCode ON PiPermission.Permid = PiPermCode.Permid
        where PiUserRole.Userid =#{key} and PiUserRole.Tenantid =#{tid}
    </select>

    <select id="getUseridsByRoleid" resultType="java.lang.String">
        SELECT DISTINCT Userid
        FROM PiUserRole
        where Roleid =#{resourceid} and Tenantid =#{tid}
    </select>


    <select id="getRoleidsByUserid" resultType="java.lang.String">
        SELECT DISTINCT Roleid
        FROM PiUserRole
        where Userid =#{userid} and Tenantid =#{tid}
    </select>
</mapper>

