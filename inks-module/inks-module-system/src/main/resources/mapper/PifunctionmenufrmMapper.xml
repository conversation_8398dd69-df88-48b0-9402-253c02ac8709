<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PifunctionmenufrmMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PifunctionmenufrmPojo">
        <include refid="selectPifunctionmenufrmVo"/>
        where PiFunctionMenuFrm.id = #{key} and PiFunctionMenuFrm.Tenantid=#{tid}
    </select>
    <sql id="selectPifunctionmenufrmVo">
        select PiFunctionMenuFrm.id,
               PiFunctionMenuFrm.Functionid,
               PiFunctionMenuFrm.FunctionCode,
               PiFunctionMenuFrm.FunctionName,
               PiFunctionMenuFrm.Navid,
               PiFunctionMenuFrm.NavCode,
               PiFunctionMenuFrm.NavName,
               PiFunctionMenuFrm.Remark,
               PiFunctionMenuFrm.CreateBy,
               PiFunctionMenuFrm.CreateByid,
               PiFunctionMenuFrm.CreateDate,
               PiFunctionMenuFrm.Lister,
               PiFunctionMenuFrm.Listerid,
               PiFunctionMenuFrm.ModifyDate,
               PiFunctionMenuFrm.Tenantid,
               PiFunctionMenuFrm.Revision,
               PiMenuFrm.NavType,
               PiMenuFrm.NavPid,
               PiMenuFrm.PageIndex
        from PiFunctionMenuFrm
                lEFT JOIN PiMenuFrm ON PiMenuFrm.Navid = PiFunctionMenuFrm.Navid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.PifunctionmenufrmPojo">
        <include refid="selectPifunctionmenufrmVo"/>
         where 1 = 1 and PiFunctionMenuFrm.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and PiFunctionMenuFrm.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.functionid != null ">
   and PiFunctionMenuFrm.Functionid like concat('%', #{SearchPojo.functionid}, '%')
</if>
<if test="SearchPojo.functioncode != null ">
   and PiFunctionMenuFrm.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
</if>
<if test="SearchPojo.functionname != null ">
   and PiFunctionMenuFrm.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
</if>
<if test="SearchPojo.navid != null ">
   and PiFunctionMenuFrm.Navid like concat('%', #{SearchPojo.navid}, '%')
</if>
<if test="SearchPojo.navcode != null ">
   and PiFunctionMenuFrm.NavCode like concat('%', #{SearchPojo.navcode}, '%')
</if>
<if test="SearchPojo.navname != null ">
   and PiFunctionMenuFrm.NavName like concat('%', #{SearchPojo.navname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and PiFunctionMenuFrm.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and PiFunctionMenuFrm.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and PiFunctionMenuFrm.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and PiFunctionMenuFrm.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and PiFunctionMenuFrm.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.functionid != null ">
   or PiFunctionMenuFrm.Functionid like concat('%', #{SearchPojo.functionid}, '%')
</if>
<if test="SearchPojo.functioncode != null ">
   or PiFunctionMenuFrm.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
</if>
<if test="SearchPojo.functionname != null ">
   or PiFunctionMenuFrm.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
</if>
<if test="SearchPojo.navid != null ">
   or PiFunctionMenuFrm.Navid like concat('%', #{SearchPojo.navid}, '%')
</if>
<if test="SearchPojo.navcode != null ">
   or PiFunctionMenuFrm.NavCode like concat('%', #{SearchPojo.navcode}, '%')
</if>
<if test="SearchPojo.navname != null ">
   or PiFunctionMenuFrm.NavName like concat('%', #{SearchPojo.navname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or PiFunctionMenuFrm.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or PiFunctionMenuFrm.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or PiFunctionMenuFrm.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or PiFunctionMenuFrm.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or PiFunctionMenuFrm.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into PiFunctionMenuFrm(id, Functionid, FunctionCode, FunctionName, Navid, NavCode, NavName, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, Revision)
        values (#{id}, #{functionid}, #{functioncode}, #{functionname}, #{navid}, #{navcode}, #{navname}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update PiFunctionMenuFrm
        <set>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="navid != null ">
                Navid =#{navid},
            </if>
            <if test="navcode != null ">
                NavCode =#{navcode},
            </if>
            <if test="navname != null ">
                NavName =#{navname},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from PiFunctionMenuFrm where id = #{key} and Tenantid=#{tid}
    </delete>

    <select id="getListByFunction" resultType="inks.system.domain.pojo.PifunctionmenuwebPojo">
        <include refid="selectPifunctionmenufrmVo"/>
        where 1 = 1 and PiFunctionMenuFrm.Functionid =#{key}
        order by PiFunctionMenuFrm.NavCode
    </select>
</mapper>

