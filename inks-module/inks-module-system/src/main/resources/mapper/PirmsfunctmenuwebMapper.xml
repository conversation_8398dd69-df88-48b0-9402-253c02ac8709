<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PirmsfunctmenuwebMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PirmsfunctmenuwebPojo">
        select id,
        RmsFunctid,
        RmsFunctCode,
        RmsFunctName,
        Navid,
        NavCode,
        NavName,
        Remark,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        Listerid,
        ModifyDate,
        Revision
        from PiRmsFunctMenuWeb
        where PiRmsFunctMenuWeb.id = #{key}
    </select>
    <sql id="selectPirmsfunctmenuwebVo">
        select id,
               RmsFunctid,
               RmsFunctCode,
               RmsFunctName,
               <PERSON>vid,
               Nav<PERSON><PERSON>,
               NavName,
               Remark,
               Create<PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               <PERSON>reateDate,
               Lister,
               Listerid,
               ModifyDate,
               Revision
        from PiRmsFunctMenuWeb
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PirmsfunctmenuwebPojo">
        <include refid="selectPirmsfunctmenuwebVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and PiRmsFunctMenuWeb.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.rmsfunctid != null">
            and PiRmsFunctMenuWeb.RmsFunctid like concat('%', #{SearchPojo.rmsfunctid}, '%')
        </if>
        <if test="SearchPojo.rmsfunctcode != null">
            and PiRmsFunctMenuWeb.RmsFunctCode like concat('%', #{SearchPojo.rmsfunctcode}, '%')
        </if>
        <if test="SearchPojo.rmsfunctname != null">
            and PiRmsFunctMenuWeb.RmsFunctName like concat('%', #{SearchPojo.rmsfunctname}, '%')
        </if>
        <if test="SearchPojo.navid != null">
            and PiRmsFunctMenuWeb.Navid like concat('%', #{SearchPojo.navid}, '%')
        </if>
        <if test="SearchPojo.navcode != null">
            and PiRmsFunctMenuWeb.NavCode like concat('%', #{SearchPojo.navcode}, '%')
        </if>
        <if test="SearchPojo.navname != null">
            and PiRmsFunctMenuWeb.NavName like concat('%', #{SearchPojo.navname}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and PiRmsFunctMenuWeb.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and PiRmsFunctMenuWeb.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and PiRmsFunctMenuWeb.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and PiRmsFunctMenuWeb.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and PiRmsFunctMenuWeb.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.rmsfunctid != null">
                or PiRmsFunctMenuWeb.RmsFunctid like concat('%', #{SearchPojo.rmsfunctid}, '%')
            </if>
            <if test="SearchPojo.rmsfunctcode != null">
                or PiRmsFunctMenuWeb.RmsFunctCode like concat('%', #{SearchPojo.rmsfunctcode}, '%')
            </if>
            <if test="SearchPojo.rmsfunctname != null">
                or PiRmsFunctMenuWeb.RmsFunctName like concat('%', #{SearchPojo.rmsfunctname}, '%')
            </if>
            <if test="SearchPojo.navid != null">
                or PiRmsFunctMenuWeb.Navid like concat('%', #{SearchPojo.navid}, '%')
            </if>
            <if test="SearchPojo.navcode != null">
                or PiRmsFunctMenuWeb.NavCode like concat('%', #{SearchPojo.navcode}, '%')
            </if>
            <if test="SearchPojo.navname != null">
                or PiRmsFunctMenuWeb.NavName like concat('%', #{SearchPojo.navname}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or PiRmsFunctMenuWeb.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or PiRmsFunctMenuWeb.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or PiRmsFunctMenuWeb.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or PiRmsFunctMenuWeb.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or PiRmsFunctMenuWeb.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiRmsFunctMenuWeb(id, RmsFunctid, RmsFunctCode, RmsFunctName, Navid, NavCode, NavName, Remark,
        CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision)
        values (#{id}, #{rmsfunctid}, #{rmsfunctcode}, #{rmsfunctname}, #{navid}, #{navcode}, #{navname}, #{remark},
        #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiRmsFunctMenuWeb
        <set>
            <if test="rmsfunctid != null">
                RmsFunctid =#{rmsfunctid},
            </if>
            <if test="rmsfunctcode != null">
                RmsFunctCode =#{rmsfunctcode},
            </if>
            <if test="rmsfunctname != null">
                RmsFunctName =#{rmsfunctname},
            </if>
            <if test="navid != null">
                Navid =#{navid},
            </if>
            <if test="navcode != null">
                NavCode =#{navcode},
            </if>
            <if test="navname != null">
                NavName =#{navname},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiRmsFunctMenuWeb
        where id = #{key}
    </delete>

    <select id="getListByFunction" resultType="inks.system.domain.pojo.PirmsfunctmenuwebPojo">
        SELECT
        PiMenuWeb.NavPid,
        PiMenuWeb.NavType,
        PiRmsFunctMenuWeb.id,
        PiRmsFunctMenuWeb.RmsFunctid,
        PiRmsFunctMenuWeb.RmsFunctCode,
        PiRmsFunctMenuWeb.RmsFunctName,
        PiRmsFunctMenuWeb.Navid,
        PiRmsFunctMenuWeb.NavCode,
        PiRmsFunctMenuWeb.NavName,
        PiRmsFunctMenuWeb.Remark,
        PiRmsFunctMenuWeb.CreateBy,
        PiRmsFunctMenuWeb.CreateByid,
        PiRmsFunctMenuWeb.CreateDate,
        PiRmsFunctMenuWeb.Lister,
        PiRmsFunctMenuWeb.Listerid,
        PiRmsFunctMenuWeb.ModifyDate,
        PiRmsFunctMenuWeb.Revision
        FROM
        PiMenuWeb
        RIGHT JOIN PiRmsFunctMenuWeb ON PiMenuWeb.Navid = PiRmsFunctMenuWeb.Navid
        where 1 = 1 and PiRmsFunctMenuWeb.RmsFunctid =#{key}
        order by PiRmsFunctMenuWeb.NavCode
    </select>

    <select id="getListByLoginUser" parameterType="inks.common.core.domain.LoginUser"
            resultType="inks.system.domain.pojo.PimenuwebPojo">
        SELECT DISTINCT PiMenuWeb.Navid,
        PiMenuWeb.NavPid,
        PiMenuWeb.NavType,
        PiMenuWeb.NavCode,
        PiMenuWeb.NavName,
        PiMenuWeb.NavGroup,
        PiMenuWeb.RowNum,
        PiMenuWeb.ImageCss,
        PiMenuWeb.IconUrl,
        PiMenuWeb.NavigateUrl,
        PiMenuWeb.MvcUrl,
        PiMenuWeb.ModuleType,
        PiMenuWeb.ModuleCode,
        PiMenuWeb.RoleCode,
        PiMenuWeb.ImageIndex,
        PiMenuWeb.ImageStyle,
        PiMenuWeb.EnabledMark,
        PiMenuWeb.Remark,
        PiMenuWeb.PermissionCode,
        PiMenuWeb.Functionid,
        PiMenuWeb.FunctionCode,
        PiMenuWeb.FunctionName,
        PiMenuWeb.IsMicroApp,
        PiMenuWeb.MicroAppName,
        PiMenuWeb.MicroAppEntry,
        PiMenuWeb.MicroAppRule,
        PiMenuWeb.MicroAppLocal,
        PiMenuWeb.Lister,
        PiMenuWeb.CreateDate,
        PiMenuWeb.ModifyDate,
        PiMenuWeb.DeleteMark,
        PiMenuWeb.DeleteLister,
        PiMenuWeb.DeleteDate
        FROM PiMenuWeb
        LEFT JOIN PiRmsFunctMenuWeb ON PiRmsFunctMenuWeb.Navid = PiMenuWeb.Navid
        WHERE PiRmsFunctMenuWeb.RmsFunctid in (select RmsFunctids
        FROM PiTenantRmsUser
        WHERE PiTenantRmsUser.Userid = #{userid}
        and PiTenantRmsUser.Tenantid = #{tenantid})
        order by RowNum
    </select>
    <select id="getListByRmsFunctids" resultType="inks.system.domain.pojo.PimenuwebPojo">
        SELECT DISTINCT PiMenuWeb.Navid,
        PiMenuWeb.NavPid,
        PiMenuWeb.NavType,
        PiMenuWeb.NavCode,
        PiMenuWeb.NavName,
        PiMenuWeb.NavGroup,
        PiMenuWeb.RowNum,
        PiMenuWeb.ImageCss,
        PiMenuWeb.IconUrl,
        PiMenuWeb.NavigateUrl,
        PiMenuWeb.MvcUrl,
        PiMenuWeb.ModuleType,
        PiMenuWeb.ModuleCode,
        PiMenuWeb.RoleCode,
        PiMenuWeb.ImageIndex,
        PiMenuWeb.ImageStyle,
        PiMenuWeb.EnabledMark,
        PiMenuWeb.Remark,
        PiMenuWeb.PermissionCode,
        PiMenuWeb.Functionid,
        PiMenuWeb.FunctionCode,
        PiMenuWeb.FunctionName,
        PiMenuWeb.IsMicroApp,
        PiMenuWeb.MicroAppName,
        PiMenuWeb.MicroAppEntry,
        PiMenuWeb.MicroAppRule,
        PiMenuWeb.MicroAppLocal,
        PiMenuWeb.Lister,
        PiMenuWeb.CreateDate,
        PiMenuWeb.ModifyDate,
        PiMenuWeb.DeleteMark,
        PiMenuWeb.DeleteLister,
        PiMenuWeb.DeleteDate
        FROM PiMenuWeb
        LEFT JOIN PiRmsFunctMenuWeb ON PiRmsFunctMenuWeb.Navid = PiMenuWeb.Navid
        WHERE PiRmsFunctMenuWeb.RmsFunctid in (${ids})
        order by RowNum
    </select>
</mapper>

