<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CiemailMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CiemailPojo">
        select id,
               ModuleCode,
               EmailName,
               EmailCode,
               EmailType,
               EmailTemplate,
               DefToJson,
               DefCcJson,
               PageRow,
               RowNum,
               EnabledMark,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Listerid,
               Lister,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from CiEmail
        where CiEmail.id = #{key}
          and CiEmail.Tenantid = #{tid}
    </select>
    <sql id="selectCiemailVo">
        select id,
               ModuleCode,
               EmailName,
               EmailCode,
               EmailType,
               EmailTemplate,
               DefToJson,
               DefCcJson,
               PageRow,
               RowNum,
               EnabledMark,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Listerid,
               Lister,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from CiEmail
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CiemailPojo">
        <include refid="selectCiemailVo"/>
        where 1 = 1 and CiEmail.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiEmail.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.modulecode != null ">
            and CiEmail.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.emailname != null ">
            and CiEmail.EmailName like concat('%', #{SearchPojo.emailname}, '%')
        </if>
        <if test="SearchPojo.emailcode != null ">
            and CiEmail.EmailCode like concat('%', #{SearchPojo.emailcode}, '%')
        </if>
        <if test="SearchPojo.emailtype != null ">
            and CiEmail.EmailType like concat('%', #{SearchPojo.emailtype}, '%')
        </if>
        <if test="SearchPojo.emailtemplate != null ">
            and CiEmail.EmailTemplate like concat('%', #{SearchPojo.emailtemplate}, '%')
        </if>
        <if test="SearchPojo.deftojson != null ">
            and CiEmail.DefToJson like concat('%', #{SearchPojo.deftojson}, '%')
        </if>
        <if test="SearchPojo.defccjson != null ">
            and CiEmail.DefCcJson like concat('%', #{SearchPojo.defccjson}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and CiEmail.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and CiEmail.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and CiEmail.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and CiEmail.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and CiEmail.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and CiEmail.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and CiEmail.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and CiEmail.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and CiEmail.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and CiEmail.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and CiEmail.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.modulecode != null ">
                or CiEmail.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.emailname != null ">
                or CiEmail.EmailName like concat('%', #{SearchPojo.emailname}, '%')
            </if>
            <if test="SearchPojo.emailcode != null ">
                or CiEmail.EmailCode like concat('%', #{SearchPojo.emailcode}, '%')
            </if>
            <if test="SearchPojo.emailtype != null ">
                or CiEmail.EmailType like concat('%', #{SearchPojo.emailtype}, '%')
            </if>
            <if test="SearchPojo.emailtemplate != null ">
                or CiEmail.EmailTemplate like concat('%', #{SearchPojo.emailtemplate}, '%')
            </if>
            <if test="SearchPojo.deftojson != null ">
                or CiEmail.DefToJson like concat('%', #{SearchPojo.deftojson}, '%')
            </if>
            <if test="SearchPojo.defccjson != null ">
                or CiEmail.DefCcJson like concat('%', #{SearchPojo.defccjson}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or CiEmail.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or CiEmail.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or CiEmail.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or CiEmail.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or CiEmail.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or CiEmail.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or CiEmail.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or CiEmail.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or CiEmail.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or CiEmail.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or CiEmail.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into CiEmail(id, ModuleCode, EmailName, EmailCode, EmailType, EmailTemplate, DefToJson, DefCcJson,
                            PageRow, RowNum, EnabledMark, Remark, CreateBy, CreateByid, CreateDate, Listerid, Lister,
                            ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{modulecode}, #{emailname}, #{emailcode}, #{emailtype}, #{emailtemplate}, #{deftojson},
                #{defccjson}, #{pagerow}, #{rownum}, #{enabledmark}, #{remark}, #{createby}, #{createbyid},
                #{createdate}, #{listerid}, #{lister}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
                #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiEmail
        <set>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="emailname != null ">
                EmailName =#{emailname},
            </if>
            <if test="emailcode != null ">
                EmailCode =#{emailcode},
            </if>
            <if test="emailtype != null ">
                EmailType =#{emailtype},
            </if>
            <if test="emailtemplate != null ">
                EmailTemplate =#{emailtemplate},
            </if>
            <if test="deftojson != null ">
                DefToJson =#{deftojson},
            </if>
            <if test="defccjson != null ">
                DefCcJson =#{defccjson},
            </if>
            <if test="pagerow != null">
                PageRow =#{pagerow},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiEmail
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <select id="getListByModuleCode" resultType="inks.system.domain.pojo.CireportsPojo">
        select id,
               ModuleCode,
               EmailName,
               EmailCode,
               EmailType,
               EmailTemplate,
               DefToJson,
               DefCcJson,
               PageRow,
               RowNum,
               EnabledMark,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Listerid,
               Lister,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from CiEmail
        where ModuleCode = #{moduleCode}
          and Tenantid = #{tid}
        Order by RowNum
    </select>
</mapper>

