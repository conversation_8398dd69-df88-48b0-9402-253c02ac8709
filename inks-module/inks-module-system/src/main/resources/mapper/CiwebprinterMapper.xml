<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CiwebprinterMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CiwebprinterPojo">
        select id,
               ModuleCode,
               PrinterSn,
               PrinterName,
               PrinterSpec,
               FrontPhoto,
               RowNum,
               EnabledMark,
               Remark,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Tenantid,
               TenantName,
               Revision
        from CiWebPrinter
        where CiWebPrinter.id = #{key}
    </select>
    <sql id="selectCiwebprinterVo">
        select id,
               ModuleCode,
               PrinterSn,
               PrinterName,
               PrinterSpec,
               FrontPhoto,
               RowNum,
               EnabledMark,
               Remark,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               CreateBy,
               CreateByid,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON>er,
               Listerid,
               ModifyDate,
               Tenantid,
               TenantName,
               Revision
        from CiWebPrinter
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CiwebprinterPojo">
        <include refid="selectCiwebprinterVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="tenantid != 'default' ">
            and CiWebPrinter.Tenantid =#{tenantid}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiWebPrinter.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.modulecode != null ">
            and CiWebPrinter.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.printersn != null ">
            and CiWebPrinter.PrinterSn like concat('%', #{SearchPojo.printersn}, '%')
        </if>
        <if test="SearchPojo.printername != null ">
            and CiWebPrinter.PrinterName like concat('%', #{SearchPojo.printername}, '%')
        </if>
        <if test="SearchPojo.printerspec != null ">
            and CiWebPrinter.PrinterSpec like concat('%', #{SearchPojo.printerspec}, '%')
        </if>
        <if test="SearchPojo.frontphoto != null ">
            and CiWebPrinter.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and CiWebPrinter.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and CiWebPrinter.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and CiWebPrinter.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and CiWebPrinter.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and CiWebPrinter.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and CiWebPrinter.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and CiWebPrinter.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and CiWebPrinter.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and CiWebPrinter.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and CiWebPrinter.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and CiWebPrinter.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.modulecode != null ">
                or CiWebPrinter.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.printersn != null ">
                or CiWebPrinter.PrinterSn like concat('%', #{SearchPojo.printersn}, '%')
            </if>
            <if test="SearchPojo.printername != null ">
                or CiWebPrinter.PrinterName like concat('%', #{SearchPojo.printername}, '%')
            </if>
            <if test="SearchPojo.printerspec != null ">
                or CiWebPrinter.PrinterSpec like concat('%', #{SearchPojo.printerspec}, '%')
            </if>
            <if test="SearchPojo.frontphoto != null ">
                or CiWebPrinter.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or CiWebPrinter.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or CiWebPrinter.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or CiWebPrinter.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or CiWebPrinter.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or CiWebPrinter.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or CiWebPrinter.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or CiWebPrinter.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or CiWebPrinter.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or CiWebPrinter.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or CiWebPrinter.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or CiWebPrinter.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into CiWebPrinter(id, ModuleCode, PrinterSn, PrinterName, PrinterSpec, FrontPhoto, RowNum, EnabledMark,
                                 Remark, Custom1, Custom2, Custom3, Custom4, Custom5, CreateBy, CreateByid, CreateDate,
                                 Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
        values (#{id}, #{modulecode}, #{printersn}, #{printername}, #{printerspec}, #{frontphoto}, #{rownum},
                #{enabledmark}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{createby},
                #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{tenantid}, #{tenantname},
                #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiWebPrinter
        <set>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="printersn != null ">
                PrinterSn =#{printersn},
            </if>
            <if test="printername != null ">
                PrinterName =#{printername},
            </if>
            <if test="printerspec != null ">
                PrinterSpec =#{printerspec},
            </if>
            <if test="frontphoto != null ">
                FrontPhoto =#{frontphoto},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantid != null ">
                Tenantid =#{tenantid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiWebPrinter
        where id = #{key}
    </delete>


    <select id="getListByModuleCode" resultType="inks.system.domain.pojo.CiwebprinterPojo">
        select id,
               ModuleCode,
               PrinterSn,
               PrinterName,
               PrinterSpec,
               FrontPhoto,
               RowNum,
               EnabledMark,
               Remark,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Tenantid,
               TenantName,
               Revision
        from CiWebPrinter
        where ModuleCode = #{moduleCode}
          and Tenantid = #{tid}
        Order by RowNum
    </select>
</mapper>

