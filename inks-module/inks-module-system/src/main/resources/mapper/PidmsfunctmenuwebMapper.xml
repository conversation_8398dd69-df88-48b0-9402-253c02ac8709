<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PidmsfunctmenuwebMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PidmsfunctmenuwebPojo">
        select id,
        DmsFunctid,
        DmsFunctCode,
        DmsFunctName,
        Navid,
        NavCode,
        NavName,
        Remark,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        Listerid,
        ModifyDate,
        Revision
        from PiDmsFunctMenuWeb
        where PiDmsFunctMenuWeb.id = #{key}
    </select>
    <sql id="selectPidmsfunctmenuwebVo">
        select id,
               DmsFunctid,
               DmsFunctCode,
               DmsFunctName,
               <PERSON>vid,
               <PERSON>vCode,
               NavName,
               Remark,
               Create<PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               <PERSON>reateDate,
               Lister,
               Listerid,
               ModifyDate,
               Revision
        from PiDmsFunctMenuWeb
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PidmsfunctmenuwebPojo">
        <include refid="selectPidmsfunctmenuwebVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and PiDmsFunctMenuWeb.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.dmsfunctid != null">
            and PiDmsFunctMenuWeb.DmsFunctid like concat('%', #{SearchPojo.dmsfunctid}, '%')
        </if>
        <if test="SearchPojo.dmsfunctcode != null">
            and PiDmsFunctMenuWeb.DmsFunctCode like concat('%', #{SearchPojo.dmsfunctcode}, '%')
        </if>
        <if test="SearchPojo.dmsfunctname != null">
            and PiDmsFunctMenuWeb.DmsFunctName like concat('%', #{SearchPojo.dmsfunctname}, '%')
        </if>
        <if test="SearchPojo.navid != null">
            and PiDmsFunctMenuWeb.Navid like concat('%', #{SearchPojo.navid}, '%')
        </if>
        <if test="SearchPojo.navcode != null">
            and PiDmsFunctMenuWeb.NavCode like concat('%', #{SearchPojo.navcode}, '%')
        </if>
        <if test="SearchPojo.navname != null">
            and PiDmsFunctMenuWeb.NavName like concat('%', #{SearchPojo.navname}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and PiDmsFunctMenuWeb.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and PiDmsFunctMenuWeb.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and PiDmsFunctMenuWeb.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and PiDmsFunctMenuWeb.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and PiDmsFunctMenuWeb.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.dmsfunctid != null">
                or PiDmsFunctMenuWeb.DmsFunctid like concat('%', #{SearchPojo.dmsfunctid}, '%')
            </if>
            <if test="SearchPojo.dmsfunctcode != null">
                or PiDmsFunctMenuWeb.DmsFunctCode like concat('%', #{SearchPojo.dmsfunctcode}, '%')
            </if>
            <if test="SearchPojo.dmsfunctname != null">
                or PiDmsFunctMenuWeb.DmsFunctName like concat('%', #{SearchPojo.dmsfunctname}, '%')
            </if>
            <if test="SearchPojo.navid != null">
                or PiDmsFunctMenuWeb.Navid like concat('%', #{SearchPojo.navid}, '%')
            </if>
            <if test="SearchPojo.navcode != null">
                or PiDmsFunctMenuWeb.NavCode like concat('%', #{SearchPojo.navcode}, '%')
            </if>
            <if test="SearchPojo.navname != null">
                or PiDmsFunctMenuWeb.NavName like concat('%', #{SearchPojo.navname}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or PiDmsFunctMenuWeb.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or PiDmsFunctMenuWeb.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or PiDmsFunctMenuWeb.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or PiDmsFunctMenuWeb.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or PiDmsFunctMenuWeb.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiDmsFunctMenuWeb(id, DmsFunctid, DmsFunctCode, DmsFunctName, Navid, NavCode, NavName, Remark,
        CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision)
        values (#{id}, #{dmsfunctid}, #{dmsfunctcode}, #{dmsfunctname}, #{navid}, #{navcode}, #{navname}, #{remark},
        #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiDmsFunctMenuWeb
        <set>
            <if test="dmsfunctid != null">
                DmsFunctid =#{dmsfunctid},
            </if>
            <if test="dmsfunctcode != null">
                DmsFunctCode =#{dmsfunctcode},
            </if>
            <if test="dmsfunctname != null">
                DmsFunctName =#{dmsfunctname},
            </if>
            <if test="navid != null">
                Navid =#{navid},
            </if>
            <if test="navcode != null">
                NavCode =#{navcode},
            </if>
            <if test="navname != null">
                NavName =#{navname},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiDmsFunctMenuWeb
        where id = #{key}
    </delete>

    <select id="getListByFunction" resultType="inks.system.domain.pojo.PidmsfunctmenuwebPojo">
        SELECT
        PiMenuWeb.NavPid,
        PiMenuWeb.NavType,
        PiDmsFunctMenuWeb.id,
        PiDmsFunctMenuWeb.DmsFunctid,
        PiDmsFunctMenuWeb.DmsFunctCode,
        PiDmsFunctMenuWeb.DmsFunctName,
        PiDmsFunctMenuWeb.Navid,
        PiDmsFunctMenuWeb.NavCode,
        PiDmsFunctMenuWeb.NavName,
        PiDmsFunctMenuWeb.Remark,
        PiDmsFunctMenuWeb.CreateBy,
        PiDmsFunctMenuWeb.CreateByid,
        PiDmsFunctMenuWeb.CreateDate,
        PiDmsFunctMenuWeb.Lister,
        PiDmsFunctMenuWeb.Listerid,
        PiDmsFunctMenuWeb.ModifyDate,
        PiDmsFunctMenuWeb.Revision
        FROM
        PiMenuWeb
        RIGHT JOIN PiDmsFunctMenuWeb ON PiMenuWeb.Navid = PiDmsFunctMenuWeb.Navid
        where 1 = 1 and PiDmsFunctMenuWeb.DmsFunctid =#{key}
        order by PiDmsFunctMenuWeb.NavCode
    </select>

    <select id="getListByLoginUser" parameterType="inks.common.core.domain.LoginUser"
            resultType="inks.system.domain.pojo.PimenuwebPojo">
        SELECT DISTINCT PiMenuWeb.Navid,
        PiMenuWeb.NavPid,
        PiMenuWeb.NavType,
        PiMenuWeb.NavCode,
        PiMenuWeb.NavName,
        PiMenuWeb.NavGroup,
        PiMenuWeb.RowNum,
        PiMenuWeb.ImageCss,
        PiMenuWeb.IconUrl,
        PiMenuWeb.NavigateUrl,
        PiMenuWeb.MvcUrl,
        PiMenuWeb.ModuleType,
        PiMenuWeb.ModuleCode,
        PiMenuWeb.RoleCode,
        PiMenuWeb.ImageIndex,
        PiMenuWeb.ImageStyle,
        PiMenuWeb.EnabledMark,
        PiMenuWeb.Remark,
        PiMenuWeb.PermissionCode,
        PiMenuWeb.Functionid,
        PiMenuWeb.FunctionCode,
        PiMenuWeb.FunctionName,
        PiMenuWeb.IsMicroApp,
        PiMenuWeb.MicroAppName,
        PiMenuWeb.MicroAppEntry,
        PiMenuWeb.MicroAppRule,
        PiMenuWeb.MicroAppLocal,
        PiMenuWeb.Lister,
        PiMenuWeb.CreateDate,
        PiMenuWeb.ModifyDate,
        PiMenuWeb.DeleteMark,
        PiMenuWeb.DeleteLister,
        PiMenuWeb.DeleteDate
        FROM PiMenuWeb
        LEFT JOIN PiDmsFunctMenuWeb ON PiDmsFunctMenuWeb.Navid = PiMenuWeb.Navid
        WHERE PiDmsFunctMenuWeb.DmsFunctid in (select DmsFunctids
        FROM PiTenantDmsUser
        WHERE PiTenantDmsUser.Userid = #{userid}
        and PiTenantDmsUser.Tenantid = #{tenantid})
        order by RowNum
    </select>
    <select id="getListByDmsFunctids" resultType="inks.system.domain.pojo.PimenuwebPojo">
        SELECT DISTINCT PiMenuWeb.Navid,
        PiMenuWeb.NavPid,
        PiMenuWeb.NavType,
        PiMenuWeb.NavCode,
        PiMenuWeb.NavName,
        PiMenuWeb.NavGroup,
        PiMenuWeb.RowNum,
        PiMenuWeb.ImageCss,
        PiMenuWeb.IconUrl,
        PiMenuWeb.NavigateUrl,
        PiMenuWeb.MvcUrl,
        PiMenuWeb.ModuleType,
        PiMenuWeb.ModuleCode,
        PiMenuWeb.RoleCode,
        PiMenuWeb.ImageIndex,
        PiMenuWeb.ImageStyle,
        PiMenuWeb.EnabledMark,
        PiMenuWeb.Remark,
        PiMenuWeb.PermissionCode,
        PiMenuWeb.Functionid,
        PiMenuWeb.FunctionCode,
        PiMenuWeb.FunctionName,
        PiMenuWeb.IsMicroApp,
        PiMenuWeb.MicroAppName,
        PiMenuWeb.MicroAppEntry,
        PiMenuWeb.MicroAppRule,
        PiMenuWeb.MicroAppLocal,
        PiMenuWeb.Lister,
        PiMenuWeb.CreateDate,
        PiMenuWeb.ModifyDate,
        PiMenuWeb.DeleteMark,
        PiMenuWeb.DeleteLister,
        PiMenuWeb.DeleteDate
        FROM PiMenuWeb
        LEFT JOIN PiDmsFunctMenuWeb ON PiDmsFunctMenuWeb.Navid = PiMenuWeb.Navid
        WHERE PiDmsFunctMenuWeb.DmsFunctid in (${ids})
        order by RowNum
    </select>
</mapper>

