<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CidgformatMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CidgformatPojo">
        select id,
               FormGroupid,
               FormCode,
               FormName,
               RowNum,
               EnabledMark,
               DefMark,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from CiDgFormat
        where CiDgFormat.id = #{key}
          and CiDgFormat.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select id,
               FormGroupid,
               FormCode,
               FormName,
               RowNum,
               EnabledMark,
               DefMark,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from CiDgFormat
    </sql>
    <sql id="selectdetailVo">
        select id,
               FormGroupid,
               FormCode,
               FormName,
               RowNum,
               EnabledMark,
               DefMark,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from CiDgFormat
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CidgformatitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and CiDgFormat.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiDgFormat.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.formgroupid != null ">
            and CiDgFormat.formgroupid like concat('%', #{SearchPojo.formgroupid}, '%')
        </if>
        <if test="SearchPojo.formcode != null ">
            and CiDgFormat.formcode like concat('%', #{SearchPojo.formcode}, '%')
        </if>
        <if test="SearchPojo.formname != null ">
            and CiDgFormat.formname like concat('%', #{SearchPojo.formname}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and CiDgFormat.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and CiDgFormat.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and CiDgFormat.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and CiDgFormat.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and CiDgFormat.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and CiDgFormat.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and CiDgFormat.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and CiDgFormat.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and CiDgFormat.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and CiDgFormat.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and CiDgFormat.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.formgroupid != null ">
                or CiDgFormat.FormGroupid like concat('%', #{SearchPojo.formgroupid}, '%')
            </if>
            <if test="SearchPojo.formcode != null ">
                or CiDgFormat.FormCode like concat('%', #{SearchPojo.formcode}, '%')
            </if>
            <if test="SearchPojo.formname != null ">
                or CiDgFormat.FormName like concat('%', #{SearchPojo.formname}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or CiDgFormat.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or CiDgFormat.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or CiDgFormat.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or CiDgFormat.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or CiDgFormat.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or CiDgFormat.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or CiDgFormat.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or CiDgFormat.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or CiDgFormat.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or CiDgFormat.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or CiDgFormat.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CidgformatPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and CiDgFormat.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiDgFormat.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.formgroupid != null ">
            and CiDgFormat.FormGroupid like concat('%', #{SearchPojo.formgroupid}, '%')
        </if>
        <if test="SearchPojo.formcode != null ">
            and CiDgFormat.FormCode like concat('%', #{SearchPojo.formcode}, '%')
        </if>
        <if test="SearchPojo.formname != null ">
            and CiDgFormat.FormName like concat('%', #{SearchPojo.formname}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and CiDgFormat.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and CiDgFormat.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and CiDgFormat.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and CiDgFormat.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and CiDgFormat.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and CiDgFormat.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and CiDgFormat.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and CiDgFormat.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and CiDgFormat.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and CiDgFormat.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and CiDgFormat.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.formgroupid != null ">
                or CiDgFormat.FormGroupid like concat('%', #{SearchPojo.formgroupid}, '%')
            </if>
            <if test="SearchPojo.formcode != null ">
                or CiDgFormat.FormCode like concat('%', #{SearchPojo.formcode}, '%')
            </if>
            <if test="SearchPojo.formname != null ">
                or CiDgFormat.FormName like concat('%', #{SearchPojo.formname}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or CiDgFormat.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or CiDgFormat.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or CiDgFormat.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or CiDgFormat.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or CiDgFormat.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or CiDgFormat.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or CiDgFormat.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or CiDgFormat.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or CiDgFormat.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or CiDgFormat.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or CiDgFormat.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into CiDgFormat(id, FormGroupid, FormCode, FormName, RowNum, EnabledMark, DefMark, Summary, CreateBy,
                               CreateByid,
                               CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5,
                               Tenantid, TenantName, Revision)
        values (#{id}, #{formgroupid}, #{formcode}, #{formname}, #{rownum}, #{enabledmark}, #{defmark}, #{summary},
                #{createby},
                #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3},
                #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiDgFormat
        <set>
            <if test="formgroupid != null ">
                FormGroupid =#{formgroupid},
            </if>
            <if test="formcode != null ">
                FormCode =#{formcode},
            </if>
            <if test="formname != null ">
                FormName =#{formname},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="defmark != null">
                DefMark =#{defmark},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiDgFormat
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.system.domain.pojo.CidgformatPojo">
        select
        id
        from CiDgFormatItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>


    <!--查询单个-->
    <select id="getEntityByCode" resultType="inks.system.domain.pojo.CidgformatPojo">
        select id,
               FormCode,
               FormName,
               RowNum,
               EnabledMark,
               DefMark,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Tenantid,
               Revision
        from CiDgFormat
        where CiDgFormat.FormCode = #{code}
          and CiDgFormat.Tenantid = #{tid}
        ORDER BY CreateDate DESC
        LIMIT 1
    </select>

    <!--查询单个-->
    <select id="getEntityByCodeUser" resultType="inks.system.domain.pojo.CidgformatPojo">
        select id,
               FormCode,
               FormName,
               RowNum,
               EnabledMark,
               DefMark,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Tenantid,
               Revision
        from CiDgFormat
        where CiDgFormat.FormCode = #{code}
          and CiDgFormat.Tenantid = #{tid}
          and CiDgFormat.Listerid = #{userid}
        ORDER BY CreateDate DESC
        LIMIT 1
    </select>
    <select id="getTenBillEntityByCode" resultType="inks.system.domain.pojo.CidgformatPojo">
        select * from CiDgFormat
          where CiDgFormat.Tenantid = #{tid}
          and CiDgFormat.FormCode = #{code}
          and CiDgFormat.DefMark = 1
        LIMIT 1
    </select>

    <select id="getIdByCode" resultType="java.lang.String">
        select id from CiDgFormat
        where FormCode = #{code}
          and Tenantid = #{tid}
          and Listerid = #{userid}
    </select>
</mapper>

