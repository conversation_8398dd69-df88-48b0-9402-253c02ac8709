<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PidmsfunctMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PidmsfunctPojo">
        select
          DmsFunctid, DmsFunctCode, DmsFunctName, Description, Functionid, FunctionCode, FunctionName, EnabledMark, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision        from PiDmsFunct
        where PiDmsFunct.DmsFunctid = #{key}
    </select>
    <sql id="selectPidmsfunctVo">
         select
          DmsFunctid, DmsFunctCode, DmsFunctName, Description, Functionid, FunctionCode, FunctionName, EnabledMark, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision        from PiDmsFunct
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.PidmsfunctPojo">
        <include refid="selectPidmsfunctVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and PiDmsFunct.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.dmsfunctcode != null ">
   and PiDmsFunct.DmsFunctCode like concat('%', #{SearchPojo.dmsfunctcode}, '%')
</if>
<if test="SearchPojo.dmsfunctname != null ">
   and PiDmsFunct.DmsFunctName like concat('%', #{SearchPojo.dmsfunctname}, '%')
</if>
<if test="SearchPojo.description != null ">
   and PiDmsFunct.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.functionid != null ">
   and PiDmsFunct.Functionid like concat('%', #{SearchPojo.functionid}, '%')
</if>
<if test="SearchPojo.functioncode != null ">
   and PiDmsFunct.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
</if>
<if test="SearchPojo.functionname != null ">
   and PiDmsFunct.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and PiDmsFunct.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and PiDmsFunct.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and PiDmsFunct.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and PiDmsFunct.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and PiDmsFunct.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.dmsfunctcode != null ">
   or PiDmsFunct.DmsFunctCode like concat('%', #{SearchPojo.dmsfunctcode}, '%')
</if>
<if test="SearchPojo.dmsfunctname != null ">
   or PiDmsFunct.DmsFunctName like concat('%', #{SearchPojo.dmsfunctname}, '%')
</if>
<if test="SearchPojo.description != null ">
   or PiDmsFunct.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.functionid != null ">
   or PiDmsFunct.Functionid like concat('%', #{SearchPojo.functionid}, '%')
</if>
<if test="SearchPojo.functioncode != null ">
   or PiDmsFunct.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
</if>
<if test="SearchPojo.functionname != null ">
   or PiDmsFunct.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or PiDmsFunct.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or PiDmsFunct.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or PiDmsFunct.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or PiDmsFunct.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or PiDmsFunct.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into PiDmsFunct(DmsFunctid, DmsFunctCode, DmsFunctName, Description, Functionid, FunctionCode, FunctionName, EnabledMark, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision)
        values (#{dmsfunctid}, #{dmsfunctcode}, #{dmsfunctname}, #{description}, #{functionid}, #{functioncode}, #{functionname}, #{enabledmark}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update PiDmsFunct
        <set>
            <if test="dmsfunctcode != null ">
                DmsFunctCode =#{dmsfunctcode},
            </if>
            <if test="dmsfunctname != null ">
                DmsFunctName =#{dmsfunctname},
            </if>
            <if test="description != null ">
                Description =#{description},
            </if>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
                Revision=Revision+1
        </set>
        where DmsFunctid = #{dmsfunctid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from PiDmsFunct where DmsFunctid = #{key}
    </delete>
                                                                    </mapper>

