<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PideptMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PideptPojo">
        select id,
               Parentid,
               Ancestors,
               DeptCode,
               DeptName,
               EnabledMark,
               Leader,
               Phone,
               Email,
               RowNum,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               Revision
        from PiDept
        where PiDept.id = #{key}
          and PiDept.Tenantid = #{tid}
    </select>
    <sql id="selectPideptVo">
        select id,
               <PERSON>rent<PERSON>,
               An<PERSON><PERSON>,
               DeptCode,
               DeptName,
               EnabledMark,
               <PERSON>,
               <PERSON>,
               <PERSON><PERSON>,
               <PERSON><PERSON><PERSON>,
               <PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON>,
               <PERSON><PERSON><PERSON>,
               <PERSON>difyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               <PERSON>antid,
               Revision
        from PiDept
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PideptPojo">
        <include refid="selectPideptVo"/>
        where 1 = 1 and PiDept.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiDept.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.parentid != null ">
            and PiDept.Parentid like concat('%', #{SearchPojo.parentid}, '%')
        </if>
        <if test="SearchPojo.ancestors != null ">
            and PiDept.Ancestors like concat('%', #{SearchPojo.ancestors}, '%')
        </if>
        <if test="SearchPojo.deptcode != null ">
            and PiDept.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
        </if>
        <if test="SearchPojo.deptname != null ">
            and PiDept.DeptName like concat('%', #{SearchPojo.deptname}, '%')
        </if>
        <if test="SearchPojo.leader != null ">
            and PiDept.Leader like concat('%', #{SearchPojo.leader}, '%')
        </if>
        <if test="SearchPojo.phone != null ">
            and PiDept.Phone like concat('%', #{SearchPojo.phone}, '%')
        </if>
        <if test="SearchPojo.email != null ">
            and PiDept.Email like concat('%', #{SearchPojo.email}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and PiDept.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and PiDept.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and PiDept.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and PiDept.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and PiDept.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and PiDept.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and PiDept.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and PiDept.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and PiDept.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and PiDept.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.parentid != null ">
                or PiDept.Parentid like concat('%', #{SearchPojo.parentid}, '%')
            </if>
            <if test="SearchPojo.ancestors != null ">
                or PiDept.Ancestors like concat('%', #{SearchPojo.ancestors}, '%')
            </if>
            <if test="SearchPojo.deptcode != null ">
                or PiDept.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
            </if>
            <if test="SearchPojo.deptname != null ">
                or PiDept.DeptName like concat('%', #{SearchPojo.deptname}, '%')
            </if>
            <if test="SearchPojo.leader != null ">
                or PiDept.Leader like concat('%', #{SearchPojo.leader}, '%')
            </if>
            <if test="SearchPojo.phone != null ">
                or PiDept.Phone like concat('%', #{SearchPojo.phone}, '%')
            </if>
            <if test="SearchPojo.email != null ">
                or PiDept.Email like concat('%', #{SearchPojo.email}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or PiDept.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or PiDept.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or PiDept.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or PiDept.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or PiDept.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or PiDept.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or PiDept.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or PiDept.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or PiDept.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or PiDept.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiDept(id, Parentid, Ancestors, DeptCode, DeptName, EnabledMark, Leader, Phone, Email, RowNum,
                           Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2,
                           Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{parentid}, #{ancestors}, #{deptcode}, #{deptname}, #{enabledmark}, #{leader}, #{phone},
                #{email}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid},
                #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiDept
        <set>
            <if test="parentid != null ">
                Parentid =#{parentid},
            </if>
            <if test="ancestors != null ">
                Ancestors =#{ancestors},
            </if>
            <if test="deptcode != null ">
                DeptCode =#{deptcode},
            </if>
            <if test="deptname != null ">
                DeptName =#{deptname},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="leader != null ">
                Leader =#{leader},
            </if>
            <if test="phone != null ">
                Phone =#{phone},
            </if>
            <if test="email != null ">
                Email =#{email},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiDept
        where id = #{key}
          and Tenantid = #{tid}
    </delete>


    <!--查询指定行数据-->
    <select id="getListByParentid"  resultType="inks.system.domain.pojo.PideptPojo">
        <include refid="selectPideptVo"/>
        where PiDept.Tenantid =#{tid} AND PiDept.Parentid=#{key}
    </select>
</mapper>

