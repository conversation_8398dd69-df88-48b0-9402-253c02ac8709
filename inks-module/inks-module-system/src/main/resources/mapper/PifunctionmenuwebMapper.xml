<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PifunctionmenuwebMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PifunctionmenuwebPojo">
        SELECT
            PiFunctionMenuWeb.id,
            PiFunctionMenuWeb.Functionid,
            PiFunctionMenuWeb.FunctionCode,
            PiFunctionMenuWeb.FunctionName,
            PiFunctionMenuWeb.Navid,
            PiFunctionMenuWeb.NavCode,
            PiFunctionMenuWeb.NavName,
            PiFunctionMenuWeb.Remark,
            PiFunctionMenuWeb.CreateBy,
            PiFunctionMenuWeb.CreateDate,
            PiFunctionMenuWeb.Lister,
            PiFunctionMenuWeb.ModifyDate,
            PiFunctionMenuWeb.Tenantid,
            PiFunctionMenuWeb.Revision,
            PiMenuWeb.NavType,
            PiMenuWeb.NavPid
        FROM
            PiMenuWeb
                RIGHT JOIN PiFunctionMenuWeb ON PiMenuWeb.Navid = PiFunctionMenuWeb.Navid
        where PiFunctionMenuWeb.id = #{key}
          and PiFunctionMenuWeb.Tenantid = #{tid}
    </select>
    <sql id="selectPifunctionmenuwebVo">
        SELECT
            PiFunctionMenuWeb.id,
            PiFunctionMenuWeb.Functionid,
            PiFunctionMenuWeb.FunctionCode,
            PiFunctionMenuWeb.FunctionName,
            PiFunctionMenuWeb.Navid,
            PiFunctionMenuWeb.NavCode,
            PiFunctionMenuWeb.NavName,
            PiFunctionMenuWeb.Remark,
            PiFunctionMenuWeb.CreateBy,
            PiFunctionMenuWeb.CreateDate,
            PiFunctionMenuWeb.Lister,
            PiFunctionMenuWeb.ModifyDate,
            PiFunctionMenuWeb.Tenantid,
            PiFunctionMenuWeb.Revision,
            PiMenuWeb.NavType,
            PiMenuWeb.NavPid
        FROM
            PiMenuWeb
                RIGHT JOIN PiFunctionMenuWeb ON PiMenuWeb.Navid = PiFunctionMenuWeb.Navid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PifunctionmenuwebPojo">
        <include refid="selectPifunctionmenuwebVo"/>
        where 1 = 1 and PiFunctionMenuWeb.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiFunctionMenuWeb.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.functionid != null and SearchPojo.functionid  != ''">
            and PiFunctionMenuWeb.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null and SearchPojo.functioncode  != ''">
            and PiFunctionMenuWeb.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null and SearchPojo.functionname  != ''">
            and PiFunctionMenuWeb.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.navid != null and SearchPojo.navid  != ''">
            and PiFunctionMenuWeb.Navid like concat('%', #{SearchPojo.navid}, '%')
        </if>
        <if test="SearchPojo.navcode != null and SearchPojo.navcode  != ''">
            and PiFunctionMenuWeb.NavCode like concat('%', #{SearchPojo.navcode}, '%')
        </if>
        <if test="SearchPojo.navname != null and SearchPojo.navname  != ''">
            and PiFunctionMenuWeb.NavName like concat('%', #{SearchPojo.navname}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and PiFunctionMenuWeb.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark  != ''">
            and PiFunctionMenuWeb.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby  != ''">
            and PiFunctionMenuWeb.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.functionid != null and SearchPojo.functionid != ''">
            or PiFunctionMenuWeb.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null and SearchPojo.functioncode != ''">
            or PiFunctionMenuWeb.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null and SearchPojo.functionname != ''">
            or PiFunctionMenuWeb.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.navid != null and SearchPojo.navid != ''">
            or PiFunctionMenuWeb.Navid like concat('%', #{SearchPojo.navid}, '%')
        </if>
        <if test="SearchPojo.navcode != null and SearchPojo.navcode != ''">
            or PiFunctionMenuWeb.NavCode like concat('%', #{SearchPojo.navcode}, '%')
        </if>
        <if test="SearchPojo.navname != null and SearchPojo.navname != ''">
            or PiFunctionMenuWeb.NavName like concat('%', #{SearchPojo.navname}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or PiFunctionMenuWeb.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or PiFunctionMenuWeb.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or PiFunctionMenuWeb.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiFunctionMenuWeb(id, Functionid, FunctionCode, FunctionName, Navid, NavCode, NavName, Lister,
                                      CreateDate, ModifyDate, Remark, CreateBy, Tenantid, Revision)
        values (#{id}, #{functionid}, #{functioncode}, #{functionname}, #{navid}, #{navcode}, #{navname}, #{lister},
                #{createdate}, #{modifydate}, #{remark}, #{createby}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiFunctionMenuWeb
        <set>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="navid != null ">
                Navid =#{navid},
            </if>
            <if test="navcode != null ">
                NavCode =#{navcode},
            </if>
            <if test="navname != null ">
                NavName =#{navname},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiFunctionMenuWeb
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--查询List-->
    <select id="getListByFunction" resultType="inks.system.domain.pojo.PifunctionmenuwebPojo">
        <include refid="selectPifunctionmenuwebVo"/>
        where 1 = 1 and PiFunctionMenuWeb.Functionid =#{key}
        order by PiFunctionMenuWeb.NavCode
    </select>

</mapper>

