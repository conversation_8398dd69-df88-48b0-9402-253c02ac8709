<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PiscmuserMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PiscmuserPojo">
        <include refid="selectPiscmuserVo"/>
        where PiScmUser.Userid = #{key}
          and PiScmUser.Tenantid = #{tid}
    </select>
    <sql id="selectPiscmuserVo">
        select Userid,
               UserName,
               RealName,
               NickName,
               UserPassword,
               Mobile,
               Email,
               Sex,
               LangCode,
               Avatar,
               UserType,
               IsAdmin,
               Deptid,
               DeptCode,
               DeptName,
               IsDeptAdmin,
               DeptRowNum,
               <PERSON><PERSON>um,
               UserStatus,
               UserCode,
               Groupids,
               GroupNames,
               ScmFunctids,
               ScmFunctNames,
               Remark,
               DataLabel,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Tenantid,
               TenantName,
               Revision
        from PiScmUser
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PiscmuserPojo">
        <include refid="selectPiscmuserVo"/>
        where 1 = 1 and PiScmUser.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiScmUser.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.username != null ">
            and PiScmUser.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null ">
            and PiScmUser.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.nickname != null ">
            and PiScmUser.NickName like concat('%', #{SearchPojo.nickname}, '%')
        </if>
        <if test="SearchPojo.userpassword != null ">
            and PiScmUser.UserPassword like concat('%', #{SearchPojo.userpassword}, '%')
        </if>
        <if test="SearchPojo.mobile != null ">
            and PiScmUser.Mobile like concat('%', #{SearchPojo.mobile}, '%')
        </if>
        <if test="SearchPojo.email != null ">
            and PiScmUser.Email like concat('%', #{SearchPojo.email}, '%')
        </if>
        <if test="SearchPojo.langcode != null ">
            and PiScmUser.LangCode like concat('%', #{SearchPojo.langcode}, '%')
        </if>
        <if test="SearchPojo.avatar != null ">
            and PiScmUser.Avatar like concat('%', #{SearchPojo.avatar}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and PiScmUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.deptcode != null ">
            and PiScmUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
        </if>
        <if test="SearchPojo.deptname != null ">
            and PiScmUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
        </if>
        <if test="SearchPojo.usercode != null ">
            and PiScmUser.UserCode like concat('%', #{SearchPojo.usercode}, '%')
        </if>
        <if test="SearchPojo.groupids != null ">
            and PiScmUser.Groupids like concat('%', #{SearchPojo.groupids}, '%')
        </if>
        <if test="SearchPojo.groupnames != null ">
            and PiScmUser.GroupNames like concat('%', #{SearchPojo.groupnames}, '%')
        </if>
        <if test="SearchPojo.scmfunctids != null ">
            and PiScmUser.ScmFunctids like concat('%', #{SearchPojo.scmfunctids}, '%')
        </if>
        <if test="SearchPojo.scmfunctnames != null ">
            and PiScmUser.ScmFunctNames like concat('%', #{SearchPojo.scmfunctnames}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and PiScmUser.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and PiScmUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and PiScmUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and PiScmUser.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and PiScmUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and PiScmUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.username != null ">
                or PiScmUser.UserName like concat('%', #{SearchPojo.username}, '%')
            </if>
            <if test="SearchPojo.realname != null ">
                or PiScmUser.RealName like concat('%', #{SearchPojo.realname}, '%')
            </if>
            <if test="SearchPojo.nickname != null ">
                or PiScmUser.NickName like concat('%', #{SearchPojo.nickname}, '%')
            </if>
            <if test="SearchPojo.userpassword != null ">
                or PiScmUser.UserPassword like concat('%', #{SearchPojo.userpassword}, '%')
            </if>
            <if test="SearchPojo.mobile != null ">
                or PiScmUser.Mobile like concat('%', #{SearchPojo.mobile}, '%')
            </if>
            <if test="SearchPojo.email != null ">
                or PiScmUser.Email like concat('%', #{SearchPojo.email}, '%')
            </if>
            <if test="SearchPojo.langcode != null ">
                or PiScmUser.LangCode like concat('%', #{SearchPojo.langcode}, '%')
            </if>
            <if test="SearchPojo.avatar != null ">
                or PiScmUser.Avatar like concat('%', #{SearchPojo.avatar}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or PiScmUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.deptcode != null ">
                or PiScmUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
            </if>
            <if test="SearchPojo.deptname != null ">
                or PiScmUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
            </if>
            <if test="SearchPojo.usercode != null ">
                or PiScmUser.UserCode like concat('%', #{SearchPojo.usercode}, '%')
            </if>
            <if test="SearchPojo.groupids != null ">
                or PiScmUser.Groupids like concat('%', #{SearchPojo.groupids}, '%')
            </if>
            <if test="SearchPojo.groupnames != null ">
                or PiScmUser.GroupNames like concat('%', #{SearchPojo.groupnames}, '%')
            </if>
            <if test="SearchPojo.scmfunctids != null ">
                or PiScmUser.ScmFunctids like concat('%', #{SearchPojo.scmfunctids}, '%')
            </if>
            <if test="SearchPojo.scmfunctnames != null ">
                or PiScmUser.ScmFunctNames like concat('%', #{SearchPojo.scmfunctnames}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or PiScmUser.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or PiScmUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or PiScmUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or PiScmUser.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or PiScmUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or PiScmUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiScmUser(Userid, UserName, RealName, NickName, UserPassword, Mobile, Email, Sex, LangCode, Avatar, UserType, IsAdmin, Deptid, DeptCode, DeptName, IsDeptAdmin, DeptRowNum, RowNum, UserStatus, UserCode, Groupids, GroupNames, ScmFunctids, ScmFunctNames, Remark, DataLabel, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
        values (#{userid}, #{username}, #{realname}, #{nickname}, #{userpassword}, #{mobile}, #{email}, #{sex}, #{langcode}, #{avatar}, #{usertype}, #{isadmin}, #{deptid}, #{deptcode}, #{deptname}, #{isdeptadmin}, #{deptrownum}, #{rownum}, #{userstatus}, #{usercode}, #{groupids}, #{groupnames}, #{scmfunctids}, #{scmfunctnames}, #{remark}, #{datalabel}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiScmUser
        <set>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="nickname != null ">
                NickName =#{nickname},
            </if>
            <if test="userpassword != null ">
                UserPassword =#{userpassword},
            </if>
            <if test="mobile != null ">
                Mobile =#{mobile},
            </if>
            <if test="email != null ">
                Email =#{email},
            </if>
            <if test="sex != null">
                Sex =#{sex},
            </if>
            <if test="langcode != null ">
                LangCode =#{langcode},
            </if>
            <if test="avatar != null ">
                Avatar =#{avatar},
            </if>
            <if test="usertype != null">
                UserType =#{usertype},
            </if>
            <if test="isadmin != null">
                IsAdmin =#{isadmin},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="deptcode != null ">
                DeptCode =#{deptcode},
            </if>
            <if test="deptname != null ">
                DeptName =#{deptname},
            </if>
            <if test="isdeptadmin != null">
                IsDeptAdmin =#{isdeptadmin},
            </if>
            <if test="deptrownum != null">
                DeptRowNum =#{deptrownum},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="userstatus != null">
                UserStatus =#{userstatus},
            </if>
            <if test="usercode != null ">
                UserCode =#{usercode},
            </if>
            <if test="groupids != null ">
                Groupids =#{groupids},
            </if>
            <if test="groupnames != null ">
                GroupNames =#{groupnames},
            </if>
            <if test="scmfunctids != null ">
                ScmFunctids =#{scmfunctids},
            </if>
            <if test="scmfunctnames != null ">
                ScmFunctNames =#{scmfunctnames},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="datalabel != null ">
                DataLabel =#{datalabel},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where Userid = #{userid} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiScmUser
        where Userid = #{key}
          and Tenantid = #{tid}
    </delete>
    <select id="getEntityByUserName" resultType="inks.system.domain.pojo.PiscmuserPojo">
        select Userid,
               UserName,
               RealName,
               NickName,
               UserPassword,
               Mobile,
               Email,
               Sex,
               LangCode,
               Avatar,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Revision
        from PiScmUser
        where PiScmUser.Mobile = #{username} or PiScmUser.Email = #{username}
    </select>
    <select id="getPageListByTen" resultType="inks.system.domain.pojo.PiscmuserPojo">
        select PiScmUser.Userid,
               PiScmUser.UserName,
               PiScmUser.RealName,
               PiScmUser.NickName,
               PiScmUser.UserPassword,
               PiScmUser.Mobile,
               PiScmUser.Email,
               PiScmUser.Sex,
               PiScmUser.LangCode,
               PiScmUser.Avatar,
               PiScmUser.Remark,
               PiScmUser.CreateBy,
               PiScmUser.CreateByid,
               PiScmUser.CreateDate,
               PiScmUser.Lister,
               PiScmUser.Listerid,
               PiScmUser.ModifyDate,
               PiScmUser.Revision,
               PiTenantScmUser.TenantId
        from PiScmUser RIGHT JOIN PiTenantScmUser ON PiScmUser.Userid = PiTenantScmUser.Userid
        where  PiTenantScmUser.TenantId=#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
    </select>
    <select id="getEntityByOpenid" resultType="inks.system.domain.pojo.PiscmuserPojo">
        select PiScmUser.Userid,
               PiScmUser.UserName,
               PiScmUser.RealName,
               PiScmUser.NickName,
               PiScmUser.UserPassword,
               PiScmUser.Mobile,
               PiScmUser.Email,
               PiScmUser.Sex,
               PiScmUser.LangCode,
               PiScmUser.Avatar,
               PiScmUser.UserType,
               PiScmUser.IsAdmin,
               PiScmUser.Deptid,
               PiScmUser.DeptCode,
               PiScmUser.DeptName,
               PiScmUser.IsDeptAdmin,
               PiScmUser.DeptRowNum,
               PiScmUser.RowNum,
               PiScmUser.UserStatus,
               PiScmUser.UserCode,
               PiScmUser.Groupids,
               PiScmUser.GroupNames,
               PiScmUser.ScmFunctids,
               PiScmUser.ScmFunctNames,
               PiScmUser.Remark,
               PiScmUser.CreateBy,
               PiScmUser.CreateByid,
               PiScmUser.CreateDate,
               PiScmUser.Lister,
               PiScmUser.Listerid,
               PiScmUser.ModifyDate,
               PiScmUser.Tenantid,
               PiScmUser.TenantName,
               PiScmUser.Revision
        from PiScmUser
                 Left Join inkssaas.PiScmJustAuth on PiScmJustAuth.Userid = PiScmUser.Userid
        where PiScmUser.Tenantid = #{tid}
          and PiScmJustAuth.AuthType = 'openid'
          and PiScmJustAuth.AuthUuid = #{openid}
    </select>
    <select id="getListByOpenid" resultType="inks.system.domain.pojo.PiscmuserPojo">
        select PiScmUser.Userid,
               PiScmUser.UserName,
               PiScmUser.RealName,
               PiScmUser.NickName,
               PiScmUser.UserPassword,
               PiScmUser.Mobile,
               PiScmUser.Email,
               PiScmUser.Sex,
               PiScmUser.LangCode,
               PiScmUser.Avatar,
               PiScmUser.UserType,
               PiScmUser.IsAdmin,
               PiScmUser.Deptid,
               PiScmUser.DeptCode,
               PiScmUser.DeptName,
               PiScmUser.IsDeptAdmin,
               PiScmUser.DeptRowNum,
               PiScmUser.RowNum,
               PiScmUser.UserStatus,
               PiScmUser.UserCode,
               PiScmUser.Groupids,
               PiScmUser.GroupNames,
               PiScmUser.ScmFunctids,
               PiScmUser.ScmFunctNames,
               PiScmUser.Remark,
               PiScmUser.CreateBy,
               PiScmUser.CreateByid,
               PiScmUser.CreateDate,
               PiScmUser.Lister,
               PiScmUser.Listerid,
               PiScmUser.ModifyDate,
               PiScmUser.Tenantid,
               PiScmUser.TenantName,
               PiScmUser.Revision
        from PiScmUser
                 Left Join inkssaas.PiScmJustAuth on PiScmJustAuth.Userid = PiScmUser.Userid
        where  PiScmJustAuth.AuthType = 'openid'
          and PiScmJustAuth.AuthUuid = #{openid}
    </select>
    <select id="getEntityByUserid" resultType="inks.system.domain.pojo.PiscmuserPojo">
        select * from PiScmUser where Userid = #{userid} and Tenantid = #{tid}
    </select>
</mapper>

