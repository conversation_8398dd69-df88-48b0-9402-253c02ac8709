<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PiwebnavMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PiwebnavPojo">
        select
Navid, NavCode, NavName, NavContent, RowNum, EnabledMark, PermissionCode, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision        from PiWebNav
        where PiWebNav.Navid = #{key} 
    </select>
    <sql id="selectPiwebnavVo">
         select
Navid, NavCode, NavName, NavContent, RowNum, EnabledMark, PermissionCode, Remark, CreateBy, C<PERSON><PERSON>yi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>er, <PERSON><PERSON>d, ModifyDate, Revision        from PiWebNav
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.PiwebnavPojo">
        <include refid="selectPiwebnavVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and PiWebNav.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.navcode != null ">
   and PiWebNav.NavCode like concat('%', #{SearchPojo.navcode}, '%')
</if>
<if test="SearchPojo.navname != null ">
   and PiWebNav.NavName like concat('%', #{SearchPojo.navname}, '%')
</if>
<if test="SearchPojo.navcontent != null ">
   and PiWebNav.NavContent like concat('%', #{SearchPojo.navcontent}, '%')
</if>
<if test="SearchPojo.permissioncode != null ">
   and PiWebNav.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and PiWebNav.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and PiWebNav.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and PiWebNav.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and PiWebNav.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and PiWebNav.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.navcode != null ">
   or PiWebNav.NavCode like concat('%', #{SearchPojo.navcode}, '%')
</if>
<if test="SearchPojo.navname != null ">
   or PiWebNav.NavName like concat('%', #{SearchPojo.navname}, '%')
</if>
<if test="SearchPojo.navcontent != null ">
   or PiWebNav.NavContent like concat('%', #{SearchPojo.navcontent}, '%')
</if>
<if test="SearchPojo.permissioncode != null ">
   or PiWebNav.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or PiWebNav.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or PiWebNav.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or PiWebNav.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or PiWebNav.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or PiWebNav.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into PiWebNav(Navid, NavCode, NavName, NavContent, RowNum, EnabledMark, PermissionCode, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision)
        values (#{navid}, #{navcode}, #{navname}, #{navcontent}, #{rownum}, #{enabledmark}, #{permissioncode}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update PiWebNav
        <set>
            <if test="navcode != null ">
                NavCode =#{navcode},
            </if>
            <if test="navname != null ">
                NavName =#{navname},
            </if>
            <if test="navcontent != null ">
                NavContent =#{navcontent},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="permissioncode != null ">
                PermissionCode =#{permissioncode},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
                Revision=Revision+1
        </set>
        where Navid = #{navid} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from PiWebNav where Navid = #{key} 
    </delete>
</mapper>

