<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PitenantscmuserMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PitenantscmuserPojo">
        select PiTenantScmUser.id,
               PiTenantScmUser.Tenantid,
               PiTenantScmUser.TenantName,
               PiTenantScmUser.Userid,
               PiTenantScmUser.UserName,
               PiTenantScmUser.RealName,
               PiTenantScmUser.UserType,
               PiTenantScmUser.IsAdmin,
               PiTenantScmUser.Deptid,
               PiTenantScmUser.DeptCode,
               PiTenantScmUser.DeptName,
               PiTenantScmUser.IsDeptAdmin,
               PiTenantScmUser.DeptRowNum,
               PiTenantScmUser.RowNum,
               PiTenantScmUser.UserStatus,
               PiTenantScmUser.UserCode,
               PiTenantScmUser.Groupids,
               PiTenantScmUser.GroupNames,
               PiTenantScmUser.ScmFunctids,
               PiTenantScmUser.ScmFunctNames,
               PiTenantScmUser.CreateBy,
               PiTenantScmUser.CreateByid,
               PiTenantScmUser.CreateDate,
               PiTenantScmUser.Lister,
               PiTenantScmUser.Listerid,
               PiTenantScmUser.ModifyDate,
               PiTenantScmUser.Custom1,
               PiTenantScmUser.Custom2,
               PiTenantScmUser.Custom3,
               PiTenantScmUser.Custom4,
               PiTenantScmUser.Custom5,
               PiTenantScmUser.Revision,
               PiScmUser.NickName,
               PiScmUser.Mobile,
               PiScmUser.Email,
               PiScmUser.Sex,
               PiScmUser.Avatar
        from PiTenantScmUser
        LEFT JOIN PiScmUser ON PiTenantScmUser.Userid = PiScmUser.Userid
        where PiTenantScmUser.id = #{key}
    </select>
    <sql id="selectPitenantscmuserVo">
        select PiTenantScmUser.id,
               PiTenantScmUser.Tenantid,
               PiTenantScmUser.TenantName,
               PiTenantScmUser.Userid,
               PiTenantScmUser.UserName,
               PiTenantScmUser.RealName,
               PiTenantScmUser.UserType,
               PiTenantScmUser.IsAdmin,
               PiTenantScmUser.Deptid,
               PiTenantScmUser.DeptCode,
               PiTenantScmUser.DeptName,
               PiTenantScmUser.IsDeptAdmin,
               PiTenantScmUser.DeptRowNum,
               PiTenantScmUser.RowNum,
               PiTenantScmUser.UserStatus,
               PiTenantScmUser.UserCode,
               PiTenantScmUser.Groupids,
               PiTenantScmUser.GroupNames,
               PiTenantScmUser.ScmFunctids,
               PiTenantScmUser.ScmFunctNames,
               PiTenantScmUser.CreateBy,
               PiTenantScmUser.CreateByid,
               PiTenantScmUser.CreateDate,
               PiTenantScmUser.Lister,
               PiTenantScmUser.Listerid,
               PiTenantScmUser.ModifyDate,
               PiTenantScmUser.Custom1,
               PiTenantScmUser.Custom2,
               PiTenantScmUser.Custom3,
               PiTenantScmUser.Custom4,
               PiTenantScmUser.Custom5,
               PiTenantScmUser.Revision,
               PiScmUser.NickName,
               PiScmUser.Mobile,
               PiScmUser.Email,
               PiScmUser.Sex,
               PiScmUser.Avatar
        from PiTenantScmUser
                 LEFT JOIN PiScmUser ON PiTenantScmUser.Userid = PiScmUser.Userid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PitenantscmuserPojo">
        <include refid="selectPitenantscmuserVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiTenantScmUser.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.tenantname != null ">
            and PiTenantScmUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.userid != null ">
            and PiTenantScmUser.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.username != null ">
            and PiTenantScmUser.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null ">
            and PiTenantScmUser.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and PiTenantScmUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.deptcode != null ">
            and PiTenantScmUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
        </if>
        <if test="SearchPojo.deptname != null ">
            and PiTenantScmUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
        </if>
        <if test="SearchPojo.usercode != null ">
            and PiTenantScmUser.UserCode like concat('%', #{SearchPojo.usercode}, '%')
        </if>
        <if test="SearchPojo.groupids != null ">
            and PiTenantScmUser.Groupids like concat('%', #{SearchPojo.groupids}, '%')
        </if>
        <if test="SearchPojo.groupnames != null ">
            and PiTenantScmUser.GroupNames like concat('%', #{SearchPojo.groupnames}, '%')
        </if>
        <if test="SearchPojo.scmfunctids != null ">
            and PiTenantScmUser.ScmFunctids like concat('%', #{SearchPojo.scmfunctids}, '%')
        </if>
        <if test="SearchPojo.scmfunctnames != null ">
            and PiTenantScmUser.ScmFunctNames like concat('%', #{SearchPojo.scmfunctnames}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and PiTenantScmUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and PiTenantScmUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and PiTenantScmUser.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and PiTenantScmUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and PiTenantScmUser.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and PiTenantScmUser.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and PiTenantScmUser.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and PiTenantScmUser.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and PiTenantScmUser.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.tenantname != null ">
                or PiTenantScmUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <if test="SearchPojo.userid != null ">
                or PiTenantScmUser.Userid like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.username != null ">
                or PiTenantScmUser.UserName like concat('%', #{SearchPojo.username}, '%')
            </if>
            <if test="SearchPojo.realname != null ">
                or PiTenantScmUser.RealName like concat('%', #{SearchPojo.realname}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or PiTenantScmUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.deptcode != null ">
                or PiTenantScmUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
            </if>
            <if test="SearchPojo.deptname != null ">
                or PiTenantScmUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
            </if>
            <if test="SearchPojo.usercode != null ">
                or PiTenantScmUser.UserCode like concat('%', #{SearchPojo.usercode}, '%')
            </if>
            <if test="SearchPojo.groupids != null ">
                or PiTenantScmUser.Groupids like concat('%', #{SearchPojo.groupids}, '%')
            </if>
            <if test="SearchPojo.groupnames != null ">
                or PiTenantScmUser.GroupNames like concat('%', #{SearchPojo.groupnames}, '%')
            </if>
            <if test="SearchPojo.scmfunctids != null ">
                or PiTenantScmUser.ScmFunctids like concat('%', #{SearchPojo.scmfunctids}, '%')
            </if>
            <if test="SearchPojo.scmfunctnames != null ">
                or PiTenantScmUser.ScmFunctNames like concat('%', #{SearchPojo.scmfunctnames}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or PiTenantScmUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or PiTenantScmUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or PiTenantScmUser.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or PiTenantScmUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or PiTenantScmUser.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or PiTenantScmUser.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or PiTenantScmUser.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or PiTenantScmUser.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or PiTenantScmUser.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiTenantScmUser(id, Tenantid, TenantName, Userid, UserName, RealName, UserType, IsAdmin, Deptid,
                                    DeptCode, DeptName, IsDeptAdmin, DeptRowNum, RowNum, UserStatus, UserCode, Groupids,
                                    GroupNames, ScmFunctids, ScmFunctNames, CreateBy, CreateByid, CreateDate, Lister,
                                    Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Revision)
        values (#{id}, #{tenantid}, #{tenantname}, #{userid}, #{username}, #{realname}, #{usertype}, #{isadmin},
                #{deptid}, #{deptcode}, #{deptname}, #{isdeptadmin}, #{deptrownum}, #{rownum}, #{userstatus},
                #{usercode}, #{groupids}, #{groupnames}, #{scmfunctids}, #{scmfunctnames}, #{createby}, #{createbyid},
                #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
                #{custom5}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiTenantScmUser
        <set>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="usertype != null">
                UserType =#{usertype},
            </if>
            <if test="isadmin != null">
                IsAdmin =#{isadmin},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="deptcode != null ">
                DeptCode =#{deptcode},
            </if>
            <if test="deptname != null ">
                DeptName =#{deptname},
            </if>
            <if test="isdeptadmin != null">
                IsDeptAdmin =#{isdeptadmin},
            </if>
            <if test="deptrownum != null">
                DeptRowNum =#{deptrownum},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="userstatus != null">
                UserStatus =#{userstatus},
            </if>
            <if test="usercode != null ">
                UserCode =#{usercode},
            </if>
            <if test="groupids != null ">
                Groupids =#{groupids},
            </if>
            <if test="groupnames != null ">
                GroupNames =#{groupnames},
            </if>
            <if test="scmfunctids != null ">
                ScmFunctids =#{scmfunctids},
            </if>
            <if test="scmfunctnames != null ">
                ScmFunctNames =#{scmfunctnames},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiTenantScmUser
        where id = #{key}
    </delete>
    <!--查询指定行数据-->
    <select id="getListByUser"
            resultType="inks.system.domain.pojo.PitenantscmuserPojo">
        <include refid="selectPitenantscmuserVo"/>
        where PiTenantScmUser.Userid=#{userid} Order by id
    </select>
    <select id="getEntityByUserid" resultType="inks.system.domain.pojo.PitenantscmuserPojo">
        <include refid="selectPitenantscmuserVo"/>
        where PiTenantScmUser.Userid=#{key} and PiTenantScmUser.Tenantid=#{tid}
    </select>

</mapper>

