<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PipricepolicyMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PipricepolicyPojo">
        SELECT PiPricePolicy.id,
               PiPricePolicy.Functionid,
               PiPricePolicy.ObjectLevel,
               PiPricePolicy.ReleaseDomain,
               PiPricePolicy.ExpirationDate,
               PiPricePolicy.EnabledMark,
               PiPricePolicy.DeleteMark,
               PiPricePolicy.DeleteLister,
               PiPricePolicy.DeleteDate,
               PiPricePolicy.Summary,
               PiPricePolicy.Lister,
               PiPricePolicy.CreateDate,
               PiPricePolicy.ModifyDate,
               PiPricePolicy.Assessor,
               PiPricePolicy.AssessDate,
               PiFunction.FunctionCode,
               PiFunction.FunctionName,
               PiFunction.Description,
               PiFunction.FrontPhoto
        FROM PiFunction
                 RIGHT JOIN PiPricePolicy ON PiPricePolicy.Functionid = PiFunction.Functionid
        where PiPricePolicy.id = #{key}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT PiPricePolicy.id,
               PiPricePolicy.Functionid,
               PiPricePolicy.ObjectLevel,
               PiPricePolicy.ReleaseDomain,
               PiPricePolicy.ExpirationDate,
               PiPricePolicy.EnabledMark,
               PiPricePolicy.RowNum,
               PiPricePolicy.LevelNum,
               PiPricePolicy.DeleteMark,
               PiPricePolicy.DeleteLister,
               PiPricePolicy.DeleteDate,
               PiPricePolicy.Summary,
               PiPricePolicy.CreateBy,
               PiPricePolicy.Lister,
               PiPricePolicy.CreateDate,
               PiPricePolicy.ModifyDate,
               PiPricePolicy.Assessor,
               PiPricePolicy.AssessDate,
               PiFunction.FunctionCode,
               PiFunction.FunctionName,
               PiFunction.Description,
               PiFunction.FrontPhoto
        FROM PiFunction
                 RIGHT JOIN PiPricePolicy ON PiPricePolicy.Functionid = PiFunction.Functionid
    </sql>
    <sql id="selectdetailVo">
        SELECT PiPricePolicy.id,
               PiPricePolicy.Functionid,
               PiPricePolicy.ObjectLevel,
               PiPricePolicy.ReleaseDomain,
               PiPricePolicy.ExpirationDate,
               PiPricePolicy.EnabledMark,
               PiPricePolicy.RowNum,
               PiPricePolicy.LevelNum,
               PiPricePolicy.DeleteMark,
               PiPricePolicy.DeleteLister,
               PiPricePolicy.DeleteDate,
               PiPricePolicy.Summary,
               PiPricePolicy.CreateBy,
               PiPricePolicy.Lister,
               PiPricePolicy.CreateDate,
               PiPricePolicy.ModifyDate,
               PiPricePolicy.Assessor,
               PiPricePolicy.AssessDate,
               PiFunction.FunctionCode,
               PiFunction.FunctionName,
               PiFunction.Description,
               PiFunction.FrontPhoto
        FROM PiFunction
                 RIGHT JOIN PiPricePolicy ON PiPricePolicy.Functionid = PiFunction.Functionid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PipricepolicyitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiPricePolicy.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.functionid != null and SearchPojo.functionid != ''">
            and PiPricePolicy.functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.parentifid != null and SearchPojo.parentifid != ''">
            and PiPricePolicy.parentifid like concat('%', #{SearchPojo.parentifid}, '%')
        </if>
        <if test="SearchPojo.deletelister != null and SearchPojo.deletelister != ''">
            and PiPricePolicy.deletelister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and PiPricePolicy.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and PiPricePolicy.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            and PiPricePolicy.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.functionid != null and SearchPojo.functionid != ''">
            or PiPricePolicy.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.parentifid != null and SearchPojo.parentifid != ''">
            or PiPricePolicy.ParentiFid like concat('%', #{SearchPojo.parentifid}, '%')
        </if>
        <if test="SearchPojo.deletelister != null and SearchPojo.deletelister != ''">
            or PiPricePolicy.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            or PiPricePolicy.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or PiPricePolicy.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            or PiPricePolicy.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        )
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PipricepolicyPojo">
        <include refid="selectbillVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiPricePolicy.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.functionid != null and SearchPojo.functionid} != ''">
            and PiPricePolicy.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.parentifid != null and SearchPojo.parentifid} != ''">
            and PiPricePolicy.ParentiFid like concat('%', #{SearchPojo.parentifid}, '%')
        </if>
        <if test="SearchPojo.deletelister != null and SearchPojo.deletelister} != ''">
            and PiPricePolicy.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary} != ''">
            and PiPricePolicy.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister} != ''">
            and PiPricePolicy.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor} != ''">
            and PiPricePolicy.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
    </sql>
    <sql id="thor">
        and (1=0
        <if test="SearchPojo.functionid != null and SearchPojo.functionid != ''">
            or PiPricePolicy.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.parentifid != null and SearchPojo.parentifid != ''">
            or PiPricePolicy.ParentiFid like concat('%', #{SearchPojo.parentifid}, '%')
        </if>
        <if test="SearchPojo.deletelister != null and SearchPojo.deletelister != ''">
            or PiPricePolicy.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            or PiPricePolicy.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or PiPricePolicy.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.assessor != null and SearchPojo.assessor != ''">
            or PiPricePolicy.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        )
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into PiPricePolicy(id, Functionid, ObjectLevel, ReleaseDomain, ExpirationDate, EnabledMark, RowNum,
                                  LevelNum, DeleteMark, DeleteLister, DeleteDate, Summary, CreateBy, CreateByid,
                                  CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, Revision)
        values (#{id}, #{functionid}, #{objectlevel}, #{releasedomain}, #{expirationdate}, #{enabledmark}, #{rownum},
                #{levelnum}, #{deletemark}, #{deletelister}, #{deletedate}, #{summary}, #{createby}, #{createbyid},
                #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate},
                #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiPricePolicy
        <set>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="objectlevel != null ">
                ObjectLevel =#{objectlevel},
            </if>
            <if test="releasedomain != null">
                ReleaseDomain =#{releasedomain},
            </if>
            <if test="expirationdate != null">
                ExpirationDate =#{expirationdate},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="levelnum != null">
                LevelNum =#{levelnum},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>


    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiPricePolicy
        where id = #{key}
    </delete>

    <update id="approval">
        update PiPricePolicy
        SET Assessor   = #{assessor},
            AssessDate = #{assessdate},
            Revision=Revision + 1
        where id = #{id}
    </update>

    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.system.domain.pojo.PipricepolicyPojo">
        select
        id
        from PiPricePolicyItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>
    <!--查询单个-->
    <select id="getEntityByFunction" resultType="inks.system.domain.pojo.PipricepolicyPojo">
        SELECT PiPricePolicy.id,
               PiPricePolicy.Functionid,
               PiPricePolicy.ObjectLevel,
               PiPricePolicy.ReleaseDomain,
               PiPricePolicy.ExpirationDate,
               PiPricePolicy.EnabledMark,
               PiPricePolicy.RowNum,
               PiPricePolicy.LevelNum,
               PiPricePolicy.DeleteMark,
               PiPricePolicy.DeleteLister,
               PiPricePolicy.DeleteDate,
               PiPricePolicy.Summary,
               PiPricePolicy.Lister,
               PiPricePolicy.CreateDate,
               PiPricePolicy.ModifyDate,
               PiPricePolicy.Assessor,
               PiPricePolicy.AssessDate,
               PiPricePolicy.Revision,
               PiFunction.FunctionCode,
               PiFunction.FunctionName,
               PiFunction.Description,
               PiFunction.FrontPhoto
        FROM PiFunction
                 RIGHT JOIN PiPricePolicy ON PiPricePolicy.Functionid = PiFunction.Functionid
        where PiPricePolicy.Functionid = #{key}
        order by PiPricePolicy.LevelNum desc LIMIT 1
    </select>
</mapper>

