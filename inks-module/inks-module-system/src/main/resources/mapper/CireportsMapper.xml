<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CireportsMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CireportsPojo">
        select id,
               GenGroupid,
               ModuleCode,
               RptType,
               RptName,
               RptData,
               PageRow,
               TempUrl,
               FileName,
               PrinterSn,
               RowNum,
               EnabledMark,
               GrfData,
               PaperLength,
               PaperWidth,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from CiReports
        where CiReports.id = #{key}
          and CiReports.Tenantid = #{tid}
    </select>
    <sql id="selectCireportsVo">
        select id,
               GenGroupid,
               ModuleCode,
               RptType,
               RptName,
               RptData,
               PageRow,
               TempUrl,
               FileName,
               PrinterSn,
               RowNum,
               EnabledMark,
               GrfData,
               PaperLength,
               PaperWidth,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from CiReports
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CireportsPojo">
        <include refid="selectCireportsVo"/>
        where 1 = 1 and CiReports.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiReports.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.gengroupid != null ">
            and CiReports.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
        </if>
        <if test="SearchPojo.modulecode != null ">
            and CiReports.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.rpttype != null ">
            and CiReports.RptType like concat('%', #{SearchPojo.rpttype}, '%')
        </if>
        <if test="SearchPojo.rptname != null ">
            and CiReports.RptName like concat('%', #{SearchPojo.rptname}, '%')
        </if>
        <if test="SearchPojo.rptdata != null ">
            and CiReports.RptData like concat('%', #{SearchPojo.rptdata}, '%')
        </if>
        <if test="SearchPojo.tempurl != null ">
            and CiReports.TempUrl like concat('%', #{SearchPojo.tempurl}, '%')
        </if>
        <if test="SearchPojo.filename != null ">
            and CiReports.FileName like concat('%', #{SearchPojo.filename}, '%')
        </if>
        <if test="SearchPojo.printersn != null ">
            and CiReports.PrinterSn like concat('%', #{SearchPojo.printersn}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and CiReports.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and CiReports.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and CiReports.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and CiReports.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and CiReports.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and CiReports.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and CiReports.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and CiReports.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and CiReports.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and CiReports.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.tenantid != null ">
            and CiReports.Tenantid like concat('%', #{SearchPojo.tenantid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and CiReports.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.gengroupid != null ">
                or CiReports.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
            </if>
            <if test="SearchPojo.modulecode != null ">
                or CiReports.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.rpttype != null ">
                or CiReports.RptType like concat('%', #{SearchPojo.rpttype}, '%')
            </if>
            <if test="SearchPojo.rptname != null ">
                or CiReports.RptName like concat('%', #{SearchPojo.rptname}, '%')
            </if>
            <if test="SearchPojo.rptdata != null ">
                or CiReports.RptData like concat('%', #{SearchPojo.rptdata}, '%')
            </if>
            <if test="SearchPojo.tempurl != null ">
                or CiReports.TempUrl like concat('%', #{SearchPojo.tempurl}, '%')
            </if>
            <if test="SearchPojo.filename != null ">
                or CiReports.FileName like concat('%', #{SearchPojo.filename}, '%')
            </if>
            <if test="SearchPojo.printersn != null ">
                or CiReports.PrinterSn like concat('%', #{SearchPojo.printersn}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or CiReports.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or CiReports.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or CiReports.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or CiReports.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or CiReports.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or CiReports.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or CiReports.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or CiReports.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or CiReports.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or CiReports.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.tenantid != null ">
                or CiReports.Tenantid like concat('%', #{SearchPojo.tenantid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or CiReports.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into CiReports(id, GenGroupid, ModuleCode, RptType, RptName, RptData, PageRow, TempUrl, FileName,
                              PrinterSn, RowNum, EnabledMark, GrfData, PaperLength, PaperWidth, Remark, CreateBy,
                              CreateByid, CreateDate, Lister,
                              Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName,
                              Revision)
        values (#{id}, #{gengroupid}, #{modulecode}, #{rpttype}, #{rptname}, #{rptdata}, #{pagerow}, #{tempurl},
                #{filename}, #{printersn}, #{rownum}, #{enabledmark}, #{grfdata}, #{paperlength}, #{paperwidth},
                #{remark}, #{createby}, #{createbyid},
                #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
                #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!-- 批量插入，每次传入 List -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO CiReports(id, GenGroupid, ModuleCode, RptType, RptName, RptData, PageRow, TempUrl, FileName,
                              PrinterSn, RowNum, EnabledMark, GrfData, PaperLength, PaperWidth, Remark, CreateBy,
                              CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4,
                              Custom5, Tenantid, TenantName, Revision)
        VALUES
        <foreach collection="list" item="report" separator=",">
            (#{report.id}, #{report.gengroupid}, #{report.modulecode}, #{report.rpttype}, #{report.rptname},
             #{report.rptdata}, #{report.pagerow}, #{report.tempurl}, #{report.filename},
             #{report.printersn}, #{report.rownum}, #{report.enabledmark}, #{report.grfdata},
             #{report.paperlength}, #{report.paperwidth}, #{report.remark}, #{report.createby},
             #{report.createbyid}, #{report.createdate}, #{report.lister}, #{report.listerid},
             #{report.modifydate}, #{report.custom1}, #{report.custom2}, #{report.custom3},
             #{report.custom4}, #{report.custom5}, #{report.tenantid}, #{report.tenantname},
             #{report.revision})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiReports
        <set>
            <if test="gengroupid != null ">
                GenGroupid =#{gengroupid},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="rpttype != null ">
                RptType =#{rpttype},
            </if>
            <if test="rptname != null ">
                RptName =#{rptname},
            </if>
            <if test="rptdata != null ">
                RptData =#{rptdata},
            </if>
            <if test="pagerow != null">
                PageRow =#{pagerow},
            </if>
            <if test="tempurl != null ">
                TempUrl =#{tempurl},
            </if>
            <if test="filename != null ">
                FileName =#{filename},
            </if>
            <if test="printersn != null ">
                PrinterSn =#{printersn},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="grfdata != null ">
                GrfData =#{grfdata},
            </if>
            <if test="paperlength != null ">
                PaperLength =#{paperlength},
            </if>
            <if test="paperwidth != null ">
                PaperWidth =#{paperwidth},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiReports
        where CiReports.id = #{key}
          and CiReports.Tenantid = #{tid}
    </delete>


    <select id="getListByModuleCode" resultType="inks.system.domain.pojo.CireportsPojo">
        <include refid="selectCireportsVo"/>
        where ModuleCode = #{moduleCode}
        and Tenantid = #{tid}
        Order by RowNum
    </select>


    <select id="getEntityByNameCode" resultType="inks.system.domain.pojo.CireportsPojo">
        <include refid="selectCireportsVo"/>
        where ModuleCode = #{moduleCode}
        and rptname=#{rptname}
        and Tenantid = #{tid}
        Order by RowNum LIMIT 1
    </select>

    <!--查询指定行数据-->
    <select id="getListByDef" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CireportsPojo">
        <include refid="selectCireportsVo"/>
        where CiReports.Tenantid = 'default'
        <if test="moduleCode != null ">
            and CiReports.moduleCode =#{moduleCode}
        </if>
        order by moduleCode
    </select>

    <!--查询指定行数据-->
    <select id="getPageListAll" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CireportsPojo">
        <include refid="selectCireportsVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiReports.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>

    <select id="getDefaultReports" resultType="inks.system.domain.pojo.CireportsPojo">
        <include refid="selectCireportsVo"/>
        where Tenantid = #{tid}
    </select>

    <select id="getListByTid" resultType="inks.system.domain.pojo.CireportsPojo">
        <include refid="selectCireportsVo"/>
        where Tenantid = #{tid}
    </select>
</mapper>

