<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CifninvoiceMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CifninvoicePojo">
        select id,
               RefNo,
               BillType,
               BillTitle,
               BillDate,
               Userid,
               UserName,
               RealName,
               Tenantid,
               TenantCode,
               TenantName,
               Company,
               Email,
               BillTaxAmount,
               InvoTitle,
               TitleType,
               InvoType,
               CreditNum,
               BankName,
               BandAccount,
               BusAddress,
               BusTel,
               Summary,
               CreateBy,
               CreateDate,
               Lister,
               ModifyDate,
               StateCode,
               StateDate,
               DisannulMark,
               DisannulLister,
               DisannulDate,
               InvoUrl,
               InvoListr,
               InvoDate,
               Revision
        from CiFnInvoice
        where CiFnInvoice.id = #{key}
          and CiFnInvoice.Tenantid = #{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select id,
               RefNo,
               BillType,
               BillTitle,
               BillDate,
               Userid,
               UserName,
               RealName,
               Tenantid,
               TenantCode,
               TenantName,
               Company,
               Email,
               BillTaxAmount,
               InvoTitle,
               TitleType,
               InvoType,
               CreditNum,
               BankName,
               BandAccount,
               BusAddress,
               BusTel,
               Summary,
               CreateBy,
               CreateDate,
               Lister,
               ModifyDate,
               StateCode,
               StateDate,
               DisannulMark,
               DisannulLister,
               DisannulDate,
               InvoUrl,
               InvoListr,
               InvoDate,
               Revision
        from CiFnInvoice
    </sql>
    <sql id="selectdetailVo">
        select id,
               RefNo,
               BillType,
               BillTitle,
               BillDate,
               Userid,
               UserName,
               RealName,
               Tenantid,
               TenantCode,
               TenantName,
               Company,
               Email,
               BillTaxAmount,
               InvoTitle,
               TitleType,
               InvoType,
               CreditNum,
               BankName,
               BandAccount,
               BusAddress,
               BusTel,
               Summary,
               CreateBy,
               CreateDate,
               Lister,
               ModifyDate,
               StateCode,
               StateDate,
               DisannulMark,
               DisannulLister,
               DisannulDate,
               InvoUrl,
               InvoListr,
               InvoDate,
               Revision
        from CiFnInvoice
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CifninvoiceitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="tenantid != 'default' ">
            and CiFnOrder.Tenantid =#{tenantid}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiFnInvoice.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and CiFnInvoice.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and CiFnInvoice.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and CiFnInvoice.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.userid != null and SearchPojo.userid != ''">
            and CiFnInvoice.userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.username != null and SearchPojo.username != ''">
            and CiFnInvoice.username like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null and SearchPojo.realname != ''">
            and CiFnInvoice.realname like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.tenantcode != null and SearchPojo.tenantcode != ''">
            and CiFnInvoice.tenantcode like concat('%', #{SearchPojo.tenantcode}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
            and CiFnInvoice.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.company != null and SearchPojo.company != ''">
            and CiFnInvoice.company like concat('%', #{SearchPojo.company}, '%')
        </if>
        <if test="SearchPojo.email != null and SearchPojo.email != ''">
            and CiFnInvoice.email like concat('%', #{SearchPojo.email}, '%')
        </if>
        <if test="SearchPojo.invotitle != null and SearchPojo.invotitle != ''">
            and CiFnInvoice.invotitle like concat('%', #{SearchPojo.invotitle}, '%')
        </if>
        <if test="SearchPojo.titletype != null and SearchPojo.titletype != ''">
            and CiFnInvoice.titletype like concat('%', #{SearchPojo.titletype}, '%')
        </if>
        <if test="SearchPojo.invotype != null and SearchPojo.invotype != ''">
            and CiFnInvoice.invotype like concat('%', #{SearchPojo.invotype}, '%')
        </if>
        <if test="SearchPojo.creditnum != null and SearchPojo.creditnum != ''">
            and CiFnInvoice.creditnum like concat('%', #{SearchPojo.creditnum}, '%')
        </if>
        <if test="SearchPojo.bankname != null and SearchPojo.bankname != ''">
            and CiFnInvoice.bankname like concat('%', #{SearchPojo.bankname}, '%')
        </if>
        <if test="SearchPojo.bandaccount != null and SearchPojo.bandaccount != ''">
            and CiFnInvoice.bandaccount like concat('%', #{SearchPojo.bandaccount}, '%')
        </if>
        <if test="SearchPojo.busaddress != null and SearchPojo.busaddress != ''">
            and CiFnInvoice.busaddress like concat('%', #{SearchPojo.busaddress}, '%')
        </if>
        <if test="SearchPojo.bustel != null and SearchPojo.bustel != ''">
            and CiFnInvoice.bustel like concat('%', #{SearchPojo.bustel}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and CiFnInvoice.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and CiFnInvoice.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and CiFnInvoice.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and CiFnInvoice.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            and CiFnInvoice.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.invourl != null and SearchPojo.invourl != ''">
            and CiFnInvoice.invourl like concat('%', #{SearchPojo.invourl}, '%')
        </if>
        <if test="SearchPojo.involistr != null and SearchPojo.involistr != ''">
            and CiFnInvoice.involistr like concat('%', #{SearchPojo.involistr}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            or CiFnInvoice.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            or CiFnInvoice.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            or CiFnInvoice.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.userid != null and SearchPojo.userid != ''">
            or CiFnInvoice.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.username != null and SearchPojo.username != ''">
            or CiFnInvoice.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null and SearchPojo.realname != ''">
            or CiFnInvoice.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.tenantcode != null and SearchPojo.tenantcode != ''">
            or CiFnInvoice.TenantCode like concat('%', #{SearchPojo.tenantcode}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
            or CiFnInvoice.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.company != null and SearchPojo.company != ''">
            or CiFnInvoice.Company like concat('%', #{SearchPojo.company}, '%')
        </if>
        <if test="SearchPojo.email != null and SearchPojo.email != ''">
            or CiFnInvoice.Email like concat('%', #{SearchPojo.email}, '%')
        </if>
        <if test="SearchPojo.invotitle != null and SearchPojo.invotitle != ''">
            or CiFnInvoice.InvoTitle like concat('%', #{SearchPojo.invotitle}, '%')
        </if>
        <if test="SearchPojo.titletype != null and SearchPojo.titletype != ''">
            or CiFnInvoice.TitleType like concat('%', #{SearchPojo.titletype}, '%')
        </if>
        <if test="SearchPojo.invotype != null and SearchPojo.invotype != ''">
            or CiFnInvoice.InvoType like concat('%', #{SearchPojo.invotype}, '%')
        </if>
        <if test="SearchPojo.creditnum != null and SearchPojo.creditnum != ''">
            or CiFnInvoice.CreditNum like concat('%', #{SearchPojo.creditnum}, '%')
        </if>
        <if test="SearchPojo.bankname != null and SearchPojo.bankname != ''">
            or CiFnInvoice.BankName like concat('%', #{SearchPojo.bankname}, '%')
        </if>
        <if test="SearchPojo.bandaccount != null and SearchPojo.bandaccount != ''">
            or CiFnInvoice.BandAccount like concat('%', #{SearchPojo.bandaccount}, '%')
        </if>
        <if test="SearchPojo.busaddress != null and SearchPojo.busaddress != ''">
            or CiFnInvoice.BusAddress like concat('%', #{SearchPojo.busaddress}, '%')
        </if>
        <if test="SearchPojo.bustel != null and SearchPojo.bustel != ''">
            or CiFnInvoice.BusTel like concat('%', #{SearchPojo.bustel}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            or CiFnInvoice.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or CiFnInvoice.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or CiFnInvoice.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            or CiFnInvoice.StateCode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            or CiFnInvoice.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.invourl != null and SearchPojo.invourl != ''">
            or CiFnInvoice.InvoUrl like concat('%', #{SearchPojo.invourl}, '%')
        </if>
        <if test="SearchPojo.involistr != null and SearchPojo.involistr != ''">
            or CiFnInvoice.InvoListr like concat('%', #{SearchPojo.involistr}, '%')
        </if>
        )
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CifninvoicePojo">
        <include refid="selectbillVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="tenantid != 'default' ">
            and CiFnOrder.Tenantid =#{tenantid}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiFnInvoice.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>

    <sql id="thand">
        <if test="SearchPojo.refno != null and SearchPojo.refno} != ''">
            and CiFnInvoice.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype} != ''">
            and CiFnInvoice.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle} != ''">
            and CiFnInvoice.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.userid != null and SearchPojo.userid} != ''">
            and CiFnInvoice.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.username != null and SearchPojo.username} != ''">
            and CiFnInvoice.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null and SearchPojo.realname} != ''">
            and CiFnInvoice.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.tenantcode != null and SearchPojo.tenantcode} != ''">
            and CiFnInvoice.TenantCode like concat('%', #{SearchPojo.tenantcode}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname} != ''">
            and CiFnInvoice.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.company != null and SearchPojo.company} != ''">
            and CiFnInvoice.Company like concat('%', #{SearchPojo.company}, '%')
        </if>
        <if test="SearchPojo.email != null and SearchPojo.email} != ''">
            and CiFnInvoice.Email like concat('%', #{SearchPojo.email}, '%')
        </if>
        <if test="SearchPojo.invotitle != null and SearchPojo.invotitle} != ''">
            and CiFnInvoice.InvoTitle like concat('%', #{SearchPojo.invotitle}, '%')
        </if>
        <if test="SearchPojo.titletype != null and SearchPojo.titletype} != ''">
            and CiFnInvoice.TitleType like concat('%', #{SearchPojo.titletype}, '%')
        </if>
        <if test="SearchPojo.invotype != null and SearchPojo.invotype} != ''">
            and CiFnInvoice.InvoType like concat('%', #{SearchPojo.invotype}, '%')
        </if>
        <if test="SearchPojo.creditnum != null and SearchPojo.creditnum} != ''">
            and CiFnInvoice.CreditNum like concat('%', #{SearchPojo.creditnum}, '%')
        </if>
        <if test="SearchPojo.bankname != null and SearchPojo.bankname} != ''">
            and CiFnInvoice.BankName like concat('%', #{SearchPojo.bankname}, '%')
        </if>
        <if test="SearchPojo.bandaccount != null and SearchPojo.bandaccount} != ''">
            and CiFnInvoice.BandAccount like concat('%', #{SearchPojo.bandaccount}, '%')
        </if>
        <if test="SearchPojo.busaddress != null and SearchPojo.busaddress} != ''">
            and CiFnInvoice.BusAddress like concat('%', #{SearchPojo.busaddress}, '%')
        </if>
        <if test="SearchPojo.bustel != null and SearchPojo.bustel} != ''">
            and CiFnInvoice.BusTel like concat('%', #{SearchPojo.bustel}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary} != ''">
            and CiFnInvoice.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby} != ''">
            and CiFnInvoice.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister} != ''">
            and CiFnInvoice.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode} != ''">
            and CiFnInvoice.StateCode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister} != ''">
            and CiFnInvoice.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.invourl != null and SearchPojo.invourl} != ''">
            and CiFnInvoice.InvoUrl like concat('%', #{SearchPojo.invourl}, '%')
        </if>
        <if test="SearchPojo.involistr != null and SearchPojo.involistr} != ''">
            and CiFnInvoice.InvoListr like concat('%', #{SearchPojo.involistr}, '%')
        </if>
    </sql>
    <sql id="thor">
        and (1=0
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            or CiFnInvoice.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            or CiFnInvoice.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            or CiFnInvoice.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.userid != null and SearchPojo.userid != ''">
            or CiFnInvoice.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.username != null and SearchPojo.username != ''">
            or CiFnInvoice.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null and SearchPojo.realname != ''">
            or CiFnInvoice.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.tenantcode != null and SearchPojo.tenantcode != ''">
            or CiFnInvoice.TenantCode like concat('%', #{SearchPojo.tenantcode}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
            or CiFnInvoice.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.company != null and SearchPojo.company != ''">
            or CiFnInvoice.Company like concat('%', #{SearchPojo.company}, '%')
        </if>
        <if test="SearchPojo.email != null and SearchPojo.email != ''">
            or CiFnInvoice.Email like concat('%', #{SearchPojo.email}, '%')
        </if>
        <if test="SearchPojo.invotitle != null and SearchPojo.invotitle != ''">
            or CiFnInvoice.InvoTitle like concat('%', #{SearchPojo.invotitle}, '%')
        </if>
        <if test="SearchPojo.titletype != null and SearchPojo.titletype != ''">
            or CiFnInvoice.TitleType like concat('%', #{SearchPojo.titletype}, '%')
        </if>
        <if test="SearchPojo.invotype != null and SearchPojo.invotype != ''">
            or CiFnInvoice.InvoType like concat('%', #{SearchPojo.invotype}, '%')
        </if>
        <if test="SearchPojo.creditnum != null and SearchPojo.creditnum != ''">
            or CiFnInvoice.CreditNum like concat('%', #{SearchPojo.creditnum}, '%')
        </if>
        <if test="SearchPojo.bankname != null and SearchPojo.bankname != ''">
            or CiFnInvoice.BankName like concat('%', #{SearchPojo.bankname}, '%')
        </if>
        <if test="SearchPojo.bandaccount != null and SearchPojo.bandaccount != ''">
            or CiFnInvoice.BandAccount like concat('%', #{SearchPojo.bandaccount}, '%')
        </if>
        <if test="SearchPojo.busaddress != null and SearchPojo.busaddress != ''">
            or CiFnInvoice.BusAddress like concat('%', #{SearchPojo.busaddress}, '%')
        </if>
        <if test="SearchPojo.bustel != null and SearchPojo.bustel != ''">
            or CiFnInvoice.BusTel like concat('%', #{SearchPojo.bustel}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            or CiFnInvoice.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or CiFnInvoice.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or CiFnInvoice.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            or CiFnInvoice.StateCode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            or CiFnInvoice.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.invourl != null and SearchPojo.invourl != ''">
            or CiFnInvoice.InvoUrl like concat('%', #{SearchPojo.invourl}, '%')
        </if>
        <if test="SearchPojo.involistr != null and SearchPojo.involistr != ''">
            or CiFnInvoice.InvoListr like concat('%', #{SearchPojo.involistr}, '%')
        </if>
        )
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into CiFnInvoice(id, RefNo, BillType, BillTitle, BillDate, Userid, UserName, RealName, Tenantid,
                                TenantCode, TenantName, Company, Email, BillTaxAmount, InvoTitle, TitleType, InvoType,
                                CreditNum, BankName, BandAccount, BusAddress, BusTel, Summary, CreateBy, CreateDate,
                                Lister, ModifyDate, StateCode, StateDate, DisannulMark, DisannulLister, DisannulDate,
                                InvoUrl, InvoListr, InvoDate, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{userid}, #{username}, #{realname},
                #{tenantid}, #{tenantcode}, #{tenantname}, #{company}, #{email}, #{billtaxamount}, #{invotitle},
                #{titletype}, #{invotype}, #{creditnum}, #{bankname}, #{bandaccount}, #{busaddress}, #{bustel},
                #{summary}, #{createby}, #{createdate}, #{lister}, #{modifydate}, #{statecode}, #{statedate},
                #{disannulmark}, #{disannullister}, #{disannuldate}, #{invourl}, #{involistr}, #{invodate}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiFnInvoice
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="tenantcode != null ">
                TenantCode =#{tenantcode},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            <if test="company != null ">
                Company =#{company},
            </if>
            <if test="email != null ">
                Email =#{email},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="invotitle != null ">
                InvoTitle =#{invotitle},
            </if>
            <if test="titletype != null ">
                TitleType =#{titletype},
            </if>
            <if test="invotype != null ">
                InvoType =#{invotype},
            </if>
            <if test="creditnum != null ">
                CreditNum =#{creditnum},
            </if>
            <if test="bankname != null ">
                BankName =#{bankname},
            </if>
            <if test="bandaccount != null ">
                BandAccount =#{bandaccount},
            </if>
            <if test="busaddress != null ">
                BusAddress =#{busaddress},
            </if>
            <if test="bustel != null ">
                BusTel =#{bustel},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="statecode != null ">
                StateCode =#{statecode},
            </if>
            <if test="statedate != null">
                StateDate =#{statedate},
            </if>
            <if test="disannulmark != null">
                DisannulMark =#{disannulmark},
            </if>
            <if test="disannullister != null ">
                DisannulLister =#{disannullister},
            </if>
            <if test="disannuldate != null">
                DisannulDate =#{disannuldate},
            </if>
            <if test="invourl != null ">
                InvoUrl =#{invourl},
            </if>
            <if test="involistr != null ">
                InvoListr =#{involistr},
            </if>
            <if test="invodate != null">
                InvoDate =#{invodate},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiFnInvoice
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.system.domain.pojo.CifninvoicePojo">
        select
        id
        from CiFnInvoiceItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>

</mapper>

