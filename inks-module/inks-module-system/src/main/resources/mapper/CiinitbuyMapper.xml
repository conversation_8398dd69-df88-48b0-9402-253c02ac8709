<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CiinitbuyMapper">

    <!--通过主键删除-->
    <delete id="deletePlanItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Buy_PlanItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Buy_Plan
                      where Tenantid = #{tenantid}
                        and Buy_Plan.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deletePlan" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Buy_Plan
        where Tenantid = #{tenantid}
          and Buy_Plan.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>

    <!--通过主键删除-->
    <delete id="deleteOrder" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Buy_Order
        where Tenantid = #{tenantid}
          and Buy_Order.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>
    <!--通过主键删除-->
    <delete id="deleteOrderItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Buy_OrderItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Buy_Order
                      where Tenantid = #{tenantid}
                        and Buy_Order.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>

    <!--通过主键删除-->
    <delete id="deleteFini" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Buy_Finishing
        where Tenantid = #{tenantid}
          and Buy_Finishing.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>
    <!--通过主键删除-->
    <delete id="deleteFiniItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Buy_FinishingItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Buy_Finishing
                      where Tenantid = #{tenantid}
                        and Buy_Finishing.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deleteDedu" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Buy_Deduction
        where Tenantid = #{tenantid}
          and Buy_Deduction.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>
    <!--通过主键删除-->
    <delete id="deleteDeduItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Buy_DeductionItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Buy_Deduction
                      where Tenantid = #{tenantid}
                        and Buy_Deduction.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deleteInvo" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Buy_Invoice
        where Tenantid = #{tenantid}
          and Buy_Invoice.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>
    <!--通过主键删除-->
    <delete id="deleteInvoItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Buy_InvoiceItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Buy_Invoice
                      where Tenantid = #{tenantid}
                        and Buy_Invoice.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deletePrep" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Buy_Prepayments
        where Tenantid = #{tenantid}
          and Buy_Prepayments.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>
    <!--通过主键删除-->
    <delete id="deletePrepItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Buy_PrepaymentsItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Buy_Prepayments
                      where Tenantid = #{tenantid}
                        and Buy_Prepayments.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deletePrepCash" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Buy_PrepaymentsCash
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Buy_Prepayments
                      where Tenantid = #{tenantid}
                        and Buy_Prepayments.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>

    <!--通过主键删除-->
    <delete id="deleteVouc" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Buy_Voucher
        where Tenantid = #{tenantid}
          and Buy_Voucher.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>
    <!--通过主键删除-->
    <delete id="deleteVoucItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Buy_VoucherItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Buy_Voucher
                      where Tenantid = #{tenantid}
                        and Buy_Voucher.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deleteVoucCash" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Buy_VoucherCash
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Buy_Voucher
                      where Tenantid = #{tenantid}
                        and Buy_Voucher.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>


    <!--通过主键删除-->
    <delete id="deleteAcco" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Buy_Account
        where Tenantid = #{tenantid}
          and Buy_Account.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>
    <!--通过主键删除-->
    <delete id="deleteAccoItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Buy_AccountItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Buy_Account
                      where Tenantid = #{tenantid}
                        and Buy_Account.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deleteAccoRec" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Buy_AccountRec
        where Tenantid = #{tenantid}
          and Buy_AccountRec.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>
</mapper>

