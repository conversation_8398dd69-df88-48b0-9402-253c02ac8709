<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CibillcodeMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CibillcodePojo">
        SELECT CiBillCode.id,
               CiBillCode.ModuleCode,
               CiBillCode.BillName,
               CiBillCode.Prefix1,
               CiBillCode.Suffix1,
               CiBillCode.Prefix2,
               CiBillCode.Suffix2,
               CiBillCode.Prefix3,
               CiBillCode.Suffix3,
               CiBillCode.Prefix4,
               CiBillCode.Suffix4,
               CiBillCode.Prefix5,
               CiBillCode.Suffix5,
               CiBillCode.CountType,
               CiBillCode.Step,
               CiBillCode.CurrentNum,
               CiBillCode.TableName,
               CiBillCode.DateColumn,
               CiBillCode.ColumnName,
               CiBillCode.DbFilter,
               CiBillCode.AllowEdit,
               CiBillCode.AllowDelete,
               CiBillCode.Param1,
               CiBillCode.Param2,
               CiBillCode.Param3,
               CiBillCode.Param4,
               CiBillCode.Param5,
               CiBillCode.Remark,
               CiBillCode.Lister,
               CiBillCode.CreateDate,
               CiBillCode.ModifyDate,
               CiBillCode.Tenantid,
               PiTenant.TenantName
        FROM CiBillCode
                 LEFT JOIN PiTenant ON CiBillCode.Tenantid = PiTenant.Tenantid
        where CiBillCode.id = #{key}
    </select>
    <sql id="selectCibillcodeVo">
        SELECT CiBillCode.id,
               CiBillCode.ModuleCode,
               CiBillCode.BillName,
               CiBillCode.Prefix1,
               CiBillCode.Suffix1,
               CiBillCode.Prefix2,
               CiBillCode.Suffix2,
               CiBillCode.Prefix3,
               CiBillCode.Suffix3,
               CiBillCode.Prefix4,
               CiBillCode.Suffix4,
               CiBillCode.Prefix5,
               CiBillCode.Suffix5,
               CiBillCode.CountType,
               CiBillCode.Step,
               CiBillCode.CurrentNum,
               CiBillCode.TableName,
               CiBillCode.DateColumn,
               CiBillCode.ColumnName,
               CiBillCode.DbFilter,
               CiBillCode.AllowEdit,
               CiBillCode.AllowDelete,
               CiBillCode.Param1,
               CiBillCode.Param2,
               CiBillCode.Param3,
               CiBillCode.Param4,
               CiBillCode.Param5,
               CiBillCode.Remark,
               CiBillCode.Lister,
               CiBillCode.CreateDate,
               CiBillCode.ModifyDate,
               CiBillCode.Tenantid,
               PiTenant.TenantName
        FROM CiBillCode
                 LEFT JOIN PiTenant ON CiBillCode.Tenantid = PiTenant.Tenantid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CibillcodePojo">
        <include refid="selectCibillcodeVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiBillCode.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.modulecode != null and SearchPojo.modulecode  != ''">
            and CiBillCode.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.billname != null and SearchPojo.billname  != ''">
            and CiBillCode.BillName like concat('%', #{SearchPojo.billname}, '%')
        </if>
        <if test="SearchPojo.prefix1 != null and SearchPojo.prefix1  != ''">
            and CiBillCode.Prefix1 like concat('%', #{SearchPojo.prefix1}, '%')
        </if>
        <if test="SearchPojo.suffix1 != null and SearchPojo.suffix1  != ''">
            and CiBillCode.Suffix1 like concat('%', #{SearchPojo.suffix1}, '%')
        </if>
        <if test="SearchPojo.prefix2 != null and SearchPojo.prefix2  != ''">
            and CiBillCode.Prefix2 like concat('%', #{SearchPojo.prefix2}, '%')
        </if>
        <if test="SearchPojo.suffix2 != null and SearchPojo.suffix2  != ''">
            and CiBillCode.Suffix2 like concat('%', #{SearchPojo.suffix2}, '%')
        </if>
        <if test="SearchPojo.prefix3 != null and SearchPojo.prefix3  != ''">
            and CiBillCode.Prefix3 like concat('%', #{SearchPojo.prefix3}, '%')
        </if>
        <if test="SearchPojo.suffix3 != null and SearchPojo.suffix3  != ''">
            and CiBillCode.Suffix3 like concat('%', #{SearchPojo.suffix3}, '%')
        </if>
        <if test="SearchPojo.prefix4 != null and SearchPojo.prefix4  != ''">
            and CiBillCode.Prefix4 like concat('%', #{SearchPojo.prefix4}, '%')
        </if>
        <if test="SearchPojo.suffix4 != null and SearchPojo.suffix4  != ''">
            and CiBillCode.Suffix4 like concat('%', #{SearchPojo.suffix4}, '%')
        </if>
        <if test="SearchPojo.prefix5 != null and SearchPojo.prefix5  != ''">
            and CiBillCode.Prefix5 like concat('%', #{SearchPojo.prefix5}, '%')
        </if>
        <if test="SearchPojo.suffix5 != null and SearchPojo.suffix5  != ''">
            and CiBillCode.Suffix5 like concat('%', #{SearchPojo.suffix5}, '%')
        </if>
        <if test="SearchPojo.counttype != null and SearchPojo.counttype  != ''">
            and CiBillCode.CountType like concat('%', #{SearchPojo.counttype}, '%')
        </if>
        <if test="SearchPojo.tablename != null and SearchPojo.tablename  != ''">
            and CiBillCode.TableName like concat('%', #{SearchPojo.tablename}, '%')
        </if>
        <if test="SearchPojo.datecolumn != null and SearchPojo.datecolumn  != ''">
            and CiBillCode.DateColumn like concat('%', #{SearchPojo.datecolumn}, '%')
        </if>
        <if test="SearchPojo.columnname != null and SearchPojo.columnname  != ''">
            and CiBillCode.ColumnName like concat('%', #{SearchPojo.columnname}, '%')
        </if>
        <if test="SearchPojo.dbfilter != null and SearchPojo.dbfilter  != ''">
            and CiBillCode.DbFilter like concat('%', #{SearchPojo.dbfilter}, '%')
        </if>
        <if test="SearchPojo.param1 != null and SearchPojo.param1  != ''">
            and CiBillCode.Param1 like concat('%', #{SearchPojo.param1}, '%')
        </if>
        <if test="SearchPojo.param2 != null and SearchPojo.param2  != ''">
            and CiBillCode.Param2 like concat('%', #{SearchPojo.param2}, '%')
        </if>
        <if test="SearchPojo.param3 != null and SearchPojo.param3  != ''">
            and CiBillCode.Param3 like concat('%', #{SearchPojo.param3}, '%')
        </if>
        <if test="SearchPojo.param4 != null and SearchPojo.param4  != ''">
            and CiBillCode.Param4 like concat('%', #{SearchPojo.param4}, '%')
        </if>
        <if test="SearchPojo.param5 != null and SearchPojo.param5  != ''">
            and CiBillCode.Param5 like concat('%', #{SearchPojo.param5}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark  != ''">
            and CiBillCode.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and CiBillCode.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.modulecode != null and SearchPojo.modulecode != ''">
            or CiBillCode.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.billname != null and SearchPojo.billname != ''">
            or CiBillCode.BillName like concat('%', #{SearchPojo.billname}, '%')
        </if>
        <if test="SearchPojo.prefix1 != null and SearchPojo.prefix1 != ''">
            or CiBillCode.Prefix1 like concat('%', #{SearchPojo.prefix1}, '%')
        </if>
        <if test="SearchPojo.suffix1 != null and SearchPojo.suffix1 != ''">
            or CiBillCode.Suffix1 like concat('%', #{SearchPojo.suffix1}, '%')
        </if>
        <if test="SearchPojo.prefix2 != null and SearchPojo.prefix2 != ''">
            or CiBillCode.Prefix2 like concat('%', #{SearchPojo.prefix2}, '%')
        </if>
        <if test="SearchPojo.suffix2 != null and SearchPojo.suffix2 != ''">
            or CiBillCode.Suffix2 like concat('%', #{SearchPojo.suffix2}, '%')
        </if>
        <if test="SearchPojo.prefix3 != null and SearchPojo.prefix3 != ''">
            or CiBillCode.Prefix3 like concat('%', #{SearchPojo.prefix3}, '%')
        </if>
        <if test="SearchPojo.suffix3 != null and SearchPojo.suffix3 != ''">
            or CiBillCode.Suffix3 like concat('%', #{SearchPojo.suffix3}, '%')
        </if>
        <if test="SearchPojo.prefix4 != null and SearchPojo.prefix4 != ''">
            or CiBillCode.Prefix4 like concat('%', #{SearchPojo.prefix4}, '%')
        </if>
        <if test="SearchPojo.suffix4 != null and SearchPojo.suffix4 != ''">
            or CiBillCode.Suffix4 like concat('%', #{SearchPojo.suffix4}, '%')
        </if>
        <if test="SearchPojo.prefix5 != null and SearchPojo.prefix5 != ''">
            or CiBillCode.Prefix5 like concat('%', #{SearchPojo.prefix5}, '%')
        </if>
        <if test="SearchPojo.suffix5 != null and SearchPojo.suffix5 != ''">
            or CiBillCode.Suffix5 like concat('%', #{SearchPojo.suffix5}, '%')
        </if>
        <if test="SearchPojo.counttype != null and SearchPojo.counttype != ''">
            or CiBillCode.CountType like concat('%', #{SearchPojo.counttype}, '%')
        </if>
        <if test="SearchPojo.tablename != null and SearchPojo.tablename != ''">
            or CiBillCode.TableName like concat('%', #{SearchPojo.tablename}, '%')
        </if>
        <if test="SearchPojo.datecolumn != null and SearchPojo.datecolumn != ''">
            or CiBillCode.DateColumn like concat('%', #{SearchPojo.datecolumn}, '%')
        </if>
        <if test="SearchPojo.columnname != null and SearchPojo.columnname != ''">
            or CiBillCode.ColumnName like concat('%', #{SearchPojo.columnname}, '%')
        </if>
        <if test="SearchPojo.dbfilter != null and SearchPojo.dbfilter != ''">
            or CiBillCode.DbFilter like concat('%', #{SearchPojo.dbfilter}, '%')
        </if>
        <if test="SearchPojo.param1 != null and SearchPojo.param1 != ''">
            or CiBillCode.Param1 like concat('%', #{SearchPojo.param1}, '%')
        </if>
        <if test="SearchPojo.param2 != null and SearchPojo.param2 != ''">
            or CiBillCode.Param2 like concat('%', #{SearchPojo.param2}, '%')
        </if>
        <if test="SearchPojo.param3 != null and SearchPojo.param3 != ''">
            or CiBillCode.Param3 like concat('%', #{SearchPojo.param3}, '%')
        </if>
        <if test="SearchPojo.param4 != null and SearchPojo.param4 != ''">
            or CiBillCode.Param4 like concat('%', #{SearchPojo.param4}, '%')
        </if>
        <if test="SearchPojo.param5 != null and SearchPojo.param5 != ''">
            or CiBillCode.Param5 like concat('%', #{SearchPojo.param5}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or CiBillCode.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or CiBillCode.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into CiBillCode(id, ModuleCode, BillName, Prefix1, Suffix1, Prefix2, Suffix2, Prefix3, Suffix3, Prefix4,
                               Suffix4, Prefix5, Suffix5, CountType, Step, CurrentNum, TableName, DateColumn,
                               ColumnName, DbFilter, AllowEdit, AllowDelete, Param1, Param2, Param3, Param4, Param5,
                               Remark, Lister, CreateDate, ModifyDate, Tenantid)
        values (#{id}, #{modulecode}, #{billname}, #{prefix1}, #{suffix1}, #{prefix2}, #{suffix2}, #{prefix3},
                #{suffix3}, #{prefix4}, #{suffix4}, #{prefix5}, #{suffix5}, #{counttype}, #{step}, #{currentnum},
                #{tablename}, #{datecolumn}, #{columnname}, #{dbfilter}, #{allowedit}, #{allowdelete}, #{param1},
                #{param2}, #{param3}, #{param4}, #{param5}, #{remark}, #{lister}, #{createdate}, #{modifydate},
                #{tenantid})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiBillCode
        <set>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="billname != null ">
                BillName =#{billname},
            </if>
            <if test="prefix1 != null ">
                Prefix1 =#{prefix1},
            </if>
            <if test="suffix1 != null ">
                Suffix1 =#{suffix1},
            </if>
            <if test="prefix2 != null ">
                Prefix2 =#{prefix2},
            </if>
            <if test="suffix2 != null ">
                Suffix2 =#{suffix2},
            </if>
            <if test="prefix3 != null ">
                Prefix3 =#{prefix3},
            </if>
            <if test="suffix3 != null ">
                Suffix3 =#{suffix3},
            </if>
            <if test="prefix4 != null ">
                Prefix4 =#{prefix4},
            </if>
            <if test="suffix4 != null ">
                Suffix4 =#{suffix4},
            </if>
            <if test="prefix5 != null ">
                Prefix5 =#{prefix5},
            </if>
            <if test="suffix5 != null ">
                Suffix5 =#{suffix5},
            </if>
            <if test="counttype != null ">
                CountType =#{counttype},
            </if>
            <if test="step != null">
                Step =#{step},
            </if>
            <if test="currentnum != null">
                CurrentNum =#{currentnum},
            </if>
            <if test="tablename != null ">
                TableName =#{tablename},
            </if>
            <if test="datecolumn != null ">
                DateColumn =#{datecolumn},
            </if>
            <if test="columnname != null ">
                ColumnName =#{columnname},
            </if>
            <if test="dbfilter != null ">
                DbFilter =#{dbfilter},
            </if>
            <if test="allowedit != null">
                AllowEdit =#{allowedit},
            </if>
            <if test="allowdelete != null">
                AllowDelete =#{allowdelete},
            </if>
            <if test="param1 != null ">
                Param1 =#{param1},
            </if>
            <if test="param2 != null ">
                Param2 =#{param2},
            </if>
            <if test="param3 != null ">
                Param3 =#{param3},
            </if>
            <if test="param4 != null ">
                Param4 =#{param4},
            </if>
            <if test="param5 != null ">
                Param5 =#{param5},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantid != null">
                Tenantid =#{tenantid},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiBillCode
        where id = #{key}
    </delete>

    <select id="getEntityByModuleCode" resultType="inks.system.domain.CibillcodeEntity">
        <include refid="selectCibillcodeVo"/>
        where CiBillCode.ModuleCode=#{ModuleCode} AND CiBillCode.Tenantid = #{tid}
    </select>

    <select id="getSerialNo" resultType="java.util.HashMap">
        select IFNULL(max(substring(RefNo,${subindex})),1) as RefNo from ${entity.TableName}
        where Tenantid = #{tid} and
        <if test="entity.CountType == 'year'">
            DATE_FORMAT(${entity.DateColumn}, '%Y')= DATE_FORMAT(#{currentDate}, '%Y')
        </if>
        <if test="entity.CountType == 'month'">
            DATE_FORMAT(${entity.DateColumn}, '%Y-%m')= DATE_FORMAT(#{currentDate}, '%Y-%m')
        </if>
        <if test="entity.CountType == 'day'">
            DATE_FORMAT(${entity.DateColumn}, '%Y-%m-%d')= DATE_FORMAT(#{currentDate}, '%Y-%m-%d')
        </if>
        <if test="entity.Dbfilter != null and entity.Dbfilter != ''">
            ${entity.Dbfilter}
        </if>
        Order by BillDate Desc
        LIMIT 1
    </select>

    <select id="getDefaultBillCodes" resultType="inks.system.domain.pojo.CibillcodePojo">
        <include refid="selectCibillcodeVo"/>
        where CiBillCode.Tenantid = #{tid}
    </select>
</mapper>

