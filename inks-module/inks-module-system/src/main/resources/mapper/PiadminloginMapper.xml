<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PiadminloginMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PiadminloginPojo">
        select
          id, Adminid, UserPassword, CheckIpAddr, Ipaddress, MacAddress, FirstVisit, PreviouVisit, BrowserName, HostSystem, Lister, CreateDate, ModifyDate        from PiAdminLogin
        where PiAdminLogin.id = #{key}
    </select>
    <sql id="selectPiadminloginVo">
         select
          id, Adminid, UserPassword, CheckIpAddr, Ipaddress, MacAddress, FirstVisit, PreviouVisit, BrowserName, HostSystem, Lister, <PERSON>reateDate, ModifyDate        from PiAdminLogin
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.PiadminloginPojo">
        <include refid="selectPiadminloginVo"/>
         where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and PiAdminLogin.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>

             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.adminid != null and SearchPojo.adminid  != ''">
   and PiAdminLogin.Adminid like concat('%', #{SearchPojo.adminid}, '%')
</if>
<if test="SearchPojo.userpassword != null and SearchPojo.userpassword  != ''">
   and PiAdminLogin.UserPassword like concat('%', #{SearchPojo.userpassword}, '%')
</if>
<if test="SearchPojo.ipaddress != null and SearchPojo.ipaddress  != ''">
   and PiAdminLogin.Ipaddress like concat('%', #{SearchPojo.ipaddress}, '%')
</if>
<if test="SearchPojo.macaddress != null and SearchPojo.macaddress  != ''">
   and PiAdminLogin.MacAddress like concat('%', #{SearchPojo.macaddress}, '%')
</if>
<if test="SearchPojo.browsername != null and SearchPojo.browsername  != ''">
   and PiAdminLogin.BrowserName like concat('%', #{SearchPojo.browsername}, '%')
</if>
<if test="SearchPojo.hostsystem != null and SearchPojo.hostsystem  != ''">
   and PiAdminLogin.HostSystem like concat('%', #{SearchPojo.hostsystem}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
   and PiAdminLogin.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
     </sql>   
     <sql id="or">
     and (1=0 
<if test="SearchPojo.adminid != null and SearchPojo.adminid != ''">
   or PiAdminLogin.Adminid like concat('%', #{SearchPojo.adminid}, '%')
</if>
<if test="SearchPojo.userpassword != null and SearchPojo.userpassword != ''">
   or PiAdminLogin.UserPassword like concat('%', #{SearchPojo.userpassword}, '%')
</if>
<if test="SearchPojo.ipaddress != null and SearchPojo.ipaddress != ''">
   or PiAdminLogin.Ipaddress like concat('%', #{SearchPojo.ipaddress}, '%')
</if>
<if test="SearchPojo.macaddress != null and SearchPojo.macaddress != ''">
   or PiAdminLogin.MacAddress like concat('%', #{SearchPojo.macaddress}, '%')
</if>
<if test="SearchPojo.browsername != null and SearchPojo.browsername != ''">
   or PiAdminLogin.BrowserName like concat('%', #{SearchPojo.browsername}, '%')
</if>
<if test="SearchPojo.hostsystem != null and SearchPojo.hostsystem != ''">
   or PiAdminLogin.HostSystem like concat('%', #{SearchPojo.hostsystem}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   or PiAdminLogin.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
)
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into PiAdminLogin(id, Adminid, UserPassword, CheckIpAddr, Ipaddress, MacAddress, FirstVisit, PreviouVisit, BrowserName, HostSystem, Lister, CreateDate, ModifyDate)
        values (#{id}, #{adminid}, #{userpassword}, #{checkipaddr}, #{ipaddress}, #{macaddress}, #{firstvisit}, #{previouvisit}, #{browsername}, #{hostsystem}, #{lister}, #{createdate}, #{modifydate})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update PiAdminLogin
        <set>
            <if test="adminid != null ">
                Adminid =#{adminid},
            </if>
            <if test="userpassword != null ">
                UserPassword =#{userpassword},
            </if>
            <if test="checkipaddr != null">
                CheckIpAddr =#{checkipaddr},
            </if>
            <if test="ipaddress != null ">
                Ipaddress =#{ipaddress},
            </if>
            <if test="macaddress != null ">
                MacAddress =#{macaddress},
            </if>
            <if test="firstvisit != null">
                FirstVisit =#{firstvisit},
            </if>
            <if test="previouvisit != null">
                PreviouVisit =#{previouvisit},
            </if>
            <if test="browsername != null ">
                BrowserName =#{browsername},
            </if>
            <if test="hostsystem != null ">
                HostSystem =#{hostsystem},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from PiAdminLogin where id = #{key}
    </delete>

</mapper>

