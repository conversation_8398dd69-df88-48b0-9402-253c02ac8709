<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CibigdataMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CibigdataPojo">
        select id,
               BdType,
               BdCode,
               BdName,
               BdTitle,
               FrontPhoto,
               ImageCss,
               MvcUrl,
               RowNum,
               EnabledMark,
               IsPublic,
               PermissionCode,
               Tenantid,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Revision
        from CiBigData
        where CiBigData.id = #{key}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select id,
               BdType,
               BdCode,
               BdName,
               BdTitle,
               FrontPhoto,
               ImageCss,
               MvcUrl,
               RowNum,
               EnabledMark,
               IsPublic,
               PermissionCode,
               Tenantid,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Revision
        from CiBigData
    </sql>
    <sql id="selectdetailVo">
        select id,
               BdType,
               BdCode,
               BdName,
               BdTitle,
               FrontPhoto,
               ImageCss,
               MvcUrl,
               RowNum,
               EnabledMark,
               IsPublic,
               PermissionCode,
               Tenantid,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Revision
        from CiBigData
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CibigdataitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiBigData.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.bdtype != null and SearchPojo.bdtype != ''">
            and CiBigData.bdtype like concat('%', #{SearchPojo.bdtype}, '%')
        </if>
        <if test="SearchPojo.bdcode != null and SearchPojo.bdcode != ''">
            and CiBigData.bdcode like concat('%', #{SearchPojo.bdcode}, '%')
        </if>
        <if test="SearchPojo.bdname != null and SearchPojo.bdname != ''">
            and CiBigData.bdname like concat('%', #{SearchPojo.bdname}, '%')
        </if>
        <if test="SearchPojo.bdtitle != null and SearchPojo.bdtitle != ''">
            and CiBigData.bdtitle like concat('%', #{SearchPojo.bdtitle}, '%')
        </if>
        <if test="SearchPojo.frontphoto != null and SearchPojo.frontphoto != ''">
            and CiBigData.frontphoto like concat('%', #{SearchPojo.frontphoto}, '%')
        </if>
        <if test="SearchPojo.imagecss != null and SearchPojo.imagecss != ''">
            and CiBigData.imagecss like concat('%', #{SearchPojo.imagecss}, '%')
        </if>
        <if test="SearchPojo.mvcurl != null and SearchPojo.mvcurl != ''">
            and CiBigData.mvcurl like concat('%', #{SearchPojo.mvcurl}, '%')
        </if>
        <if test="SearchPojo.permissioncode != null and SearchPojo.permissioncode != ''">
            and CiBigData.permissioncode like concat('%', #{SearchPojo.permissioncode}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and CiBigData.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and CiBigData.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and CiBigData.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and CiBigData.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and CiBigData.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.bdtype != null and SearchPojo.bdtype != ''">
            or CiBigData.BdType like concat('%', #{SearchPojo.bdtype}, '%')
        </if>
        <if test="SearchPojo.bdcode != null and SearchPojo.bdcode != ''">
            or CiBigData.BdCode like concat('%', #{SearchPojo.bdcode}, '%')
        </if>
        <if test="SearchPojo.bdname != null and SearchPojo.bdname != ''">
            or CiBigData.BdName like concat('%', #{SearchPojo.bdname}, '%')
        </if>
        <if test="SearchPojo.bdtitle != null and SearchPojo.bdtitle != ''">
            or CiBigData.BdTitle like concat('%', #{SearchPojo.bdtitle}, '%')
        </if>
        <if test="SearchPojo.frontphoto != null and SearchPojo.frontphoto != ''">
            or CiBigData.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
        </if>
        <if test="SearchPojo.imagecss != null and SearchPojo.imagecss != ''">
            or CiBigData.ImageCss like concat('%', #{SearchPojo.imagecss}, '%')
        </if>
        <if test="SearchPojo.mvcurl != null and SearchPojo.mvcurl != ''">
            or CiBigData.MvcUrl like concat('%', #{SearchPojo.mvcurl}, '%')
        </if>
        <if test="SearchPojo.permissioncode != null and SearchPojo.permissioncode != ''">
            or CiBigData.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or CiBigData.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or CiBigData.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            or CiBigData.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or CiBigData.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            or CiBigData.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        )
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CibigdataPojo">
        <include refid="selectbillVo"/>
        where 1 = 1
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiBigData.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.bdtype != null and SearchPojo.bdtype != ''">
            and CiBigData.BdType like concat('%', #{SearchPojo.bdtype}, '%')
        </if>
        <if test="SearchPojo.bdcode != null and SearchPojo.bdcode != ''">
            and CiBigData.BdCode like concat('%', #{SearchPojo.bdcode}, '%')
        </if>
        <if test="SearchPojo.bdname != null and SearchPojo.bdname != ''">
            and CiBigData.BdName like concat('%', #{SearchPojo.bdname}, '%')
        </if>
        <if test="SearchPojo.bdtitle != null and SearchPojo.bdtitle != ''">
            and CiBigData.BdTitle like concat('%', #{SearchPojo.bdtitle}, '%')
        </if>
        <if test="SearchPojo.frontphoto != null and SearchPojo.frontphoto != ''">
            and CiBigData.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
        </if>
        <if test="SearchPojo.imagecss != null and SearchPojo.imagecss != ''">
            and CiBigData.ImageCss like concat('%', #{SearchPojo.imagecss}, '%')
        </if>
        <if test="SearchPojo.mvcurl != null and SearchPojo.mvcurl != ''">
            and CiBigData.MvcUrl like concat('%', #{SearchPojo.mvcurl}, '%')
        </if>
        <if test="SearchPojo.permissioncode != null and SearchPojo.permissioncode != ''">
            and CiBigData.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and CiBigData.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and CiBigData.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and CiBigData.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and CiBigData.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and CiBigData.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
    </sql>
    <sql id="thor">
        and (1=0
        <if test="SearchPojo.bdtype != null and SearchPojo.bdtype != ''">
            or CiBigData.BdType like concat('%', #{SearchPojo.bdtype}, '%')
        </if>
        <if test="SearchPojo.bdcode != null and SearchPojo.bdcode != ''">
            or CiBigData.BdCode like concat('%', #{SearchPojo.bdcode}, '%')
        </if>
        <if test="SearchPojo.bdname != null and SearchPojo.bdname != ''">
            or CiBigData.BdName like concat('%', #{SearchPojo.bdname}, '%')
        </if>
        <if test="SearchPojo.bdtitle != null and SearchPojo.bdtitle != ''">
            or CiBigData.BdTitle like concat('%', #{SearchPojo.bdtitle}, '%')
        </if>
        <if test="SearchPojo.frontphoto != null and SearchPojo.frontphoto != ''">
            or CiBigData.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
        </if>
        <if test="SearchPojo.imagecss != null and SearchPojo.imagecss != ''">
            or CiBigData.ImageCss like concat('%', #{SearchPojo.imagecss}, '%')
        </if>
        <if test="SearchPojo.mvcurl != null and SearchPojo.mvcurl != ''">
            or CiBigData.MvcUrl like concat('%', #{SearchPojo.mvcurl}, '%')
        </if>
        <if test="SearchPojo.permissioncode != null and SearchPojo.permissioncode != ''">
            or CiBigData.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or CiBigData.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or CiBigData.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            or CiBigData.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or CiBigData.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            or CiBigData.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        )
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into CiBigData(id, BdType, BdCode, BdName, BdTitle, FrontPhoto, ImageCss, MvcUrl, RowNum, EnabledMark,
                              IsPublic, PermissionCode, Tenantid, Remark, CreateBy, CreateByid, CreateDate, Lister,
                              Listerid, ModifyDate, Revision)
        values (#{id}, #{bdtype}, #{bdcode}, #{bdname}, #{bdtitle}, #{frontphoto}, #{imagecss}, #{mvcurl}, #{rownum},
                #{enabledmark}, #{ispublic}, #{permissioncode}, #{tenantid}, #{remark}, #{createby}, #{createbyid},
                #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiBigData
        <set>
            <if test="bdtype != null ">
                BdType =#{bdtype},
            </if>
            <if test="bdcode != null ">
                BdCode =#{bdcode},
            </if>
            <if test="bdname != null ">
                BdName =#{bdname},
            </if>
            <if test="bdtitle != null ">
                BdTitle =#{bdtitle},
            </if>
            <if test="frontphoto != null ">
                FrontPhoto =#{frontphoto},
            </if>
            <if test="imagecss != null ">
                ImageCss =#{imagecss},
            </if>
            <if test="mvcurl != null ">
                MvcUrl =#{mvcurl},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="ispublic != null">
                IsPublic =#{ispublic},
            </if>
            <if test="permissioncode != null ">
                PermissionCode =#{permissioncode},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiBigData
        where id = #{key}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.system.domain.pojo.CibigdataPojo">
        select
        id
        from CiBigDataItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>

    <!--查询List-->
    <select id="getListByTenant" resultType="inks.system.domain.pojo.CibigdataPojo">
        SELECT
            CiBigData.id,
            CiBigData.BdType,
            CiBigData.BdCode,
            CiBigData.BdName,
            CiBigData.BdTitle,
            CiBigData.FrontPhoto,
            CiBigData.ImageCss,
            CiBigData.MvcUrl,
            CiBigData.RowNum,
            CiBigData.EnabledMark,
            CiBigData.IsPublic,
            CiBigData.PermissionCode,
            CiBigData.Tenantid,
            CiBigData.Remark,
            CiBigData.CreateBy,
            CiBigData.CreateByid,
            CiBigData.CreateDate,
            CiBigData.Lister,
            CiBigData.Listerid,
            CiBigData.ModifyDate,
            CiBigData.Revision,
            CiBigDataItem.FunctionCode,
            CiBigDataItem.FunctionName,
            PiSubscriber.Tenantid
        FROM
            CiBigData
                LEFT JOIN CiBigDataItem ON CiBigDataItem.Pid = CiBigData.id
                LEFT JOIN PiSubscriber ON PiSubscriber.Functionid = CiBigDataItem.Functionid
        WHERE #{date}>=PiSubscriber.StartDate and PiSubscriber.EndDate >= #{date} and  PiSubscriber.Tenantid= #{key}
        order by RowNum
    </select>
</mapper>

