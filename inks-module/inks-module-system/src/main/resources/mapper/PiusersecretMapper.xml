<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PiusersecretMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PiusersecretPojo">
        select id,
               SecretCode,
               Userid,
               CheckIpAddr,
               Ipaddress,
               MacAddress,
               FirstVisit,
               PreviouVisit,
               BrowserName,
               HostSystem,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Tenantid,
               TenantName,
               Revision
        from PiUserSecret
        where PiUserSecret.id = #{key}
          and PiUserSecret.Tenantid = #{tid}
    </select>
    <sql id="selectPiusersecretVo">
        select id,
               SecretCode,
               Userid,
               CheckIp<PERSON>dd<PERSON>,
               <PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               FirstVisit,
               PreviouVisit,
               <PERSON>rowser<PERSON>ame,
               HostSystem,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Tenantid,
               TenantName,
               Revision
        from PiUserSecret
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PiusersecretPojo">
        <include refid="selectPiusersecretVo"/>
        where 1 = 1 and PiUserSecret.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiUserSecret.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.secretcode != null ">
            and PiUserSecret.SecretCode like concat('%', #{SearchPojo.secretcode}, '%')
        </if>
        <if test="SearchPojo.userid != null ">
            and PiUserSecret.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.ipaddress != null ">
            and PiUserSecret.Ipaddress like concat('%', #{SearchPojo.ipaddress}, '%')
        </if>
        <if test="SearchPojo.macaddress != null ">
            and PiUserSecret.MacAddress like concat('%', #{SearchPojo.macaddress}, '%')
        </if>
        <if test="SearchPojo.browsername != null ">
            and PiUserSecret.BrowserName like concat('%', #{SearchPojo.browsername}, '%')
        </if>
        <if test="SearchPojo.hostsystem != null ">
            and PiUserSecret.HostSystem like concat('%', #{SearchPojo.hostsystem}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and PiUserSecret.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and PiUserSecret.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and PiUserSecret.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and PiUserSecret.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and PiUserSecret.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.secretcode != null ">
                or PiUserSecret.SecretCode like concat('%', #{SearchPojo.secretcode}, '%')
            </if>
            <if test="SearchPojo.userid != null ">
                or PiUserSecret.Userid like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.ipaddress != null ">
                or PiUserSecret.Ipaddress like concat('%', #{SearchPojo.ipaddress}, '%')
            </if>
            <if test="SearchPojo.macaddress != null ">
                or PiUserSecret.MacAddress like concat('%', #{SearchPojo.macaddress}, '%')
            </if>
            <if test="SearchPojo.browsername != null ">
                or PiUserSecret.BrowserName like concat('%', #{SearchPojo.browsername}, '%')
            </if>
            <if test="SearchPojo.hostsystem != null ">
                or PiUserSecret.HostSystem like concat('%', #{SearchPojo.hostsystem}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or PiUserSecret.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or PiUserSecret.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or PiUserSecret.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or PiUserSecret.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or PiUserSecret.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiUserSecret(id, SecretCode, Userid, CheckIpAddr, Ipaddress, MacAddress, FirstVisit, PreviouVisit,
                                 BrowserName, HostSystem, CreateBy, CreateByid, CreateDate, Lister, Listerid,
                                 ModifyDate, Tenantid, TenantName, Revision)
        values (#{id}, #{secretcode}, #{userid}, #{checkipaddr}, #{ipaddress}, #{macaddress}, #{firstvisit},
                #{previouvisit}, #{browsername}, #{hostsystem}, #{createby}, #{createbyid}, #{createdate}, #{lister},
                #{listerid}, #{modifydate}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiUserSecret
        <set>
            <if test="secretcode != null ">
                SecretCode =#{secretcode},
            </if>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="checkipaddr != null">
                CheckIpAddr =#{checkipaddr},
            </if>
            <if test="ipaddress != null ">
                Ipaddress =#{ipaddress},
            </if>
            <if test="macaddress != null ">
                MacAddress =#{macaddress},
            </if>
            <if test="firstvisit != null">
                FirstVisit =#{firstvisit},
            </if>
            <if test="previouvisit != null">
                PreviouVisit =#{previouvisit},
            </if>
            <if test="browsername != null ">
                BrowserName =#{browsername},
            </if>
            <if test="hostsystem != null ">
                HostSystem =#{hostsystem},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiUserSecret
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询单个-->
    <select id="getEntityByUserid" resultType="inks.system.domain.pojo.PiusersecretPojo">
        select id,
               SecretCode,
               Userid,
               CheckIpAddr,
               Ipaddress,
               MacAddress,
               FirstVisit,
               PreviouVisit,
               BrowserName,
               HostSystem,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Tenantid,
               TenantName,
               Revision
        from PiUserSecret
        where PiUserSecret.Userid = #{userid}
          and PiUserSecret.Tenantid = #{tid}
    </select>
</mapper>

