<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CibillgroupMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CibillgroupPojo">
        select id,
               Parentid,
               ModuleCode,
               GroupCode,
               GroupName,
               EnabledMark,
               RowNum,
               Remark,
               Lister,
               CreateDate,
               ModifyDate,
               Tenantid
        from CiBillGroup
        where CiBillGroup.id = #{key}
          and CiBillGroup.Tenantid = #{tid}
    </select>
    <sql id="selectCibillgroupVo">
        select id,
               Parentid,
               ModuleCode,
               GroupCode,
               GroupName,
               EnabledMark,
               RowNum,
               Remark,
               Lister,
               CreateDate,
               ModifyDate,
               Tenantid
        from CiBillGroup
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CibillgroupPojo">
        <include refid="selectCibillgroupVo"/>
        where 1 = 1 and CiBillGroup.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiBillGroup.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.parentid != null and SearchPojo.parentid  != ''">
            and CiBillGroup.Parentid like concat('%', #{SearchPojo.parentid}, '%')
        </if>
        <if test="SearchPojo.modulecode != null and SearchPojo.modulecode  != ''">
            and CiBillGroup.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.groupcode != null and SearchPojo.groupcode  != ''">
            and CiBillGroup.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
        </if>
        <if test="SearchPojo.groupname != null and SearchPojo.groupname  != ''">
            and CiBillGroup.GroupName like concat('%', #{SearchPojo.groupname}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark  != ''">
            and CiBillGroup.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and CiBillGroup.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.parentid != null and SearchPojo.parentid != ''">
            or CiBillGroup.Parentid like concat('%', #{SearchPojo.parentid}, '%')
        </if>
        <if test="SearchPojo.modulecode != null and SearchPojo.modulecode != ''">
            or CiBillGroup.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.groupcode != null and SearchPojo.groupcode != ''">
            or CiBillGroup.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
        </if>
        <if test="SearchPojo.groupname != null and SearchPojo.groupname != ''">
            or CiBillGroup.GroupName like concat('%', #{SearchPojo.groupname}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or CiBillGroup.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or CiBillGroup.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into CiBillGroup(id, Parentid, ModuleCode, GroupCode, GroupName, EnabledMark, RowNum, Remark, Lister,
                                CreateDate, ModifyDate, Tenantid)
        values (#{id}, #{parentid}, #{modulecode}, #{groupcode}, #{groupname}, #{enabledmark}, #{rownum}, #{remark},
                #{lister}, #{createdate}, #{modifydate}, #{tenantid})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiBillGroup
        <set>
            <if test="parentid != null ">
                Parentid =#{parentid},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="groupcode != null ">
                GroupCode =#{groupcode},
            </if>
            <if test="groupname != null ">
                GroupName =#{groupname},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiBillGroup
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--获得EntityByModuleCode-->
    <select id="getListByModuleCode" resultType="inks.system.domain.pojo.CibillgroupPojo">
        select id,
               Parentid,
               ModuleCode,
               GroupCode,
               GroupName,
               EnabledMark,
               RowNum,
               Remark,
               Lister,
               CreateDate,
               ModifyDate,
               Tenantid
        from CiBillGroup
        where ModuleCode = #{moduleCode}
          and Tenantid = #{tid}
        Order by RowNum
    </select>
</mapper>

