<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CioperlogMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CioperlogPojo">
        select id,
               OperTitle,
               BusinessType,
               Method,
               RequestMethod,
               OperatorType,
               OperUserid,
               OperName,
               DeptName,
               OperUrl,
               OperIp,
               OperLocation,
               OperParam,
               JsonResult,
               Status,
               ErrorMsg,
               OperTime,
               Tenantid
        from CiOperLog
        where CiOperLog.id = #{key}
          and CiOperLog.Tenantid = #{tid}
    </select>
    <sql id="selectCioperlogVo">
        select id,
               OperTitle,
               BusinessType,
               Method,
               RequestMethod,
               OperatorType,
               OperUserid,
               OperName,
               DeptName,
               OperUrl,
               OperIp,
               OperLocation,
               OperParam,
               JsonResult,
               Status,
               ErrorMsg,
               OperTime,
               Tenantid
        from CiOperLog
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CioperlogPojo">
        <include refid="selectCioperlogVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}

        </if>
        <if test="tenantid != 'default'">
            and CiOperLog.Tenantid = #{tenantid}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and CiOperLog.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.opertitle != null and SearchPojo.opertitle != ''">
            and CiOperLog.OperTitle like concat('%', #{SearchPojo.opertitle}, '%')
        </if>
        <if test="SearchPojo.method != null and SearchPojo.method != ''">
            and CiOperLog.Method like concat('%',
                #{SearchPojo.method}, '%')
        </if>
        <if test="SearchPojo.requestmethod != null and SearchPojo.requestmethod != ''">
            and CiOperLog.RequestMethod like concat('%',
                #{SearchPojo.requestmethod}, '%')
        </if>
        <if test="SearchPojo.operuserid != null and SearchPojo.operuserid != ''">
            and CiOperLog.OperUserid like concat('%',
                #{SearchPojo.operuserid}, '%')
        </if>
        <if test="SearchPojo.opername != null and SearchPojo.opername != ''">
            and CiOperLog.OperName like concat('%',
                #{SearchPojo.opername}, '%')
        </if>
        <if test="SearchPojo.deptname != null and SearchPojo.deptname != ''">
            and CiOperLog.DeptName like concat('%',
                #{SearchPojo.deptname}, '%')
        </if>
        <if test="SearchPojo.operurl != null and SearchPojo.operurl != ''">
            and CiOperLog.OperUrl like concat('%',
                #{SearchPojo.operurl}, '%')
        </if>
        <if test="SearchPojo.operip != null and SearchPojo.operip != ''">
            and CiOperLog.OperIp like concat('%',
                #{SearchPojo.operip}, '%')
        </if>
        <if test="SearchPojo.operlocation != null and SearchPojo.operlocation != ''">
            and CiOperLog.OperLocation like concat('%',
                #{SearchPojo.operlocation}, '%')
        </if>
        <if test="SearchPojo.operparam != null and SearchPojo.operparam != ''">
            and CiOperLog.OperParam like concat('%',
                #{SearchPojo.operparam}, '%')
        </if>
        <if test="SearchPojo.jsonresult != null and SearchPojo.jsonresult != ''">
            and CiOperLog.JsonResult like concat('%',
                #{SearchPojo.jsonresult}, '%')
        </if>
        <if test="SearchPojo.errormsg != null and SearchPojo.errormsg != ''">
            and CiOperLog.ErrorMsg like concat('%',
                #{SearchPojo.errormsg}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.opertitle != null and SearchPojo.opertitle != ''">
            or CiOperLog.OperTitle like concat('%', #{SearchPojo.opertitle}, '%')
        </if>
        <if test="SearchPojo.method != null and SearchPojo.method != ''">
            or CiOperLog.Method like concat('%', #{SearchPojo.method}, '%')
        </if>
        <if test="SearchPojo.requestmethod != null and SearchPojo.requestmethod != ''">
            or CiOperLog.RequestMethod like concat('%', #{SearchPojo.requestmethod}, '%')
        </if>
        <if test="SearchPojo.operuserid != null and SearchPojo.operuserid != ''">
            or CiOperLog.OperUserid like concat('%', #{SearchPojo.operuserid}, '%')
        </if>
        <if test="SearchPojo.opername != null and SearchPojo.opername != ''">
            or CiOperLog.OperName like concat('%', #{SearchPojo.opername}, '%')
        </if>
        <if test="SearchPojo.deptname != null and SearchPojo.deptname != ''">
            or CiOperLog.DeptName like concat('%', #{SearchPojo.deptname}, '%')
        </if>
        <if test="SearchPojo.operurl != null and SearchPojo.operurl != ''">
            or CiOperLog.OperUrl like concat('%', #{SearchPojo.operurl}, '%')
        </if>
        <if test="SearchPojo.operip != null and SearchPojo.operip != ''">
            or CiOperLog.OperIp like concat('%', #{SearchPojo.operip}, '%')
        </if>
        <if test="SearchPojo.operlocation != null and SearchPojo.operlocation != ''">
            or CiOperLog.OperLocation like concat('%', #{SearchPojo.operlocation}, '%')
        </if>
        <if test="SearchPojo.operparam != null and SearchPojo.operparam != ''">
            or CiOperLog.OperParam like concat('%', #{SearchPojo.operparam}, '%')
        </if>
        <if test="SearchPojo.jsonresult != null and SearchPojo.jsonresult != ''">
            or CiOperLog.JsonResult like concat('%', #{SearchPojo.jsonresult}, '%')
        </if>
        <if test="SearchPojo.errormsg != null and SearchPojo.errormsg != ''">
            or CiOperLog.ErrorMsg like concat('%', #{SearchPojo.errormsg}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into CiOperLog(id, OperTitle, BusinessType, Method, RequestMethod, OperatorType, OperUserid, OperName,
                              DeptName, OperUrl, OperIp, OperLocation, OperParam, JsonResult, Status, ErrorMsg,
                              OperTime, Tenantid)
        values (#{id}, #{opertitle}, #{businesstype}, #{method}, #{requestmethod}, #{operatortype}, #{operuserid},
                #{opername}, #{deptname}, #{operurl}, #{operip}, #{operlocation}, #{operparam}, #{jsonresult},
                #{status}, #{errormsg}, #{opertime}, #{tenantid})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiOperLog
        <set>
            <if test="opertitle != null">
                OperTitle =#{opertitle},
            </if>
            <if test="businesstype != null">
                BusinessType =#{businesstype},
            </if>
            <if test="method != null">
                Method =#{method},
            </if>
            <if test="requestmethod != null">
                RequestMethod =#{requestmethod},
            </if>
            <if test="operatortype != null">
                OperatorType =#{operatortype},
            </if>
            <if test="operuserid != null">
                OperUserid =#{operuserid},
            </if>
            <if test="opername != null">
                OperName =#{opername},
            </if>
            <if test="deptname != null">
                DeptName =#{deptname},
            </if>
            <if test="operurl != null">
                OperUrl =#{operurl},
            </if>
            <if test="operip != null">
                OperIp =#{operip},
            </if>
            <if test="operlocation != null">
                OperLocation =#{operlocation},
            </if>
            <if test="operparam != null">
                OperParam =#{operparam},
            </if>
            <if test="jsonresult != null">
                JsonResult =#{jsonresult},
            </if>
            <if test="status != null">
                Status =#{status},
            </if>
            <if test="errormsg != null">
                ErrorMsg =#{errormsg},
            </if>
            <if test="opertime != null">
                OperTime =#{opertime},
            </if>
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiOperLog
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <delete id="deleteByTime" parameterType="inks.common.core.domain.QueryParam">
        delete
        from CiOperLog
        where 1 = 1
        <if test="tenantid != null and tenantid != ''">
            and Tenantid = #{tenantid}
        </if>
        <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
            and CiOperLog.OperTime BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
        </if>
        <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
            and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
        </if>
    </delete>
</mapper>

