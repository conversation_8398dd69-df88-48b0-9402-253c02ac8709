<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PipricepolicyitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PipricepolicyitemPojo">
        select id,
               Pid,
               CycleCode,
               Container,
               TaxPrice,
               Remark,
               RowNum,
               EnabledMark
        from PiPricePolicyItem
        where PiPricePolicyItem.id = #{key}
    </select>
    <sql id="selectPipricepolicyitemVo">
        select id,
               Pid,
               CycleCode,
               Container,
               TaxPrice,
               Remark,
               RowNum,
               EnabledMark
        from PiPricePolicyItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PipricepolicyitemPojo">
        <include refid="selectPipricepolicyitemVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiPricePolicyItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and PiPricePolicyItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.cyclecode != null and SearchPojo.cyclecode != ''">
            and PiPricePolicyItem.cyclecode like concat('%', #{SearchPojo.cyclecode}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and PiPricePolicyItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            or PiPricePolicyItem.Pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.cyclecode != null and SearchPojo.cyclecode != ''">
            or PiPricePolicyItem.CycleCode like concat('%', #{SearchPojo.cyclecode}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or PiPricePolicyItem.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        )
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.system.domain.pojo.PipricepolicyitemPojo">
        select id,
               Pid,
               CycleCode,
               Container,
               TaxPrice,
               Remark,
               RowNum,
               EnabledMark
        from PiPricePolicyItem
        where PiPricePolicyItem.Pid = #{Pid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into PiPricePolicyItem(id, Pid, CycleCode, Container, TaxPrice, Remark, RowNum, EnabledMark)
        values (#{id}, #{pid}, #{cyclecode}, #{container}, #{taxprice}, #{remark}, #{rownum}, #{enabledmark})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiPricePolicyItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="cyclecode != null ">
                CycleCode = #{cyclecode},
            </if>
            <if test="container != null">
                Container = #{container},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark = #{enabledmark},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiPricePolicyItem
        where id = #{key}
    </delete>

</mapper>

