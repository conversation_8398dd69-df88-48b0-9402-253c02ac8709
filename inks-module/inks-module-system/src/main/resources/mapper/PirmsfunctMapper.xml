<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PirmsfunctMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PirmsfunctPojo">
        select
          RmsFunctid, RmsFunctCode, RmsFunctName, Description, Functionid, FunctionCode, FunctionName, EnabledMark, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision        from PiRmsFunct
        where PiRmsFunct.RmsFunctid = #{key}
    </select>
    <sql id="selectPirmsfunctVo">
         select
          RmsFunctid, RmsFunctCode, RmsFunctName, Description, Functionid, FunctionCode, FunctionName, EnabledMark, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision        from PiRmsFunct
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.PirmsfunctPojo">
        <include refid="selectPirmsfunctVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and PiRmsFunct.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.rmsfunctcode != null ">
   and PiRmsFunct.RmsFunctCode like concat('%', #{SearchPojo.rmsfunctcode}, '%')
</if>
<if test="SearchPojo.rmsfunctname != null ">
   and PiRmsFunct.RmsFunctName like concat('%', #{SearchPojo.rmsfunctname}, '%')
</if>
<if test="SearchPojo.description != null ">
   and PiRmsFunct.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.functionid != null ">
   and PiRmsFunct.Functionid like concat('%', #{SearchPojo.functionid}, '%')
</if>
<if test="SearchPojo.functioncode != null ">
   and PiRmsFunct.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
</if>
<if test="SearchPojo.functionname != null ">
   and PiRmsFunct.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and PiRmsFunct.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and PiRmsFunct.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and PiRmsFunct.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and PiRmsFunct.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and PiRmsFunct.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.rmsfunctcode != null ">
   or PiRmsFunct.RmsFunctCode like concat('%', #{SearchPojo.rmsfunctcode}, '%')
</if>
<if test="SearchPojo.rmsfunctname != null ">
   or PiRmsFunct.RmsFunctName like concat('%', #{SearchPojo.rmsfunctname}, '%')
</if>
<if test="SearchPojo.description != null ">
   or PiRmsFunct.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.functionid != null ">
   or PiRmsFunct.Functionid like concat('%', #{SearchPojo.functionid}, '%')
</if>
<if test="SearchPojo.functioncode != null ">
   or PiRmsFunct.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
</if>
<if test="SearchPojo.functionname != null ">
   or PiRmsFunct.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or PiRmsFunct.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or PiRmsFunct.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or PiRmsFunct.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or PiRmsFunct.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or PiRmsFunct.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into PiRmsFunct(RmsFunctid, RmsFunctCode, RmsFunctName, Description, Functionid, FunctionCode, FunctionName, EnabledMark, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision)
        values (#{rmsfunctid}, #{rmsfunctcode}, #{rmsfunctname}, #{description}, #{functionid}, #{functioncode}, #{functionname}, #{enabledmark}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update PiRmsFunct
        <set>
            <if test="rmsfunctcode != null ">
                RmsFunctCode =#{rmsfunctcode},
            </if>
            <if test="rmsfunctname != null ">
                RmsFunctName =#{rmsfunctname},
            </if>
            <if test="description != null ">
                Description =#{description},
            </if>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
                Revision=Revision+1
        </set>
        where RmsFunctid = #{rmsfunctid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from PiRmsFunct where RmsFunctid = #{key}
    </delete>
                                                                    </mapper>

