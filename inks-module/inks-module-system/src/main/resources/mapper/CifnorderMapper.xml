<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CifnorderMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CifnorderPojo">
        SELECT
        CiFnOrder.id,
        CiFnOrder.RefNo,
        CiFnOrder.BillType,
        CiFnOrder.BillTitle,
        CiFnOrder.BillDate,
        CiFnOrder.Sellerid,
        CiFnOrder.Userid,
        CiFnOrder.UserName,
        CiFnOrder.RealName,
        CiFnOrder.Tenantid,
        CiFnOrder.TenantCode,
        CiFnOrder.TenantName,
        CiFnOrder.Company,
        CiFnOrder.Summary,
        CiFnOrder.CreateBy,
        CiFnOrder.Create<PERSON>ate,
        CiFnOrder<PERSON>,
        CiFnOrder.ModifyDate,
        CiFnOrder.BillTaxAmount,
        CiFnOrder.PayAmount,
        CiFnOrder.PayBillCode,
        CiFnOrder.StateCode,
        CiFnOrder.StateDate,
        CiFnOrder.DisannulMark,
        CiFnOrder.DisannulLister,
        CiFnOrder.DisannulDate,
        CiFnOrder.Revision
        from CiFnOrder
        where CiFnOrder.id = #{key}
        <if test="tid != 'default' ">
            and CiFnOrder.Tenantid =#{tid}
        </if>
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        SELECT CiFnOrder.id,
               CiFnOrder.RefNo,
               CiFnOrder.BillType,
               CiFnOrder.BillTitle,
               CiFnOrder.BillDate,
               CiFnOrder.Sellerid,
               CiFnOrder.Userid,
               CiFnOrder.UserName,
               CiFnOrder.RealName,
               CiFnOrder.Tenantid,
               CiFnOrder.TenantCode,
               CiFnOrder.TenantName,
               CiFnOrder.Company,
               CiFnOrder.Summary,
               CiFnOrder.CreateBy,
               CiFnOrder.CreateDate,
               CiFnOrder.Lister,
               CiFnOrder.ModifyDate,
               CiFnOrder.BillTaxAmount,
               CiFnOrder.PayAmount,
               CiFnOrder.PayBillCode,
               CiFnOrder.StateCode,
               CiFnOrder.StateDate,
               CiFnOrder.DisannulMark,
               CiFnOrder.DisannulLister,
               CiFnOrder.DisannulDate,
               CiFnOrder.Revision
        from CiFnOrder
    </sql>
    <sql id="selectdetailVo">
        SELECT CiFnOrderItem.id,
               CiFnOrderItem.Pid,
               CiFnOrderItem.PricePolicyid,
               CiFnOrderItem.Functionid,
               CiFnOrderItem.CycleCode,
               CiFnOrderItem.Container,
               CiFnOrderItem.Quantity,
               CiFnOrderItem.TaxPrice,
               CiFnOrderItem.TaxAmount,
               CiFnOrderItem.RowNum,
               CiFnOrderItem.Remark,
               CiFnOrderItem.InvoFinish,
               CiFnOrderItem.InvoClosed,
               CiFnOrderItem.Revision,
               PiFunction.FunctionCode,
               PiFunction.FunctionName,
               CiFnOrder.RefNo,
               CiFnOrder.BillType,
               CiFnOrder.BillTitle,
               CiFnOrder.BillDate,
               CiFnOrder.Userid,
               CiFnOrder.UserName,
               CiFnOrder.RealName,
               CiFnOrder.Tenantid,
               CiFnOrder.TenantCode,
               CiFnOrder.TenantName
        FROM CiFnOrder
                 RIGHT JOIN CiFnOrderItem ON CiFnOrderItem.Pid = CiFnOrder.id
                 LEFT JOIN PiFunction ON PiFunction.Functionid = CiFnOrderItem.Functionid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CifnorderitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="tenantid != 'default' ">
            and CiFnOrder.Tenantid =#{tenantid}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiFnOrder.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>


    <sql id="and">
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and CiFnOrder.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and CiFnOrder.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and CiFnOrder.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.sellerid != null and SearchPojo.sellerid != ''">
            and CiFnOrder.sellerid like concat('%', #{SearchPojo.sellerid}, '%')
        </if>
        <if test="SearchPojo.userid != null and SearchPojo.userid != ''">
            and CiFnOrder.userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.username != null and SearchPojo.username != ''">
            and CiFnOrder.username like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null and SearchPojo.realname != ''">
            and CiFnOrder.realname like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.tenantcode != null and SearchPojo.tenantcode != ''">
            and CiFnOrder.tenantcode like concat('%', #{SearchPojo.tenantcode}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
            and CiFnOrder.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and CiFnOrder.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and CiFnOrder.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.paybillcode != null and SearchPojo.paybillcode != ''">
            and CiFnOrder.paybillcode like concat('%', #{SearchPojo.paybillcode}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            and CiFnOrder.statecode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            and CiFnOrder.disannullister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.company != null and SearchPojo.company != ''">
            and CiFnOrder.company like concat('%', #{SearchPojo.company}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            or CiFnOrder.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            or CiFnOrder.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            or CiFnOrder.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.sellerid != null and SearchPojo.sellerid != ''">
            or CiFnOrder.Sellerid like concat('%', #{SearchPojo.sellerid}, '%')
        </if>
        <if test="SearchPojo.userid != null and SearchPojo.userid != ''">
            or CiFnOrder.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.username != null and SearchPojo.username != ''">
            or CiFnOrder.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null and SearchPojo.realname != ''">
            or CiFnOrder.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.tenantcode != null and SearchPojo.tenantcode != ''">
            or CiFnOrder.TenantCode like concat('%', #{SearchPojo.tenantcode}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
            or CiFnOrder.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            or CiFnOrder.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or CiFnOrder.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.paybillcode != null and SearchPojo.paybillcode != ''">
            or CiFnOrder.PayBillCode like concat('%', #{SearchPojo.paybillcode}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            or CiFnOrder.StateCode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            or CiFnOrder.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.company != null and SearchPojo.company != ''">
            or CiFnOrder.Company like concat('%', #{SearchPojo.company}, '%')
        </if>
        )
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CifnorderPojo">
        <include refid="selectbillVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="tenantid != 'default' ">
            and CiFnOrder.Tenantid =#{tenantid}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiFnOrder.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null and SearchPojo.refno} != ''">
            and CiFnOrder.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype} != ''">
            and CiFnOrder.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle} != ''">
            and CiFnOrder.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.sellerid != null and SearchPojo.sellerid} != ''">
            and CiFnOrder.Sellerid like concat('%', #{SearchPojo.sellerid}, '%')
        </if>
        <if test="SearchPojo.userid != null and SearchPojo.userid} != ''">
            and CiFnOrder.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.username != null and SearchPojo.username} != ''">
            and CiFnOrder.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null and SearchPojo.realname} != ''">
            and CiFnOrder.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.tenantcode != null and SearchPojo.tenantcode} != ''">
            and CiFnOrder.TenantCode like concat('%', #{SearchPojo.tenantcode}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname} != ''">
            and CiFnOrder.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary} != ''">
            and CiFnOrder.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister} != ''">
            and CiFnOrder.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.paybillcode != null and SearchPojo.paybillcode} != ''">
            and CiFnOrder.PayBillCode like concat('%', #{SearchPojo.paybillcode}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode} != ''">
            and CiFnOrder.StateCode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister} != ''">
            and CiFnOrder.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.company != null and SearchPojo.company} != ''">
            and CiFnOrder.Company like concat('%', #{SearchPojo.company}, '%')
        </if>
    </sql>
    <sql id="thor">
        and (1=0
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            or CiFnOrder.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            or CiFnOrder.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            or CiFnOrder.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.sellerid != null and SearchPojo.sellerid != ''">
            or CiFnOrder.Sellerid like concat('%', #{SearchPojo.sellerid}, '%')
        </if>
        <if test="SearchPojo.userid != null and SearchPojo.userid != ''">
            or CiFnOrder.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.username != null and SearchPojo.username != ''">
            or CiFnOrder.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null and SearchPojo.realname != ''">
            or CiFnOrder.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.tenantcode != null and SearchPojo.tenantcode != ''">
            or CiFnOrder.TenantCode like concat('%', #{SearchPojo.tenantcode}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
            or CiFnOrder.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            or CiFnOrder.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or CiFnOrder.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.paybillcode != null and SearchPojo.paybillcode != ''">
            or CiFnOrder.PayBillCode like concat('%', #{SearchPojo.paybillcode}, '%')
        </if>
        <if test="SearchPojo.statecode != null and SearchPojo.statecode != ''">
            or CiFnOrder.StateCode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.disannullister != null and SearchPojo.disannullister != ''">
            or CiFnOrder.DisannulLister like concat('%', #{SearchPojo.disannullister}, '%')
        </if>
        <if test="SearchPojo.company != null and SearchPojo.company != ''">
            or CiFnOrder.Company like concat('%', #{SearchPojo.company}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into CiFnOrder(id, RefNo, BillType, BillTitle, BillDate, Sellerid, Userid, UserName, RealName, Tenantid,
                              TenantCode, TenantName, Summary, Lister, CreateDate, ModifyDate, BillTaxAmount, PayAmount,
                              PayBillCode, StateCode, StateDate, DisannulMark, DisannulLister, DisannulDate, Company,
                              CreateBy, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{sellerid}, #{userid}, #{username},
                #{realname}, #{tenantid}, #{tenantcode}, #{tenantname}, #{summary}, #{lister}, #{createdate},
                #{modifydate}, #{billtaxamount}, #{payamount}, #{paybillcode}, #{statecode}, #{statedate},
                #{disannulmark}, #{disannullister}, #{disannuldate}, #{company}, #{createby}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiFnOrder
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="sellerid != null ">
                Sellerid =#{sellerid},
            </if>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="tenantcode != null ">
                TenantCode =#{tenantcode},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="billtaxamount != null">
                BillTaxAmount =#{billtaxamount},
            </if>
            <if test="payamount != null">
                PayAmount =#{payamount},
            </if>
            <if test="paybillcode != null ">
                PayBillCode =#{paybillcode},
            </if>
            <if test="statecode != null ">
                StateCode =#{statecode},
            </if>
            <if test="statedate != null">
                StateDate =#{statedate},
            </if>
            <if test="disannulmark != null">
                DisannulMark =#{disannulmark},
            </if>
            <if test="disannullister != null ">
                DisannulLister =#{disannullister},
            </if>
            <if test="disannuldate != null">
                DisannulDate =#{disannuldate},
            </if>
            <if test="company != null ">
                Company =#{company},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiFnOrder
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.system.domain.pojo.CifnorderPojo">
        select
        id
        from CiFnOrderItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>

    <!--查询单个-->
    <select id="getEntityByRefno" resultType="inks.system.domain.pojo.CifnorderPojo">
        SELECT CiFnOrder.id,
               CiFnOrder.RefNo,
               CiFnOrder.BillType,
               CiFnOrder.BillTitle,
               CiFnOrder.BillDate,
               CiFnOrder.Sellerid,
               CiFnOrder.Userid,
               CiFnOrder.UserName,
               CiFnOrder.RealName,
               CiFnOrder.Tenantid,
               CiFnOrder.TenantCode,
               CiFnOrder.TenantName,
               CiFnOrder.Company,
               CiFnOrder.Summary,
               CiFnOrder.CreateBy,
               CiFnOrder.CreateDate,
               CiFnOrder.Lister,
               CiFnOrder.ModifyDate,
               CiFnOrder.BillTaxAmount,
               CiFnOrder.PayAmount,
               CiFnOrder.PayBillCode,
               CiFnOrder.StateCode,
               CiFnOrder.StateDate,
               CiFnOrder.DisannulMark,
               CiFnOrder.DisannulLister,
               CiFnOrder.DisannulDate,
               CiFnOrder.Revision
        from CiFnOrder
        where CiFnOrder.RefNo = #{key}
    </select>
</mapper>

