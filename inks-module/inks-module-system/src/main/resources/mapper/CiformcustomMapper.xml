<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CiformcustomMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CiformcustomPojo">
        select id,
               GenGroupid,
               ModuleCode,
               FrmCode,
               FrmName,
               FrmContent,
               RowNum,
               EnabledMark,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from CiFormCustom
        where CiFormCustom.id = #{key}
          and CiFormCustom.Tenantid = #{tid}
    </select>
    <sql id="selectCiformcustomVo">
        select id,
               GenGroupid,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               <PERSON>m<PERSON><PERSON>,
               <PERSON>m<PERSON>ame,
               <PERSON>m<PERSON><PERSON>nt,
               <PERSON><PERSON><PERSON>,
               EnabledMark,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from CiFormCustom
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CiformcustomPojo">
        <include refid="selectCiformcustomVo"/>
        where 1 = 1 and CiFormCustom.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiFormCustom.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.gengroupid != null ">
            and CiFormCustom.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
        </if>
        <if test="SearchPojo.modulecode != null ">
            and CiFormCustom.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.frmcode != null ">
            and CiFormCustom.FrmCode like concat('%', #{SearchPojo.frmcode}, '%')
        </if>
        <if test="SearchPojo.frmname != null ">
            and CiFormCustom.FrmName like concat('%', #{SearchPojo.frmname}, '%')
        </if>
        <if test="SearchPojo.frmcontent != null ">
            and CiFormCustom.FrmContent like concat('%', #{SearchPojo.frmcontent}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and CiFormCustom.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and CiFormCustom.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and CiFormCustom.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and CiFormCustom.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and CiFormCustom.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and CiFormCustom.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and CiFormCustom.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and CiFormCustom.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and CiFormCustom.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and CiFormCustom.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and CiFormCustom.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.gengroupid != null ">
                or CiFormCustom.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
            </if>
            <if test="SearchPojo.modulecode != null ">
                or CiFormCustom.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.frmcode != null ">
                or CiFormCustom.FrmCode like concat('%', #{SearchPojo.frmcode}, '%')
            </if>
            <if test="SearchPojo.frmname != null ">
                or CiFormCustom.FrmName like concat('%', #{SearchPojo.frmname}, '%')
            </if>
            <if test="SearchPojo.frmcontent != null ">
                or CiFormCustom.FrmContent like concat('%', #{SearchPojo.frmcontent}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or CiFormCustom.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or CiFormCustom.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or CiFormCustom.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or CiFormCustom.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or CiFormCustom.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or CiFormCustom.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or CiFormCustom.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or CiFormCustom.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or CiFormCustom.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or CiFormCustom.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or CiFormCustom.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into CiFormCustom(id, GenGroupid, ModuleCode, FrmCode, FrmName, FrmContent, RowNum, EnabledMark, Remark,
                                 CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2,
                                 Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{gengroupid}, #{modulecode}, #{frmcode}, #{frmname}, #{frmcontent}, #{rownum}, #{enabledmark},
                #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1},
                #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiFormCustom
        <set>
            <if test="gengroupid != null ">
                GenGroupid =#{gengroupid},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="frmcode != null ">
                FrmCode =#{frmcode},
            </if>
            <if test="frmname != null ">
                FrmName =#{frmname},
            </if>
            <if test="frmcontent != null ">
                FrmContent =#{frmcontent},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiFormCustom
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--查询单个-->
    <select id="getEntityByCode" resultType="inks.system.domain.pojo.CiformcustomPojo">
        select id,
               GenGroupid,
               ModuleCode,
               FrmCode,
               FrmName,
               FrmContent,
               RowNum,
               EnabledMark,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from CiFormCustom
        where CiFormCustom.ModuleCode = #{key}
          and CiFormCustom.Tenantid = #{tid}
    </select>
</mapper>

