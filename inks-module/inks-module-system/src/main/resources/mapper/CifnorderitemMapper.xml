<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CifnorderitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CifnorderitemPojo">
        SELECT
            CiFnOrderItem.id,
            CiFnOrderItem.Pid,
            CiFnOrderItem.PricePolicyid,
            CiFnOrderItem.Functionid,
            CiFnOrderItem.CycleCode,
            CiFnOrderItem.Container,
            CiFnOrderItem.Quantity,
            CiFnOrderItem.TaxPrice,
            CiFnOrderItem.TaxAmount,
            CiFnOrderItem.RowNum,
            CiFnOrderItem.Remark,
            CiFnOrderItem.InvoFinish,
            CiFnOrderItem.InvoClosed,
            CiFnOrderItem.Revision,
            PiFunction.FunctionCode,
            PiFunction.FunctionName
        FROM
            CiFnOrderItem
                LEFT JOIN PiFunction ON CiFnOrderItem.Functionid = PiFunction.Functionid
        where CiFnOrderItem.id = #{key}
    </select>
    <sql id="selectCifnorderitemVo">
        SELECT
            CiFnOrderItem.id,
            CiFnOrderItem.Pid,
            CiFnOrderItem.PricePolicyid,
            CiFnOrderItem.Functionid,
            CiFnOrderItem.CycleCode,
            CiFnOrderItem.Container,
            CiFnOrderItem.Quantity,
            CiFnOrderItem.TaxPrice,
            CiFnOrderItem.TaxAmount,
            CiFnOrderItem.RowNum,
            CiFnOrderItem.Remark,
            CiFnOrderItem.InvoFinish,
            CiFnOrderItem.InvoClosed,
            CiFnOrderItem.Revision,
            PiFunction.FunctionCode,
            PiFunction.FunctionName
        FROM
            CiFnOrderItem
                LEFT JOIN PiFunction ON CiFnOrderItem.Functionid = PiFunction.Functionid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CifnorderitemPojo">
        <include refid="selectCifnorderitemVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiFnOrderItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and CiFnOrderItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.pricepolicyid != null and SearchPojo.pricepolicyid != ''">
            and CiFnOrderItem.pricepolicyid like concat('%', #{SearchPojo.pricepolicyid}, '%')
        </if>
        <if test="SearchPojo.functionid != null and SearchPojo.functionid != ''">
            and CiFnOrderItem.functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.cyclecode != null and SearchPojo.cyclecode != ''">
            and CiFnOrderItem.cyclecode like concat('%', #{SearchPojo.cyclecode}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and CiFnOrderItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            or CiFnOrderItem.Pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.pricepolicyid != null and SearchPojo.pricepolicyid != ''">
            or CiFnOrderItem.PricePolicyid like concat('%', #{SearchPojo.pricepolicyid}, '%')
        </if>
        <if test="SearchPojo.functionid != null and SearchPojo.functionid != ''">
            or CiFnOrderItem.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.cyclecode != null and SearchPojo.cyclecode != ''">
            or CiFnOrderItem.CycleCode like concat('%', #{SearchPojo.cyclecode}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or CiFnOrderItem.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        )
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.system.domain.pojo.CifnorderitemPojo">
        SELECT
            CiFnOrderItem.id,
            CiFnOrderItem.Pid,
            CiFnOrderItem.PricePolicyid,
            CiFnOrderItem.Functionid,
            CiFnOrderItem.CycleCode,
            CiFnOrderItem.Container,
            CiFnOrderItem.Quantity,
            CiFnOrderItem.TaxPrice,
            CiFnOrderItem.TaxAmount,
            CiFnOrderItem.RowNum,
            CiFnOrderItem.Remark,
            CiFnOrderItem.InvoFinish,
            CiFnOrderItem.InvoClosed,
            CiFnOrderItem.Revision,
            PiFunction.FunctionCode,
            PiFunction.FunctionName
        FROM
            PiFunction
                RIGHT JOIN CiFnOrderItem ON PiFunction.Functionid = CiFnOrderItem.Functionid
        where CiFnOrderItem.Pid = #{Pid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into CiFnOrderItem(id, Pid, PricePolicyid, Functionid, CycleCode, Container, Quantity, TaxPrice,
                                  TaxAmount, RowNum, Remark, InvoFinish, InvoClosed, Revision)
        values (#{id}, #{pid}, #{pricepolicyid}, #{functionid}, #{cyclecode}, #{container}, #{quantity}, #{taxprice},
                #{taxamount}, #{rownum}, #{remark}, #{invofinish}, #{invoclosed}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiFnOrderItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="pricepolicyid != null ">
                PricePolicyid = #{pricepolicyid},
            </if>
            <if test="functionid != null ">
                Functionid = #{functionid},
            </if>
            <if test="cyclecode != null ">
                CycleCode = #{cyclecode},
            </if>
            <if test="container != null">
                Container = #{container},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="invofinish != null">
                InvoFinish = #{invofinish},
            </if>
            <if test="invoclosed != null">
                InvoClosed = #{invoclosed},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiFnOrderItem
        where id = #{key}
    </delete>

</mapper>

