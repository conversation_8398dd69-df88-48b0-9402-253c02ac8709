<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PifunctionconfigMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PifunctionconfigPojo">
        SELECT
            PiFunctionConfig.id,
            PiFunctionConfig.Functionid,
            PiFunctionConfig.FunctionCode,
            PiFunctionConfig.FunctionName,
            PiFunctionConfig.Cfgid,
            PiFunctionConfig.Remark,
            PiFunctionConfig.CreateBy,
            PiFunctionConfig.CreateByid,
            PiFunctionConfig.CreateDate,
            PiFunctionConfig.Lister,
            PiFunctionConfig.Listerid,
            PiFunctionConfig.ModifyDate,
            PiFunctionConfig.Revision,
            CiConfig.Parentid,
            CiConfig.CfgName,
            CiConfig.CfgKey,
            CiConfig.CfgValue,
            CiConfig.CfgType,
            CiConfig.CfgLevel,
            CiConfig.CfgOption
        FROM
            PiFunctionConfig
                LEFT JOIN CiConfig ON PiFunctionConfig.Cfgid = CiConfig.id
        where PiFunctionConfig.id = #{key}
    </select>
    <sql id="selectPifunctionconfigVo">
        SELECT
            PiFunctionConfig.id,
            PiFunctionConfig.Functionid,
            PiFunctionConfig.FunctionCode,
            PiFunctionConfig.FunctionName,
            PiFunctionConfig.Cfgid,
            PiFunctionConfig.Remark,
            PiFunctionConfig.CreateBy,
            PiFunctionConfig.CreateByid,
            PiFunctionConfig.CreateDate,
            PiFunctionConfig.Lister,
            PiFunctionConfig.Listerid,
            PiFunctionConfig.ModifyDate,
            PiFunctionConfig.Revision,
            CiConfig.Parentid,
            CiConfig.CfgName,
            CiConfig.CfgKey,
            CiConfig.CfgValue,
            CiConfig.CfgType,
            CiConfig.CfgLevel,
            CiConfig.CfgOption
        FROM
            PiFunctionConfig
                LEFT JOIN CiConfig ON PiFunctionConfig.Cfgid = CiConfig.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PifunctionconfigPojo">
        <include refid="selectPifunctionconfigVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiFunctionConfig.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.listerid != null ">
            and PiFunctionConfig.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and PiFunctionConfig.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and PiFunctionConfig.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and PiFunctionConfig.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and PiFunctionConfig.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.cfgvalue != null ">
            and PiFunctionConfig.CfgValue like concat('%', #{SearchPojo.cfgvalue}, '%')
        </if>
        <if test="SearchPojo.cfgkey != null ">
            and PiFunctionConfig.CfgKey like concat('%', #{SearchPojo.cfgkey}, '%')
        </if>
        <if test="SearchPojo.cfgname != null ">
            and PiFunctionConfig.CfgName like concat('%', #{SearchPojo.cfgname}, '%')
        </if>
        <if test="SearchPojo.cfgid != null ">
            and PiFunctionConfig.Cfgid like concat('%', #{SearchPojo.cfgid}, '%')
        </if>
        <if test="SearchPojo.functionname != null ">
            and PiFunctionConfig.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.functioncode != null ">
            and PiFunctionConfig.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionid != null ">
            and PiFunctionConfig.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.listerid != null ">
                or PiFunctionConfig.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or PiFunctionConfig.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or PiFunctionConfig.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or PiFunctionConfig.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or PiFunctionConfig.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.cfgvalue != null ">
                or PiFunctionConfig.CfgValue like concat('%', #{SearchPojo.cfgvalue}, '%')
            </if>
            <if test="SearchPojo.cfgkey != null ">
                or PiFunctionConfig.CfgKey like concat('%', #{SearchPojo.cfgkey}, '%')
            </if>
            <if test="SearchPojo.cfgname != null ">
                or PiFunctionConfig.CfgName like concat('%', #{SearchPojo.cfgname}, '%')
            </if>
            <if test="SearchPojo.cfgid != null ">
                or PiFunctionConfig.Cfgid like concat('%', #{SearchPojo.cfgid}, '%')
            </if>
            <if test="SearchPojo.functionname != null ">
                or PiFunctionConfig.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
            </if>
            <if test="SearchPojo.functioncode != null ">
                or PiFunctionConfig.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
            </if>
            <if test="SearchPojo.functionid != null ">
                or PiFunctionConfig.Functionid like concat('%', #{SearchPojo.functionid}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiFunctionConfig(Revision, ModifyDate, Listerid, Lister, CreateDate, CreateByid, CreateBy, Remark,
                                     CfgLevel, CfgType, CfgValue, CfgKey, CfgName, Cfgid, FunctionName, FunctionCode,
                                     Functionid, id)
        values (#{revision}, #{modifydate}, #{listerid}, #{lister}, #{createdate}, #{createbyid}, #{createby},
                #{remark}, #{cfglevel}, #{cfgtype}, #{cfgvalue}, #{cfgkey}, #{cfgname}, #{cfgid}, #{functionname},
                #{functioncode}, #{functionid}, #{id})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiFunctionConfig
        <set>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="cfglevel != null">
                CfgLevel =#{cfglevel},
            </if>
            <if test="cfgtype != null">
                CfgType =#{cfgtype},
            </if>
            <if test="cfgvalue != null ">
                CfgValue =#{cfgvalue},
            </if>
            <if test="cfgkey != null ">
                CfgKey =#{cfgkey},
            </if>
            <if test="cfgname != null ">
                CfgName =#{cfgname},
            </if>
            <if test="cfgid != null ">
                Cfgid =#{cfgid},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiFunctionConfig
        where id = #{key}
    </delete>


    <!--查询List-->
    <select id="getListByFunction" resultType="inks.system.domain.pojo.PifunctionconfigPojo">
        <include refid="selectPifunctionconfigVo"/>
        where 1 = 1 and PiFunctionConfig.Functionid =#{key}
        order by PiFunctionConfig.CfgKey
    </select>
</mapper>

