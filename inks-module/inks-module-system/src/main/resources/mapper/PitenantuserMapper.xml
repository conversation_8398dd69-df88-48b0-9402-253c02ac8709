<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PitenantuserMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PitenantuserPojo">
        SELECT
            PiTenantUser.id,
            PiTenantUser.Tenantid,
            PiTenantUser.Userid,
            PiTenantUser.IsAdmin,
            PiTenantUser.Deptid,
            PiTenantUser.DeptCode,
            PiTenantUser.DeptName,
            PiTenantUser.IsDeptAdmin,
            PiTenantUser.DeptRowNum,
            PiTenantUser.RowNum,
            PiTenantUser.CreateBy,
            PiTenantUser.CreateByid,
            PiTenantUser.CreateDate,
            PiTenantUser.Lister,
            PiTenantUser.Listerid,
            PiTenantUser.ModifyDate,
            PiTenantUser.Custom1,
            PiTenantUser.Custom2,
            PiTenantUser.Custom3,
            PiTenantUser.Custom4,
            PiTenantUser.Custom5,
            PiTenantUser.Revision,
            PiUser.UserName,
            PiUser.RealName,
            PiTenant.TenantName
        FROM
            PiUser
                RIGHT JOIN PiTenantUser ON PiUser.Userid = PiTenantUser.Userid
                LEFT JOIN PiTenant ON PiTenant.Tenantid = PiTenantUser.Tenantid
        where PiTenantUser.id = #{key}
          and PiTenantUser.Tenantid = #{tid}
    </select>
    <sql id="selectPitenantuserVo">
        SELECT
            PiTenantUser.id,
            PiTenantUser.Tenantid,
            PiTenantUser.Userid,
            PiTenantUser.IsAdmin,
            PiTenantUser.Deptid,
            PiTenantUser.DeptCode,
            PiTenantUser.DeptName,
            PiTenantUser.IsDeptAdmin,
            PiTenantUser.DeptRowNum,
            PiTenantUser.RowNum,
            PiTenantUser.CreateBy,
            PiTenantUser.CreateByid,
            PiTenantUser.CreateDate,
            PiTenantUser.Lister,
            PiTenantUser.Listerid,
            PiTenantUser.ModifyDate,
            PiTenantUser.Custom1,
            PiTenantUser.Custom2,
            PiTenantUser.Custom3,
            PiTenantUser.Custom4,
            PiTenantUser.Custom5,
            PiTenantUser.Revision,
            PiUser.UserName,
            PiUser.RealName,
            PiTenant.TenantName
        FROM
            PiUser
                RIGHT JOIN PiTenantUser ON PiUser.Userid = PiTenantUser.Userid
                LEFT JOIN PiTenant ON PiTenant.Tenantid = PiTenantUser.Tenantid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PitenantuserPojo">
        <include refid="selectPitenantuserVo"/>
        where 1 = 1 and PiTenantUser.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiTenantUser.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.tenantname != null ">
            and PiTenantUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.userid != null ">
            and PiTenantUser.Userid=#{SearchPojo.userid}
        </if>
        <if test="SearchPojo.username != null ">
            and PiTenantUser.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null ">
            and PiTenantUser.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and PiTenantUser.Deptid= #{SearchPojo.deptid}
        </if>
        <if test="SearchPojo.deptcode != null ">
            and PiTenantUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
        </if>
        <if test="SearchPojo.deptname != null ">
            and PiTenantUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and PiTenantUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and PiTenantUser.CreateByid= #{SearchPojo.createbyid}
        </if>
        <if test="SearchPojo.lister != null ">
            and PiTenantUser.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and PiTenantUser.Listerid = #{SearchPojo.listerid}
        </if>
        <if test="SearchPojo.custom1 != null ">
            and PiTenantUser.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and PiTenantUser.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and PiTenantUser.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and PiTenantUser.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and PiTenantUser.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.tenantname != null ">
                or PiTenantUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <if test="SearchPojo.userid != null ">
                or PiTenantUser.Userid like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.username != null ">
                or PiTenantUser.UserName like concat('%', #{SearchPojo.username}, '%')
            </if>
            <if test="SearchPojo.realname != null ">
                or PiTenantUser.RealName like concat('%', #{SearchPojo.realname}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or PiTenantUser.Deptid =#{SearchPojo.deptid}
            </if>
            <if test="SearchPojo.deptcode != null ">
                or PiTenantUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
            </if>
            <if test="SearchPojo.deptname != null ">
                or PiTenantUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or PiTenantUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or PiTenantUser.CreateByid=#{SearchPojo.createbyid}
            </if>
            <if test="SearchPojo.lister != null ">
                or PiTenantUser.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or PiTenantUser.Listerid =#{SearchPojo.listerid}
            </if>
            <if test="SearchPojo.custom1 != null ">
                or PiTenantUser.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or PiTenantUser.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or PiTenantUser.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or PiTenantUser.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or PiTenantUser.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiTenantUser(id, Tenantid, TenantName, Userid, UserName, RealName, IsAdmin, Deptid, DeptCode,
                                 DeptName, IsDeptAdmin, DeptRowNum, RowNum, CreateBy, CreateByid, CreateDate, Lister,
                                 Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Revision)
        values (#{id}, #{tenantid}, #{tenantname}, #{userid}, #{username}, #{realname}, #{isadmin}, #{deptid},
                #{deptcode}, #{deptname}, #{isdeptadmin}, #{deptrownum}, #{rownum}, #{createby}, #{createbyid},
                #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
                #{custom5}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiTenantUser
        <set>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="isadmin != null">
                IsAdmin =#{isadmin},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="deptcode != null ">
                DeptCode =#{deptcode},
            </if>
            <if test="deptname != null ">
                DeptName =#{deptname},
            </if>
            <if test="isdeptadmin != null">
                IsDeptAdmin =#{isdeptadmin},
            </if>
            <if test="deptrownum != null">
                DeptRowNum =#{deptrownum},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiTenantUser
        where id = #{key}
    </delete>

    <!--查询List-->
    <select id="getListByTenant" resultType="inks.system.domain.pojo.PitenantuserPojo">
        <include refid="selectPitenantuserVo"/>
        where 1 = 1 and PiTenantUser.Tenantid =#{key}
        order by PiUser.UserName
    </select>

    <!--查询List-->
    <select id="getListByUser" resultType="inks.system.domain.pojo.PitenantuserPojo">
        <include refid="selectPitenantuserVo"/>
        where 1 = 1 and PiTenantUser.Userid =#{key}
        order by PiTenant.TenantCode
    </select>

    <select id="getListByUserName" resultType="inks.system.domain.pojo.PitenantuserPojo">
        <include refid="selectPitenantuserVo"/>
        where 1 = 1 and PiUser.UserName =#{username}
        order by PiTenant.TenantCode
    </select>

    <!--查询单个-->
    <select id="getEntityByUser" resultType="inks.system.domain.pojo.PitenantuserPojo">
        SELECT
            PiTenantUser.id,
            PiTenantUser.Tenantid,
            PiTenantUser.Userid,
            PiTenantUser.IsAdmin,
            PiTenantUser.Deptid,
            PiTenantUser.DeptCode,
            PiTenantUser.DeptName,
            PiTenantUser.IsDeptAdmin,
            PiTenantUser.DeptRowNum,
            PiTenantUser.RowNum,
            PiTenantUser.CreateBy,
            PiTenantUser.CreateByid,
            PiTenantUser.CreateDate,
            PiTenantUser.Lister,
            PiTenantUser.Listerid,
            PiTenantUser.ModifyDate,
            PiTenantUser.Custom1,
            PiTenantUser.Custom2,
            PiTenantUser.Custom3,
            PiTenantUser.Custom4,
            PiTenantUser.Custom5,
            PiTenantUser.Revision,
            PiUser.UserName,
            PiUser.RealName,
            PiTenant.TenantName
        FROM
            PiUser
                RIGHT JOIN PiTenantUser ON PiUser.Userid = PiTenantUser.Userid
                LEFT JOIN PiTenant ON PiTenant.Tenantid = PiTenantUser.Tenantid
        where PiTenantUser.Userid = #{userid}
          and PiTenantUser.Tenantid = #{tid}
    </select>

    <select id="getCountRecord" resultType="map">
        <foreach collection="tables" item="tableName" separator="UNION ALL">
            SELECT
            '${tableName}' AS tablename,
            COUNT(*) AS count,
            MIN(BillDate) AS firstdate,
            MAX(BillDate) AS lastdate
            FROM ${tableName}
            WHERE Tenantid = #{tid}
        </foreach>
    </select>


</mapper>

