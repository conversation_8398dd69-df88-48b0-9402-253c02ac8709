<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PitenantMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PitenantPojo">
        select Tenantid,
               TenantCode,
               TenantName,
               Company,
               CompanyAdd,
               CompanyTel,
               Contactor,
               TenantState,
               Sellerid,
               SellerCode,
               CreateBy,
               Lister,
               CreateDate,
               ModifyDate,
               PreviouVisit,
               Revision
        from PiTenant
        where PiTenant.Tenantid = #{key}
    </select>
    <sql id="selectPitenantVo">
        select Tenantid,
               TenantCode,
               TenantName,
               Company,
               CompanyAdd,
               CompanyTel,
               Contactor,
               TenantState,
               Sellerid,
               SellerCode,
               Create<PERSON><PERSON>,
               <PERSON><PERSON>,
               <PERSON>reate<PERSON><PERSON>,
               ModifyDate,
               PreviouVisit,
               Revision
        from PiTenant
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PitenantPojo">
        <include refid="selectPitenantVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiTenant.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.tenantcode != null and SearchPojo.tenantcode  != ''">
            and PiTenant.TenantCode like concat('%', #{SearchPojo.tenantcode}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname  != ''">
            and PiTenant.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.company != null and SearchPojo.company  != ''">
            and PiTenant.Company like concat('%', #{SearchPojo.company}, '%')
        </if>
        <if test="SearchPojo.companyadd != null and SearchPojo.companyadd  != ''">
            and PiTenant.CompanyAdd like concat('%', #{SearchPojo.companyadd}, '%')
        </if>
        <if test="SearchPojo.companytel != null and SearchPojo.companytel  != ''">
            and PiTenant.CompanyTel like concat('%', #{SearchPojo.companytel}, '%')
        </if>
        <if test="SearchPojo.contactor != null and SearchPojo.contactor  != ''">
            and PiTenant.Contactor like concat('%', #{SearchPojo.contactor}, '%')
        </if>
        <if test="SearchPojo.sellerid != null and SearchPojo.sellerid  != ''">
            and PiTenant.Sellerid like concat('%', #{SearchPojo.sellerid}, '%')
        </if>
        <if test="SearchPojo.sellercode != null and SearchPojo.sellercode  != ''">
            and PiTenant.SellerCode like concat('%', #{SearchPojo.sellercode}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby  != ''">
            and PiTenant.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and PiTenant.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.tenantcode != null and SearchPojo.tenantcode != ''">
            or PiTenant.TenantCode like concat('%', #{SearchPojo.tenantcode}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
            or PiTenant.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.company != null and SearchPojo.company != ''">
            or PiTenant.Company like concat('%', #{SearchPojo.company}, '%')
        </if>
        <if test="SearchPojo.companyadd != null and SearchPojo.companyadd != ''">
            or PiTenant.CompanyAdd like concat('%', #{SearchPojo.companyadd}, '%')
        </if>
        <if test="SearchPojo.companytel != null and SearchPojo.companytel != ''">
            or PiTenant.CompanyTel like concat('%', #{SearchPojo.companytel}, '%')
        </if>
        <if test="SearchPojo.contactor != null and SearchPojo.contactor != ''">
            or PiTenant.Contactor like concat('%', #{SearchPojo.contactor}, '%')
        </if>
        <if test="SearchPojo.sellerid != null and SearchPojo.sellerid != ''">
            or PiTenant.Sellerid like concat('%', #{SearchPojo.sellerid}, '%')
        </if>
        <if test="SearchPojo.sellercode != null and SearchPojo.sellercode != ''">
            or PiTenant.SellerCode like concat('%', #{SearchPojo.sellercode}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or PiTenant.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or PiTenant.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiTenant(Tenantid, TenantCode, TenantName, Company, CompanyAdd, CompanyTel, Contactor, TenantState,
                             Sellerid, SellerCode, CreateBy, Lister, CreateDate, ModifyDate, PreviouVisit, Revision)
        values (#{tenantid}, #{tenantcode}, #{tenantname}, #{company}, #{companyadd}, #{companytel}, #{contactor},
                #{tenantstate}, #{sellerid}, #{sellercode}, #{createby}, #{lister}, #{createdate}, #{modifydate},
                #{previouvisit}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiTenant
        <set>
            <if test="tenantcode != null ">
                TenantCode =#{tenantcode},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            <if test="company != null ">
                Company =#{company},
            </if>
            <if test="companyadd != null ">
                CompanyAdd =#{companyadd},
            </if>
            <if test="companytel != null ">
                CompanyTel =#{companytel},
            </if>
            <if test="contactor != null ">
                Contactor =#{contactor},
            </if>
            <if test="tenantstate != null">
                TenantState =#{tenantstate},
            </if>
            <if test="sellerid != null ">
                Sellerid =#{sellerid},
            </if>
            <if test="sellercode != null ">
                SellerCode =#{sellercode},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="previouvisit != null">
                PreviouVisit =#{previouvisit},
            </if>
            Revision=Revision+1
        </set>
        where Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiTenant
        where Tenantid = #{key}
    </delete>

</mapper>

