<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CinoticeMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CinoticePojo">
        select
          id, NoticeCode, NoticeType, NoticeTitle, NoticeContent, NoticeDate, RowNum, EnabledMark, Tenantid, Functionid, FunctionCode, FunctionName, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision        from CiNotice
        where CiNotice.id = #{key} and CiNotice.Tenantid=#{tid}
    </select>
    <sql id="selectCinoticeVo">
         select
          id, NoticeCode, NoticeType, NoticeTitle, NoticeContent, NoticeDate, RowNum, EnabledMark, Tenantid, Functionid, FunctionCode, FunctionName, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision        from CiNotice
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.CinoticePojo">
        <include refid="selectCinoticeVo"/>
         where 1 = 1 and CiNotice.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and CiNotice.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>

             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.noticecode != null and SearchPojo.noticecode  != ''">
   and CiNotice.NoticeCode like concat('%', #{SearchPojo.noticecode}, '%')
</if>
<if test="SearchPojo.noticetype != null and SearchPojo.noticetype  != ''">
   and CiNotice.NoticeType like concat('%', #{SearchPojo.noticetype}, '%')
</if>
<if test="SearchPojo.noticetitle != null and SearchPojo.noticetitle  != ''">
   and CiNotice.NoticeTitle like concat('%', #{SearchPojo.noticetitle}, '%')
</if>
<if test="SearchPojo.noticecontent != null and SearchPojo.noticecontent  != ''">
   and CiNotice.NoticeContent like concat('%', #{SearchPojo.noticecontent}, '%')
</if>
<if test="SearchPojo.functionid != null and SearchPojo.functionid  != ''">
   and CiNotice.Functionid like concat('%', #{SearchPojo.functionid}, '%')
</if>
<if test="SearchPojo.functioncode != null and SearchPojo.functioncode  != ''">
   and CiNotice.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
</if>
<if test="SearchPojo.functionname != null and SearchPojo.functionname  != ''">
   and CiNotice.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark  != ''">
   and CiNotice.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null and SearchPojo.createby  != ''">
   and CiNotice.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null and SearchPojo.createbyid  != ''">
   and CiNotice.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
   and CiNotice.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid  != ''">
   and CiNotice.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
     </sql>   
     <sql id="or">
     and (1=0 
<if test="SearchPojo.noticecode != null and SearchPojo.noticecode != ''">
   or CiNotice.NoticeCode like concat('%', #{SearchPojo.noticecode}, '%')
</if>
<if test="SearchPojo.noticetype != null and SearchPojo.noticetype != ''">
   or CiNotice.NoticeType like concat('%', #{SearchPojo.noticetype}, '%')
</if>
<if test="SearchPojo.noticetitle != null and SearchPojo.noticetitle != ''">
   or CiNotice.NoticeTitle like concat('%', #{SearchPojo.noticetitle}, '%')
</if>
<if test="SearchPojo.noticecontent != null and SearchPojo.noticecontent != ''">
   or CiNotice.NoticeContent like concat('%', #{SearchPojo.noticecontent}, '%')
</if>
<if test="SearchPojo.functionid != null and SearchPojo.functionid != ''">
   or CiNotice.Functionid like concat('%', #{SearchPojo.functionid}, '%')
</if>
<if test="SearchPojo.functioncode != null and SearchPojo.functioncode != ''">
   or CiNotice.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
</if>
<if test="SearchPojo.functionname != null and SearchPojo.functionname != ''">
   or CiNotice.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or CiNotice.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null and SearchPojo.createby != ''">
   or CiNotice.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
   or CiNotice.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   or CiNotice.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
   or CiNotice.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
)
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into CiNotice(id, NoticeCode, NoticeType, NoticeTitle, NoticeContent, NoticeDate, RowNum, EnabledMark, Tenantid, Functionid, FunctionCode, FunctionName, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision)
        values (#{id}, #{noticecode}, #{noticetype}, #{noticetitle}, #{noticecontent}, #{noticedate}, #{rownum}, #{enabledmark}, #{tenantid}, #{functionid}, #{functioncode}, #{functionname}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update CiNotice
        <set>
            <if test="noticecode != null ">
                NoticeCode =#{noticecode},
            </if>
            <if test="noticetype != null ">
                NoticeType =#{noticetype},
            </if>
            <if test="noticetitle != null ">
                NoticeTitle =#{noticetitle},
            </if>
            <if test="noticecontent != null ">
                NoticeContent =#{noticecontent},
            </if>
            <if test="noticedate != null">
                NoticeDate =#{noticedate},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from CiNotice where id = #{key} and Tenantid=#{tid}
    </delete>

</mapper>

