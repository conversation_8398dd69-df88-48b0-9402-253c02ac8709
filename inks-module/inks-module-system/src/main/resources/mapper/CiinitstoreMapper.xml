<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CiinitstoreMapper">

    <!--通过主键删除-->
    <delete id="deleteAcceItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Mat_AccessItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Mat_Access
                      where Tenantid = #{tenantid}
                        and Mat_Access.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deleteAcce" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Mat_Access
        where Tenantid = #{tenantid}
          and Mat_Access.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>

    <!--通过主键删除-->
    <delete id="deleteRequItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Mat_RequisitionItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Mat_Requisition
                      where Tenantid = #{tenantid}
                        and Mat_Requisition.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deleteRequ" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Mat_Requisition
        where Tenantid = #{tenantid}
          and Mat_Requisition.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>


    <!--通过主键删除-->
    <delete id="deleteReqRItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Mat_ReqRetrunItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Mat_ReqRetrun
                      where Tenantid = #{tenantid}
                        and Mat_ReqRetrun.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deleteReqR" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Mat_ReqRetrun
        where Tenantid = #{tenantid}
          and Mat_ReqRetrun.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>

</mapper>

