<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PifunctionpermMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PifunctionpermPojo">
        select id,
               Functionid,
               FunctionCode,
               FunctionName,
               Permid,
               Lister,
               CreateDate,
               ModifyDate,
               Remark,
               CreateBy,
               Tenantid,
               Revision,
               PermCode,
               PermName
        from PiFunctionPerm
        where PiFunctionPerm.id = #{key}
          and PiFunctionPerm.Tenantid = #{tid}
    </select>
    <sql id="selectPifunctionpermVo">
        SELECT
            PiFunctionPerm.id,
            PiFunctionPerm.Functionid,
            PiFunctionPerm.FunctionCode,
            PiFunctionPerm.FunctionName,
            PiFunctionPerm.Permid,
            PiFunctionPerm.Remark,
            PiFunctionPerm.CreateBy,
            PiFunctionPerm.CreateDate,
            PiFunctionPerm.Lister,
            PiFunctionPerm.ModifyDate,
            PiFunctionPerm.Tenantid,
            PiFunctionPerm.Revision,
            PiPermCode.Permid,
            PiPermCode.Parentid,
            PiPermCode.PermCode,
            PiPermCode.PermName,
            PiPermCode.RowNum,
            PiPermCode.IsPublic,
            PiPermCode.EnabledMark
        FROM
            PiFunctionPerm
                LEFT JOIN PiPermCode ON PiPermCode.Permid = PiFunctionPerm.Permid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PifunctionpermPojo">
        <include refid="selectPifunctionpermVo"/>
        where 1 = 1 and PiFunctionPerm.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiFunctionPerm.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.functionid != null and SearchPojo.functionid  != ''">
            and PiFunctionPerm.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null and SearchPojo.functioncode  != ''">
            and PiFunctionPerm.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null and SearchPojo.functionname  != ''">
            and PiFunctionPerm.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.permid != null and SearchPojo.permid  != ''">
            and PiFunctionPerm.Permid like concat('%', #{SearchPojo.permid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and PiFunctionPerm.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark  != ''">
            and PiFunctionPerm.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby  != ''">
            and PiFunctionPerm.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.permcode != null and SearchPojo.permcode  != ''">
            and PiFunctionPerm.PermCode like concat('%', #{SearchPojo.permcode}, '%')
        </if>
        <if test="SearchPojo.permname != null and SearchPojo.permname  != ''">
            and PiFunctionPerm.PermName like concat('%', #{SearchPojo.permname}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.functionid != null and SearchPojo.functionid != ''">
            or PiFunctionPerm.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null and SearchPojo.functioncode != ''">
            or PiFunctionPerm.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null and SearchPojo.functionname != ''">
            or PiFunctionPerm.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.permid != null and SearchPojo.permid != ''">
            or PiFunctionPerm.Permid like concat('%', #{SearchPojo.permid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or PiFunctionPerm.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or PiFunctionPerm.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or PiFunctionPerm.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.permcode != null and SearchPojo.permcode != ''">
            or PiFunctionPerm.PermCode like concat('%', #{SearchPojo.permcode}, '%')
        </if>
        <if test="SearchPojo.permname != null and SearchPojo.permname != ''">
            or PiFunctionPerm.PermName like concat('%', #{SearchPojo.permname}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiFunctionPerm(id, Functionid, FunctionCode, FunctionName, Permid, Lister, CreateDate, ModifyDate,
                                   Remark, CreateBy, Tenantid, Revision, PermCode, PermName)
        values (#{id}, #{functionid}, #{functioncode}, #{functionname}, #{permid}, #{lister}, #{createdate},
                #{modifydate}, #{remark}, #{createby}, #{tenantid}, #{revision}, #{permcode}, #{permname})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiFunctionPerm
        <set>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="permid != null ">
                Permid =#{permid},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="permcode != null ">
                PermCode =#{permcode},
            </if>
            <if test="permname != null ">
                PermName =#{permname},
            </if>
            Revision=Revision+1

        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiFunctionPerm
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--查询List-->
    <select id="getListByFunction" resultType="inks.system.domain.pojo.PifunctionpermPojo">
        <include refid="selectPifunctionpermVo"/>
        where 1 = 1 and PiFunctionPerm.Functionid =#{key}
        order by PiFunctionPerm.PermCode
    </select>

    <select id="getFunctionPermsByFunctionCode" resultType="java.lang.String">
        SELECT PiPermCode.PermCode
        FROM PiFunctionPerm
                 LEFT JOIN PiPermCode ON PiPermCode.Permid = PiFunctionPerm.Permid
                 INNER JOIN PiSubscriber ON PiFunctionPerm.Functionid = PiSubscriber.Functionid
        WHERE PiSubscriber.FunctionCode = #{functioncode}
          AND PiSubscriber.Tenantid = #{tid}
        ORDER BY PiPermCode.PermCode
    </select>
</mapper>

