<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PitenantdmsuserMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PitenantdmsuserPojo">
        select PiTenantDmsUser.id,
               PiTenantDmsUser.Tenantid,
               PiTenantDmsUser.TenantName,
               PiTenantDmsUser.Userid,
               PiTenantDmsUser.UserName,
               PiTenantDmsUser.RealName,
               PiTenantDmsUser.UserType,
               PiTenantDmsUser.IsAdmin,
               PiTenantDmsUser.Deptid,
               PiTenantDmsUser.DeptCode,
               PiTenantDmsUser.DeptName,
               PiTenantDmsUser.IsDeptAdmin,
               PiTenantDmsUser.DeptRowNum,
               PiTenantDmsUser.RowNum,
               PiTenantDmsUser.UserStatus,
               PiTenantDmsUser.UserCode,
               PiTenantDmsUser.Groupids,
               PiTenantDmsUser.GroupNames,
               PiTenantDmsUser.DmsFunctids,
               PiTenantDmsUser.DmsFunctNames,
               PiTenantDmsUser.CreateBy,
               PiTenantDmsUser.CreateByid,
               PiTenantDmsUser.CreateDate,
               PiTenantDmsUser.Lister,
               PiTenantDmsUser.Listerid,
               PiTenantDmsUser.ModifyDate,
               PiTenantDmsUser.Custom1,
               PiTenantDmsUser.Custom2,
               PiTenantDmsUser.Custom3,
               PiTenantDmsUser.Custom4,
               PiTenantDmsUser.Custom5,
               PiTenantDmsUser.Revision,
               PiDmsUser.NickName,
               PiDmsUser.Mobile,
               PiDmsUser.Email,
               PiDmsUser.Sex,
               PiDmsUser.Avatar
        from PiTenantDmsUser
        LEFT JOIN PiDmsUser ON PiTenantDmsUser.Userid = PiDmsUser.Userid
        where PiTenantDmsUser.id = #{key}
    </select>
    <sql id="selectPitenantdmsuserVo">
        select PiTenantDmsUser.id,
               PiTenantDmsUser.Tenantid,
               PiTenantDmsUser.TenantName,
               PiTenantDmsUser.Userid,
               PiTenantDmsUser.UserName,
               PiTenantDmsUser.RealName,
               PiTenantDmsUser.UserType,
               PiTenantDmsUser.IsAdmin,
               PiTenantDmsUser.Deptid,
               PiTenantDmsUser.DeptCode,
               PiTenantDmsUser.DeptName,
               PiTenantDmsUser.IsDeptAdmin,
               PiTenantDmsUser.DeptRowNum,
               PiTenantDmsUser.RowNum,
               PiTenantDmsUser.UserStatus,
               PiTenantDmsUser.UserCode,
               PiTenantDmsUser.Groupids,
               PiTenantDmsUser.GroupNames,
               PiTenantDmsUser.DmsFunctids,
               PiTenantDmsUser.DmsFunctNames,
               PiTenantDmsUser.CreateBy,
               PiTenantDmsUser.CreateByid,
               PiTenantDmsUser.CreateDate,
               PiTenantDmsUser.Lister,
               PiTenantDmsUser.Listerid,
               PiTenantDmsUser.ModifyDate,
               PiTenantDmsUser.Custom1,
               PiTenantDmsUser.Custom2,
               PiTenantDmsUser.Custom3,
               PiTenantDmsUser.Custom4,
               PiTenantDmsUser.Custom5,
               PiTenantDmsUser.Revision,
               PiDmsUser.NickName,
               PiDmsUser.Mobile,
               PiDmsUser.Email,
               PiDmsUser.Sex,
               PiDmsUser.Avatar
        from PiTenantDmsUser
                 LEFT JOIN PiDmsUser ON PiTenantDmsUser.Userid = PiDmsUser.Userid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PitenantdmsuserPojo">
        <include refid="selectPitenantdmsuserVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiTenantDmsUser.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.tenantname != null ">
            and PiTenantDmsUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.userid != null ">
            and PiTenantDmsUser.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.username != null ">
            and PiTenantDmsUser.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null ">
            and PiTenantDmsUser.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and PiTenantDmsUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.deptcode != null ">
            and PiTenantDmsUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
        </if>
        <if test="SearchPojo.deptname != null ">
            and PiTenantDmsUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
        </if>
        <if test="SearchPojo.usercode != null ">
            and PiTenantDmsUser.UserCode like concat('%', #{SearchPojo.usercode}, '%')
        </if>
        <if test="SearchPojo.groupids != null ">
            and PiTenantDmsUser.Groupids like concat('%', #{SearchPojo.groupids}, '%')
        </if>
        <if test="SearchPojo.groupnames != null ">
            and PiTenantDmsUser.GroupNames like concat('%', #{SearchPojo.groupnames}, '%')
        </if>
        <if test="SearchPojo.dmsfunctids != null ">
            and PiTenantDmsUser.DmsFunctids like concat('%', #{SearchPojo.dmsfunctids}, '%')
        </if>
        <if test="SearchPojo.dmsfunctnames != null ">
            and PiTenantDmsUser.DmsFunctNames like concat('%', #{SearchPojo.dmsfunctnames}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and PiTenantDmsUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and PiTenantDmsUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and PiTenantDmsUser.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and PiTenantDmsUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and PiTenantDmsUser.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and PiTenantDmsUser.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and PiTenantDmsUser.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and PiTenantDmsUser.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and PiTenantDmsUser.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.tenantname != null ">
                or PiTenantDmsUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <if test="SearchPojo.userid != null ">
                or PiTenantDmsUser.Userid like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.username != null ">
                or PiTenantDmsUser.UserName like concat('%', #{SearchPojo.username}, '%')
            </if>
            <if test="SearchPojo.realname != null ">
                or PiTenantDmsUser.RealName like concat('%', #{SearchPojo.realname}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or PiTenantDmsUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.deptcode != null ">
                or PiTenantDmsUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
            </if>
            <if test="SearchPojo.deptname != null ">
                or PiTenantDmsUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
            </if>
            <if test="SearchPojo.usercode != null ">
                or PiTenantDmsUser.UserCode like concat('%', #{SearchPojo.usercode}, '%')
            </if>
            <if test="SearchPojo.groupids != null ">
                or PiTenantDmsUser.Groupids like concat('%', #{SearchPojo.groupids}, '%')
            </if>
            <if test="SearchPojo.groupnames != null ">
                or PiTenantDmsUser.GroupNames like concat('%', #{SearchPojo.groupnames}, '%')
            </if>
            <if test="SearchPojo.dmsfunctids != null ">
                or PiTenantDmsUser.DmsFunctids like concat('%', #{SearchPojo.dmsfunctids}, '%')
            </if>
            <if test="SearchPojo.dmsfunctnames != null ">
                or PiTenantDmsUser.DmsFunctNames like concat('%', #{SearchPojo.dmsfunctnames}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or PiTenantDmsUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or PiTenantDmsUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or PiTenantDmsUser.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or PiTenantDmsUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or PiTenantDmsUser.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or PiTenantDmsUser.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or PiTenantDmsUser.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or PiTenantDmsUser.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or PiTenantDmsUser.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiTenantDmsUser(id, Tenantid, TenantName, Userid, UserName, RealName, UserType, IsAdmin, Deptid,
                                    DeptCode, DeptName, IsDeptAdmin, DeptRowNum, RowNum, UserStatus, UserCode, Groupids,
                                    GroupNames, DmsFunctids, DmsFunctNames, CreateBy, CreateByid, CreateDate, Lister,
                                    Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Revision)
        values (#{id}, #{tenantid}, #{tenantname}, #{userid}, #{username}, #{realname}, #{usertype}, #{isadmin},
                #{deptid}, #{deptcode}, #{deptname}, #{isdeptadmin}, #{deptrownum}, #{rownum}, #{userstatus},
                #{usercode}, #{groupids}, #{groupnames}, #{dmsfunctids}, #{dmsfunctnames}, #{createby}, #{createbyid},
                #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
                #{custom5}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiTenantDmsUser
        <set>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="usertype != null">
                UserType =#{usertype},
            </if>
            <if test="isadmin != null">
                IsAdmin =#{isadmin},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="deptcode != null ">
                DeptCode =#{deptcode},
            </if>
            <if test="deptname != null ">
                DeptName =#{deptname},
            </if>
            <if test="isdeptadmin != null">
                IsDeptAdmin =#{isdeptadmin},
            </if>
            <if test="deptrownum != null">
                DeptRowNum =#{deptrownum},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="userstatus != null">
                UserStatus =#{userstatus},
            </if>
            <if test="usercode != null ">
                UserCode =#{usercode},
            </if>
            <if test="groupids != null ">
                Groupids =#{groupids},
            </if>
            <if test="groupnames != null ">
                GroupNames =#{groupnames},
            </if>
            <if test="dmsfunctids != null ">
                DmsFunctids =#{dmsfunctids},
            </if>
            <if test="dmsfunctnames != null ">
                DmsFunctNames =#{dmsfunctnames},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiTenantDmsUser
        where id = #{key}
    </delete>
    <!--查询指定行数据-->
    <select id="getListByUser"
            resultType="inks.system.domain.pojo.PitenantdmsuserPojo">
        <include refid="selectPitenantdmsuserVo"/>
        where PiTenantDmsUser.Userid=#{userid} Order by id
    </select>
    <select id="getEntityByUserid" resultType="inks.system.domain.pojo.PitenantdmsuserPojo">
        <include refid="selectPitenantdmsuserVo"/>
        where PiTenantDmsUser.Userid=#{key} and PiTenantDmsUser.Tenantid=#{tid}
    </select>

</mapper>

