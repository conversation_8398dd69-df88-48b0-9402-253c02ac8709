<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CiwarningMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CiwarningPojo">
        select id,
               GenGroupid,
               ModuleCode,
               WarnCode,
               WarnName,
               WarnField,
               SvcCode,
               WarnApi,
               WebPath,
               ImageCss,
               TagTitle,
               PermCode,
               RowNum,
               EnabledMark,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Revision
        from CiWarning
        where CiWarning.id = #{key}
    </select>
    <sql id="selectCiwarningVo">
        select id,
               GenGroupid,
               ModuleCode,
               WarnCode,
               Warn<PERSON>ame,
               Warn<PERSON>ield,
               SvcCode,
               WarnApi,
               WebPath,
               ImageCss,
               TagTitle,
               PermCode,
               RowNum,
               EnabledMark,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Revision
        from CiWarning
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CiwarningPojo">
        <include refid="selectCiwarningVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiWarning.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.gengroupid != null ">
            and CiWarning.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
        </if>
        <if test="SearchPojo.modulecode != null ">
            and CiWarning.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.warncode != null ">
            and CiWarning.WarnCode like concat('%', #{SearchPojo.warncode}, '%')
        </if>
        <if test="SearchPojo.warnname != null ">
            and CiWarning.WarnName like concat('%', #{SearchPojo.warnname}, '%')
        </if>
        <if test="SearchPojo.warnfield != null ">
            and CiWarning.WarnField like concat('%', #{SearchPojo.warnfield}, '%')
        </if>
        <if test="SearchPojo.svccode != null ">
            and CiWarning.SvcCode like concat('%', #{SearchPojo.svccode}, '%')
        </if>
        <if test="SearchPojo.warnapi != null ">
            and CiWarning.WarnApi like concat('%', #{SearchPojo.warnapi}, '%')
        </if>
        <if test="SearchPojo.webpath != null ">
            and CiWarning.WebPath like concat('%', #{SearchPojo.webpath}, '%')
        </if>
        <if test="SearchPojo.imagecss != null ">
            and CiWarning.ImageCss like concat('%', #{SearchPojo.imagecss}, '%')
        </if>
        <if test="SearchPojo.tagtitle != null ">
            and CiWarning.TagTitle like concat('%', #{SearchPojo.tagtitle}, '%')
        </if>
        <if test="SearchPojo.permcode != null ">
            and CiWarning.PermCode like concat('%', #{SearchPojo.permcode}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and CiWarning.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and CiWarning.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and CiWarning.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and CiWarning.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and CiWarning.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.gengroupid != null ">
                or CiWarning.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
            </if>
            <if test="SearchPojo.modulecode != null ">
                or CiWarning.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.warncode != null ">
                or CiWarning.WarnCode like concat('%', #{SearchPojo.warncode}, '%')
            </if>
            <if test="SearchPojo.warnname != null ">
                or CiWarning.WarnName like concat('%', #{SearchPojo.warnname}, '%')
            </if>
            <if test="SearchPojo.warnfield != null ">
                or CiWarning.WarnField like concat('%', #{SearchPojo.warnfield}, '%')
            </if>
            <if test="SearchPojo.svccode != null ">
                or CiWarning.SvcCode like concat('%', #{SearchPojo.svccode}, '%')
            </if>
            <if test="SearchPojo.warnapi != null ">
                or CiWarning.WarnApi like concat('%', #{SearchPojo.warnapi}, '%')
            </if>
            <if test="SearchPojo.webpath != null ">
                or CiWarning.WebPath like concat('%', #{SearchPojo.webpath}, '%')
            </if>
            <if test="SearchPojo.imagecss != null ">
                or CiWarning.ImageCss like concat('%', #{SearchPojo.imagecss}, '%')
            </if>
            <if test="SearchPojo.tagtitle != null ">
                or CiWarning.TagTitle like concat('%', #{SearchPojo.tagtitle}, '%')
            </if>
            <if test="SearchPojo.permcode != null ">
                or CiWarning.PermCode like concat('%', #{SearchPojo.permcode}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or CiWarning.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or CiWarning.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or CiWarning.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or CiWarning.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or CiWarning.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into CiWarning(id, GenGroupid, ModuleCode, WarnCode, WarnName, WarnField, SvcCode, WarnApi, WebPath,
                              ImageCss, TagTitle, PermCode, RowNum, EnabledMark, Remark, CreateBy, CreateByid,
                              CreateDate, Lister, Listerid, ModifyDate, Revision)
        values (#{id}, #{gengroupid}, #{modulecode}, #{warncode}, #{warnname}, #{warnfield}, #{svccode}, #{warnapi},
                #{webpath}, #{imagecss}, #{tagtitle}, #{permcode}, #{rownum}, #{enabledmark}, #{remark}, #{createby},
                #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiWarning
        <set>
            <if test="gengroupid != null ">
                GenGroupid =#{gengroupid},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="warncode != null ">
                WarnCode =#{warncode},
            </if>
            <if test="warnname != null ">
                WarnName =#{warnname},
            </if>
            <if test="warnfield != null ">
                WarnField =#{warnfield},
            </if>
            <if test="svccode != null ">
                SvcCode =#{svccode},
            </if>
            <if test="warnapi != null ">
                WarnApi =#{warnapi},
            </if>
            <if test="webpath != null ">
                WebPath =#{webpath},
            </if>
            <if test="imagecss != null ">
                ImageCss =#{imagecss},
            </if>
            <if test="tagtitle != null ">
                TagTitle =#{tagtitle},
            </if>
            <if test="permcode != null ">
                PermCode =#{permcode},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiWarning
        where id = #{key}
    </delete>
</mapper>

