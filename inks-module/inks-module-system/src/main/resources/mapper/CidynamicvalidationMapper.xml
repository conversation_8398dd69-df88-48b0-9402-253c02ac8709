<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CidynamicvalidationMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CidynamicvalidationPojo">
        <include refid="selectbillVo"/>
        where CiDynamicValidation.id = #{key} and CiDynamicValidation.Tenantid=#{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select
id, ValidationCode, ValidationTitle, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from CiDynamicValidation
    </sql>
    <sql id="selectdetailVo">
        select
               CiDynamicValidation.CreateBy,
               CiDynamicValidation.Lister,
               CiDynamicValidationItem.*
        from CiDynamicValidationItem left join CiDynamicValidation on CiDynamicValidation.id = CiDynamicValidationItem.Pid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.CidynamicvalidationitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and CiDynamicValidation.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiDynamicValidation.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.validationcode != null ">
            and CiDynamicValidation.ValidationCode like concat('%', #{SearchPojo.validationcode}, '%')
        </if>
        <if test="SearchPojo.validationtitle != null ">
            and CiDynamicValidation.ValidationTitle like concat('%', #{SearchPojo.validationtitle}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and CiDynamicValidation.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and CiDynamicValidation.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and CiDynamicValidation.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and CiDynamicValidation.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and CiDynamicValidation.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and CiDynamicValidation.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and CiDynamicValidation.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and CiDynamicValidation.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and CiDynamicValidation.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and CiDynamicValidation.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and CiDynamicValidation.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and CiDynamicValidation.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and CiDynamicValidation.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and CiDynamicValidation.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and CiDynamicValidation.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.validationcode != null ">
                or CiDynamicValidation.ValidationCode like concat('%', #{SearchPojo.validationcode}, '%')
            </if>
            <if test="SearchPojo.validationtitle != null ">
                or CiDynamicValidation.ValidationTitle like concat('%', #{SearchPojo.validationtitle}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or CiDynamicValidation.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or CiDynamicValidation.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or CiDynamicValidation.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or CiDynamicValidation.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or CiDynamicValidation.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or CiDynamicValidation.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or CiDynamicValidation.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or CiDynamicValidation.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or CiDynamicValidation.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or CiDynamicValidation.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or CiDynamicValidation.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or CiDynamicValidation.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or CiDynamicValidation.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or CiDynamicValidation.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or CiDynamicValidation.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.CidynamicvalidationPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and CiDynamicValidation.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiDynamicValidation.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.validationcode != null ">
            and CiDynamicValidation.ValidationCode like concat('%', #{SearchPojo.validationcode}, '%')
        </if>
        <if test="SearchPojo.validationtitle != null ">
            and CiDynamicValidation.ValidationTitle like concat('%', #{SearchPojo.validationtitle}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and CiDynamicValidation.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and CiDynamicValidation.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and CiDynamicValidation.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and CiDynamicValidation.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and CiDynamicValidation.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and CiDynamicValidation.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and CiDynamicValidation.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and CiDynamicValidation.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and CiDynamicValidation.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and CiDynamicValidation.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null ">
            and CiDynamicValidation.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null ">
            and CiDynamicValidation.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null ">
            and CiDynamicValidation.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null ">
            and CiDynamicValidation.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null ">
            and CiDynamicValidation.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.validationcode != null ">
                or CiDynamicValidation.ValidationCode like concat('%', #{SearchPojo.validationcode}, '%')
            </if>
            <if test="SearchPojo.validationtitle != null ">
                or CiDynamicValidation.ValidationTitle like concat('%', #{SearchPojo.validationtitle}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or CiDynamicValidation.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or CiDynamicValidation.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or CiDynamicValidation.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or CiDynamicValidation.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or CiDynamicValidation.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or CiDynamicValidation.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or CiDynamicValidation.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or CiDynamicValidation.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or CiDynamicValidation.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or CiDynamicValidation.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null ">
                or CiDynamicValidation.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null ">
                or CiDynamicValidation.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null ">
                or CiDynamicValidation.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null ">
                or CiDynamicValidation.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null ">
                or CiDynamicValidation.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert" >
        insert into CiDynamicValidation(id, ValidationCode, ValidationTitle, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{validationcode}, #{validationtitle}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiDynamicValidation
        <set>
            <if test="validationcode != null ">
            ValidationCode =#{validationcode},
        </if>
            <if test="validationtitle != null ">
            ValidationTitle =#{validationtitle},
        </if>
            <if test="summary != null ">
            Summary =#{summary},
        </if>
            <if test="createby != null ">
            CreateBy =#{createby},
        </if>
            <if test="createbyid != null ">
            CreateByid =#{createbyid},
        </if>
            <if test="createdate != null">
            CreateDate =#{createdate},
        </if>
            <if test="lister != null ">
            Lister =#{lister},
        </if>
            <if test="listerid != null ">
            Listerid =#{listerid},
        </if>
            <if test="modifydate != null">
            ModifyDate =#{modifydate},
        </if>
            <if test="custom1 != null ">
            Custom1 =#{custom1},
        </if>
            <if test="custom2 != null ">
            Custom2 =#{custom2},
        </if>
            <if test="custom3 != null ">
            Custom3 =#{custom3},
        </if>
            <if test="custom4 != null ">
            Custom4 =#{custom4},
        </if>
            <if test="custom5 != null ">
            Custom5 =#{custom5},
        </if>
            <if test="custom6 != null ">
            Custom6 =#{custom6},
        </if>
            <if test="custom7 != null ">
            Custom7 =#{custom7},
        </if>
            <if test="custom8 != null ">
            Custom8 =#{custom8},
        </if>
            <if test="custom9 != null ">
            Custom9 =#{custom9},
        </if>
            <if test="custom10 != null ">
            Custom10 =#{custom10},
        </if>
        Revision=Revision+1
    </set>
    where id = #{id} and Tenantid =#{tenantid}
</update>

        <!--通过主键删除-->
<delete id="delete">
delete from CiDynamicValidation where id = #{key} and Tenantid=#{tid}
</delete>
        <!--查询DelListIds-->
<select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.system.domain.pojo.CidynamicvalidationPojo">
select
id
from CiDynamicValidationItem
where Pid = #{id}
<if test="item !=null and item.size()>0">
    and id not in
    <foreach collection="item" open="(" close=")" separator="," item="item">
        <if test="item.id != null">
            #{item.id}
        </if>
        <if test="item.id == null">
            ''
        </if>
    </foreach>
</if>
</select>

</mapper>

