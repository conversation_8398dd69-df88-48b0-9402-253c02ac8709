<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PiroleMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PirolePojo">
        select Roleid,
               RoleCode,
               RoleName,
               Functionid,
               FunctionCode,
               FunctionName,
               EnabledMark,
               RowNum,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Tenantid,
               TenantName,
               Revision
        from PiRole
        where PiRole.Roleid = #{key}
          and PiRole.Tenantid = #{tid}
    </select>
    <sql id="selectPiroleVo">
        select Roleid,
               RoleCode,
               RoleName,
               Functionid,
               FunctionCode,
               FunctionName,
               EnabledMark,
               RowN<PERSON>,
               Remark,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               <PERSON>reate<PERSON><PERSON>,
               <PERSON>er,
               <PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>ate,
               <PERSON>anti<PERSON>,
               <PERSON>ant<PERSON><PERSON>,
               Revision
        from PiRole
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PirolePojo">
        <include refid="selectPiroleVo"/>
        where 1 = 1 and PiRole.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiRole.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.rolecode != null ">
            and PiRole.RoleCode like concat('%', #{SearchPojo.rolecode}, '%')
        </if>
        <if test="SearchPojo.rolename != null ">
            and PiRole.RoleName like concat('%', #{SearchPojo.rolename}, '%')
        </if>
        <if test="SearchPojo.functionid != null ">
            and PiRole.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null ">
            and PiRole.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null ">
            and PiRole.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and PiRole.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and PiRole.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and PiRole.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and PiRole.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and PiRole.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and PiRole.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.rolecode != null ">
                or PiRole.RoleCode like concat('%', #{SearchPojo.rolecode}, '%')
            </if>
            <if test="SearchPojo.rolename != null ">
                or PiRole.RoleName like concat('%', #{SearchPojo.rolename}, '%')
            </if>
            <if test="SearchPojo.functionid != null ">
                or PiRole.Functionid like concat('%', #{SearchPojo.functionid}, '%')
            </if>
            <if test="SearchPojo.functioncode != null ">
                or PiRole.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
            </if>
            <if test="SearchPojo.functionname != null ">
                or PiRole.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or PiRole.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or PiRole.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or PiRole.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or PiRole.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or PiRole.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or PiRole.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiRole(Roleid, RoleCode, RoleName, Functionid, FunctionCode, FunctionName, EnabledMark, RowNum,
                           Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName,
                           Revision)
        values (#{roleid}, #{rolecode}, #{rolename}, #{functionid}, #{functioncode}, #{functionname}, #{enabledmark},
                #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate},
                #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiRole
        <set>
            <if test="rolecode != null ">
                RoleCode =#{rolecode},
            </if>
            <if test="rolename != null ">
                RoleName =#{rolename},
            </if>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where Roleid = #{roleid} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiRole
        where Roleid = #{key}
          and Tenantid = #{tid}
    </delete>
</mapper>

