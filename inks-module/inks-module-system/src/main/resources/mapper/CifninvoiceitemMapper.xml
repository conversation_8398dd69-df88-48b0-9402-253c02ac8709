<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CifninvoiceitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CifninvoiceitemPojo">
        select id,
               Pid,
               OrderUid,
               OrderType,
               OrderTitle,
               OrderDate,
               Functionid,
               FunctionCode,
               FunctionName,
               CycleCode,
               Container,
               Quantity,
               TaxPrice,
               TaxAmount,
               RowNum,
               Remark,
               Revision,
               Tenantid,
               OrderItemid
        from CiFnInvoiceItem
        where CiFnInvoiceItem.id = #{key}
          and CiFnInvoiceItem.Tenantid = #{tid}
    </select>
    <sql id="selectCifninvoiceitemVo">
        select id,
               Pid,
               OrderUid,
               OrderType,
               OrderTitle,
               OrderDate,
               Functionid,
               FunctionCode,
               FunctionName,
               CycleCode,
               Container,
               Quantity,
               TaxPrice,
               TaxAmount,
               RowNum,
               Remark,
               Revision,
               Tenantid,
               OrderItemid
        from CiFnInvoiceItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CifninvoiceitemPojo">
        <include refid="selectCifninvoiceitemVo"/>
        where 1 = 1 and CiFnInvoiceItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiFnInvoiceItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by #{orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and CiFnInvoiceItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.orderuid != null and SearchPojo.orderuid != ''">
            and CiFnInvoiceItem.orderuid like concat('%', #{SearchPojo.orderuid}, '%')
        </if>
        <if test="SearchPojo.ordertype != null and SearchPojo.ordertype != ''">
            and CiFnInvoiceItem.ordertype like concat('%', #{SearchPojo.ordertype}, '%')
        </if>
        <if test="SearchPojo.ordertitle != null and SearchPojo.ordertitle != ''">
            and CiFnInvoiceItem.ordertitle like concat('%', #{SearchPojo.ordertitle}, '%')
        </if>
        <if test="SearchPojo.functionid != null and SearchPojo.functionid != ''">
            and CiFnInvoiceItem.functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null and SearchPojo.functioncode != ''">
            and CiFnInvoiceItem.functioncode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null and SearchPojo.functionname != ''">
            and CiFnInvoiceItem.functionname like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.cyclecode != null and SearchPojo.cyclecode != ''">
            and CiFnInvoiceItem.cyclecode like concat('%', #{SearchPojo.cyclecode}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and CiFnInvoiceItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.orderitemid != null and SearchPojo.orderitemid != ''">
            and CiFnInvoiceItem.orderitemid like concat('%', #{SearchPojo.orderitemid}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            or CiFnInvoiceItem.Pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.orderuid != null and SearchPojo.orderuid != ''">
            or CiFnInvoiceItem.OrderUid like concat('%', #{SearchPojo.orderuid}, '%')
        </if>
        <if test="SearchPojo.ordertype != null and SearchPojo.ordertype != ''">
            or CiFnInvoiceItem.OrderType like concat('%', #{SearchPojo.ordertype}, '%')
        </if>
        <if test="SearchPojo.ordertitle != null and SearchPojo.ordertitle != ''">
            or CiFnInvoiceItem.OrderTitle like concat('%', #{SearchPojo.ordertitle}, '%')
        </if>
        <if test="SearchPojo.functionid != null and SearchPojo.functionid != ''">
            or CiFnInvoiceItem.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null and SearchPojo.functioncode != ''">
            or CiFnInvoiceItem.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null and SearchPojo.functionname != ''">
            or CiFnInvoiceItem.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.cyclecode != null and SearchPojo.cyclecode != ''">
            or CiFnInvoiceItem.CycleCode like concat('%', #{SearchPojo.cyclecode}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or CiFnInvoiceItem.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.orderitemid != null and SearchPojo.orderitemid != ''">
            or CiFnInvoiceItem.OrderItemid like concat('%', #{SearchPojo.orderitemid}, '%')
        </if>
        )
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.system.domain.pojo.CifninvoiceitemPojo">
        select id,
               Pid,
               OrderUid,
               OrderType,
               OrderTitle,
               OrderDate,
               Functionid,
               FunctionCode,
               FunctionName,
               CycleCode,
               Container,
               Quantity,
               TaxPrice,
               TaxAmount,
               RowNum,
               Remark,
               Revision,
               Tenantid,
               OrderItemid
        from CiFnInvoiceItem
        where CiFnInvoiceItem.Pid = #{Pid}
          and CiFnInvoiceItem.Tenantid = #{tid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into CiFnInvoiceItem(id, Pid, OrderUid, OrderType, OrderTitle, OrderDate, Functionid, FunctionCode,
                                    FunctionName, CycleCode, Container, Quantity, TaxPrice, TaxAmount, RowNum, Remark,
                                    Revision, Tenantid, OrderItemid)
        values (#{id}, #{pid}, #{orderuid}, #{ordertype}, #{ordertitle}, #{orderdate}, #{functionid}, #{functioncode},
                #{functionname}, #{cyclecode}, #{container}, #{quantity}, #{taxprice}, #{taxamount}, #{rownum},
                #{remark}, #{revision}, #{tenantid}, #{orderitemid})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiFnInvoiceItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="orderuid != null ">
                OrderUid = #{orderuid},
            </if>
            <if test="ordertype != null ">
                OrderType = #{ordertype},
            </if>
            <if test="ordertitle != null ">
                OrderTitle = #{ordertitle},
            </if>
            <if test="orderdate != null">
                OrderDate = #{orderdate},
            </if>
            <if test="functionid != null ">
                Functionid = #{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode = #{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName = #{functionname},
            </if>
            <if test="cyclecode != null ">
                CycleCode = #{cyclecode},
            </if>
            <if test="container != null">
                Container = #{container},
            </if>
            <if test="quantity != null">
                Quantity = #{quantity},
            </if>
            <if test="taxprice != null">
                TaxPrice = #{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount = #{taxamount},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            Revision=Revision+1
            <if test="orderitemid != null ">
                OrderItemid = #{orderitemid},
            </if>
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiFnInvoiceItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

