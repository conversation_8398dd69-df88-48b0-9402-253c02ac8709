<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PiscmfunctMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PiscmfunctPojo">
        select
          ScmFunctid, ScmFunctCode, ScmFunctName, Description, Functionid, FunctionCode, FunctionName, EnabledMark, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision        from PiScmFunct
        where PiScmFunct.ScmFunctid = #{key} 
    </select>
    <sql id="selectPiscmfunctVo">
         select
          ScmFunctid, ScmFunctCode, ScmFunctName, Description, Functionid, FunctionCode, FunctionName, EnabledMark, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision        from PiScmFunct
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.PiscmfunctPojo">
        <include refid="selectPiscmfunctVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and PiScmFunct.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.scmfunctcode != null ">
   and PiScmFunct.ScmFunctCode like concat('%', #{SearchPojo.scmfunctcode}, '%')
</if>
<if test="SearchPojo.scmfunctname != null ">
   and PiScmFunct.ScmFunctName like concat('%', #{SearchPojo.scmfunctname}, '%')
</if>
<if test="SearchPojo.description != null ">
   and PiScmFunct.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.functionid != null ">
   and PiScmFunct.Functionid like concat('%', #{SearchPojo.functionid}, '%')
</if>
<if test="SearchPojo.functioncode != null ">
   and PiScmFunct.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
</if>
<if test="SearchPojo.functionname != null ">
   and PiScmFunct.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and PiScmFunct.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and PiScmFunct.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and PiScmFunct.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and PiScmFunct.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and PiScmFunct.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.scmfunctcode != null ">
   or PiScmFunct.ScmFunctCode like concat('%', #{SearchPojo.scmfunctcode}, '%')
</if>
<if test="SearchPojo.scmfunctname != null ">
   or PiScmFunct.ScmFunctName like concat('%', #{SearchPojo.scmfunctname}, '%')
</if>
<if test="SearchPojo.description != null ">
   or PiScmFunct.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.functionid != null ">
   or PiScmFunct.Functionid like concat('%', #{SearchPojo.functionid}, '%')
</if>
<if test="SearchPojo.functioncode != null ">
   or PiScmFunct.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
</if>
<if test="SearchPojo.functionname != null ">
   or PiScmFunct.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or PiScmFunct.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or PiScmFunct.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or PiScmFunct.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or PiScmFunct.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or PiScmFunct.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into PiScmFunct(ScmFunctid, ScmFunctCode, ScmFunctName, Description, Functionid, FunctionCode, FunctionName, EnabledMark, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision)
        values (#{scmfunctid}, #{scmfunctcode}, #{scmfunctname}, #{description}, #{functionid}, #{functioncode}, #{functionname}, #{enabledmark}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update PiScmFunct
        <set>
            <if test="scmfunctcode != null ">
                ScmFunctCode =#{scmfunctcode},
            </if>
            <if test="scmfunctname != null ">
                ScmFunctName =#{scmfunctname},
            </if>
            <if test="description != null ">
                Description =#{description},
            </if>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
                Revision=Revision+1
        </set>
        where ScmFunctid = #{scmfunctid} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from PiScmFunct where ScmFunctid = #{key} 
    </delete>
                                                                    </mapper>

