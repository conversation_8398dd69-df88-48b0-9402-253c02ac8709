<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CitextgeneratorMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CitextgeneratorPojo">
        select id,
               Parentid,
               TgCode,
               TgBillCode,
               TgValue,
               TgLevel,
               Essential,
               CssClass,
               RowNum,
               DefaultMark,
               EnabledMark,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               Revision
        from CiTextGenerator
        where CiTextGenerator.id = #{key}
          and CiTextGenerator.Tenantid = #{tid}
    </select>
    <sql id="selectCitextgeneratorVo">
        select id,
               Parentid,
               TgCode,
               TgBillCode,
               TgValue,
               TgLevel,
               Essential,
               CssClass,
               RowNum,
               DefaultMark,
               EnabledMark,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               Revision
        from CiTextGenerator
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CitextgeneratorPojo">
        <include refid="selectCitextgeneratorVo"/>
        where 1 = 1
          and CiTextGenerator.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and CiTextGenerator.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.parentid != null">
            and CiTextGenerator.Parentid like concat('%', #{SearchPojo.parentid}, '%')
        </if>
        <if test="SearchPojo.tgcode != null">
            and CiTextGenerator.TgCode like concat('%',
                #{SearchPojo.tgcode}, '%')
        </if>
        <if test="SearchPojo.tgvalue != null">
            and CiTextGenerator.TgValue like concat('%',
                #{SearchPojo.tgvalue}, '%')
        </if>
        <if test="SearchPojo.cssclass != null">
            and CiTextGenerator.CssClass like concat('%',
                #{SearchPojo.cssclass}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and CiTextGenerator.Remark like concat('%',
                #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and CiTextGenerator.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and CiTextGenerator.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and CiTextGenerator.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and CiTextGenerator.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and CiTextGenerator.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and CiTextGenerator.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and CiTextGenerator.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and CiTextGenerator.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and CiTextGenerator.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.parentid != null">
                or CiTextGenerator.Parentid like concat('%', #{SearchPojo.parentid}, '%')
            </if>
            <if test="SearchPojo.tgcode != null">
                or CiTextGenerator.TgCode like concat('%', #{SearchPojo.tgcode}, '%')
            </if>
            <if test="SearchPojo.tgvalue != null">
                or CiTextGenerator.TgValue like concat('%', #{SearchPojo.tgvalue}, '%')
            </if>
            <if test="SearchPojo.cssclass != null">
                or CiTextGenerator.CssClass like concat('%', #{SearchPojo.cssclass}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or CiTextGenerator.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or CiTextGenerator.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or CiTextGenerator.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or CiTextGenerator.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or CiTextGenerator.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or CiTextGenerator.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or CiTextGenerator.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or CiTextGenerator.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or CiTextGenerator.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or CiTextGenerator.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into CiTextGenerator(id, Parentid, TgCode,TgBillCode, TgValue, TgLevel, Essential, CssClass, RowNum, DefaultMark,
                                    EnabledMark, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate,
                                    Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{parentid}, #{tgcode},#{tgbillcode}, #{tgvalue}, #{tglevel}, #{essential}, #{cssclass}, #{rownum},
                #{defaultmark}, #{enabledmark}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister},
                #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid},
                #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiTextGenerator
        <set>
            <if test="parentid != null">
                Parentid =#{parentid},
            </if>
            <if test="tgcode != null">
                TgCode =#{tgcode},
            </if>
            <if test="tgbillcode != null">
                TgBillCode =#{tgbillcode},
            </if>
            <if test="tgvalue != null">
                TgValue =#{tgvalue},
            </if>
            <if test="tglevel != null">
                TgLevel =#{tglevel},
            </if>
            <if test="essential != null">
                Essential =#{essential},
            </if>
            <if test="cssclass != null">
                CssClass =#{cssclass},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="defaultmark != null">
                DefaultMark =#{defaultmark},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
          and Tenantid = #{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiTextGenerator
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <update id="orderByRowNum">
        UPDATE CiTextGenerator
        SET RowNum =
        CASE
        <foreach collection="ids" item="id" index="index" separator=" ">
            WHEN id = #{id} THEN #{index}
        </foreach>
        ELSE RowNum
            END
        WHERE id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND Tenantid = #{tid}
    </update>

    <select id="getListByid" resultType="inks.system.domain.pojo.CitextgeneratorPojo">
        <include refid="selectCitextgeneratorVo"/>
        where CiTextGenerator.Parentid = (select Parentid from CiTextGenerator where id = #{id})
          and CiTextGenerator.Tenantid = #{tid}
        order by RowNum
    </select>

    <select id="getListByParentid" resultType="inks.system.domain.pojo.CitextgeneratorPojo">
        <include refid="selectCitextgeneratorVo"/>
        where CiTextGenerator.Parentid = #{parentId}
          and CiTextGenerator.Tenantid = #{tid}
        order by RowNum
    </select>
</mapper>

