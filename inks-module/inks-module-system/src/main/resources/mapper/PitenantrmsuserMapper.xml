<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PitenantrmsuserMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PitenantrmsuserPojo">
        select PiTenantRmsUser.id,
               PiTenantRmsUser.Tenantid,
               PiTenantRmsUser.TenantName,
               PiTenantRmsUser.Userid,
               PiTenantRmsUser.UserName,
               PiTenantRmsUser.RealName,
               PiTenantRmsUser.UserType,
               PiTenantRmsUser.IsAdmin,
               PiTenantRmsUser.Deptid,
               PiTenantRmsUser.DeptCode,
               PiTenantRmsUser.DeptName,
               PiTenantRmsUser.IsDeptAdmin,
               PiTenantRmsUser.DeptRowNum,
               PiTenantRmsUser.RowNum,
               PiTenantRmsUser.UserStatus,
               PiTenantRmsUser.UserCode,
               PiTenantRmsUser.Groupids,
               PiTenantRmsUser.GroupNames,
               PiTenantRmsUser.RmsFunctids,
               PiTenantRmsUser.RmsFunctNames,
               PiTenantRmsUser.CreateBy,
               PiTenantRmsUser.CreateByid,
               PiTenantRmsUser.CreateDate,
               PiTenantRmsUser.Lister,
               PiTenantRmsUser.Listerid,
               PiTenantRmsUser.ModifyDate,
               PiTenantRmsUser.Custom1,
               PiTenantRmsUser.Custom2,
               PiTenantRmsUser.Custom3,
               PiTenantRmsUser.Custom4,
               PiTenantRmsUser.Custom5,
               PiTenantRmsUser.Revision,
               PiRmsUser.NickName,
               PiRmsUser.Mobile,
               PiRmsUser.Email,
               PiRmsUser.Sex,
               PiRmsUser.Avatar
        from PiTenantRmsUser
        LEFT JOIN PiRmsUser ON PiTenantRmsUser.Userid = PiRmsUser.Userid
        where PiTenantRmsUser.id = #{key}
    </select>
    <sql id="selectPitenantrmsuserVo">
        select PiTenantRmsUser.id,
               PiTenantRmsUser.Tenantid,
               PiTenantRmsUser.TenantName,
               PiTenantRmsUser.Userid,
               PiTenantRmsUser.UserName,
               PiTenantRmsUser.RealName,
               PiTenantRmsUser.UserType,
               PiTenantRmsUser.IsAdmin,
               PiTenantRmsUser.Deptid,
               PiTenantRmsUser.DeptCode,
               PiTenantRmsUser.DeptName,
               PiTenantRmsUser.IsDeptAdmin,
               PiTenantRmsUser.DeptRowNum,
               PiTenantRmsUser.RowNum,
               PiTenantRmsUser.UserStatus,
               PiTenantRmsUser.UserCode,
               PiTenantRmsUser.Groupids,
               PiTenantRmsUser.GroupNames,
               PiTenantRmsUser.RmsFunctids,
               PiTenantRmsUser.RmsFunctNames,
               PiTenantRmsUser.CreateBy,
               PiTenantRmsUser.CreateByid,
               PiTenantRmsUser.CreateDate,
               PiTenantRmsUser.Lister,
               PiTenantRmsUser.Listerid,
               PiTenantRmsUser.ModifyDate,
               PiTenantRmsUser.Custom1,
               PiTenantRmsUser.Custom2,
               PiTenantRmsUser.Custom3,
               PiTenantRmsUser.Custom4,
               PiTenantRmsUser.Custom5,
               PiTenantRmsUser.Revision,
               PiRmsUser.NickName,
               PiRmsUser.Mobile,
               PiRmsUser.Email,
               PiRmsUser.Sex,
               PiRmsUser.Avatar
        from PiTenantRmsUser
                 LEFT JOIN PiRmsUser ON PiTenantRmsUser.Userid = PiRmsUser.Userid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PitenantrmsuserPojo">
        <include refid="selectPitenantrmsuserVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiTenantRmsUser.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.tenantname != null ">
            and PiTenantRmsUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.userid != null ">
            and PiTenantRmsUser.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.username != null ">
            and PiTenantRmsUser.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null ">
            and PiTenantRmsUser.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and PiTenantRmsUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.deptcode != null ">
            and PiTenantRmsUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
        </if>
        <if test="SearchPojo.deptname != null ">
            and PiTenantRmsUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
        </if>
        <if test="SearchPojo.usercode != null ">
            and PiTenantRmsUser.UserCode like concat('%', #{SearchPojo.usercode}, '%')
        </if>
        <if test="SearchPojo.groupids != null ">
            and PiTenantRmsUser.Groupids like concat('%', #{SearchPojo.groupids}, '%')
        </if>
        <if test="SearchPojo.groupnames != null ">
            and PiTenantRmsUser.GroupNames like concat('%', #{SearchPojo.groupnames}, '%')
        </if>
        <if test="SearchPojo.rmsfunctids != null ">
            and PiTenantRmsUser.RmsFunctids like concat('%', #{SearchPojo.rmsfunctids}, '%')
        </if>
        <if test="SearchPojo.rmsfunctnames != null ">
            and PiTenantRmsUser.RmsFunctNames like concat('%', #{SearchPojo.rmsfunctnames}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and PiTenantRmsUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and PiTenantRmsUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and PiTenantRmsUser.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and PiTenantRmsUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and PiTenantRmsUser.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and PiTenantRmsUser.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and PiTenantRmsUser.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and PiTenantRmsUser.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and PiTenantRmsUser.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.tenantname != null ">
                or PiTenantRmsUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <if test="SearchPojo.userid != null ">
                or PiTenantRmsUser.Userid like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.username != null ">
                or PiTenantRmsUser.UserName like concat('%', #{SearchPojo.username}, '%')
            </if>
            <if test="SearchPojo.realname != null ">
                or PiTenantRmsUser.RealName like concat('%', #{SearchPojo.realname}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or PiTenantRmsUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.deptcode != null ">
                or PiTenantRmsUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
            </if>
            <if test="SearchPojo.deptname != null ">
                or PiTenantRmsUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
            </if>
            <if test="SearchPojo.usercode != null ">
                or PiTenantRmsUser.UserCode like concat('%', #{SearchPojo.usercode}, '%')
            </if>
            <if test="SearchPojo.groupids != null ">
                or PiTenantRmsUser.Groupids like concat('%', #{SearchPojo.groupids}, '%')
            </if>
            <if test="SearchPojo.groupnames != null ">
                or PiTenantRmsUser.GroupNames like concat('%', #{SearchPojo.groupnames}, '%')
            </if>
            <if test="SearchPojo.rmsfunctids != null ">
                or PiTenantRmsUser.RmsFunctids like concat('%', #{SearchPojo.rmsfunctids}, '%')
            </if>
            <if test="SearchPojo.rmsfunctnames != null ">
                or PiTenantRmsUser.RmsFunctNames like concat('%', #{SearchPojo.rmsfunctnames}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or PiTenantRmsUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or PiTenantRmsUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or PiTenantRmsUser.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or PiTenantRmsUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or PiTenantRmsUser.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or PiTenantRmsUser.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or PiTenantRmsUser.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or PiTenantRmsUser.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or PiTenantRmsUser.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiTenantRmsUser(id, Tenantid, TenantName, Userid, UserName, RealName, UserType, IsAdmin, Deptid,
                                    DeptCode, DeptName, IsDeptAdmin, DeptRowNum, RowNum, UserStatus, UserCode, Groupids,
                                    GroupNames, RmsFunctids, RmsFunctNames, CreateBy, CreateByid, CreateDate, Lister,
                                    Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Revision)
        values (#{id}, #{tenantid}, #{tenantname}, #{userid}, #{username}, #{realname}, #{usertype}, #{isadmin},
                #{deptid}, #{deptcode}, #{deptname}, #{isdeptadmin}, #{deptrownum}, #{rownum}, #{userstatus},
                #{usercode}, #{groupids}, #{groupnames}, #{rmsfunctids}, #{rmsfunctnames}, #{createby}, #{createbyid},
                #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
                #{custom5}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiTenantRmsUser
        <set>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="usertype != null">
                UserType =#{usertype},
            </if>
            <if test="isadmin != null">
                IsAdmin =#{isadmin},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="deptcode != null ">
                DeptCode =#{deptcode},
            </if>
            <if test="deptname != null ">
                DeptName =#{deptname},
            </if>
            <if test="isdeptadmin != null">
                IsDeptAdmin =#{isdeptadmin},
            </if>
            <if test="deptrownum != null">
                DeptRowNum =#{deptrownum},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="userstatus != null">
                UserStatus =#{userstatus},
            </if>
            <if test="usercode != null ">
                UserCode =#{usercode},
            </if>
            <if test="groupids != null ">
                Groupids =#{groupids},
            </if>
            <if test="groupnames != null ">
                GroupNames =#{groupnames},
            </if>
            <if test="rmsfunctids != null ">
                RmsFunctids =#{rmsfunctids},
            </if>
            <if test="rmsfunctnames != null ">
                RmsFunctNames =#{rmsfunctnames},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiTenantRmsUser
        where id = #{key}
    </delete>
    <!--查询指定行数据-->
    <select id="getListByUser"
            resultType="inks.system.domain.pojo.PitenantrmsuserPojo">
        <include refid="selectPitenantrmsuserVo"/>
        where PiTenantRmsUser.Userid=#{userid} Order by id
    </select>
    <select id="getEntityByUserid" resultType="inks.system.domain.pojo.PitenantrmsuserPojo">
        <include refid="selectPitenantrmsuserVo"/>
        where PiTenantRmsUser.Userid=#{key} and PiTenantRmsUser.Tenantid=#{tid}
    </select>

</mapper>

