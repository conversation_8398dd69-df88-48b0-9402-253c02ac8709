<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CiinitlogMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CiinitlogPojo">
        select id,
               ModuleCode,
               InitTitle,
               InitStatus,
               RecCount,
               ErrorMsg,
               Remark,
               Tenantid,
               TenantName,
               CreateBy,
               CreateByid,
               CreateDate
        from CiInitLog
        where CiInitLog.id = #{key}
          and CiInitLog.Tenantid = #{tid}
    </select>
    <sql id="selectCiinitlogVo">
        select id,
               ModuleCode,
               InitTitle,
               InitStatus,
               RecCount,
               ErrorMsg,
               Remark,
               Tenantid,
               TenantName,
               Create<PERSON><PERSON>,
               <PERSON>reate<PERSON>yid,
               CreateDate
        from CiInitLog
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CiinitlogPojo">
        <include refid="selectCiinitlogVo"/>
        where 1 = 1 and CiInitLog.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiInitLog.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.modulecode != null ">
            and CiInitLog.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.inittitle != null ">
            and CiInitLog.InitTitle like concat('%', #{SearchPojo.inittitle}, '%')
        </if>
        <if test="SearchPojo.errormsg != null ">
            and CiInitLog.ErrorMsg like concat('%', #{SearchPojo.errormsg}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and CiInitLog.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and CiInitLog.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and CiInitLog.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and CiInitLog.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.modulecode != null ">
                or CiInitLog.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.inittitle != null ">
                or CiInitLog.InitTitle like concat('%', #{SearchPojo.inittitle}, '%')
            </if>
            <if test="SearchPojo.errormsg != null ">
                or CiInitLog.ErrorMsg like concat('%', #{SearchPojo.errormsg}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or CiInitLog.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or CiInitLog.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or CiInitLog.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or CiInitLog.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into CiInitLog(id, ModuleCode, InitTitle, InitStatus, RecCount, ErrorMsg, Remark, Tenantid, TenantName,
                              CreateBy, CreateByid, CreateDate)
        values (#{id}, #{modulecode}, #{inittitle}, #{initstatus}, #{reccount}, #{errormsg}, #{remark}, #{tenantid},
                #{tenantname}, #{createby}, #{createbyid}, #{createdate})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiInitLog
        <set>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="inittitle != null ">
                InitTitle =#{inittitle},
            </if>
            <if test="initstatus != null">
                InitStatus =#{initstatus},
            </if>
            <if test="reccount != null">
                RecCount =#{reccount},
            </if>
            <if test="errormsg != null ">
                ErrorMsg =#{errormsg},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiInitLog
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
</mapper>

