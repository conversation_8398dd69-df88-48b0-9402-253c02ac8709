<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PimenuopsMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PimenuopsPojo">
        <include refid="selectPimenuopsVo"/>
        where PiMenuOps.Navid = #{key}
    </select>
    <sql id="selectPimenuopsVo">
         select
Navid, NavPid, NavType, NavCode, NavName, NavGroup, RowNum, AssemblyName, FormName, ImageCss, ModuleType, ModuleCode, RoleCode, ImageIndex, ImageStyle, EnabledMark, IconUrl, NavigateUrl, MvcUrl, Remark, PermissionCode, CreateBy, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>er, <PERSON><PERSON><PERSON>, ModifyDate, Revision        from PiMenuOps
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.PimenuopsPojo">
        <include refid="selectPimenuopsVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and PiMenuOps.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.navid != null ">
   and PiMenuOps.Navid like concat('%', #{SearchPojo.navid}, '%')
</if>
<if test="SearchPojo.navpid != null ">
   and PiMenuOps.NavPid like concat('%', #{SearchPojo.navpid}, '%')
</if>
<if test="SearchPojo.navtype != null ">
   and PiMenuOps.NavType like concat('%', #{SearchPojo.navtype}, '%')
</if>
<if test="SearchPojo.navcode != null ">
   and PiMenuOps.NavCode like concat('%', #{SearchPojo.navcode}, '%')
</if>
<if test="SearchPojo.navname != null ">
   and PiMenuOps.NavName like concat('%', #{SearchPojo.navname}, '%')
</if>
<if test="SearchPojo.navgroup != null ">
   and PiMenuOps.NavGroup like concat('%', #{SearchPojo.navgroup}, '%')
</if>
<if test="SearchPojo.assemblyname != null ">
   and PiMenuOps.AssemblyName like concat('%', #{SearchPojo.assemblyname}, '%')
</if>
<if test="SearchPojo.formname != null ">
   and PiMenuOps.FormName like concat('%', #{SearchPojo.formname}, '%')
</if>
<if test="SearchPojo.imagecss != null ">
   and PiMenuOps.ImageCss like concat('%', #{SearchPojo.imagecss}, '%')
</if>
<if test="SearchPojo.moduletype != null ">
   and PiMenuOps.ModuleType like concat('%', #{SearchPojo.moduletype}, '%')
</if>
<if test="SearchPojo.modulecode != null ">
   and PiMenuOps.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.rolecode != null ">
   and PiMenuOps.RoleCode like concat('%', #{SearchPojo.rolecode}, '%')
</if>
<if test="SearchPojo.imageindex != null ">
   and PiMenuOps.ImageIndex like concat('%', #{SearchPojo.imageindex}, '%')
</if>
<if test="SearchPojo.imagestyle != null ">
   and PiMenuOps.ImageStyle like concat('%', #{SearchPojo.imagestyle}, '%')
</if>
<if test="SearchPojo.iconurl != null ">
   and PiMenuOps.IconUrl like concat('%', #{SearchPojo.iconurl}, '%')
</if>
<if test="SearchPojo.navigateurl != null ">
   and PiMenuOps.NavigateUrl like concat('%', #{SearchPojo.navigateurl}, '%')
</if>
<if test="SearchPojo.mvcurl != null ">
   and PiMenuOps.MvcUrl like concat('%', #{SearchPojo.mvcurl}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and PiMenuOps.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.permissioncode != null ">
   and PiMenuOps.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and PiMenuOps.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and PiMenuOps.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and PiMenuOps.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and PiMenuOps.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.navid != null ">
   or PiMenuOps.Navid like concat('%', #{SearchPojo.navid}, '%')
</if>
<if test="SearchPojo.navpid != null ">
   or PiMenuOps.NavPid like concat('%', #{SearchPojo.navpid}, '%')
</if>
<if test="SearchPojo.navtype != null ">
   or PiMenuOps.NavType like concat('%', #{SearchPojo.navtype}, '%')
</if>
<if test="SearchPojo.navcode != null ">
   or PiMenuOps.NavCode like concat('%', #{SearchPojo.navcode}, '%')
</if>
<if test="SearchPojo.navname != null ">
   or PiMenuOps.NavName like concat('%', #{SearchPojo.navname}, '%')
</if>
<if test="SearchPojo.navgroup != null ">
   or PiMenuOps.NavGroup like concat('%', #{SearchPojo.navgroup}, '%')
</if>
<if test="SearchPojo.assemblyname != null ">
   or PiMenuOps.AssemblyName like concat('%', #{SearchPojo.assemblyname}, '%')
</if>
<if test="SearchPojo.formname != null ">
   or PiMenuOps.FormName like concat('%', #{SearchPojo.formname}, '%')
</if>
<if test="SearchPojo.imagecss != null ">
   or PiMenuOps.ImageCss like concat('%', #{SearchPojo.imagecss}, '%')
</if>
<if test="SearchPojo.moduletype != null ">
   or PiMenuOps.ModuleType like concat('%', #{SearchPojo.moduletype}, '%')
</if>
<if test="SearchPojo.modulecode != null ">
   or PiMenuOps.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.rolecode != null ">
   or PiMenuOps.RoleCode like concat('%', #{SearchPojo.rolecode}, '%')
</if>
<if test="SearchPojo.imageindex != null ">
   or PiMenuOps.ImageIndex like concat('%', #{SearchPojo.imageindex}, '%')
</if>
<if test="SearchPojo.imagestyle != null ">
   or PiMenuOps.ImageStyle like concat('%', #{SearchPojo.imagestyle}, '%')
</if>
<if test="SearchPojo.iconurl != null ">
   or PiMenuOps.IconUrl like concat('%', #{SearchPojo.iconurl}, '%')
</if>
<if test="SearchPojo.navigateurl != null ">
   or PiMenuOps.NavigateUrl like concat('%', #{SearchPojo.navigateurl}, '%')
</if>
<if test="SearchPojo.mvcurl != null ">
   or PiMenuOps.MvcUrl like concat('%', #{SearchPojo.mvcurl}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or PiMenuOps.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.permissioncode != null ">
   or PiMenuOps.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or PiMenuOps.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or PiMenuOps.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or PiMenuOps.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or PiMenuOps.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into PiMenuOps(Navid, NavPid, NavType, NavCode, NavName, NavGroup, RowNum, AssemblyName, FormName, ImageCss, ModuleType, ModuleCode, RoleCode, ImageIndex, ImageStyle, EnabledMark, IconUrl, NavigateUrl, MvcUrl, Remark, PermissionCode, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision)
        values (#{navid}, #{navpid}, #{navtype}, #{navcode}, #{navname}, #{navgroup}, #{rownum}, #{assemblyname}, #{formname}, #{imagecss}, #{moduletype}, #{modulecode}, #{rolecode}, #{imageindex}, #{imagestyle}, #{enabledmark}, #{iconurl}, #{navigateurl}, #{mvcurl}, #{remark}, #{permissioncode}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update PiMenuOps
        <set>
            <if test="navid != null ">
                Navid =#{navid},
            </if>
            <if test="navpid != null ">
                NavPid =#{navpid},
            </if>
            <if test="navtype != null ">
                NavType =#{navtype},
            </if>
            <if test="navcode != null ">
                NavCode =#{navcode},
            </if>
            <if test="navname != null ">
                NavName =#{navname},
            </if>
            <if test="navgroup != null ">
                NavGroup =#{navgroup},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="assemblyname != null ">
                AssemblyName =#{assemblyname},
            </if>
            <if test="formname != null ">
                FormName =#{formname},
            </if>
            <if test="imagecss != null ">
                ImageCss =#{imagecss},
            </if>
            <if test="moduletype != null ">
                ModuleType =#{moduletype},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="rolecode != null ">
                RoleCode =#{rolecode},
            </if>
            <if test="imageindex != null ">
                ImageIndex =#{imageindex},
            </if>
            <if test="imagestyle != null ">
                ImageStyle =#{imagestyle},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="iconurl != null ">
                IconUrl =#{iconurl},
            </if>
            <if test="navigateurl != null ">
                NavigateUrl =#{navigateurl},
            </if>
            <if test="mvcurl != null ">
                MvcUrl =#{mvcurl},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="permissioncode != null ">
                PermissionCode =#{permissioncode},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
                Revision=Revision+1
        </set>
        where Navid = #{navid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from PiMenuOps where Navid = #{key}
    </delete>

    <select id="getListByPid" resultType="inks.system.domain.pojo.PimenuopsPojo">
        <include refid="selectPimenuopsVo"/>
        where PiMenuOps.NavPid = #{key}
        Order by RowNum
    </select>
</mapper>

