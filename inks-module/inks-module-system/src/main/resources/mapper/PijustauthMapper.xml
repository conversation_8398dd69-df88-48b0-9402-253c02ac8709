<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PijustauthMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PijustauthPojo">
        SELECT PiTenant.TenantName,
               PiJustAuth.id,
               PiJustAuth.Userid,
               PiJustAuth.UserName,
               PiJustAuth.RealName,
               PiJustAuth.NickName,
               PiJustAuth.AuthType,
               PiJustAuth.AuthUuid,
               PiJustAuth.Unionid,
               PiJustAuth.AuthAvatar,
               PiJustAuth.CreateBy,
               PiJustAuth.CreateByid,
               PiJustAuth.CreateDate,
               PiJustAuth.Lister,
               PiJustA<PERSON>.Listerid,
               PiJustAuth.ModifyDate,
               PiJustAuth.Custom1,
               PiJustAuth.Custom2,
               <PERSON><PERSON>ustA<PERSON>.Custom3,
               <PERSON>JustAuth.Custom4,
               PiJustAuth.Custom5,
               Pi<PERSON>ustAuth.Tenantid,
               PiJustAuth.Revision
        FROM PiTenant
                 RIGHT JOIN PiJustAuth ON PiJustAuth.Tenantid = PiTenant.Tenantid
        where PiJustAuth.id = #{key}
    </select>
    <sql id="selectPijustauthVo">
        SELECT PiTenant.TenantName,
               PiJustAuth.id,
               PiJustAuth.Userid,
               PiJustAuth.UserName,
               PiJustAuth.RealName,
               PiJustAuth.NickName,
               PiJustAuth.AuthType,
               PiJustAuth.AuthUuid,
               PiJustAuth.Unionid,
               PiJustAuth.AuthAvatar,
               PiJustAuth.CreateBy,
               PiJustAuth.CreateByid,
               PiJustAuth.CreateDate,
               PiJustAuth.Lister,
               PiJustAuth.Listerid,
               PiJustAuth.ModifyDate,
               PiJustAuth.Custom1,
               PiJustAuth.Custom2,
               PiJustAuth.Custom3,
               PiJustAuth.Custom4,
               PiJustAuth.Custom5,
               PiJustAuth.Tenantid,
               PiJustAuth.Revision
        FROM PiTenant
                 RIGHT JOIN PiJustAuth ON PiJustAuth.Tenantid = PiTenant.Tenantid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PijustauthPojo">
        <include refid="selectPijustauthVo"/>
        where 1 = 1
        <if test="tenantid != 'default' ">
            and PiJustAuth.Tenantid =#{tenantid}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiJustAuth.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.userid != null and SearchPojo.userid  != ''">
            and PiJustAuth.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.username != null and SearchPojo.username  != ''">
            and PiJustAuth.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null and SearchPojo.realname  != ''">
            and PiJustAuth.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.nickname != null and SearchPojo.nickname  != ''">
            and PiJustAuth.NickName like concat('%', #{SearchPojo.nickname}, '%')
        </if>
        <if test="SearchPojo.authtype != null and SearchPojo.authtype  != ''">
            and PiJustAuth.AuthType like concat('%', #{SearchPojo.authtype}, '%')
        </if>
        <if test="SearchPojo.authuuid != null and SearchPojo.authuuid  != ''">
            and PiJustAuth.AuthUuid like concat('%', #{SearchPojo.authuuid}, '%')
        </if>
        <if test="SearchPojo.unionid != null and SearchPojo.unionid != ''">
            and PiJustAuth.Unionid like concat('%', #{SearchPojo.authuuid}, '%')
        </if>
        <if test="SearchPojo.authavatar != null and SearchPojo.authavatar  != ''">
            and PiJustAuth.AuthAvatar like concat('%', #{SearchPojo.authavatar}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby  != ''">
            and PiJustAuth.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid  != ''">
            and PiJustAuth.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and PiJustAuth.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid  != ''">
            and PiJustAuth.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1  != ''">
            and PiJustAuth.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2  != ''">
            and PiJustAuth.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3  != ''">
            and PiJustAuth.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4  != ''">
            and PiJustAuth.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5  != ''">
            and PiJustAuth.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.userid != null and SearchPojo.userid != ''">
                or PiJustAuth.Userid like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.username != null and SearchPojo.username != ''">
                or PiJustAuth.UserName like concat('%', #{SearchPojo.username}, '%')
            </if>
            <if test="SearchPojo.realname != null and SearchPojo.realname != ''">
                or PiJustAuth.RealName like concat('%', #{SearchPojo.realname}, '%')
            </if>
            <if test="SearchPojo.nickname != null and SearchPojo.nickname != ''">
                or PiJustAuth.NickName like concat('%', #{SearchPojo.nickname}, '%')
            </if>
            <if test="SearchPojo.authtype != null and SearchPojo.authtype != ''">
                or PiJustAuth.AuthType like concat('%', #{SearchPojo.authtype}, '%')
            </if>
            <if test="SearchPojo.authuuid != null and SearchPojo.authuuid != ''">
                or PiJustAuth.AuthUuid like concat('%', #{SearchPojo.authuuid}, '%')
            </if>
            <if test="SearchPojo.unionid != null and SearchPojo.unionid != ''">
                or PiJustAuth.Unionid like concat('%', #{SearchPojo.authuuid}, '%')
            </if>
            <if test="SearchPojo.authavatar != null and SearchPojo.authavatar != ''">
                or PiJustAuth.AuthAvatar like concat('%', #{SearchPojo.authavatar}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or PiJustAuth.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or PiJustAuth.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or PiJustAuth.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or PiJustAuth.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or PiJustAuth.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or PiJustAuth.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or PiJustAuth.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or PiJustAuth.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or PiJustAuth.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiJustAuth(id, Userid, UserName, RealName, NickName, AuthType, AuthUuid,Unionid, AuthAvatar, CreateBy,
                               CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4,
                               Custom5, Tenantid,TenantName, Revision)
        values (#{id}, #{userid}, #{username}, #{realname}, #{nickname}, #{authtype}, #{authuuid},#{unionid}, #{authavatar},
                #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1},
                #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid},#{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiJustAuth
        <set>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="nickname != null ">
                NickName =#{nickname},
            </if>
            <if test="authtype != null ">
                AuthType =#{authtype},
            </if>
            <if test="authuuid != null ">
                AuthUuid =#{authuuid},
            </if>
            <if test="unionid != null ">
                Unionid =#{unionid},
            </if>
            <if test="authavatar != null ">
                AuthAvatar =#{authavatar},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantid != null ">
                Tenantid =#{tenantid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiJustAuth
        where id = #{key}
    </delete>

    <!--查询单个-->
    <select id="getJustauthByUuid" resultType="inks.system.domain.pojo.PijustauthPojo">
        select id,
               Userid,
               UserName,
               RealName,
               NickName,
               AuthType,
               AuthUuid,
               Unionid,
               AuthAvatar,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from PiJustAuth
        where PiJustAuth.AuthUuid = #{key}
          and PiJustAuth.Tenantid = #{tid}
          and PiJustAuth.AuthType = #{type}
    </select>


    <!--查询单个-->
    <select id="getJustauthByUserid" resultType="inks.system.domain.pojo.PijustauthPojo">
        select id,
               Userid,
               UserName,
               RealName,
               NickName,
               AuthType,
               AuthUuid,
               AuthAvatar,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               Revision
        from PiJustAuth
        where PiJustAuth.Userid = #{key}
          and PiJustAuth.Tenantid = #{tid}
          and PiJustAuth.AuthType = #{type}
    </select>

    <!--查询单个-->
    <select id="getAdminListByDeptid" resultType="inks.common.core.domain.JustauthPojo">
        SELECT PiJustAuth.id,
               PiJustAuth.Userid,
               PiJustAuth.UserName,
               PiJustAuth.RealName,
               PiJustAuth.NickName,
               PiJustAuth.AuthType,
               PiJustAuth.AuthUuid,
               PiJustAuth.Unionid,
               PiJustAuth.AuthAvatar,
               PiJustAuth.CreateBy,
               PiJustAuth.CreateByid,
               PiJustAuth.CreateDate,
               PiJustAuth.Lister,
               PiJustAuth.Listerid,
               PiJustAuth.ModifyDate,
               PiJustAuth.Custom1,
               PiJustAuth.Custom2,
               PiJustAuth.Custom3,
               PiJustAuth.Custom4,
               PiJustAuth.Custom5,
               PiJustAuth.Tenantid,
               PiJustAuth.TenantName,
               PiJustAuth.Revision,
               PiTenantUser.IsAdmin,
               PiTenantUser.Deptid
        FROM PiJustAuth
                 LEFT JOIN PiTenantUser ON PiJustAuth.Userid = PiTenantUser.Userid
        where PiTenantUser.IsAdmin = 1 and PiTenantUser.Deptid = #{key}
          and PiJustAuth.Tenantid = #{tid}
          and PiJustAuth.AuthType = #{type}
    </select>

    <!--查询单个-->
    <select id="getListByUnionid" resultType="inks.common.core.domain.JustauthPojo">
        SELECT PiJustAuth.id,
               PiJustAuth.Userid,
               PiJustAuth.UserName,
               PiJustAuth.RealName,
               PiJustAuth.NickName,
               PiJustAuth.AuthType,
               PiJustAuth.AuthUuid,
               PiJustAuth.Unionid,
               PiJustAuth.AuthAvatar,
               PiJustAuth.CreateBy,
               PiJustAuth.CreateByid,
               PiJustAuth.CreateDate,
               PiJustAuth.Lister,
               PiJustAuth.Listerid,
               PiJustAuth.ModifyDate,
               PiJustAuth.Custom1,
               PiJustAuth.Custom2,
               PiJustAuth.Custom3,
               PiJustAuth.Custom4,
               PiJustAuth.Custom5,
               PiJustAuth.Tenantid,
               PiJustAuth.TenantName,
               PiJustAuth.Revision
        FROM PiJustAuth
        where PiJustAuth.Unionid = #{key}
    </select>
</mapper>

