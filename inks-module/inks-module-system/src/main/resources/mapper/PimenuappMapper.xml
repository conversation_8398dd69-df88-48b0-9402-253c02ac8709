<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PimenuappMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PimenuappPojo">
        <include refid="selectPimenuappVo"/>
        where PiMenuApp.Navid = #{key}
    </select>
    <sql id="selectPimenuappVo">
        select Navid,
               NavPid,
               NavType,
               NavCode,
               NavName,
               NavGroup,
               RowNum,
               ImageCss,
               IconUrl,
               NavigateUrl,
               MvcUrl,
               ModuleType,
               ModuleCode,
               RoleCode,
               ImageIndex,
               ImageStyle,
               EnabledMark,
               Remark,
               PermissionCode,
               Functionid,
               FunctionCode,
               FunctionName,
               <PERSON>er,
               <PERSON>reateDate,
               <PERSON>dify<PERSON>ate,
               DeleteMark,
               DeleteLister,
               DeleteDate
        from PiMenuApp
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PimenuappPojo">
        <include refid="selectPimenuappVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiMenuApp.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.navpid != null and SearchPojo.navpid  != ''">
            and PiMenuApp.NavPid like concat('%', #{SearchPojo.navpid}, '%')
        </if>
        <if test="SearchPojo.navtype != null and SearchPojo.navtype  != ''">
            and PiMenuApp.NavType like concat('%', #{SearchPojo.navtype}, '%')
        </if>
        <if test="SearchPojo.navcode != null and SearchPojo.navcode  != ''">
            and PiMenuApp.NavCode like concat('%', #{SearchPojo.navcode}, '%')
        </if>
        <if test="SearchPojo.navname != null and SearchPojo.navname  != ''">
            and PiMenuApp.NavName like concat('%', #{SearchPojo.navname}, '%')
        </if>
        <if test="SearchPojo.navgroup != null and SearchPojo.navgroup  != ''">
            and PiMenuApp.NavGroup like concat('%', #{SearchPojo.navgroup}, '%')
        </if>
        <if test="SearchPojo.imagecss != null and SearchPojo.imagecss  != ''">
            and PiMenuApp.ImageCss like concat('%', #{SearchPojo.imagecss}, '%')
        </if>
        <if test="SearchPojo.iconurl != null and SearchPojo.iconurl  != ''">
            and PiMenuApp.IconUrl like concat('%', #{SearchPojo.iconurl}, '%')
        </if>
        <if test="SearchPojo.navigateurl != null and SearchPojo.navigateurl  != ''">
            and PiMenuApp.NavigateUrl like concat('%', #{SearchPojo.navigateurl}, '%')
        </if>
        <if test="SearchPojo.mvcurl != null and SearchPojo.mvcurl  != ''">
            and PiMenuApp.MvcUrl like concat('%', #{SearchPojo.mvcurl}, '%')
        </if>
        <if test="SearchPojo.moduletype != null and SearchPojo.moduletype  != ''">
            and PiMenuApp.ModuleType like concat('%', #{SearchPojo.moduletype}, '%')
        </if>
        <if test="SearchPojo.modulecode != null and SearchPojo.modulecode  != ''">
            and PiMenuApp.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.rolecode != null and SearchPojo.rolecode  != ''">
            and PiMenuApp.RoleCode like concat('%', #{SearchPojo.rolecode}, '%')
        </if>
        <if test="SearchPojo.imageindex != null and SearchPojo.imageindex  != ''">
            and PiMenuApp.ImageIndex like concat('%', #{SearchPojo.imageindex}, '%')
        </if>
        <if test="SearchPojo.imagestyle != null and SearchPojo.imagestyle  != ''">
            and PiMenuApp.ImageStyle like concat('%', #{SearchPojo.imagestyle}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark  != ''">
            and PiMenuApp.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.permissioncode != null and SearchPojo.permissioncode  != ''">
            and PiMenuApp.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
        </if>
        <if test="SearchPojo.functionid != null and SearchPojo.functionid  != ''">
            and PiMenuApp.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null and SearchPojo.functioncode  != ''">
            and PiMenuApp.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null and SearchPojo.functionname  != ''">
            and PiMenuApp.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and PiMenuApp.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.deletelister != null and SearchPojo.deletelister  != ''">
            and PiMenuApp.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.navpid != null and SearchPojo.navpid != ''">
            or PiMenuApp.NavPid like concat('%', #{SearchPojo.navpid}, '%')
        </if>
        <if test="SearchPojo.navtype != null and SearchPojo.navtype != ''">
            or PiMenuApp.NavType like concat('%', #{SearchPojo.navtype}, '%')
        </if>
        <if test="SearchPojo.navcode != null and SearchPojo.navcode != ''">
            or PiMenuApp.NavCode like concat('%', #{SearchPojo.navcode}, '%')
        </if>
        <if test="SearchPojo.navname != null and SearchPojo.navname != ''">
            or PiMenuApp.NavName like concat('%', #{SearchPojo.navname}, '%')
        </if>
        <if test="SearchPojo.navgroup != null and SearchPojo.navgroup != ''">
            or PiMenuApp.NavGroup like concat('%', #{SearchPojo.navgroup}, '%')
        </if>
        <if test="SearchPojo.imagecss != null and SearchPojo.imagecss != ''">
            or PiMenuApp.ImageCss like concat('%', #{SearchPojo.imagecss}, '%')
        </if>
        <if test="SearchPojo.iconurl != null and SearchPojo.iconurl != ''">
            or PiMenuApp.IconUrl like concat('%', #{SearchPojo.iconurl}, '%')
        </if>
        <if test="SearchPojo.navigateurl != null and SearchPojo.navigateurl != ''">
            or PiMenuApp.NavigateUrl like concat('%', #{SearchPojo.navigateurl}, '%')
        </if>
        <if test="SearchPojo.mvcurl != null and SearchPojo.mvcurl != ''">
            or PiMenuApp.MvcUrl like concat('%', #{SearchPojo.mvcurl}, '%')
        </if>
        <if test="SearchPojo.moduletype != null and SearchPojo.moduletype != ''">
            or PiMenuApp.ModuleType like concat('%', #{SearchPojo.moduletype}, '%')
        </if>
        <if test="SearchPojo.modulecode != null and SearchPojo.modulecode != ''">
            or PiMenuApp.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.rolecode != null and SearchPojo.rolecode != ''">
            or PiMenuApp.RoleCode like concat('%', #{SearchPojo.rolecode}, '%')
        </if>
        <if test="SearchPojo.imageindex != null and SearchPojo.imageindex != ''">
            or PiMenuApp.ImageIndex like concat('%', #{SearchPojo.imageindex}, '%')
        </if>
        <if test="SearchPojo.imagestyle != null and SearchPojo.imagestyle != ''">
            or PiMenuApp.ImageStyle like concat('%', #{SearchPojo.imagestyle}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or PiMenuApp.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.permissioncode != null and SearchPojo.permissioncode != ''">
            or PiMenuApp.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
        </if>
        <if test="SearchPojo.functionid != null and SearchPojo.functionid != ''">
            or PiMenuApp.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null and SearchPojo.functioncode != ''">
            or PiMenuApp.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null and SearchPojo.functionname != ''">
            or PiMenuApp.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or PiMenuApp.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.deletelister != null and SearchPojo.deletelister != ''">
            or PiMenuApp.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiMenuApp(Navid, NavPid, NavType, NavCode, NavName, NavGroup, RowNum, ImageCss, IconUrl,
                              NavigateUrl, MvcUrl, ModuleType, ModuleCode, RoleCode, ImageIndex, ImageStyle,
                              EnabledMark, Remark, PermissionCode, Functionid, FunctionCode, FunctionName, Lister,
                              CreateDate, ModifyDate, DeleteMark, DeleteLister, DeleteDate)
        values (#{navid}, #{navpid}, #{navtype}, #{navcode}, #{navname}, #{navgroup}, #{rownum}, #{imagecss},
                #{iconurl}, #{navigateurl}, #{mvcurl}, #{moduletype}, #{modulecode}, #{rolecode}, #{imageindex},
                #{imagestyle}, #{enabledmark}, #{remark}, #{permissioncode}, #{functionid}, #{functioncode},
                #{functionname}, #{lister}, #{createdate}, #{modifydate}, #{deletemark}, #{deletelister}, #{deletedate})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiMenuApp
        <set>
            <if test="navpid != null ">
                NavPid =#{navpid},
            </if>
            <if test="navtype != null ">
                NavType =#{navtype},
            </if>
            <if test="navcode != null ">
                NavCode =#{navcode},
            </if>
            <if test="navname != null ">
                NavName =#{navname},
            </if>
            <if test="navgroup != null ">
                NavGroup =#{navgroup},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="imagecss != null ">
                ImageCss =#{imagecss},
            </if>
            <if test="iconurl != null ">
                IconUrl =#{iconurl},
            </if>
            <if test="navigateurl != null ">
                NavigateUrl =#{navigateurl},
            </if>
            <if test="mvcurl != null ">
                MvcUrl =#{mvcurl},
            </if>
            <if test="moduletype != null ">
                ModuleType =#{moduletype},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="rolecode != null ">
                RoleCode =#{rolecode},
            </if>
            <if test="imageindex != null ">
                ImageIndex =#{imageindex},
            </if>
            <if test="imagestyle != null ">
                ImageStyle =#{imagestyle},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="permissioncode != null ">
                PermissionCode =#{permissioncode},
            </if>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
        </set>
        where Navid = #{navid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiMenuApp
        where Navid = #{key}
    </delete>


    <!--查询单个-->
    <select id="getListByPid" resultType="inks.system.domain.pojo.PimenuappPojo">
        <include refid="selectPimenuappVo"/>
        where PiMenuApp.NavPid = #{key}
        Order by RowNum
    </select>

    <select id="getListByNavids" resultType="inks.system.domain.pojo.PimenuappPojo">
        <include refid="selectPimenuappVo"/>
        where PiMenuApp.Navid in
        <foreach collection="navids" item="navid" open="(" separator="," close=")">
            #{navid}
        </foreach>
        Order by RowNum
    </select>
</mapper>

