<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PifunctionwebnavMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PifunctionwebnavPojo">
        select
id, Functionid, FunctionCode, FunctionName, Navid, NavCode, NavName, Remark, CreateBy, CreateDate, Lister, ModifyDate, Tenantid, Revision        from PiFunctionWebNav
        where PiFunctionWebNav.id = #{key} and PiFunctionWebNav.Tenantid=#{tid}
    </select>
    <sql id="selectPifunctionwebnavVo">
         select
id, Functionid, FunctionCode, FunctionName, <PERSON>vid, <PERSON>vC<PERSON>, NavName, Remark, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ate, <PERSON>er, ModifyDate, Tenantid, Revision        from PiFunctionWebNav
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.PifunctionwebnavPojo">
        <include refid="selectPifunctionwebnavVo"/>
         where 1 = 1 and PiFunctionWebNav.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and PiFunctionWebNav.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.functionid != null ">
   and PiFunctionWebNav.Functionid like concat('%', #{SearchPojo.functionid}, '%')
</if>
<if test="SearchPojo.functioncode != null ">
   and PiFunctionWebNav.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
</if>
<if test="SearchPojo.functionname != null ">
   and PiFunctionWebNav.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
</if>
<if test="SearchPojo.navid != null ">
   and PiFunctionWebNav.Navid like concat('%', #{SearchPojo.navid}, '%')
</if>
<if test="SearchPojo.navcode != null ">
   and PiFunctionWebNav.NavCode like concat('%', #{SearchPojo.navcode}, '%')
</if>
<if test="SearchPojo.navname != null ">
   and PiFunctionWebNav.NavName like concat('%', #{SearchPojo.navname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and PiFunctionWebNav.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and PiFunctionWebNav.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and PiFunctionWebNav.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.functionid != null ">
   or PiFunctionWebNav.Functionid like concat('%', #{SearchPojo.functionid}, '%')
</if>
<if test="SearchPojo.functioncode != null ">
   or PiFunctionWebNav.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
</if>
<if test="SearchPojo.functionname != null ">
   or PiFunctionWebNav.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
</if>
<if test="SearchPojo.navid != null ">
   or PiFunctionWebNav.Navid like concat('%', #{SearchPojo.navid}, '%')
</if>
<if test="SearchPojo.navcode != null ">
   or PiFunctionWebNav.NavCode like concat('%', #{SearchPojo.navcode}, '%')
</if>
<if test="SearchPojo.navname != null ">
   or PiFunctionWebNav.NavName like concat('%', #{SearchPojo.navname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or PiFunctionWebNav.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or PiFunctionWebNav.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or PiFunctionWebNav.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into PiFunctionWebNav(id, Functionid, FunctionCode, FunctionName, Navid, NavCode, NavName, Remark, CreateBy, CreateDate, Lister, ModifyDate, Tenantid, Revision)
        values (#{id}, #{functionid}, #{functioncode}, #{functionname}, #{navid}, #{navcode}, #{navname}, #{remark}, #{createby}, #{createdate}, #{lister}, #{modifydate}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update PiFunctionWebNav
        <set>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="navid != null ">
                Navid =#{navid},
            </if>
            <if test="navcode != null ">
                NavCode =#{navcode},
            </if>
            <if test="navname != null ">
                NavName =#{navname},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from PiFunctionWebNav where id = #{key} and Tenantid=#{tid}
    </delete>

    <select id="getListByFunction" resultType="inks.system.domain.pojo.PifunctionwebnavPojo">
        <include refid="selectPifunctionwebnavVo"/>
        where 1 = 1 and PiFunctionWebNav.Functionid =#{key}
        order by PiFunctionWebNav.NavCode
    </select>
</mapper>

