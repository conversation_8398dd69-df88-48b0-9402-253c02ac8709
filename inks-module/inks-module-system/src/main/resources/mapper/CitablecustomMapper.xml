<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CitablecustomMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CitablecustomPojo">
        select id,
               GenGroupid,
               ModuleCode,
               TableName,
               TableCode,
               ColumnCode,
               ItemCode,
               ItemName,
               ItemLabel,
               RowNum,
               EnabledMark,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from CiTableCustom
        where CiTableCustom.id = #{key}
          and CiTableCustom.Tenantid = #{tid}
    </select>
    <sql id="selectCitablecustomVo">
        select id,
               GenGroupid,
               ModuleCode,
               TableName,
               TableCode,
               ColumnCode,
               ItemCode,
               ItemName,
               ItemLabel,
               RowNum,
               EnabledMark,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from CiTableCustom
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CitablecustomPojo">
        <include refid="selectCitablecustomVo"/>
        where 1 = 1 and CiTableCustom.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiTableCustom.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.gengroupid != null ">
            and CiTableCustom.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
        </if>
        <if test="SearchPojo.modulecode != null ">
            and CiTableCustom.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.tablename != null ">
            and CiTableCustom.TableName like concat('%', #{SearchPojo.tablename}, '%')
        </if>
        <if test="SearchPojo.tablecode != null ">
            and CiTableCustom.TableCode like concat('%', #{SearchPojo.tablecode}, '%')
        </if>
        <if test="SearchPojo.columncode != null ">
            and CiTableCustom.ColumnCode like concat('%', #{SearchPojo.columncode}, '%')
        </if>
        <if test="SearchPojo.itemcode != null ">
            and CiTableCustom.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null ">
            and CiTableCustom.ItemName like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.itemlabel != null ">
            and CiTableCustom.ItemLabel like concat('%', #{SearchPojo.itemlabel}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and CiTableCustom.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and CiTableCustom.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and CiTableCustom.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and CiTableCustom.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and CiTableCustom.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and CiTableCustom.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and CiTableCustom.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and CiTableCustom.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and CiTableCustom.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and CiTableCustom.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and CiTableCustom.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.gengroupid != null ">
                or CiTableCustom.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
            </if>
            <if test="SearchPojo.modulecode != null ">
                or CiTableCustom.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.tablename != null ">
                or CiTableCustom.TableName like concat('%', #{SearchPojo.tablename}, '%')
            </if>
            <if test="SearchPojo.tablecode != null ">
                or CiTableCustom.TableCode like concat('%', #{SearchPojo.tablecode}, '%')
            </if>
            <if test="SearchPojo.columncode != null ">
                or CiTableCustom.ColumnCode like concat('%', #{SearchPojo.columncode}, '%')
            </if>
            <if test="SearchPojo.itemcode != null ">
                or CiTableCustom.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null ">
                or CiTableCustom.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.itemlabel != null ">
                or CiTableCustom.ItemLabel like concat('%', #{SearchPojo.itemlabel}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or CiTableCustom.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or CiTableCustom.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or CiTableCustom.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or CiTableCustom.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or CiTableCustom.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or CiTableCustom.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or CiTableCustom.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or CiTableCustom.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or CiTableCustom.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or CiTableCustom.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or CiTableCustom.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into CiTableCustom(id, GenGroupid, ModuleCode, TableName, TableCode, ColumnCode, ItemCode, ItemName,
                                  ItemLabel, RowNum, EnabledMark, Remark, CreateBy, CreateByid, CreateDate, Lister,
                                  Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid,
                                  TenantName, Revision)
        values (#{id}, #{gengroupid}, #{modulecode}, #{tablename}, #{tablecode}, #{columncode}, #{itemcode},
                #{itemname}, #{itemlabel}, #{rownum}, #{enabledmark}, #{remark}, #{createby}, #{createbyid},
                #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
                #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiTableCustom
        <set>
            <if test="gengroupid != null ">
                GenGroupid =#{gengroupid},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="tablename != null ">
                TableName =#{tablename},
            </if>
            <if test="tablecode != null ">
                TableCode =#{tablecode},
            </if>
            <if test="columncode != null ">
                ColumnCode =#{columncode},
            </if>
            <if test="itemcode != null ">
                ItemCode =#{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName =#{itemname},
            </if>
            <if test="itemlabel != null ">
                ItemLabel =#{itemlabel},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiTableCustom
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--查询指定行数据-->
    <select id="getListByCode" resultType="inks.system.domain.pojo.CitablecustomPojo">
        <include refid="selectCitablecustomVo"/>
        where CiTableCustom.Tenantid =#{tid} and CiTableCustom.ModuleCode =#{key}
        order by RowNum
    </select>

    <!--查询指定行数据-->
    <select id="getListByGroupid" resultType="inks.system.domain.pojo.CitablecustomPojo">
        <include refid="selectCitablecustomVo"/>
        where CiTableCustom.Tenantid =#{tid} and CiTableCustom.GenGroupid =#{key}
        order by RowNum
    </select>
</mapper>

