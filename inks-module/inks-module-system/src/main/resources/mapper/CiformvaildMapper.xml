<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CiformvaildMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CiformvaildPojo">
        <include refid="selectbillVo"/>
        where CiFormVaild.id = #{key} and CiFormVaild.Tenantid=#{tid}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select
id, FormCode, FormName, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision        from CiFormVaild
    </sql>
    <sql id="selectdetailVo">
        select
               CiFormVaild.CreateBy,
               CiFormVaild.Lister,
               CiFormVaildItem.*
        from CiFormVaildItem left join CiFormVaild on CiFormVaild.id = CiFormVaildItem.Pid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.CiformvailditemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1 and CiFormVaild.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiFormVaild.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.formcode != null ">
            and CiFormVaild.FormCode like concat('%', #{SearchPojo.formcode}, '%')
        </if>
        <if test="SearchPojo.formname != null ">
            and CiFormVaild.FormName like concat('%', #{SearchPojo.formname}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and CiFormVaild.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and CiFormVaild.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and CiFormVaild.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and CiFormVaild.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and CiFormVaild.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and CiFormVaild.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and CiFormVaild.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and CiFormVaild.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and CiFormVaild.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and CiFormVaild.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and CiFormVaild.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.formcode != null ">
                or CiFormVaild.FormCode like concat('%', #{SearchPojo.formcode}, '%')
            </if>
            <if test="SearchPojo.formname != null ">
                or CiFormVaild.FormName like concat('%', #{SearchPojo.formname}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or CiFormVaild.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or CiFormVaild.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or CiFormVaild.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or CiFormVaild.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or CiFormVaild.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or CiFormVaild.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or CiFormVaild.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or CiFormVaild.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or CiFormVaild.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or CiFormVaild.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or CiFormVaild.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.CiformvaildPojo">
        <include refid="selectbillVo"/>
        where 1 = 1 and CiFormVaild.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiFormVaild.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.formcode != null ">
            and CiFormVaild.FormCode like concat('%', #{SearchPojo.formcode}, '%')
        </if>
        <if test="SearchPojo.formname != null ">
            and CiFormVaild.FormName like concat('%', #{SearchPojo.formname}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and CiFormVaild.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and CiFormVaild.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and CiFormVaild.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and CiFormVaild.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and CiFormVaild.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and CiFormVaild.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and CiFormVaild.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and CiFormVaild.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and CiFormVaild.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and CiFormVaild.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and CiFormVaild.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.formcode != null ">
                or CiFormVaild.FormCode like concat('%', #{SearchPojo.formcode}, '%')
            </if>
            <if test="SearchPojo.formname != null ">
                or CiFormVaild.FormName like concat('%', #{SearchPojo.formname}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or CiFormVaild.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or CiFormVaild.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or CiFormVaild.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or CiFormVaild.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or CiFormVaild.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or CiFormVaild.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or CiFormVaild.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or CiFormVaild.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or CiFormVaild.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or CiFormVaild.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or CiFormVaild.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert" >
        insert into CiFormVaild(id, FormCode, FormName, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{formcode}, #{formname}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiFormVaild
        <set>
            <if test="formcode != null ">
            FormCode =#{formcode},
        </if>
            <if test="formname != null ">
            FormName =#{formname},
        </if>
            <if test="rownum != null">
            RowNum =#{rownum},
        </if>
            <if test="remark != null ">
            Remark =#{remark},
        </if>
            <if test="createby != null ">
            CreateBy =#{createby},
        </if>
            <if test="createbyid != null ">
            CreateByid =#{createbyid},
        </if>
            <if test="createdate != null">
            CreateDate =#{createdate},
        </if>
            <if test="lister != null ">
            Lister =#{lister},
        </if>
            <if test="listerid != null ">
            Listerid =#{listerid},
        </if>
            <if test="modifydate != null">
            ModifyDate =#{modifydate},
        </if>
            <if test="custom1 != null ">
            Custom1 =#{custom1},
        </if>
            <if test="custom2 != null ">
            Custom2 =#{custom2},
        </if>
            <if test="custom3 != null ">
            Custom3 =#{custom3},
        </if>
            <if test="custom4 != null ">
            Custom4 =#{custom4},
        </if>
            <if test="custom5 != null ">
            Custom5 =#{custom5},
        </if>
            <if test="tenantname != null ">
            TenantName =#{tenantname},
        </if>
        Revision=Revision+1
    </set>
    where id = #{id} and Tenantid =#{tenantid}
</update>

        <!--通过主键删除-->
<delete id="delete">
delete from CiFormVaild where id = #{key} and Tenantid=#{tid}
</delete>
        <!--查询DelListIds-->
<select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.system.domain.pojo.CiformvaildPojo">
select
id
from CiFormVaildItem
where Pid = #{id}
<if test="item !=null and item.size()>0">
    and id not in
    <foreach collection="item" open="(" close=")" separator="," item="item">
        <if test="item.id != null">
            #{item.id}
        </if>
        <if test="item.id == null">
            ''
        </if>
    </foreach>
</if>
</select>

    <select id="getEntityByFormCode" resultType="inks.system.domain.pojo.CiformvaildPojo">
        <include refid="selectbillVo"/>
        where CiFormVaild.FormCode = #{code} and CiFormVaild.Tenantid=#{tid}
    </select>
</mapper>

