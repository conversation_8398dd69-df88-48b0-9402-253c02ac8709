<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PipermcodeMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PipermcodePojo">
        select Permid,
               Parentid,
               PermType,
               PermCode,
               PermName,
               RowNum,
               IsPublic,
               EnabledMark,
               AllowDelete,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Revision
        from PiPermCode
        where PiPermCode.Permid = #{key}
    </select>
    <sql id="selectPipermcodeVo">
        select Permid,
               Parentid,
               PermType,
               PermCode,
               PermName,
               RowNum,
               IsPublic,
               EnabledMark,
               Allow<PERSON><PERSON><PERSON>,
               <PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON>er,
               <PERSON><PERSON><PERSON>,
               <PERSON>difyDate,
               Revision
        from PiPermCode
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PipermcodePojo">
        <include refid="selectPipermcodeVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiPermCode.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.parentid != null and SearchPojo.parentid  != ''">
            and PiPermCode.Parentid like concat('%', #{SearchPojo.parentid}, '%')
        </if>
        <if test="SearchPojo.permtype != null and SearchPojo.permtype  != ''">
            and PiPermCode.PermType like concat('%', #{SearchPojo.permtype}, '%')
        </if>
        <if test="SearchPojo.permcode != null and SearchPojo.permcode  != ''">
            and PiPermCode.PermCode like concat('%', #{SearchPojo.permcode}, '%')
        </if>
        <if test="SearchPojo.permname != null and SearchPojo.permname  != ''">
            and PiPermCode.PermName like concat('%', #{SearchPojo.permname}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark  != ''">
            and PiPermCode.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby  != ''">
            and PiPermCode.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid  != ''">
            and PiPermCode.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and PiPermCode.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid  != ''">
            and PiPermCode.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.parentid != null and SearchPojo.parentid != ''">
            or PiPermCode.Parentid like concat('%', #{SearchPojo.parentid}, '%')
        </if>
        <if test="SearchPojo.permtype != null and SearchPojo.permtype != ''">
            or PiPermCode.PermType like concat('%', #{SearchPojo.permtype}, '%')
        </if>
        <if test="SearchPojo.permcode != null and SearchPojo.permcode != ''">
            or PiPermCode.PermCode like concat('%', #{SearchPojo.permcode}, '%')
        </if>
        <if test="SearchPojo.permname != null and SearchPojo.permname != ''">
            or PiPermCode.PermName like concat('%', #{SearchPojo.permname}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or PiPermCode.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or PiPermCode.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            or PiPermCode.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or PiPermCode.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            or PiPermCode.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiPermCode(Permid, Parentid, PermType, PermCode, PermName, RowNum, IsPublic, EnabledMark,
                               AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate,
                               Revision)
        values (#{permid}, #{parentid}, #{permtype}, #{permcode}, #{permname}, #{rownum}, #{ispublic}, #{enabledmark},
                #{allowdelete}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid},
                #{modifydate}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiPermCode
        <set>
            <if test="parentid != null ">
                Parentid =#{parentid},
            </if>
            <if test="permtype != null ">
                PermType =#{permtype},
            </if>
            <if test="permcode != null ">
                PermCode =#{permcode},
            </if>
            <if test="permname != null ">
                PermName =#{permname},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="ispublic != null">
                IsPublic =#{ispublic},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="allowdelete != null">
                AllowDelete =#{allowdelete},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            Revision=Revision+1
        </set>
        where Permid = #{permid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiPermCode
        where Permid = #{key}
    </delete>

</mapper>

