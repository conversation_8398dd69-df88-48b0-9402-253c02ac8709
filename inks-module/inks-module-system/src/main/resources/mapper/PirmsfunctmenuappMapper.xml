<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PirmsfunctmenuappMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PirmsfunctmenuappPojo">
        select id,
               RmsFunctid,
               RmsFunctCode,
               RmsFunctName,
               Navid,
               NavCode,
               NavName,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Revision
        from PiRmsFunctMenuApp
        where PiRmsFunctMenuApp.id = #{key}
    </select>
    <sql id="selectPirmsfunctmenuappVo">
        select id,
               RmsFunctid,
               RmsFunctCode,
               RmsFunctName,
               <PERSON><PERSON>,
               <PERSON>v<PERSON><PERSON>,
               Nav<PERSON><PERSON>,
               <PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON>,
               <PERSON><PERSON><PERSON>,
               <PERSON>difyDate,
               Revision
        from PiRmsFunctMenuApp
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PirmsfunctmenuappPojo">
        <include refid="selectPirmsfunctmenuappVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiRmsFunctMenuApp.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.rmsfunctid != null ">
            and PiRmsFunctMenuApp.RmsFunctid like concat('%', #{SearchPojo.rmsfunctid}, '%')
        </if>
        <if test="SearchPojo.rmsfunctcode != null ">
            and PiRmsFunctMenuApp.RmsFunctCode like concat('%', #{SearchPojo.rmsfunctcode}, '%')
        </if>
        <if test="SearchPojo.rmsfunctname != null ">
            and PiRmsFunctMenuApp.RmsFunctName like concat('%', #{SearchPojo.rmsfunctname}, '%')
        </if>
        <if test="SearchPojo.navid != null ">
            and PiRmsFunctMenuApp.Navid like concat('%', #{SearchPojo.navid}, '%')
        </if>
        <if test="SearchPojo.navcode != null ">
            and PiRmsFunctMenuApp.NavCode like concat('%', #{SearchPojo.navcode}, '%')
        </if>
        <if test="SearchPojo.navname != null ">
            and PiRmsFunctMenuApp.NavName like concat('%', #{SearchPojo.navname}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and PiRmsFunctMenuApp.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and PiRmsFunctMenuApp.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and PiRmsFunctMenuApp.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and PiRmsFunctMenuApp.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and PiRmsFunctMenuApp.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.rmsfunctid != null ">
                or PiRmsFunctMenuApp.RmsFunctid like concat('%', #{SearchPojo.rmsfunctid}, '%')
            </if>
            <if test="SearchPojo.rmsfunctcode != null ">
                or PiRmsFunctMenuApp.RmsFunctCode like concat('%', #{SearchPojo.rmsfunctcode}, '%')
            </if>
            <if test="SearchPojo.rmsfunctname != null ">
                or PiRmsFunctMenuApp.RmsFunctName like concat('%', #{SearchPojo.rmsfunctname}, '%')
            </if>
            <if test="SearchPojo.navid != null ">
                or PiRmsFunctMenuApp.Navid like concat('%', #{SearchPojo.navid}, '%')
            </if>
            <if test="SearchPojo.navcode != null ">
                or PiRmsFunctMenuApp.NavCode like concat('%', #{SearchPojo.navcode}, '%')
            </if>
            <if test="SearchPojo.navname != null ">
                or PiRmsFunctMenuApp.NavName like concat('%', #{SearchPojo.navname}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or PiRmsFunctMenuApp.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or PiRmsFunctMenuApp.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or PiRmsFunctMenuApp.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or PiRmsFunctMenuApp.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or PiRmsFunctMenuApp.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiRmsFunctMenuApp(id, RmsFunctid, RmsFunctCode, RmsFunctName, Navid, NavCode, NavName, Remark,
                                      CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision)
        values (#{id}, #{rmsfunctid}, #{rmsfunctcode}, #{rmsfunctname}, #{navid}, #{navcode}, #{navname}, #{remark},
                #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiRmsFunctMenuApp
        <set>
            <if test="rmsfunctid != null ">
                RmsFunctid =#{rmsfunctid},
            </if>
            <if test="rmsfunctcode != null ">
                RmsFunctCode =#{rmsfunctcode},
            </if>
            <if test="rmsfunctname != null ">
                RmsFunctName =#{rmsfunctname},
            </if>
            <if test="navid != null ">
                Navid =#{navid},
            </if>
            <if test="navcode != null ">
                NavCode =#{navcode},
            </if>
            <if test="navname != null ">
                NavName =#{navname},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiRmsFunctMenuApp
        where id = #{key}
    </delete>

    <select id="getListByFunction" resultType="inks.system.domain.pojo.PirmsfunctmenuappPojo">
        SELECT
            PiMenuWeb.NavPid,
            PiMenuWeb.NavType,
            PiRmsFunctMenuApp.id,
            PiRmsFunctMenuApp.RmsFunctid,
            PiRmsFunctMenuApp.RmsFunctCode,
            PiRmsFunctMenuApp.RmsFunctName,
            PiRmsFunctMenuApp.Navid,
            PiRmsFunctMenuApp.NavCode,
            PiRmsFunctMenuApp.NavName,
            PiRmsFunctMenuApp.Remark,
            PiRmsFunctMenuApp.CreateBy,
            PiRmsFunctMenuApp.CreateByid,
            PiRmsFunctMenuApp.CreateDate,
            PiRmsFunctMenuApp.Lister,
            PiRmsFunctMenuApp.Listerid,
            PiRmsFunctMenuApp.ModifyDate,
            PiRmsFunctMenuApp.Revision
        FROM
            PiMenuWeb
                RIGHT JOIN PiRmsFunctMenuApp ON PiMenuWeb.Navid = PiRmsFunctMenuApp.Navid
        where 1 = 1 and PiRmsFunctMenuApp.RmsFunctid =#{key}
        order by PiRmsFunctMenuApp.NavCode
    </select>
    <select id="getListByRmsFunctids" resultType="inks.system.domain.pojo.PimenuappPojo">
        SELECT DISTINCT PiMenuApp.Navid,
                        PiMenuApp.NavPid,
                        PiMenuApp.NavType,
                        PiMenuApp.NavCode,
                        PiMenuApp.NavName,
                        PiMenuApp.NavGroup,
                        PiMenuApp.RowNum,
                        PiMenuApp.ImageCss,
                        PiMenuApp.IconUrl,
                        PiMenuApp.NavigateUrl,
                        PiMenuApp.MvcUrl,
                        PiMenuApp.ModuleType,
                        PiMenuApp.ModuleCode,
                        PiMenuApp.RoleCode,
                        PiMenuApp.ImageIndex,
                        PiMenuApp.ImageStyle,
                        PiMenuApp.EnabledMark,
                        PiMenuApp.Remark,
                        PiMenuApp.PermissionCode,
                        PiMenuApp.Functionid,
                        PiMenuApp.FunctionCode,
                        PiMenuApp.FunctionName,
                        PiMenuApp.Lister,
                        PiMenuApp.CreateDate,
                        PiMenuApp.ModifyDate,
                        PiMenuApp.DeleteMark,
                        PiMenuApp.DeleteLister,
                        PiMenuApp.DeleteDate
        FROM PiMenuApp
                 LEFT JOIN PiRmsFunctMenuApp ON PiRmsFunctMenuApp.Navid = PiMenuApp.Navid
        WHERE PiRmsFunctMenuApp.RmsFunctid in (${ids})
        order by RowNum
    </select>

</mapper>

