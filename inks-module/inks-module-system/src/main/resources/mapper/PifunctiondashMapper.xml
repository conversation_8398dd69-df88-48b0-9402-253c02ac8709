<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PifunctiondashMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PifunctiondashPojo">
        select id,
               Functionid,
               FunctionCode,
               FunctionName,
               Dashid,
               DashCode,
               DashName,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Revision
        from PiFunctionDash
        where PiFunctionDash.id = #{key}
    </select>
    <sql id="selectPifunctiondashVo">
        select id,
               Functionid,
               FunctionCode,
               FunctionName,
               Dashid,
               DashCode,
               DashName,
               Remark,
               CreateBy,
               <PERSON>reate<PERSON>yid,
               <PERSON>reate<PERSON><PERSON>,
               <PERSON>er,
               <PERSON><PERSON><PERSON>,
               ModifyDate,
               Revision
        from PiFunctionDash
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PifunctiondashPojo">
        <include refid="selectPifunctiondashVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiFunctionDash.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.functionid != null ">
            and PiFunctionDash.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null ">
            and PiFunctionDash.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null ">
            and PiFunctionDash.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.dashid != null ">
            and PiFunctionDash.Dashid like concat('%', #{SearchPojo.dashid}, '%')
        </if>
        <if test="SearchPojo.dashcode != null ">
            and PiFunctionDash.DashCode like concat('%', #{SearchPojo.dashcode}, '%')
        </if>
        <if test="SearchPojo.dashname != null ">
            and PiFunctionDash.DashName like concat('%', #{SearchPojo.dashname}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and PiFunctionDash.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and PiFunctionDash.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and PiFunctionDash.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and PiFunctionDash.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and PiFunctionDash.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.functionid != null ">
                or PiFunctionDash.Functionid like concat('%', #{SearchPojo.functionid}, '%')
            </if>
            <if test="SearchPojo.functioncode != null ">
                or PiFunctionDash.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
            </if>
            <if test="SearchPojo.functionname != null ">
                or PiFunctionDash.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
            </if>
            <if test="SearchPojo.dashid != null ">
                or PiFunctionDash.Dashid like concat('%', #{SearchPojo.dashid}, '%')
            </if>
            <if test="SearchPojo.dashcode != null ">
                or PiFunctionDash.DashCode like concat('%', #{SearchPojo.dashcode}, '%')
            </if>
            <if test="SearchPojo.dashname != null ">
                or PiFunctionDash.DashName like concat('%', #{SearchPojo.dashname}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or PiFunctionDash.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or PiFunctionDash.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or PiFunctionDash.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or PiFunctionDash.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or PiFunctionDash.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiFunctionDash(id, Functionid, FunctionCode, FunctionName, Dashid, DashCode, DashName, Remark,
                                   CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision)
        values (#{id}, #{functionid}, #{functioncode}, #{functionname}, #{dashid}, #{dashcode}, #{dashname}, #{remark},
                #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiFunctionDash
        <set>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="dashid != null ">
                Dashid =#{dashid},
            </if>
            <if test="dashcode != null ">
                DashCode =#{dashcode},
            </if>
            <if test="dashname != null ">
                DashName =#{dashname},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiFunctionDash
        where id = #{key}
    </delete>

    <!--查询List-->
    <select id="getListByFunction" resultType="inks.system.domain.pojo.PifunctiondashPojo">
        <include refid="selectPifunctiondashVo"/>
        where PiFunctionDash.Functionid =#{key}
        order by PiFunctionDash.DashName
    </select>
</mapper>

