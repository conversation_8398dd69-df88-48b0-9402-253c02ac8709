<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CiselfcheckMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CiselfcheckPojo">
        select
          id, Functionid, FunctionCode, FunctionName, ApiUrl, Score, Critical, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Revision        from CiSelfCheck
        where CiSelfCheck.id = #{key} 
    </select>
    <sql id="selectCiselfcheckVo">
         select
          id, Functionid, FunctionCode, FunctionName, ApiUrl, Score, Critical, Remark, CreateBy, <PERSON><PERSON><PERSON>yi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>er, <PERSON><PERSON>d, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Revision        from CiSelfCheck
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.CiselfcheckPojo">
        <include refid="selectCiselfcheckVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and CiSelfCheck.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.functionid != null ">
   and CiSelfCheck.Functionid like concat('%', #{SearchPojo.functionid}, '%')
</if>
<if test="SearchPojo.functioncode != null ">
   and CiSelfCheck.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
</if>
<if test="SearchPojo.functionname != null ">
   and CiSelfCheck.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
</if>
<if test="SearchPojo.apiurl != null ">
   and CiSelfCheck.ApiUrl like concat('%', #{SearchPojo.apiurl}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and CiSelfCheck.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and CiSelfCheck.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and CiSelfCheck.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and CiSelfCheck.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and CiSelfCheck.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and CiSelfCheck.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and CiSelfCheck.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and CiSelfCheck.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and CiSelfCheck.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and CiSelfCheck.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.functionid != null ">
   or CiSelfCheck.Functionid like concat('%', #{SearchPojo.functionid}, '%')
</if>
<if test="SearchPojo.functioncode != null ">
   or CiSelfCheck.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
</if>
<if test="SearchPojo.functionname != null ">
   or CiSelfCheck.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
</if>
<if test="SearchPojo.apiurl != null ">
   or CiSelfCheck.ApiUrl like concat('%', #{SearchPojo.apiurl}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or CiSelfCheck.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or CiSelfCheck.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or CiSelfCheck.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or CiSelfCheck.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or CiSelfCheck.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or CiSelfCheck.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or CiSelfCheck.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or CiSelfCheck.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or CiSelfCheck.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or CiSelfCheck.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into CiSelfCheck(id, Functionid, FunctionCode, FunctionName, ApiUrl, Score, Critical, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Revision)
        values (#{id}, #{functionid}, #{functioncode}, #{functionname}, #{apiurl}, #{score}, #{critical}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update CiSelfCheck
        <set>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="apiurl != null ">
                ApiUrl =#{apiurl},
            </if>
            <if test="score != null">
                Score =#{score},
            </if>
            <if test="critical != null">
                Critical =#{critical},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from CiSelfCheck where id = #{key} 
    </delete>

    <select id="getFunctionidByTid" resultType="java.lang.String">
        SELECT DISTINCT Functionid
        FROM PiSubscriber
        WHERE #{date} >= StartDate
        and EndDate >= #{date}
        and Tenantid = #{tid}
    </select>

    <select id="checkPasswordComplexity" resultType="java.lang.String">
        SELECT distinct PiTenantUser.RealName
        FROM PiUserLogin left join PiTenantUser on PiUserLogin.Userid = PiTenantUser.Userid
        WHERE PiUserLogin.Userid in (select Userid from PiTenantUser where Tenantid = #{tid})
          and PiTenantUser.UserName is not null
          and PiUserLogin.UserPassword
        <foreach collection="pwdList" item="pwd" index="index" open="in (" separator="," close=")">
            #{pwd}
        </foreach>
    </select>

    <!--    检查是否有过期的销售订单,过滤到Online订单且计划时间超时-->
    <select id="checkExpiredMach" resultType="java.lang.String">
        SELECT RefNo
        from Bus_Machining
        where Tenantid = #{tid}
        and FinishCount+DisannulCount <![CDATA[<]]> ItemCount
        and BillPlanDate <![CDATA[<]]> #{date}
    </select>
</mapper>

