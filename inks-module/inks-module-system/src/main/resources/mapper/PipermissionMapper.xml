<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PipermissionMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PipermissionPojo">
        select id,
               ResourceType,
               Resourceid,
               Permid,
               PermCode,
               PermName,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Tenantid,
               TenantName,
               Revision
        from PiPermission
        where PiPermission.id = #{key}
          and PiPermission.Tenantid = #{tid}
    </select>
    <sql id="selectPipermissionVo">
        select id,
               ResourceType,
               Resourceid,
               Permid,
               PermCode,
               PermName,
               CreateBy,
               CreateByid,
               <PERSON>reate<PERSON><PERSON>,
               <PERSON><PERSON>,
               <PERSON><PERSON><PERSON>,
               <PERSON>difyDate,
               Tenanti<PERSON>,
               TenantName,
               Revision
        from PiPermission
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PipermissionPojo">
        <include refid="selectPipermissionVo"/>
        where 1 = 1 and PiPermission.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiPermission.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.resourcetype != null ">
            and PiPermission.ResourceType like concat('%', #{SearchPojo.resourcetype}, '%')
        </if>
        <if test="SearchPojo.resourceid != null ">
            and PiPermission.Resourceid like concat('%', #{SearchPojo.resourceid}, '%')
        </if>
        <if test="SearchPojo.permid != null ">
            and PiPermission.Permid like concat('%', #{SearchPojo.permid}, '%')
        </if>
        <if test="SearchPojo.permcode != null ">
            and PiPermission.PermCode like concat('%', #{SearchPojo.permcode}, '%')
        </if>
        <if test="SearchPojo.permname != null ">
            and PiPermission.PermName like concat('%', #{SearchPojo.permname}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and PiPermission.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and PiPermission.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and PiPermission.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and PiPermission.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and PiPermission.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.resourcetype != null ">
                or PiPermission.ResourceType like concat('%', #{SearchPojo.resourcetype}, '%')
            </if>
            <if test="SearchPojo.resourceid != null ">
                or PiPermission.Resourceid like concat('%', #{SearchPojo.resourceid}, '%')
            </if>
            <if test="SearchPojo.permid != null ">
                or PiPermission.Permid like concat('%', #{SearchPojo.permid}, '%')
            </if>
            <if test="SearchPojo.permcode != null ">
                or PiPermission.PermCode like concat('%', #{SearchPojo.permcode}, '%')
            </if>
            <if test="SearchPojo.permname != null ">
                or PiPermission.PermName like concat('%', #{SearchPojo.permname}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or PiPermission.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or PiPermission.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or PiPermission.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or PiPermission.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or PiPermission.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiPermission(id, ResourceType, Resourceid, Permid, PermCode, PermName, CreateBy, CreateByid,
                                 CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
        values (#{id}, #{resourcetype}, #{resourceid}, #{permid}, #{permcode}, #{permname}, #{createby}, #{createbyid},
                #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <insert id="batchInsert">
        INSERT INTO PiPermission (id, ResourceType, Resourceid, Permid, PermCode, PermName, CreateBy, CreateByid,
        CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.resourcetype}, #{item.resourceid}, #{item.permid}, #{item.permcode}, #{item.permname},
            #{item.createby}, #{item.createbyid}, #{item.createdate}, #{item.lister}, #{item.listerid},
            #{item.modifydate}, #{item.tenantid}, #{item.tenantname}, #{item.revision})
        </foreach>
    </insert>

    <delete id="batchDelete">
        DELETE FROM PiPermission
        WHERE (resourceid, permid, tenantid) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.resourceid}, #{item.permid}, #{item.tenantid})
        </foreach>
    </delete>



    <!--通过主键修改数据-->
    <update id="update">
        update PiPermission
        <set>
            <if test="resourcetype != null ">
                ResourceType =#{resourcetype},
            </if>
            <if test="resourceid != null ">
                Resourceid =#{resourceid},
            </if>
            <if test="permid != null ">
                Permid =#{permid},
            </if>
            <if test="permcode != null ">
                PermCode =#{permcode},
            </if>
            <if test="permname != null ">
                PermName =#{permname},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiPermission
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--按角色查询权限-->
    <select id="getListByRole" resultType="inks.system.domain.pojo.PipermissionPojo">
        SELECT
            PiPermission.id,
            PiPermission.ResourceType,
            PiPermission.Resourceid,
            PiPermission.Permid,
            PiPermission.CreateBy,
            PiPermission.CreateByid,
            PiPermission.CreateDate,
            PiPermission.Lister,
            PiPermission.Listerid,
            PiPermission.ModifyDate,
            PiPermission.Tenantid,
            PiPermission.TenantName,
            PiPermission.Revision,
            PiPermCode.Parentid,
            PiPermCode.PermType,
            PiPermCode.PermCode,
            PiPermCode.PermName
        FROM
            PiPermCode
                RIGHT JOIN PiPermission ON PiPermission.Permid = PiPermCode.Permid
        where PiPermission.ResourceType = 'Role'
          and PiPermission.Resourceid = #{key}
    </select>

<!--    获取一个用户的所有权限-->
    <select id="getUserAllPerm" resultType="inks.system.domain.pojo.PipermissionPojo">
        SELECT PiPermission.id,
               PiPermission.ResourceType,
               PiPermission.Resourceid,
               PiPermission.Permid,
               PiPermission.CreateBy,
               PiPermission.CreateByid,
               PiPermission.CreateDate,
               PiPermission.Lister,
               PiPermission.Listerid,
               PiPermission.ModifyDate,
               PiPermission.Tenantid,
               PiPermission.TenantName,
               PiPermission.Revision,
               PiPermCode.Parentid,
               PiPermCode.PermType,
               PiPermCode.PermCode,
               PiPermCode.PermName
        FROM PiPermCode
                 RIGHT JOIN PiPermission ON PiPermission.Permid = PiPermCode.Permid
        where PiPermission.Resourceid in (SELECT Roleid
                                          FROM PiUserRole
                                          WHERE Userid = #{key}
                                          UNION
                                          SELECT #{key} AS Roleid)
    </select>

</mapper>

