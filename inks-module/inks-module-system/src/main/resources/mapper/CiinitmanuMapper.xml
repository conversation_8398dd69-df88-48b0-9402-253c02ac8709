<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CiinitmanuMapper">
    <!--通过主键删除-->
    <delete id="deleteWsItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Wk_WorksheetItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Wk_Worksheet
                      where Tenantid = #{tenantid}
                        and Wk_Worksheet.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deleteWs" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Wk_Worksheet
        where Tenantid = #{tenantid}
          and Wk_Worksheet.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>

    <!--通过主键删除-->
    <delete id="deleteScItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Wk_SubcontractItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Wk_Subcontract
                      where Tenantid = #{tenantid}
                        and Wk_Subcontract.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deleteSc" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Wk_Subcontract
        where Tenantid = #{tenantid}
          and Wk_Subcontract.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>

    <!--通过主键删除-->
    <delete id="deleteCompItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Wk_CompleteItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Wk_Complete
                      where Tenantid = #{tenantid}
                        and Wk_Complete.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deleteComp" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Wk_Complete
        where Tenantid = #{tenantid}
          and Wk_Complete.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>

    <!--通过主键删除-->
    <delete id="deleteMpItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Wk_MainPlanItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Wk_MainPlan
                      where Tenantid = #{tenantid}
                        and Wk_MainPlan.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deleteMp" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Wk_MainPlan
        where Tenantid = #{tenantid}
          and Wk_MainPlan.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>

    <!--通过主键删除-->
    <delete id="deleteWipItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Wk_WipNoteItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Wk_WipNote
                      where Tenantid = #{tenantid}
                        and Wk_WipNote.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deleteWip" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Wk_WipNote
        where Tenantid = #{tenantid}
          and Wk_WipNote.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>

    <!--通过主键删除-->
    <delete id="deleteManuPtItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Wk_ManuReportItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Wk_ManuReport
                      where Tenantid = #{tenantid}
                        and Wk_ManuReport.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deleteManuPt" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Wk_ManuReport
        where Tenantid = #{tenantid}
          and Wk_ManuReport.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>

    <!--通过主键删除-->
    <delete id="deleteWipEpItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Wk_WipEpiboleItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Wk_WipEpibole
                      where Tenantid = #{tenantid}
                        and Wk_WipEpibole.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deleteWipEp" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Wk_WipEpibole
        where Tenantid = #{tenantid}
          and Wk_WipEpibole.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>


    <!--    ===================以下是新代码删除表=========================================-->
    <!--    int deleteWk_Worksheet(QueryParam queryParam); //D05M01B1 厂制工单
        int deleteWk_Subcontract(QueryParam queryParam); //D05M02B1 委制单据
        int deleteWk_Complete(QueryParam queryParam); //D05M03B1 生产验收/完工验收
        int deleteWk_ScDeduction(QueryParam queryParam); //D05M03B1De 委制扣款
        int deleteWk_ScComplete(QueryParam queryParam); //D05M03B1Sc 委外加工单验收
        int deleteWk_MainPlan(QueryParam queryParam); //D05M04B1 生产主计划
        int deleteWk_WipNote(QueryParam queryParam); //D05M05B1 WIP记录
        int deleteHi_WipNote(QueryParam queryParam); //D05M05H1 WIP记录
        int deleteWk_ManuReport(QueryParam queryParam); //D05M06B1 生产报工
        int deleteWk_WipQty(QueryParam queryParam); //D05M07B1 生产过数
        int deleteHi_WipQty(QueryParam queryParam); //D05M07H1 Hi生产过数
        int deleteWk_WipEpibole(QueryParam queryParam); //D05M09B1 Wip委外
        int deleteWk_WipEpFinishing(QueryParam queryParam); //D05M09B1FIN 工序收货
        int deleteWk_Mrp(QueryParam queryParam); //D05M11B1 MRP运算-->

    <!-- D05M01B1 厂制工单 -->
    <delete id="deleteWk_Worksheet">
        DELETE t1, t2, t3, t4 ,t5
        FROM Wk_Worksheet t1
                 LEFT JOIN Wk_WorksheetItem t2 ON t1.id = t2.Pid
                 LEFT JOIN Wk_WorksheetMat t3 ON t1.id = t3.Pid
                 LEFT JOIN Wk_WorksheetMatMerge t4 ON t1.id = t4.Pid
                 LEFT JOIN Wk_WorksheetMerge t5 ON t2.id = t5.Itemid
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
          and t1.Tenantid = #{tenantid}
    </delete>

    <!-- D05M02B1 委制单据 -->
    <delete id="deleteWk_Subcontract">
        delete t1,t2,t3
        from Wk_Subcontract t1
                 left join Wk_SubcontractItem t2 on t1.id = t2.Pid
                 left join Wk_SubcontractMat t3 on t1.id = t3.Pid
        where t1.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
          and t1.Tenantid = #{tenantid}
    </delete>
    <!-- D05M03B1 生产验收/完工验收 -->
    <delete id="deleteWk_Complete">
        delete t1,t2,t3
        from Wk_Complete t1
                 left join Wk_CompleteItem t2 on t1.id = t2.Pid
                 left join Wk_CompleteMat t3 on t1.id = t3.Pid
        where t1.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
          and t1.Tenantid = #{tenantid}
    </delete>
    <!-- D05M03B1De 委制扣款 -->
    <delete id="deleteWk_ScDeduction">
        delete t1,t2
        from Wk_ScDeduction t1
                 left join Wk_ScDeductionItem t2 on t1.id = t2.Pid
        where t1.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
          and t1.Tenantid = #{tenantid}
    </delete>
    <!-- D05M03B1Sc 委外加工单验收 -->
    <delete id="deleteWk_ScComplete">
        delete t1,t2,t3
        from Wk_ScComplete t1
                 left join Wk_ScCompleteItem t2 on t1.id = t2.Pid
                 left join Wk_ScCompleteMat t3 on t1.id = t3.Pid
        where t1.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
          and t1.Tenantid = #{tenantid}
    </delete>
    <!-- D05M04B1 生产主计划 -->
    <delete id="deleteWk_MainPlan">
        delete t1,t2
        from Wk_MainPlan t1
                 left join Wk_MainPlanItem t2 on t1.id = t2.Pid
        where t1.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
          and t1.Tenantid = #{tenantid}
    </delete>
    <!-- D05M05B1 WIP记录 -->
    <delete id="deleteWk_WipNote">
        delete t1,t2
        from Wk_WipNote t1
                 left join Wk_WipNoteItem t2 on t1.id = t2.Pid
        where t1.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
          and t1.Tenantid = #{tenantid}
    </delete>
    <!-- D05M05H1 HiWIP记录 -->
    <delete id="deleteHi_WipNote">
        delete t1,t2
        from Hi_WipNote t1
                 left join Hi_WipNoteItem t2 on t1.id = t2.Pid
        where t1.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
          and t1.Tenantid = #{tenantid}
    </delete>
    <!-- D05M06B1 生产报工 -->
    <delete id="deleteWk_ManuReport">
        delete t1,t2
        from Wk_ManuReport t1
                 left join Wk_ManuReportItem t2 on t1.id = t2.Pid
        where t1.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
          and t1.Tenantid = #{tenantid}
    </delete>
    <!-- D05M07B1 生产过数 -->
    <delete id="deleteWk_WipQty" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Wk_WipQty
        where Tenantid = #{tenantid}
          and Wk_WipQty.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>
    <!-- D05M07H1 Hi生产过数 -->
    <delete id="deleteHi_WipQty">
        delete t1
        from Hi_WipQty t1
        where t1.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
          and t1.Tenantid = #{tenantid}
    </delete>
    <!-- D05M09B1 Wip委外 -->
    <delete id="deleteWk_WipEpibole">
        delete t1,t2
        from Wk_WipEpibole t1
                 left join Wk_WipEpiboleItem t2 on t1.id = t2.Pid
        where t1.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
          and t1.Tenantid = #{tenantid}
    </delete>
    <!-- D05M09B1FIN 工序收货 -->
    <delete id="deleteWk_WipEpFinishing">
        delete t1,t2
        from Wk_WipEpFinishing t1
                 left join Wk_WipEpFinishingItem t2 on t1.id = t2.Pid
        where t1.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
          and t1.Tenantid = #{tenantid}
    </delete>
    <!-- D05M11B1 MRP运算 -->
    <delete id="deleteWk_Mrp">
        DELETE t1, t2, t3,t4
        FROM Wk_Mrp t1
                 LEFT JOIN Wk_MrpItem t2 ON t1.id = t2.Pid
                 LEFT JOIN Wk_MrpLog t3 ON t1.id = t3.Pid
                 LEFT JOIN Wk_MrpObj t4 ON t1.id = t4.Pid
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
          and t1.Tenantid = #{tenantid}
    </delete>
    <!-- D05M12B1 SMT上料表 D05M12B2 上料记录 合并了 -->
    <delete id="deleteWk_SmtPart">
        DELETE t1, t2,t3
        FROM Wk_SmtPart t1
                 LEFT JOIN Wk_SmtPartItem t2 ON t1.id = t2.Pid
                 LEFT JOIN Wk_SmtPartRec t3 ON t1.RefNo = t3.PartUid
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
          AND t1.Tenantid = #{tenantid}
    </delete>

    <!-- D05M13B1 成本预测 -->
    <delete id="deleteWk_CostBudget">
        DELETE t1,t2
        FROM Wk_CostBudget t1
                 LEFT JOIN Wk_CostBudgetItem t2 ON t1.id = t2.Pid
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
          AND t1.Tenantid = #{tenantid}
    </delete>

    <!-- D05M14B1 生产结转 -->
    <delete id="deleteWk_MpCarryover">
        DELETE t1, t2
        FROM Wk_MpCarryover t1
                 LEFT JOIN Wk_MpCarryoverItem t2 ON t1.id = t2.Pid
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
          AND t1.Tenantid = #{tenantid}
    </delete>

    <!-- D05M14B1SC 加工结转 -->
    <delete id="deleteWk_ScCarryover">
        DELETE t1, t2
        FROM Wk_ScCarryover t1
                 LEFT JOIN Wk_ScCarryoverItem t2 ON t1.id = t2.Pid
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
          AND t1.Tenantid = #{tenantid}
    </delete>

    <!-- D05M14B1WS 车间结转 -->
    <delete id="deleteWk_WsCarryover">
        DELETE t1, t2
        FROM Wk_WsCarryover t1
                 LEFT JOIN Wk_WsCarryoverItem t2 ON t1.id = t2.Pid
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
          AND t1.Tenantid = #{tenantid}
    </delete>

    <!-- D05M15B1 可视化排程 -->
    <delete id="deleteWk_VisualPlan">
        DELETE t1
        FROM Wk_VisualPlan t1
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
          AND t1.Tenantid = #{tenantid}
    </delete>

    <!-- D05M16B1 阶梯工价 -->
    <delete id="deleteWk_StepPrice">
        DELETE t1, t2
        FROM Wk_StepPrice t1
                 LEFT JOIN Wk_StepPriceItem t2 ON t1.id = t2.Pid
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
          AND t1.Tenantid = #{tenantid}
    </delete>

    <!-- D05M16B2 阶梯制程 -->
    <delete id="deleteWk_StepProGroup">
        DELETE t1, t2
        FROM Wk_StepProGroup t1
                 LEFT JOIN Wk_StepProGroupItem t2 ON t1.id = t2.Pid
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
          AND t1.Tenantid = #{tenantid}
    </delete>

    <!-- D05M16S1 阶梯工价设置 -->
    <delete id="deleteWk_StepPriceSet">
        DELETE t1
        FROM Wk_StepPriceSet t1
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
          AND t1.Tenantid = #{tenantid}
    </delete>

    <!-- D05M17B1 工位状态 -->
    <delete id="deleteWk_StationState">
        DELETE t1
        FROM Wk_StationState t1
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
          AND t1.Tenantid = #{tenantid}
    </delete>

    <!-- D05M21S1 生产工序 -->
    <delete id="deleteWk_Process">
        DELETE t1, t2, t3
        FROM Wk_Process t1
                 LEFT JOIN Wk_ProcessItem t2 ON t1.id = t2.Pid
                 LEFT JOIN Wk_ProcessStat t3 ON t1.id = t3.Pid
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
          AND t1.Tenantid = #{tenantid}
    </delete>

    <!-- D05M21S2 生产制程 -->
    <delete id="deleteWk_ProGroup">
        DELETE t1, t2
        FROM Wk_ProGroup t1
                 LEFT JOIN Wk_ProGroupItem t2 ON t1.id = t2.Pid
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
          AND t1.Tenantid = #{tenantid}
    </delete>

    <!-- D05M21S3 WIP设定 -->
    <delete id="deleteWk_WipGroup">
        DELETE t1, t2
        FROM Wk_WipGroup t1
                 LEFT JOIN Wk_WipGroupItem t2 ON t1.id = t2.Pid
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
          AND t1.Tenantid = #{tenantid}
    </delete>

    <!-- D05M21S4 过数角色 -->
    <delete id="deleteWk_WipQtyRoles">
        DELETE t1, t2
        FROM Wk_WipQtyRoles t1
                 LEFT JOIN Wk_WipQtyRolesItem t2 ON t1.id = t2.Pid
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
          AND t1.Tenantid = #{tenantid}
    </delete>

    <!-- D05M21S5 过数小组 -->
    <delete id="deleteWk_WipQtyGroup">
        DELETE t1, t2
        FROM Wk_WipQtyGroup t1
                 LEFT JOIN Wk_WipQtyGroupItem t2 ON t1.id = t2.Pid
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
          AND t1.Tenantid = #{tenantid}
    </delete>

    <!-- D05M21S6 工位 -->
    <delete id="deleteWk_Station">
        DELETE t1, t2, t3
        FROM Wk_Station t1
                 LEFT JOIN Wk_StationItem t2 ON t1.id = t2.Pid
                 LEFT JOIN Wk_StationDev t3 ON t1.id = t3.Pid
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
          AND t1.Tenantid = #{tenantid}
    </delete>

    <!-- D05M21S8 工序成本 -->
    <delete id="deleteWk_ProcCost">
        DELETE t1
        FROM Wk_ProcCost t1
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
          AND t1.Tenantid = #{tenantid}
    </delete>

    <!-- D05M21S9 委外加工类型 -->
    <delete id="deleteWk_ScMachType">
        DELETE t1
        FROM Wk_ScMachType t1
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
          AND t1.Tenantid = #{tenantid}
    </delete>

    <!-- D05M21S10 委外费用类型 -->
    <delete id="deleteWk_ScCostType">
        DELETE t1
        FROM Wk_ScCostType t1
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
          AND t1.Tenantid = #{tenantid}
    </delete>

    <!-- D05M21S11 车间预损率 -->
    <delete id="deleteWk_GroupLossRate">
        DELETE t1
        FROM Wk_GroupLossRate t1
        WHERE t1.CreateDate BETWEEN #{DateRange.StartDate} AND #{DateRange.EndDate}
          AND t1.Tenantid = #{tenantid}
    </delete>

</mapper>

