<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PiprojectitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PiprojectitemPojo">
        select id,
               Pid,
               Functionid,
               FunctionCode,
               FunctionName,
               RowNum,
               Remark,
               Revision
        from PiProjectItem
        where PiProjectItem.id = #{key}
    </select>
    <sql id="selectPiprojectitemVo">
        select id,
               Pid,
               Functionid,
               FunctionCode,
               FunctionName,
               RowNum,
               Remark,
               Revision
        from PiProjectItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PiprojectitemPojo">
        <include refid="selectPiprojectitemVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiProjectItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by #{orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and PiProjectItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.functionid != null and SearchPojo.functionid != ''">
            and PiProjectItem.functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null and SearchPojo.functioncode != ''">
            and PiProjectItem.functioncode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null and SearchPojo.functionname != ''">
            and PiProjectItem.functionname like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and PiProjectItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            or PiProjectItem.Pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.functionid != null and SearchPojo.functionid != ''">
            or PiProjectItem.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null and SearchPojo.functioncode != ''">
            or PiProjectItem.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null and SearchPojo.functionname != ''">
            or PiProjectItem.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or PiProjectItem.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        )
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.system.domain.pojo.PiprojectitemPojo">
        select id,
               Pid,
               Functionid,
               FunctionCode,
               FunctionName,
               RowNum,
               Remark,
               Revision
        from PiProjectItem
        where PiProjectItem.Pid = #{Pid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into PiProjectItem(id, Pid, Functionid, FunctionCode, FunctionName, RowNum, Remark, Revision)
        values (#{id}, #{pid}, #{functionid}, #{functioncode}, #{functionname}, #{rownum}, #{remark}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiProjectItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="functionid != null ">
                Functionid = #{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode = #{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName = #{functionname},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiProjectItem
        where id = #{key}
    </delete>

</mapper>

