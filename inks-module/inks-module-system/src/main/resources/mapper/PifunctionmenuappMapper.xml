<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PifunctionmenuappMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PifunctionmenuappPojo">
        select id,
               Functionid,
               FunctionCode,
               FunctionName,
               Navid,
               NavCode,
               NavName,
               Lister,
               CreateDate,
               ModifyDate,
               Remark,
               CreateBy,
               Tenantid,
               Revision
        from PiFunctionMenuApp
        where PiFunctionMenuApp.id = #{key}
          and PiFunctionMenuApp.Tenantid = #{tid}
    </select>
    <sql id="selectPifunctionmenuappVo">
        SELECT
            PiFunctionMenuApp.id,
            PiFunctionMenuApp.Functionid,
            PiFunctionMenuApp.FunctionCode,
            PiFunctionMenuApp.FunctionName,
            PiFunctionMenuApp.Navid,
            PiFunctionMenuApp.NavCode,
            PiFunctionMenuApp.NavName,
            PiFunctionMenuApp.Remark,
            PiFunctionMenuApp.CreateBy,
            PiFunctionMenuApp.CreateDate,
            PiFunctionMenuApp.Lister,
            PiFunctionMenuApp.ModifyDate,
            PiFunctionMenuApp.Tenantid,
            PiFunctionMenuApp.Revision,
            PiMenuApp.NavType,
            PiMenuApp.NavPid
        FROM
            PiMenuApp
                RIGHT JOIN PiFunctionMenuApp ON PiMenuApp.Navid = PiFunctionMenuApp.Navid

    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PifunctionmenuappPojo">
        <include refid="selectPifunctionmenuappVo"/>
        where 1 = 1 and PiFunctionMenuApp.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiFunctionMenuApp.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.functionid != null and SearchPojo.functionid  != ''">
            and PiFunctionMenuApp.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null and SearchPojo.functioncode  != ''">
            and PiFunctionMenuApp.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null and SearchPojo.functionname  != ''">
            and PiFunctionMenuApp.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.navid != null and SearchPojo.navid  != ''">
            and PiFunctionMenuApp.Navid like concat('%', #{SearchPojo.navid}, '%')
        </if>
        <if test="SearchPojo.navcode != null and SearchPojo.navcode  != ''">
            and PiFunctionMenuApp.NavCode like concat('%', #{SearchPojo.navcode}, '%')
        </if>
        <if test="SearchPojo.navname != null and SearchPojo.navname  != ''">
            and PiFunctionMenuApp.NavName like concat('%', #{SearchPojo.navname}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and PiFunctionMenuApp.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark  != ''">
            and PiFunctionMenuApp.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby  != ''">
            and PiFunctionMenuApp.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.functionid != null and SearchPojo.functionid != ''">
            or PiFunctionMenuApp.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null and SearchPojo.functioncode != ''">
            or PiFunctionMenuApp.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null and SearchPojo.functionname != ''">
            or PiFunctionMenuApp.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.navid != null and SearchPojo.navid != ''">
            or PiFunctionMenuApp.Navid like concat('%', #{SearchPojo.navid}, '%')
        </if>
        <if test="SearchPojo.navcode != null and SearchPojo.navcode != ''">
            or PiFunctionMenuApp.NavCode like concat('%', #{SearchPojo.navcode}, '%')
        </if>
        <if test="SearchPojo.navname != null and SearchPojo.navname != ''">
            or PiFunctionMenuApp.NavName like concat('%', #{SearchPojo.navname}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or PiFunctionMenuApp.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or PiFunctionMenuApp.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or PiFunctionMenuApp.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiFunctionMenuApp(id, Functionid, FunctionCode, FunctionName, Navid, NavCode, NavName, Lister,
                                      CreateDate, ModifyDate, Remark, CreateBy, Tenantid, Revision)
        values (#{id}, #{functionid}, #{functioncode}, #{functionname}, #{navid}, #{navcode}, #{navname}, #{lister},
                #{createdate}, #{modifydate}, #{remark}, #{createby}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiFunctionMenuApp
        <set>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="navid != null ">
                Navid =#{navid},
            </if>
            <if test="navcode != null ">
                NavCode =#{navcode},
            </if>
            <if test="navname != null ">
                NavName =#{navname},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiFunctionMenuApp
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--查询List-->
    <select id="getListByFunction" resultType="inks.system.domain.pojo.PifunctionmenuappPojo">
        <include refid="selectPifunctionmenuappVo"/>
        where 1 = 1 and PiFunctionMenuApp.Functionid =#{key}
        order by PiFunctionMenuApp.NavCode
    </select>


</mapper>

