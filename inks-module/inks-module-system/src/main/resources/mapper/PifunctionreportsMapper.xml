<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PifunctionreportsMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PifunctionreportsPojo">
        select id,
               Functionid,
               FunctionCode,
               FunctionName,
               Reportid,
               ReportCode,
               ReportName,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Tenantid,
               Revision
        from PiFunctionReports
        where PiFunctionReports.id = #{key}
    </select>
    <sql id="selectPifunctionreportsVo">
        select id,
               Functionid,
               FunctionCode,
               FunctionName,
               Reportid,
               ReportCode,
               ReportName,
               Remark,
               CreateBy,
               CreateByid,
               <PERSON>reateDate,
               <PERSON>er,
               <PERSON><PERSON><PERSON>,
               ModifyDate,
               Tenantid,
               Revision
        from PiFunctionReports
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PifunctionreportsPojo">
        <include refid="selectPifunctionreportsVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiFunctionReports.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.functionid != null ">
            and PiFunctionReports.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null ">
            and PiFunctionReports.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null ">
            and PiFunctionReports.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.reportid != null ">
            and PiFunctionReports.Reportid like concat('%', #{SearchPojo.reportid}, '%')
        </if>
        <if test="SearchPojo.reportcode != null ">
            and PiFunctionReports.ReportCode like concat('%', #{SearchPojo.reportcode}, '%')
        </if>
        <if test="SearchPojo.reportname != null ">
            and PiFunctionReports.ReportName like concat('%', #{SearchPojo.reportname}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and PiFunctionReports.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and PiFunctionReports.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and PiFunctionReports.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and PiFunctionReports.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and PiFunctionReports.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.functionid != null ">
                or PiFunctionReports.Functionid like concat('%', #{SearchPojo.functionid}, '%')
            </if>
            <if test="SearchPojo.functioncode != null ">
                or PiFunctionReports.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
            </if>
            <if test="SearchPojo.functionname != null ">
                or PiFunctionReports.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
            </if>
            <if test="SearchPojo.reportid != null ">
                or PiFunctionReports.Reportid like concat('%', #{SearchPojo.reportid}, '%')
            </if>
            <if test="SearchPojo.reportcode != null ">
                or PiFunctionReports.ReportCode like concat('%', #{SearchPojo.reportcode}, '%')
            </if>
            <if test="SearchPojo.reportname != null ">
                or PiFunctionReports.ReportName like concat('%', #{SearchPojo.reportname}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or PiFunctionReports.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or PiFunctionReports.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or PiFunctionReports.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or PiFunctionReports.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or PiFunctionReports.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiFunctionReports(id, Functionid, FunctionCode, FunctionName, Reportid, ReportCode, ReportName,
                                      Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid,
                                      Revision)
        values (#{id}, #{functionid}, #{functioncode}, #{functionname}, #{reportid}, #{reportcode}, #{reportname},
                #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate},
                #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiFunctionReports
        <set>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="reportid != null ">
                Reportid =#{reportid},
            </if>
            <if test="reportcode != null ">
                ReportCode =#{reportcode},
            </if>
            <if test="reportname != null ">
                ReportName =#{reportname},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiFunctionReports
        where id = #{key}
    </delete>

    <!--查询List-->
    <select id="getListByFunction" resultType="inks.system.domain.pojo.PifunctionreportsPojo">
        <include refid="selectPifunctionreportsVo"/>
        where 1 = 1 and PiFunctionReports.Functionid =#{key}
        order by PiFunctionReports.ReportCode
    </select>
</mapper>

