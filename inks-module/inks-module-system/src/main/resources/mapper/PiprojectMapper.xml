<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PiprojectMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PiprojectPojo">
        select id,
               ProjectCode,
               ProjectName,
               Description,
               FrontPhoto,
               EnabledMark,
               AllowDelete,
               RowNum,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Revision
        from PiProject
        where PiProject.id = #{key}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select id,
               ProjectCode,
               ProjectName,
               Description,
               FrontPhoto,
               EnabledMark,
               AllowDelete,
               RowNum,
               Su<PERSON>ry,
               <PERSON>reate<PERSON><PERSON>,
               <PERSON><PERSON><PERSON>yid,
               <PERSON><PERSON><PERSON>ate,
               Lister,
               <PERSON>erid,
               ModifyDate,
               Revision
        from PiProject
    </sql>
    <sql id="selectdetailVo">
        select id,
               ProjectCode,
               ProjectName,
               Description,
               FrontPhoto,
               EnabledMark,
               AllowDelete,
               RowNum,
               Summary,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Revision
        from PiProject
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PiprojectitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiProject.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.projectcode != null and SearchPojo.projectcode != ''">
            and PiProject.projectcode like concat('%', #{SearchPojo.projectcode}, '%')
        </if>
        <if test="SearchPojo.projectname != null and SearchPojo.projectname != ''">
            and PiProject.projectname like concat('%', #{SearchPojo.projectname}, '%')
        </if>
        <if test="SearchPojo.description != null and SearchPojo.description != ''">
            and PiProject.description like concat('%', #{SearchPojo.description}, '%')
        </if>
        <if test="SearchPojo.frontphoto != null and SearchPojo.frontphoto != ''">
            and PiProject.frontphoto like concat('%', #{SearchPojo.frontphoto}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and PiProject.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and PiProject.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and PiProject.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and PiProject.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and PiProject.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.projectcode != null and SearchPojo.projectcode != ''">
            or PiProject.ProjectCode like concat('%', #{SearchPojo.projectcode}, '%')
        </if>
        <if test="SearchPojo.projectname != null and SearchPojo.projectname != ''">
            or PiProject.ProjectName like concat('%', #{SearchPojo.projectname}, '%')
        </if>
        <if test="SearchPojo.description != null and SearchPojo.description != ''">
            or PiProject.Description like concat('%', #{SearchPojo.description}, '%')
        </if>
        <if test="SearchPojo.frontphoto != null and SearchPojo.frontphoto != ''">
            or PiProject.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            or PiProject.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or PiProject.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            or PiProject.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or PiProject.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            or PiProject.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        )
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PiprojectPojo">
        <include refid="selectbillVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiProject.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.projectcode != null and SearchPojo.projectcode != ''">
            and PiProject.ProjectCode like concat('%', #{SearchPojo.projectcode}, '%')
        </if>
        <if test="SearchPojo.projectname != null and SearchPojo.projectname != ''">
            and PiProject.ProjectName like concat('%', #{SearchPojo.projectname}, '%')
        </if>
        <if test="SearchPojo.description != null and SearchPojo.description != ''">
            and PiProject.Description like concat('%', #{SearchPojo.description}, '%')
        </if>
        <if test="SearchPojo.frontphoto != null and SearchPojo.frontphoto != ''">
            and PiProject.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            and PiProject.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and PiProject.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and PiProject.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and PiProject.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and PiProject.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
    </sql>
    <sql id="thor">
        and (1=0
        <if test="SearchPojo.projectcode != null and SearchPojo.projectcode != ''">
            or PiProject.ProjectCode like concat('%', #{SearchPojo.projectcode}, '%')
        </if>
        <if test="SearchPojo.projectname != null and SearchPojo.projectname != ''">
            or PiProject.ProjectName like concat('%', #{SearchPojo.projectname}, '%')
        </if>
        <if test="SearchPojo.description != null and SearchPojo.description != ''">
            or PiProject.Description like concat('%', #{SearchPojo.description}, '%')
        </if>
        <if test="SearchPojo.frontphoto != null and SearchPojo.frontphoto != ''">
            or PiProject.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
        </if>
        <if test="SearchPojo.summary != null and SearchPojo.summary != ''">
            or PiProject.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or PiProject.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            or PiProject.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or PiProject.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            or PiProject.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        )
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into PiProject(id, ProjectCode, ProjectName, Description, FrontPhoto, EnabledMark, AllowDelete, RowNum,
                              Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision)
        values (#{id}, #{projectcode}, #{projectname}, #{description}, #{frontphoto}, #{enabledmark}, #{allowdelete},
                #{rownum}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate},
                #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiProject
        <set>
            <if test="projectcode != null ">
                ProjectCode =#{projectcode},
            </if>
            <if test="projectname != null ">
                ProjectName =#{projectname},
            </if>
            <if test="description != null ">
                Description =#{description},
            </if>
            <if test="frontphoto != null ">
                FrontPhoto =#{frontphoto},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="allowdelete != null">
                AllowDelete =#{allowdelete},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiProject
        where id = #{key}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.system.domain.pojo.PiprojectPojo">
        select
        id
        from PiProjectItem
        where Pid = #{id} and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                ''
            </if>
        </foreach>
    </select>

</mapper>

