<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PiscmfunctmenuwebMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PiscmfunctmenuwebPojo">
        select id,
        ScmFunctid,
        ScmFunctCode,
        ScmFunctName,
        Navid,
        NavCode,
        NavName,
        Remark,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        Listerid,
        ModifyDate,
        Revision
        from PiScmFunctMenuWeb
        where PiScmFunctMenuWeb.id = #{key}
    </select>
    <sql id="selectPiscmfunctmenuwebVo">
        select id,
               ScmFunctid,
               ScmFunctCode,
               ScmFunctName,
               <PERSON>vid,
               NavCode,
               NavName,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Revision
        from PiScmFunctMenuWeb
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PiscmfunctmenuwebPojo">
        <include refid="selectPiscmfunctmenuwebVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and PiScmFunctMenuWeb.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.scmfunctid != null">
            and PiScmFunctMenuWeb.ScmFunctid like concat('%', #{SearchPojo.scmfunctid}, '%')
        </if>
        <if test="SearchPojo.scmfunctcode != null">
            and PiScmFunctMenuWeb.ScmFunctCode like concat('%', #{SearchPojo.scmfunctcode}, '%')
        </if>
        <if test="SearchPojo.scmfunctname != null">
            and PiScmFunctMenuWeb.ScmFunctName like concat('%', #{SearchPojo.scmfunctname}, '%')
        </if>
        <if test="SearchPojo.navid != null">
            and PiScmFunctMenuWeb.Navid like concat('%', #{SearchPojo.navid}, '%')
        </if>
        <if test="SearchPojo.navcode != null">
            and PiScmFunctMenuWeb.NavCode like concat('%', #{SearchPojo.navcode}, '%')
        </if>
        <if test="SearchPojo.navname != null">
            and PiScmFunctMenuWeb.NavName like concat('%', #{SearchPojo.navname}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and PiScmFunctMenuWeb.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and PiScmFunctMenuWeb.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and PiScmFunctMenuWeb.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and PiScmFunctMenuWeb.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and PiScmFunctMenuWeb.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.scmfunctid != null">
                or PiScmFunctMenuWeb.ScmFunctid like concat('%', #{SearchPojo.scmfunctid}, '%')
            </if>
            <if test="SearchPojo.scmfunctcode != null">
                or PiScmFunctMenuWeb.ScmFunctCode like concat('%', #{SearchPojo.scmfunctcode}, '%')
            </if>
            <if test="SearchPojo.scmfunctname != null">
                or PiScmFunctMenuWeb.ScmFunctName like concat('%', #{SearchPojo.scmfunctname}, '%')
            </if>
            <if test="SearchPojo.navid != null">
                or PiScmFunctMenuWeb.Navid like concat('%', #{SearchPojo.navid}, '%')
            </if>
            <if test="SearchPojo.navcode != null">
                or PiScmFunctMenuWeb.NavCode like concat('%', #{SearchPojo.navcode}, '%')
            </if>
            <if test="SearchPojo.navname != null">
                or PiScmFunctMenuWeb.NavName like concat('%', #{SearchPojo.navname}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or PiScmFunctMenuWeb.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or PiScmFunctMenuWeb.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or PiScmFunctMenuWeb.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or PiScmFunctMenuWeb.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or PiScmFunctMenuWeb.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiScmFunctMenuWeb(id, ScmFunctid, ScmFunctCode, ScmFunctName, Navid, NavCode, NavName, Remark,
        CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision)
        values (#{id}, #{scmfunctid}, #{scmfunctcode}, #{scmfunctname}, #{navid}, #{navcode}, #{navname}, #{remark},
        #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiScmFunctMenuWeb
        <set>
            <if test="scmfunctid != null">
                ScmFunctid =#{scmfunctid},
            </if>
            <if test="scmfunctcode != null">
                ScmFunctCode =#{scmfunctcode},
            </if>
            <if test="scmfunctname != null">
                ScmFunctName =#{scmfunctname},
            </if>
            <if test="navid != null">
                Navid =#{navid},
            </if>
            <if test="navcode != null">
                NavCode =#{navcode},
            </if>
            <if test="navname != null">
                NavName =#{navname},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiScmFunctMenuWeb
        where id = #{key}
    </delete>

    <select id="getListByFunction" resultType="inks.system.domain.pojo.PiscmfunctmenuwebPojo">
        SELECT
        PiMenuWeb.NavPid,
        PiMenuWeb.NavType,
        PiScmFunctMenuWeb.id,
        PiScmFunctMenuWeb.ScmFunctid,
        PiScmFunctMenuWeb.ScmFunctCode,
        PiScmFunctMenuWeb.ScmFunctName,
        PiScmFunctMenuWeb.Navid,
        PiScmFunctMenuWeb.NavCode,
        PiScmFunctMenuWeb.NavName,
        PiScmFunctMenuWeb.Remark,
        PiScmFunctMenuWeb.CreateBy,
        PiScmFunctMenuWeb.CreateByid,
        PiScmFunctMenuWeb.CreateDate,
        PiScmFunctMenuWeb.Lister,
        PiScmFunctMenuWeb.Listerid,
        PiScmFunctMenuWeb.ModifyDate,
        PiScmFunctMenuWeb.Revision
        FROM
        PiMenuWeb
        RIGHT JOIN PiScmFunctMenuWeb ON PiMenuWeb.Navid = PiScmFunctMenuWeb.Navid
        where 1 = 1 and PiScmFunctMenuWeb.ScmFunctid =#{key}
        order by PiScmFunctMenuWeb.NavCode
    </select>

    <select id="getListByLoginUser" parameterType="inks.common.core.domain.LoginUser"
            resultType="inks.system.domain.pojo.PimenuwebPojo">
        SELECT DISTINCT PiMenuWeb.Navid,
        PiMenuWeb.NavPid,
        PiMenuWeb.NavType,
        PiMenuWeb.NavCode,
        PiMenuWeb.NavName,
        PiMenuWeb.NavGroup,
        PiMenuWeb.RowNum,
        PiMenuWeb.ImageCss,
        PiMenuWeb.IconUrl,
        PiMenuWeb.NavigateUrl,
        PiMenuWeb.MvcUrl,
        PiMenuWeb.ModuleType,
        PiMenuWeb.ModuleCode,
        PiMenuWeb.RoleCode,
        PiMenuWeb.ImageIndex,
        PiMenuWeb.ImageStyle,
        PiMenuWeb.EnabledMark,
        PiMenuWeb.Remark,
        PiMenuWeb.PermissionCode,
        PiMenuWeb.Functionid,
        PiMenuWeb.FunctionCode,
        PiMenuWeb.FunctionName,
        PiMenuWeb.IsMicroApp,
        PiMenuWeb.MicroAppName,
        PiMenuWeb.MicroAppEntry,
        PiMenuWeb.MicroAppRule,
        PiMenuWeb.MicroAppLocal,
        PiMenuWeb.Lister,
        PiMenuWeb.CreateDate,
        PiMenuWeb.ModifyDate,
        PiMenuWeb.DeleteMark,
        PiMenuWeb.DeleteLister,
        PiMenuWeb.DeleteDate
        FROM PiMenuWeb
        LEFT JOIN PiScmFunctMenuWeb ON PiScmFunctMenuWeb.Navid = PiMenuWeb.Navid
        WHERE PiScmFunctMenuWeb.ScmFunctid in (select ScmFunctids
        FROM PiTenantScmUser
        WHERE PiTenantScmUser.Userid = #{userid}
        and PiTenantScmUser.Tenantid = #{tenantid})
        order by RowNum
    </select>
    <select id="getListByScmFunctids" resultType="inks.system.domain.pojo.PimenuwebPojo">
        SELECT DISTINCT PiMenuWeb.Navid,
        PiMenuWeb.NavPid,
        PiMenuWeb.NavType,
        PiMenuWeb.NavCode,
        PiMenuWeb.NavName,
        PiMenuWeb.NavGroup,
        PiMenuWeb.RowNum,
        PiMenuWeb.ImageCss,
        PiMenuWeb.IconUrl,
        PiMenuWeb.NavigateUrl,
        PiMenuWeb.MvcUrl,
        PiMenuWeb.ModuleType,
        PiMenuWeb.ModuleCode,
        PiMenuWeb.RoleCode,
        PiMenuWeb.ImageIndex,
        PiMenuWeb.ImageStyle,
        PiMenuWeb.EnabledMark,
        PiMenuWeb.Remark,
        PiMenuWeb.PermissionCode,
        PiMenuWeb.Functionid,
        PiMenuWeb.FunctionCode,
        PiMenuWeb.FunctionName,
        PiMenuWeb.IsMicroApp,
        PiMenuWeb.MicroAppName,
        PiMenuWeb.MicroAppEntry,
        PiMenuWeb.MicroAppRule,
        PiMenuWeb.MicroAppLocal,
        PiMenuWeb.Lister,
        PiMenuWeb.CreateDate,
        PiMenuWeb.ModifyDate,
        PiMenuWeb.DeleteMark,
        PiMenuWeb.DeleteLister,
        PiMenuWeb.DeleteDate
        FROM PiMenuWeb
        LEFT JOIN PiScmFunctMenuWeb ON PiScmFunctMenuWeb.Navid = PiMenuWeb.Navid
        WHERE PiScmFunctMenuWeb.ScmFunctid in (${ids})
        order by RowNum
    </select>
</mapper>

