<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CiinitsaleMapper">

    <!--通过主键删除-->
    <delete id="deleteMachItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Bus_MachiningItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Bus_Machining
                      where Tenantid = #{tenantid}
                        and Bus_Machining.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deleteMach" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Bus_Machining
        where Tenantid = #{tenantid}
          and Bus_Machining.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>

    <!--通过主键删除-->
    <delete id="deleteDeli" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Bus_Deliery
        where Tenantid = #{tenantid}
          and Bus_Deliery.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>
    <!--通过主键删除-->
    <delete id="deleteDeliItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Bus_DelieryItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Bus_Deliery
                      where Tenantid = #{tenantid}
                        and Bus_Deliery.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>

    <!--通过主键删除-->
    <delete id="deleteInvo" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Bus_Invoice
        where Tenantid = #{tenantid}
          and Bus_Invoice.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>
    <!--通过主键删除-->
    <delete id="deleteInvoItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Bus_InvoiceItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Bus_Invoice
                      where Tenantid = #{tenantid}
                        and Bus_Invoice.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deleteDepo" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Bus_Deposit
        where Tenantid = #{tenantid}
          and Bus_Deposit.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>
    <!--通过主键删除-->
    <delete id="deleteDepoItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Bus_DepositItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Bus_Deposit
                      where Tenantid = #{tenantid}
                        and Bus_Deposit.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deleteDepoCash" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Bus_DepositCash
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Bus_Deposit
                      where Tenantid = #{tenantid}
                        and Bus_Deposit.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>

    <!--通过主键删除-->
    <delete id="deleteRece" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Bus_Receipt
        where Tenantid = #{tenantid}
          and Bus_Receipt.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>
    <!--通过主键删除-->
    <delete id="deleteReceItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Bus_ReceiptItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Bus_Receipt
                      where Tenantid = #{tenantid}
                        and Bus_Receipt.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deleteReceCash" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Bus_ReceiptCash
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Bus_Receipt
                      where Tenantid = #{tenantid}
                        and Bus_Receipt.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deleteDedu" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Bus_Deduction
        where Tenantid = #{tenantid}
          and Bus_Deduction.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>
    <!--通过主键删除-->
    <delete id="deleteDeduItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Bus_DeductionItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Bus_Deduction
                      where Tenantid = #{tenantid}
                        and Bus_Deduction.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>

    <!--通过主键删除-->
    <delete id="deleteAcco" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Bus_Account
        where Tenantid = #{tenantid}
          and Bus_Account.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>
    <!--通过主键删除-->
    <delete id="deleteAccoItem" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Bus_AccountItem
        where Tenantid = #{tenantid}
          and Pid in (Select id
                      from Bus_Account
                      where Tenantid = #{tenantid}
                        and Bus_Account.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate})
    </delete>
    <!--通过主键删除-->
    <delete id="deleteAccoRec" parameterType="inks.common.core.domain.QueryParam">
        delete
        from Bus_AccountRec
        where Tenantid = #{tenantid}
          and Bus_AccountRec.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
    </delete>
</mapper>

