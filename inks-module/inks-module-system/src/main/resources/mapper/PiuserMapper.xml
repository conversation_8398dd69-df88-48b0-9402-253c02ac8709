<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PiuserMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PiuserPojo">
        select Userid,
               UserName,
               RealName,
               NickName,
               Mobile,
               Email,
               Sex,
               LangCode,
               Avatar,
               UserStatus,
               UserCode,
               Remark,
               Lister,
               CreateDate,
               ModifyDate,
               CreateBy,
               Revision
        from PiUser
        where PiUser.Userid = #{key}
    </select>
    <sql id="selectPiuserVo">
        select Userid,
               UserName,
               RealName,
               NickName,
               Mobile,
               Email,
               Sex,
               LangCode,
               Avatar,
               UserStatus,
               UserCode,
               Remark,
               Lister,
               CreateDate,
               ModifyDate,
               <PERSON>reate<PERSON>y,
               Revision
        from PiUser
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PiuserPojo">
        <include refid="selectPiuserVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiUser.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.username != null and SearchPojo.username  != ''">
            and PiUser.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null and SearchPojo.realname  != ''">
            and PiUser.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.nickname != null and SearchPojo.nickname  != ''">
            and PiUser.NickName like concat('%', #{SearchPojo.nickname}, '%')
        </if>
        <if test="SearchPojo.mobile != null and SearchPojo.mobile  != ''">
            and PiUser.Mobile like concat('%', #{SearchPojo.mobile}, '%')
        </if>
        <if test="SearchPojo.email != null and SearchPojo.email  != ''">
            and PiUser.Email like concat('%', #{SearchPojo.email}, '%')
        </if>
        <if test="SearchPojo.langcode != null and SearchPojo.langcode  != ''">
            and PiUser.LangCode like concat('%', #{SearchPojo.langcode}, '%')
        </if>
        <if test="SearchPojo.avatar != null and SearchPojo.avatar  != ''">
            and PiUser.Avatar like concat('%', #{SearchPojo.avatar}, '%')
        </if>
        <if test="SearchPojo.usercode != null and SearchPojo.usercode  != ''">
            and PiUser.UserCode like concat('%', #{SearchPojo.usercode}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark  != ''">
            and PiUser.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and PiUser.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.username != null and SearchPojo.username != ''">
            or PiUser.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null and SearchPojo.realname != ''">
            or PiUser.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.nickname != null and SearchPojo.nickname != ''">
            or PiUser.NickName like concat('%', #{SearchPojo.nickname}, '%')
        </if>
        <if test="SearchPojo.mobile != null and SearchPojo.mobile != ''">
            or PiUser.Mobile like concat('%', #{SearchPojo.mobile}, '%')
        </if>
        <if test="SearchPojo.email != null and SearchPojo.email != ''">
            or PiUser.Email like concat('%', #{SearchPojo.email}, '%')
        </if>
        <if test="SearchPojo.langcode != null and SearchPojo.langcode != ''">
            or PiUser.LangCode like concat('%', #{SearchPojo.langcode}, '%')
        </if>
        <if test="SearchPojo.avatar != null and SearchPojo.avatar != ''">
            or PiUser.Avatar like concat('%', #{SearchPojo.avatar}, '%')
        </if>
        <if test="SearchPojo.usercode != null and SearchPojo.usercode != ''">
            or PiUser.UserCode like concat('%', #{SearchPojo.usercode}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or PiUser.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or PiUser.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiUser(Userid, UserName, RealName, NickName, Mobile, Email, Sex, LangCode, Avatar, UserStatus,
                           UserCode, Remark, Lister, CreateDate, ModifyDate, CreateBy, Revision)
        values (#{userid}, #{username}, #{realname}, #{nickname}, #{mobile}, #{email}, #{sex}, #{langcode}, #{avatar},
                #{userstatus}, #{usercode}, #{remark}, #{lister}, #{createdate}, #{modifydate}, #{createby},
                #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiUser
        <set>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="nickname != null ">
                NickName =#{nickname},
            </if>
            <if test="mobile != null ">
                Mobile =#{mobile},
            </if>
            <if test="email != null ">
                Email =#{email},
            </if>
            <if test="sex != null">
                Sex =#{sex},
            </if>
            <if test="langcode != null ">
                LangCode =#{langcode},
            </if>
            <if test="avatar != null ">
                Avatar =#{avatar},
            </if>
            <if test="userstatus != null">
                UserStatus =#{userstatus},
            </if>
            <if test="usercode != null ">
                UserCode =#{usercode},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            Revision=Revision+1
        </set>
        where Userid = #{userid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiUser
        where Userid = #{key}
    </delete>
    <!--查询单个-->
    <select id="getEntityByUserName" resultType="inks.system.domain.pojo.PiuserPojo">
        select Userid,
               UserName,
               RealName,
               NickName,
               Mobile,
               Email,
               Sex,
               LangCode,
               Avatar,
               UserStatus,
               UserCode,
               Remark,
               Lister,
               CreateDate,
               ModifyDate,
               CreateBy,
               Revision
        from PiUser
        where PiUser.UserName = #{key} LIMIT 1
    </select>

    <select id="getUseridsByTid" resultType="java.lang.String">
        select distinct Userid from PiTenantUser where Tenantid = #{tid}
    </select>
</mapper>

