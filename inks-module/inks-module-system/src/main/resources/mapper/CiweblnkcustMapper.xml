<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CiweblnkcustMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CiweblnkcustPojo">
        <include refid="selectCiweblnkcustVo"/>
        where CiWebLnkCust.id = #{key} and CiWebLnkCust.Tenantid=#{tid}
    </select>
    <sql id="selectCiweblnkcustVo">
         select
id, Userid, RealName, LnkContent, FnCode, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision        from CiWebLnkCust
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CiweblnkcustPojo">
        <include refid="selectCiweblnkcustVo"/>
        where 1 = 1 and CiWebLnkCust.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiWebLnkCust.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.userid != null ">
            and CiWebLnkCust.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.realname != null ">
            and CiWebLnkCust.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.lnkcontent != null ">
            and CiWebLnkCust.LnkContent like concat('%', #{SearchPojo.lnkcontent}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and CiWebLnkCust.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and CiWebLnkCust.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and CiWebLnkCust.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and CiWebLnkCust.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and CiWebLnkCust.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and CiWebLnkCust.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and CiWebLnkCust.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and CiWebLnkCust.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and CiWebLnkCust.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and CiWebLnkCust.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.userid != null ">
                or CiWebLnkCust.Userid like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.realname != null ">
                or CiWebLnkCust.RealName like concat('%', #{SearchPojo.realname}, '%')
            </if>
            <if test="SearchPojo.lnkcontent != null ">
                or CiWebLnkCust.LnkContent like concat('%', #{SearchPojo.lnkcontent}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or CiWebLnkCust.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or CiWebLnkCust.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or CiWebLnkCust.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or CiWebLnkCust.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or CiWebLnkCust.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or CiWebLnkCust.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or CiWebLnkCust.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or CiWebLnkCust.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or CiWebLnkCust.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or CiWebLnkCust.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into CiWebLnkCust(id, Userid, RealName, LnkContent, FnCode, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{userid}, #{realname}, #{lnkcontent}, #{fncode}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiWebLnkCust
        <set>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="lnkcontent != null ">
                LnkContent =#{lnkcontent},
            </if>
            <if test="fncode != null ">
                FnCode =#{fncode},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiWebLnkCust
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <select id="getEntityBySelf" resultType="inks.system.domain.pojo.CiweblnkcustPojo">
        <include refid="selectCiweblnkcustVo"/>
        where CiWebLnkCust.Userid = #{userid}
          and CiWebLnkCust.Tenantid = #{tid} limit 1
    </select>

    <select id="getListBySelf" resultType="inks.system.domain.pojo.CiweblnkcustPojo">
        <include refid="selectCiweblnkcustVo"/>
        where CiWebLnkCust.Userid = #{userid}
        and CiWebLnkCust.Tenantid = #{tenantid}
        <if test="fncode != null and fncode != ''">
            and CiWebLnkCust.FnCode = #{fncode}
        </if>
    </select>

</mapper>

