<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CidashboardMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CidashboardPojo">
        select id,
               GenGroupid,
               DashCode,
               DashName,
               DashDesc,
               FrontPhoto,
               MvcUrl,
               PermCode,
               EnabledMark,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Tenantid,
               TenantName,
               Revision
        from CiDashboard
        where CiDashboard.id = #{key}
          and CiDashboard.Tenantid = #{tid}
    </select>
    <sql id="selectCidashboardVo">
        select id,
               GenGroupid,
               DashCode,
               DashName,
               DashDesc,
               FrontPhoto,
               Mvc<PERSON>rl,
               PermCode,
               EnabledMark,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Tenantid,
               TenantName,
               Revision
        from CiDashboard
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CidashboardPojo">
        <include refid="selectCidashboardVo"/>
        where 1 = 1 and CiDashboard.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiDashboard.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.gengroupid != null ">
            and CiDashboard.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
        </if>
        <if test="SearchPojo.dashcode != null ">
            and CiDashboard.DashCode like concat('%', #{SearchPojo.dashcode}, '%')
        </if>
        <if test="SearchPojo.dashname != null ">
            and CiDashboard.DashName like concat('%', #{SearchPojo.dashname}, '%')
        </if>
        <if test="SearchPojo.dashdesc != null ">
            and CiDashboard.DashDesc like concat('%', #{SearchPojo.dashdesc}, '%')
        </if>
        <if test="SearchPojo.frontphoto != null ">
            and CiDashboard.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
        </if>
        <if test="SearchPojo.mvcurl != null ">
            and CiDashboard.MvcUrl like concat('%', #{SearchPojo.mvcurl}, '%')
        </if>
        <if test="SearchPojo.permcode != null ">
            and CiDashboard.PermCode like concat('%', #{SearchPojo.permcode}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and CiDashboard.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and CiDashboard.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and CiDashboard.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and CiDashboard.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and CiDashboard.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and CiDashboard.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.gengroupid != null ">
                or CiDashboard.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
            </if>
            <if test="SearchPojo.dashcode != null ">
                or CiDashboard.DashCode like concat('%', #{SearchPojo.dashcode}, '%')
            </if>
            <if test="SearchPojo.dashname != null ">
                or CiDashboard.DashName like concat('%', #{SearchPojo.dashname}, '%')
            </if>
            <if test="SearchPojo.dashdesc != null ">
                or CiDashboard.DashDesc like concat('%', #{SearchPojo.dashdesc}, '%')
            </if>
            <if test="SearchPojo.frontphoto != null ">
                or CiDashboard.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
            </if>
            <if test="SearchPojo.mvcurl != null ">
                or CiDashboard.MvcUrl like concat('%', #{SearchPojo.mvcurl}, '%')
            </if>
            <if test="SearchPojo.permcode != null ">
                or CiDashboard.PermCode like concat('%', #{SearchPojo.permcode}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or CiDashboard.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or CiDashboard.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or CiDashboard.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or CiDashboard.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or CiDashboard.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or CiDashboard.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into CiDashboard(id, GenGroupid, DashCode, DashName, DashDesc, FrontPhoto, MvcUrl, PermCode, EnabledMark,
                                Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid,
                                TenantName, Revision)
        values (#{id}, #{gengroupid}, #{dashcode}, #{dashname}, #{dashdesc}, #{frontphoto}, #{mvcurl}, #{permcode},
                #{enabledmark}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid},
                #{modifydate}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiDashboard
        <set>
            <if test="gengroupid != null ">
                GenGroupid =#{gengroupid},
            </if>
            <if test="dashcode != null ">
                DashCode =#{dashcode},
            </if>
            <if test="dashname != null ">
                DashName =#{dashname},
            </if>
            <if test="dashdesc != null ">
                DashDesc =#{dashdesc},
            </if>
            <if test="frontphoto != null ">
                FrontPhoto =#{frontphoto},
            </if>
            <if test="mvcurl != null ">
                MvcUrl =#{mvcurl},
            </if>
            <if test="permcode != null ">
                PermCode =#{permcode},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiDashboard
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
</mapper>

