<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PirmsuserMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PirmsuserPojo">
        <include refid="selectPirmsuserVo"/>
        where PiRmsUser.Userid = #{key}
        and PiRmsUser.Tenantid = #{tid}
    </select>
    <sql id="selectPirmsuserVo">
        select PiRmsUser.Userid,
               PiRmsUser.UserName,
               PiRmsUser.RealName,
               PiRmsUser.NickName,
               PiRmsUser.UserPassword,
               PiRmsUser.Mobile,
               PiRmsUser.Email,
               PiRmsUser.Sex,
               PiRmsUser.LangCode,
               PiRmsUser.Avatar,
               PiRmsUser.UserType,
               PiRmsUser.IsAdmin,
               PiRmsUser.Deptid,
               PiRmsUser.DeptCode,
               PiRmsUser.DeptName,
               PiRmsUser.IsDeptAdmin,
               PiRmsUser.DeptRowNum,
               PiRmsUser.RowNum,
               PiRmsUser.UserStatus,
               PiRmsUser.UserCode,
               PiRmsUser.Groupids,
               PiRmsUser.GroupNames,
               PiRmsUser.RmsFunctids,
               PiRmsUser.RmsFunctNames,
               PiRmsUser.Remark,
               PiRmsUser.DataLabel,
               PiRmsUser.CreateBy,
               PiRmsUser.CreateByid,
               PiRmsUser.CreateDate,
               PiRmsUser.Lister,
               PiRmsUser.Listerid,
               PiRmsUser.ModifyDate,
               PiRmsUser.Tenantid,
               PiRmsUser.TenantName
        from PiRmsUser
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PirmsuserPojo">
        <include refid="selectPirmsuserVo"/>
        where PiRmsUser.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiRmsUser.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.username != null ">
            and PiRmsUser.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null ">
            and PiRmsUser.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.nickname != null ">
            and PiRmsUser.NickName like concat('%', #{SearchPojo.nickname}, '%')
        </if>
        <if test="SearchPojo.userpassword != null ">
            and PiRmsUser.UserPassword like concat('%', #{SearchPojo.userpassword}, '%')
        </if>
        <if test="SearchPojo.mobile != null ">
            and PiRmsUser.Mobile like concat('%', #{SearchPojo.mobile}, '%')
        </if>
        <if test="SearchPojo.email != null ">
            and PiRmsUser.Email like concat('%', #{SearchPojo.email}, '%')
        </if>
        <if test="SearchPojo.langcode != null ">
            and PiRmsUser.LangCode like concat('%', #{SearchPojo.langcode}, '%')
        </if>
        <if test="SearchPojo.avatar != null ">
            and PiRmsUser.Avatar like concat('%', #{SearchPojo.avatar}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and PiRmsUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.deptcode != null ">
            and PiRmsUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
        </if>
        <if test="SearchPojo.deptname != null ">
            and PiRmsUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
        </if>
        <if test="SearchPojo.usercode != null ">
            and PiRmsUser.UserCode like concat('%', #{SearchPojo.usercode}, '%')
        </if>
        <if test="SearchPojo.groupids != null ">
            and PiRmsUser.Groupids like concat('%', #{SearchPojo.groupids}, '%')
        </if>
        <if test="SearchPojo.groupnames != null ">
            and PiRmsUser.GroupNames like concat('%', #{SearchPojo.groupnames}, '%')
        </if>
        <if test="SearchPojo.rmsfunctids != null ">
            and PiRmsUser.RmsFunctids like concat('%', #{SearchPojo.rmsfunctids}, '%')
        </if>
        <if test="SearchPojo.rmsfunctnames != null ">
            and PiRmsUser.RmsFunctNames like concat('%', #{SearchPojo.rmsfunctnames}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and PiRmsUser.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and PiRmsUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and PiRmsUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and PiRmsUser.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and PiRmsUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and PiRmsUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.username != null ">
                or PiRmsUser.UserName like concat('%', #{SearchPojo.username}, '%')
            </if>
            <if test="SearchPojo.realname != null ">
                or PiRmsUser.RealName like concat('%', #{SearchPojo.realname}, '%')
            </if>
            <if test="SearchPojo.nickname != null ">
                or PiRmsUser.NickName like concat('%', #{SearchPojo.nickname}, '%')
            </if>
            <if test="SearchPojo.userpassword != null ">
                or PiRmsUser.UserPassword like concat('%', #{SearchPojo.userpassword}, '%')
            </if>
            <if test="SearchPojo.mobile != null ">
                or PiRmsUser.Mobile like concat('%', #{SearchPojo.mobile}, '%')
            </if>
            <if test="SearchPojo.email != null ">
                or PiRmsUser.Email like concat('%', #{SearchPojo.email}, '%')
            </if>
            <if test="SearchPojo.langcode != null ">
                or PiRmsUser.LangCode like concat('%', #{SearchPojo.langcode}, '%')
            </if>
            <if test="SearchPojo.avatar != null ">
                or PiRmsUser.Avatar like concat('%', #{SearchPojo.avatar}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or PiRmsUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.deptcode != null ">
                or PiRmsUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
            </if>
            <if test="SearchPojo.deptname != null ">
                or PiRmsUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
            </if>
            <if test="SearchPojo.usercode != null ">
                or PiRmsUser.UserCode like concat('%', #{SearchPojo.usercode}, '%')
            </if>
            <if test="SearchPojo.groupids != null ">
                or PiRmsUser.Groupids like concat('%', #{SearchPojo.groupids}, '%')
            </if>
            <if test="SearchPojo.groupnames != null ">
                or PiRmsUser.GroupNames like concat('%', #{SearchPojo.groupnames}, '%')
            </if>
            <if test="SearchPojo.rmsfunctids != null ">
                or PiRmsUser.RmsFunctids like concat('%', #{SearchPojo.rmsfunctids}, '%')
            </if>
            <if test="SearchPojo.rmsfunctnames != null ">
                or PiRmsUser.RmsFunctNames like concat('%', #{SearchPojo.rmsfunctnames}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or PiRmsUser.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or PiRmsUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or PiRmsUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or PiRmsUser.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or PiRmsUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or PiRmsUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiRmsUser(Userid, UserName, RealName, NickName, UserPassword, Mobile, Email, Sex, LangCode, Avatar,
                              UserType, IsAdmin, Deptid, DeptCode, DeptName, IsDeptAdmin, DeptRowNum, RowNum,
                              UserStatus, UserCode, Groupids, GroupNames, RmsFunctids, RmsFunctNames, Remark, CreateBy,
                              CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
        values (#{userid}, #{username}, #{realname}, #{nickname}, #{userpassword}, #{mobile}, #{email}, #{sex},
                #{langcode}, #{avatar}, #{usertype}, #{isadmin}, #{deptid}, #{deptcode}, #{deptname}, #{isdeptadmin},
                #{deptrownum}, #{rownum}, #{userstatus}, #{usercode}, #{groupids}, #{groupnames}, #{rmsfunctids},
                #{rmsfunctnames}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid},
                #{modifydate}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiRmsUser
        <set>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="nickname != null ">
                NickName =#{nickname},
            </if>
            <if test="userpassword != null ">
                UserPassword =#{userpassword},
            </if>
            <if test="mobile != null ">
                Mobile =#{mobile},
            </if>
            <if test="email != null ">
                Email =#{email},
            </if>
            <if test="sex != null">
                Sex =#{sex},
            </if>
            <if test="langcode != null ">
                LangCode =#{langcode},
            </if>
            <if test="avatar != null ">
                Avatar =#{avatar},
            </if>
            <if test="usertype != null">
                UserType =#{usertype},
            </if>
            <if test="isadmin != null">
                IsAdmin =#{isadmin},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="deptcode != null ">
                DeptCode =#{deptcode},
            </if>
            <if test="deptname != null ">
                DeptName =#{deptname},
            </if>
            <if test="isdeptadmin != null">
                IsDeptAdmin =#{isdeptadmin},
            </if>
            <if test="deptrownum != null">
                DeptRowNum =#{deptrownum},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="userstatus != null">
                UserStatus =#{userstatus},
            </if>
            <if test="usercode != null ">
                UserCode =#{usercode},
            </if>
            <if test="groupids != null ">
                Groupids =#{groupids},
            </if>
            <if test="groupnames != null ">
                GroupNames =#{groupnames},
            </if>
            <if test="rmsfunctids != null ">
                RmsFunctids =#{rmsfunctids},
            </if>
            <if test="rmsfunctnames != null ">
                RmsFunctNames =#{rmsfunctnames},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="datalabel != null ">
                DataLabel =#{datalabel},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where Userid = #{userid} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiRmsUser
        where Userid = #{key}
          and Tenantid = #{tid}
    </delete>
    <select id="getEntityByUserName" resultType="inks.system.domain.pojo.PirmsuserPojo">
        select Userid,
               UserName,
               RealName,
               NickName,
               UserPassword,
               Mobile,
               Email,
               Sex,
               LangCode,
               Avatar,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Revision
        from PiRmsUser
        where PiRmsUser.Mobile = #{username} or PiRmsUser.Email = #{username}
    </select>
    <select id="getPageListByTen" resultType="inks.system.domain.pojo.PirmsuserPojo">
        select PiRmsUser.Userid,
               PiRmsUser.UserName,
               PiRmsUser.RealName,
               PiRmsUser.NickName,
               PiRmsUser.UserPassword,
               PiRmsUser.Mobile,
               PiRmsUser.Email,
               PiRmsUser.Sex,
               PiRmsUser.LangCode,
               PiRmsUser.Avatar,
               PiRmsUser.Remark,
               PiRmsUser.CreateBy,
               PiRmsUser.CreateByid,
               PiRmsUser.CreateDate,
               PiRmsUser.Lister,
               PiRmsUser.Listerid,
               PiRmsUser.ModifyDate,
               PiRmsUser.Revision,
               PiTenantRmsUser.TenantId
        from PiRmsUser Left JOIN PiTenantRmsUser ON PiRmsUser.Userid = PiTenantRmsUser.Userid
        where 1=1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        <if test="tenantid != null and tenantid != ''">
            and PiTenantRmsUser.TenantId=#{tenantid}
        </if>
    </select>
    <select id="getEntityByOpenid" resultType="inks.system.domain.pojo.PirmsuserPojo">
        select PiRmsUser.Userid,
               PiRmsUser.UserName,
               PiRmsUser.RealName,
               PiRmsUser.NickName,
               PiRmsUser.UserPassword,
               PiRmsUser.Mobile,
               PiRmsUser.Email,
               PiRmsUser.Sex,
               PiRmsUser.LangCode,
               PiRmsUser.Avatar,
               PiRmsUser.UserType,
               PiRmsUser.IsAdmin,
               PiRmsUser.Deptid,
               PiRmsUser.DeptCode,
               PiRmsUser.DeptName,
               PiRmsUser.IsDeptAdmin,
               PiRmsUser.DeptRowNum,
               PiRmsUser.RowNum,
               PiRmsUser.UserStatus,
               PiRmsUser.UserCode,
               PiRmsUser.Groupids,
               PiRmsUser.GroupNames,
               PiRmsUser.RmsFunctids,
               PiRmsUser.RmsFunctNames,
               PiRmsUser.Remark,
               PiRmsUser.CreateBy,
               PiRmsUser.CreateByid,
               PiRmsUser.CreateDate,
               PiRmsUser.Lister,
               PiRmsUser.Listerid,
               PiRmsUser.ModifyDate,
               PiRmsUser.Tenantid,
               PiRmsUser.TenantName,
               PiRmsUser.Revision
        from PiRmsUser
                 Left Join inkssaas.PiRmsJustAuth on PiRmsJustAuth.Userid = PiRmsUser.Userid
        where PiRmsUser.Tenantid = #{tid}
          and PiRmsJustAuth.AuthType = 'openid'
          and PiRmsJustAuth.AuthUuid = #{openid}
    </select>
    <select id="getListByOpenid" resultType="inks.system.domain.pojo.PirmsuserPojo">
        select PiRmsUser.Userid,
               PiRmsUser.UserName,
               PiRmsUser.RealName,
               PiRmsUser.NickName,
               PiRmsUser.UserPassword,
               PiRmsUser.Mobile,
               PiRmsUser.Email,
               PiRmsUser.Sex,
               PiRmsUser.LangCode,
               PiRmsUser.Avatar,
               PiRmsUser.UserType,
               PiRmsUser.IsAdmin,
               PiRmsUser.Deptid,
               PiRmsUser.DeptCode,
               PiRmsUser.DeptName,
               PiRmsUser.IsDeptAdmin,
               PiRmsUser.DeptRowNum,
               PiRmsUser.RowNum,
               PiRmsUser.UserStatus,
               PiRmsUser.UserCode,
               PiRmsUser.Groupids,
               PiRmsUser.GroupNames,
               PiRmsUser.RmsFunctids,
               PiRmsUser.RmsFunctNames,
               PiRmsUser.Remark,
               PiRmsUser.CreateBy,
               PiRmsUser.CreateByid,
               PiRmsUser.CreateDate,
               PiRmsUser.Lister,
               PiRmsUser.Listerid,
               PiRmsUser.ModifyDate,
               PiRmsUser.Tenantid,
               PiRmsUser.TenantName,
               PiRmsUser.Revision
        from PiRmsUser
                 Left Join inkssaas.PiRmsJustAuth on PiRmsJustAuth.Userid = PiRmsUser.Userid
        where  PiRmsJustAuth.AuthType = 'openid'
          and PiRmsJustAuth.AuthUuid = #{openid}
    </select>
    <select id="getEntityByUserid" resultType="inks.system.domain.pojo.PirmsuserPojo">
        select * from PiRmsUser where Userid = #{userid} and Tenantid = #{tid}
    </select>

    <delete id="deletePiTenantRmsUser">
        delete from PiTenantRmsUser where Userid = #{userid} and Tenantid = #{tid}
    </delete>
</mapper>

