<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CivalidatorMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CivalidatorPojo">
        <include refid="selectCivalidatorVo"/>
        where CiValidator.id = #{key} and CiValidator.Tenantid=#{tid}
    </select>
    <sql id="selectCivalidatorVo">
         select
id, ValiCode, ValiTitle, SqlMark, SqlStr, Expression, TipMsg, TipMsgEn, RequiredMark, ItemLoopMark, EnabledMark, RowNum, Remark, CreateBy, CreateByid, <PERSON>reate<PERSON>ate, <PERSON>er, <PERSON><PERSON>d, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision        from CiValidator
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.CivalidatorPojo">
        <include refid="selectCivalidatorVo"/>
         where 1 = 1 and CiValidator.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and CiValidator.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.valicode != null ">
   and CiValidator.ValiCode like concat('%', #{SearchPojo.valicode}, '%')
</if>
<if test="SearchPojo.valititle != null ">
   and CiValidator.ValiTitle like concat('%', #{SearchPojo.valititle}, '%')
</if>
<if test="SearchPojo.sqlstr != null ">
   and CiValidator.SqlStr like concat('%', #{SearchPojo.sqlstr}, '%')
</if>
<if test="SearchPojo.expression != null ">
   and CiValidator.Expression like concat('%', #{SearchPojo.expression}, '%')
</if>
<if test="SearchPojo.tipmsg != null ">
   and CiValidator.TipMsg like concat('%', #{SearchPojo.tipmsg}, '%')
</if>
<if test="SearchPojo.tipmsgen != null ">
   and CiValidator.TipMsgEn like concat('%', #{SearchPojo.tipmsgen}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and CiValidator.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and CiValidator.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and CiValidator.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and CiValidator.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and CiValidator.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and CiValidator.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and CiValidator.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and CiValidator.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and CiValidator.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and CiValidator.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and CiValidator.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.valicode != null ">
   or CiValidator.ValiCode like concat('%', #{SearchPojo.valicode}, '%')
</if>
<if test="SearchPojo.valititle != null ">
   or CiValidator.ValiTitle like concat('%', #{SearchPojo.valititle}, '%')
</if>
<if test="SearchPojo.sqlstr != null ">
   or CiValidator.SqlStr like concat('%', #{SearchPojo.sqlstr}, '%')
</if>
<if test="SearchPojo.expression != null ">
   or CiValidator.Expression like concat('%', #{SearchPojo.expression}, '%')
</if>
<if test="SearchPojo.tipmsg != null ">
   or CiValidator.TipMsg like concat('%', #{SearchPojo.tipmsg}, '%')
</if>
<if test="SearchPojo.tipmsgen != null ">
   or CiValidator.TipMsgEn like concat('%', #{SearchPojo.tipmsgen}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or CiValidator.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or CiValidator.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or CiValidator.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or CiValidator.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or CiValidator.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or CiValidator.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or CiValidator.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or CiValidator.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or CiValidator.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or CiValidator.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or CiValidator.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into CiValidator(id, ValiCode, ValiTitle, SqlMark, SqlStr, Expression, TipMsg, TipMsgEn, RequiredMark, ItemLoopMark, EnabledMark, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{valicode}, #{valititle}, #{sqlmark}, #{sqlstr}, #{expression}, #{tipmsg}, #{tipmsgen}, #{requiredmark}, #{itemloopmark}, #{enabledmark}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update CiValidator
        <set>
            <if test="valicode != null ">
                ValiCode =#{valicode},
            </if>
            <if test="valititle != null ">
                ValiTitle =#{valititle},
            </if>
            <if test="sqlmark != null">
                SqlMark =#{sqlmark},
            </if>
            <if test="sqlstr != null ">
                SqlStr =#{sqlstr},
            </if>
            <if test="expression != null ">
                Expression =#{expression},
            </if>
            <if test="tipmsg != null ">
                TipMsg =#{tipmsg},
            </if>
            <if test="tipmsgen != null ">
                TipMsgEn =#{tipmsgen},
            </if>
            <if test="requiredmark != null">
                RequiredMark =#{requiredmark},
            </if>
            <if test="itemloopmark != null">
                ItemLoopMark =#{itemloopmark},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from CiValidator where id = #{key} and Tenantid=#{tid}
    </delete>

    <select id="getListByValicodeEnabled" resultType="inks.system.domain.pojo.CivalidatorPojo">
        <include refid="selectCivalidatorVo"/>
        where ValiCode = #{valicode} and Tenantid = #{tid}
        and EnabledMark = 1 order by RowNum
    </select>
</mapper>

