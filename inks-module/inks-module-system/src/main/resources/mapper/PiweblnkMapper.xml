<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PiweblnkMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PiweblnkPojo">
        select Navid,
               NavPid,
               NavType,
               NavCode,
               NavName,
               NavGroup,
               RowNum,
               ImageCss,
               IconUrl,
               NavigateUrl,
               MvcUrl,
               ModuleType,
               ModuleCode,
               RoleCode,
               ImageIndex,
               ImageStyle,
               EnabledMark,
               Remark,
               PermissionCode,
               Functionid,
               FunctionCode,
               FunctionName,
               Lister,
               CreateDate,
               ModifyDate,
               DeleteMark,
               DeleteLister,
               DeleteDate
        from PiWebLnk
        where PiWebLnk.Navid = #{key}
    </select>
    <sql id="selectPiweblnkVo">
        select Navid,
               NavPid,
               NavType,
               NavCode,
               NavName,
               NavGroup,
               RowNum,
               ImageCss,
               IconUrl,
               NavigateUrl,
               MvcUrl,
               ModuleType,
               ModuleCode,
               RoleCode,
               ImageIndex,
               ImageStyle,
               EnabledMark,
               Remark,
               PermissionCode,
               Functionid,
               FunctionCode,
               FunctionName,
               Lister,
               CreateDate,
               ModifyDate,
               DeleteMark,
               DeleteLister,
               DeleteDate
        from PiWebLnk
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PiweblnkPojo">
        <include refid="selectPiweblnkVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiWebLnk.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.navpid != null ">
            and PiWebLnk.NavPid like concat('%', #{SearchPojo.navpid}, '%')
        </if>
        <if test="SearchPojo.navtype != null ">
            and PiWebLnk.NavType like concat('%', #{SearchPojo.navtype}, '%')
        </if>
        <if test="SearchPojo.navcode != null ">
            and PiWebLnk.NavCode like concat('%', #{SearchPojo.navcode}, '%')
        </if>
        <if test="SearchPojo.navname != null ">
            and PiWebLnk.NavName like concat('%', #{SearchPojo.navname}, '%')
        </if>
        <if test="SearchPojo.navgroup != null ">
            and PiWebLnk.NavGroup like concat('%', #{SearchPojo.navgroup}, '%')
        </if>
        <if test="SearchPojo.imagecss != null ">
            and PiWebLnk.ImageCss like concat('%', #{SearchPojo.imagecss}, '%')
        </if>
        <if test="SearchPojo.iconurl != null ">
            and PiWebLnk.IconUrl like concat('%', #{SearchPojo.iconurl}, '%')
        </if>
        <if test="SearchPojo.navigateurl != null ">
            and PiWebLnk.NavigateUrl like concat('%', #{SearchPojo.navigateurl}, '%')
        </if>
        <if test="SearchPojo.mvcurl != null ">
            and PiWebLnk.MvcUrl like concat('%', #{SearchPojo.mvcurl}, '%')
        </if>
        <if test="SearchPojo.moduletype != null ">
            and PiWebLnk.ModuleType like concat('%', #{SearchPojo.moduletype}, '%')
        </if>
        <if test="SearchPojo.modulecode != null ">
            and PiWebLnk.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.rolecode != null ">
            and PiWebLnk.RoleCode like concat('%', #{SearchPojo.rolecode}, '%')
        </if>
        <if test="SearchPojo.imageindex != null ">
            and PiWebLnk.ImageIndex like concat('%', #{SearchPojo.imageindex}, '%')
        </if>
        <if test="SearchPojo.imagestyle != null ">
            and PiWebLnk.ImageStyle like concat('%', #{SearchPojo.imagestyle}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and PiWebLnk.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.permissioncode != null ">
            and PiWebLnk.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
        </if>
        <if test="SearchPojo.functionid != null ">
            and PiWebLnk.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null ">
            and PiWebLnk.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null ">
            and PiWebLnk.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and PiWebLnk.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.deletelister != null ">
            and PiWebLnk.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.navpid != null ">
                or PiWebLnk.NavPid like concat('%', #{SearchPojo.navpid}, '%')
            </if>
            <if test="SearchPojo.navtype != null ">
                or PiWebLnk.NavType like concat('%', #{SearchPojo.navtype}, '%')
            </if>
            <if test="SearchPojo.navcode != null ">
                or PiWebLnk.NavCode like concat('%', #{SearchPojo.navcode}, '%')
            </if>
            <if test="SearchPojo.navname != null ">
                or PiWebLnk.NavName like concat('%', #{SearchPojo.navname}, '%')
            </if>
            <if test="SearchPojo.navgroup != null ">
                or PiWebLnk.NavGroup like concat('%', #{SearchPojo.navgroup}, '%')
            </if>
            <if test="SearchPojo.imagecss != null ">
                or PiWebLnk.ImageCss like concat('%', #{SearchPojo.imagecss}, '%')
            </if>
            <if test="SearchPojo.iconurl != null ">
                or PiWebLnk.IconUrl like concat('%', #{SearchPojo.iconurl}, '%')
            </if>
            <if test="SearchPojo.navigateurl != null ">
                or PiWebLnk.NavigateUrl like concat('%', #{SearchPojo.navigateurl}, '%')
            </if>
            <if test="SearchPojo.mvcurl != null ">
                or PiWebLnk.MvcUrl like concat('%', #{SearchPojo.mvcurl}, '%')
            </if>
            <if test="SearchPojo.moduletype != null ">
                or PiWebLnk.ModuleType like concat('%', #{SearchPojo.moduletype}, '%')
            </if>
            <if test="SearchPojo.modulecode != null ">
                or PiWebLnk.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.rolecode != null ">
                or PiWebLnk.RoleCode like concat('%', #{SearchPojo.rolecode}, '%')
            </if>
            <if test="SearchPojo.imageindex != null ">
                or PiWebLnk.ImageIndex like concat('%', #{SearchPojo.imageindex}, '%')
            </if>
            <if test="SearchPojo.imagestyle != null ">
                or PiWebLnk.ImageStyle like concat('%', #{SearchPojo.imagestyle}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or PiWebLnk.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.permissioncode != null ">
                or PiWebLnk.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
            </if>
            <if test="SearchPojo.functionid != null ">
                or PiWebLnk.Functionid like concat('%', #{SearchPojo.functionid}, '%')
            </if>
            <if test="SearchPojo.functioncode != null ">
                or PiWebLnk.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
            </if>
            <if test="SearchPojo.functionname != null ">
                or PiWebLnk.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or PiWebLnk.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.deletelister != null ">
                or PiWebLnk.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiWebLnk(Navid, NavPid, NavType, NavCode, NavName, NavGroup, RowNum, ImageCss, IconUrl, NavigateUrl,
                             MvcUrl, ModuleType, ModuleCode, RoleCode, ImageIndex, ImageStyle, EnabledMark, Remark,
                             PermissionCode, Functionid, FunctionCode, FunctionName, Lister, CreateDate, ModifyDate,
                             DeleteMark, DeleteLister, DeleteDate)
        values (#{navid}, #{navpid}, #{navtype}, #{navcode}, #{navname}, #{navgroup}, #{rownum}, #{imagecss},
                #{iconurl}, #{navigateurl}, #{mvcurl}, #{moduletype}, #{modulecode}, #{rolecode}, #{imageindex},
                #{imagestyle}, #{enabledmark}, #{remark}, #{permissioncode}, #{functionid}, #{functioncode},
                #{functionname}, #{lister}, #{createdate}, #{modifydate}, #{deletemark}, #{deletelister}, #{deletedate})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiWebLnk
        <set>
            <if test="navpid != null ">
                NavPid =#{navpid},
            </if>
            <if test="navtype != null ">
                NavType =#{navtype},
            </if>
            <if test="navcode != null ">
                NavCode =#{navcode},
            </if>
            <if test="navname != null ">
                NavName =#{navname},
            </if>
            <if test="navgroup != null ">
                NavGroup =#{navgroup},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="imagecss != null ">
                ImageCss =#{imagecss},
            </if>
            <if test="iconurl != null ">
                IconUrl =#{iconurl},
            </if>
            <if test="navigateurl != null ">
                NavigateUrl =#{navigateurl},
            </if>
            <if test="mvcurl != null ">
                MvcUrl =#{mvcurl},
            </if>
            <if test="moduletype != null ">
                ModuleType =#{moduletype},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="rolecode != null ">
                RoleCode =#{rolecode},
            </if>
            <if test="imageindex != null ">
                ImageIndex =#{imageindex},
            </if>
            <if test="imagestyle != null ">
                ImageStyle =#{imagestyle},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="permissioncode != null ">
                PermissionCode =#{permissioncode},
            </if>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
        </set>
        where Navid = #{navid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiWebLnk
        where Navid = #{key}
    </delete>
    <!--查询单个-->
    <select id="getListByPid" resultType="inks.system.domain.pojo.PiweblnkPojo">
        select Navid,
               NavPid,
               NavType,
               NavCode,
               NavName,
               NavGroup,
               RowNum,
               ImageCss,
               IconUrl,
               NavigateUrl,
               MvcUrl,
               ModuleType,
               ModuleCode,
               RoleCode,
               ImageIndex,
               ImageStyle,
               EnabledMark,
               Remark,
               PermissionCode,
               Functionid,
               FunctionCode,
               FunctionName,
               Lister,
               CreateDate,
               ModifyDate,
               DeleteMark,
               DeleteLister,
               DeleteDate
        from PiWebLnk
        where PiWebLnk.NavPid = #{key}
        Order by RowNum
    </select>

</mapper>

