<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PiuseronlineMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PiuseronlinePojo">
        <include refid="selectPiuseronlineVo"/>
        where PiUserOnline.id = #{key} and PiUserOnline.Tenantid=#{tid}
    </select>
    <sql id="selectPiuseronlineVo">
         select
id, Userid, UserName, RealName, NickName, Tenantid, TenantName, IpAddress, IpLocation, MacAddress, BrowserName, HostSystem, TerminalType, Token, LoginDate, LastActivityDate, TokenExpiryDate        from PiUserOnline
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.PiuseronlinePojo">
        <include refid="selectPiuseronlineVo"/>
         where 1 = 1 and PiUserOnline.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and PiUserOnline.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.userid != null ">
   and PiUserOnline.Userid like concat('%', #{SearchPojo.userid}, '%')
</if>
<if test="SearchPojo.username != null ">
   and PiUserOnline.UserName like concat('%', #{SearchPojo.username}, '%')
</if>
<if test="SearchPojo.realname != null ">
   and PiUserOnline.RealName like concat('%', #{SearchPojo.realname}, '%')
</if>
<if test="SearchPojo.nickname != null ">
   and PiUserOnline.NickName like concat('%', #{SearchPojo.nickname}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and PiUserOnline.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
<if test="SearchPojo.ipaddress != null ">
   and PiUserOnline.IpAddress like concat('%', #{SearchPojo.ipaddress}, '%')
</if>
<if test="SearchPojo.iplocation != null ">
   and PiUserOnline.IpLocation like concat('%', #{SearchPojo.iplocation}, '%')
</if>
<if test="SearchPojo.macaddress != null ">
   and PiUserOnline.MacAddress like concat('%', #{SearchPojo.macaddress}, '%')
</if>
<if test="SearchPojo.browsername != null ">
   and PiUserOnline.BrowserName like concat('%', #{SearchPojo.browsername}, '%')
</if>
<if test="SearchPojo.hostsystem != null ">
   and PiUserOnline.HostSystem like concat('%', #{SearchPojo.hostsystem}, '%')
</if>
<if test="SearchPojo.token != null ">
   and PiUserOnline.Token like concat('%', #{SearchPojo.token}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.userid != null ">
   or PiUserOnline.Userid like concat('%', #{SearchPojo.userid}, '%')
</if>
<if test="SearchPojo.username != null ">
   or PiUserOnline.UserName like concat('%', #{SearchPojo.username}, '%')
</if>
<if test="SearchPojo.realname != null ">
   or PiUserOnline.RealName like concat('%', #{SearchPojo.realname}, '%')
</if>
<if test="SearchPojo.nickname != null ">
   or PiUserOnline.NickName like concat('%', #{SearchPojo.nickname}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or PiUserOnline.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
<if test="SearchPojo.ipaddress != null ">
   or PiUserOnline.IpAddress like concat('%', #{SearchPojo.ipaddress}, '%')
</if>
<if test="SearchPojo.iplocation != null ">
   or PiUserOnline.IpLocation like concat('%', #{SearchPojo.iplocation}, '%')
</if>
<if test="SearchPojo.macaddress != null ">
   or PiUserOnline.MacAddress like concat('%', #{SearchPojo.macaddress}, '%')
</if>
<if test="SearchPojo.browsername != null ">
   or PiUserOnline.BrowserName like concat('%', #{SearchPojo.browsername}, '%')
</if>
<if test="SearchPojo.hostsystem != null ">
   or PiUserOnline.HostSystem like concat('%', #{SearchPojo.hostsystem}, '%')
</if>
<if test="SearchPojo.token != null ">
   or PiUserOnline.Token like concat('%', #{SearchPojo.token}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into PiUserOnline(id, Userid, UserName, RealName, NickName, Tenantid, TenantName, IpAddress, IpLocation, MacAddress, BrowserName, HostSystem, TerminalType, Token, LoginDate, LastActivityDate, TokenExpiryDate)
        values (#{id}, #{userid}, #{username}, #{realname}, #{nickname}, #{tenantid}, #{tenantname}, #{ipaddress}, #{iplocation}, #{macaddress}, #{browsername}, #{hostsystem}, #{terminaltype}, #{token}, #{logindate}, #{lastactivitydate}, #{tokenexpirydate})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update PiUserOnline
        <set>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="nickname != null ">
                NickName =#{nickname},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            <if test="ipaddress != null ">
                IpAddress =#{ipaddress},
            </if>
            <if test="iplocation != null ">
                IpLocation =#{iplocation},
            </if>
            <if test="macaddress != null ">
                MacAddress =#{macaddress},
            </if>
            <if test="browsername != null ">
                BrowserName =#{browsername},
            </if>
            <if test="hostsystem != null ">
                HostSystem =#{hostsystem},
            </if>
            <if test="terminaltype != null">
                TerminalType =#{terminaltype},
            </if>
            <if test="token != null ">
                Token =#{token},
            </if>
            <if test="logindate != null">
                LoginDate =#{logindate},
            </if>
            <if test="lastactivitydate != null">
                LastActivityDate =#{lastactivitydate},
            </if>
            <if test="tokenexpirydate != null">
                TokenExpiryDate =#{tokenexpirydate},
            </if>
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from PiUserOnline where id = #{key} and Tenantid=#{tid}
    </delete>

    <select id="getAllTokens" resultType="java.lang.String">
        select Token from PiUserOnline where Userid = #{userid} and Tenantid =#{tid}
    </select>
</mapper>

