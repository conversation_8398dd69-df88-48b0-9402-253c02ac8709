<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PidmsfunctmenuappMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PidmsfunctmenuappPojo">
        select id,
               DmsFunctid,
               DmsFunctCode,
               DmsFunctName,
               Navid,
               NavCode,
               NavName,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Revision
        from PiDmsFunctMenuApp
        where PiDmsFunctMenuApp.id = #{key}
    </select>
    <sql id="selectPidmsfunctmenuappVo">
        select id,
               DmsFunctid,
               DmsFunctCode,
               DmsFunctName,
               <PERSON>vid,
               NavCode,
               NavName,
               Remark,
               <PERSON>reate<PERSON>y,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON>ate,
               <PERSON>er,
               Listerid,
               ModifyDate,
               Revision
        from PiDmsFunctMenuApp
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PidmsfunctmenuappPojo">
        <include refid="selectPidmsfunctmenuappVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiDmsFunctMenuApp.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.dmsfunctid != null ">
            and PiDmsFunctMenuApp.DmsFunctid like concat('%', #{SearchPojo.dmsfunctid}, '%')
        </if>
        <if test="SearchPojo.dmsfunctcode != null ">
            and PiDmsFunctMenuApp.DmsFunctCode like concat('%', #{SearchPojo.dmsfunctcode}, '%')
        </if>
        <if test="SearchPojo.dmsfunctname != null ">
            and PiDmsFunctMenuApp.DmsFunctName like concat('%', #{SearchPojo.dmsfunctname}, '%')
        </if>
        <if test="SearchPojo.navid != null ">
            and PiDmsFunctMenuApp.Navid like concat('%', #{SearchPojo.navid}, '%')
        </if>
        <if test="SearchPojo.navcode != null ">
            and PiDmsFunctMenuApp.NavCode like concat('%', #{SearchPojo.navcode}, '%')
        </if>
        <if test="SearchPojo.navname != null ">
            and PiDmsFunctMenuApp.NavName like concat('%', #{SearchPojo.navname}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and PiDmsFunctMenuApp.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and PiDmsFunctMenuApp.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and PiDmsFunctMenuApp.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and PiDmsFunctMenuApp.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and PiDmsFunctMenuApp.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.dmsfunctid != null ">
                or PiDmsFunctMenuApp.DmsFunctid like concat('%', #{SearchPojo.dmsfunctid}, '%')
            </if>
            <if test="SearchPojo.dmsfunctcode != null ">
                or PiDmsFunctMenuApp.DmsFunctCode like concat('%', #{SearchPojo.dmsfunctcode}, '%')
            </if>
            <if test="SearchPojo.dmsfunctname != null ">
                or PiDmsFunctMenuApp.DmsFunctName like concat('%', #{SearchPojo.dmsfunctname}, '%')
            </if>
            <if test="SearchPojo.navid != null ">
                or PiDmsFunctMenuApp.Navid like concat('%', #{SearchPojo.navid}, '%')
            </if>
            <if test="SearchPojo.navcode != null ">
                or PiDmsFunctMenuApp.NavCode like concat('%', #{SearchPojo.navcode}, '%')
            </if>
            <if test="SearchPojo.navname != null ">
                or PiDmsFunctMenuApp.NavName like concat('%', #{SearchPojo.navname}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or PiDmsFunctMenuApp.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or PiDmsFunctMenuApp.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or PiDmsFunctMenuApp.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or PiDmsFunctMenuApp.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or PiDmsFunctMenuApp.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiDmsFunctMenuApp(id, DmsFunctid, DmsFunctCode, DmsFunctName, Navid, NavCode, NavName, Remark,
                                      CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision)
        values (#{id}, #{dmsfunctid}, #{dmsfunctcode}, #{dmsfunctname}, #{navid}, #{navcode}, #{navname}, #{remark},
                #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiDmsFunctMenuApp
        <set>
            <if test="dmsfunctid != null ">
                DmsFunctid =#{dmsfunctid},
            </if>
            <if test="dmsfunctcode != null ">
                DmsFunctCode =#{dmsfunctcode},
            </if>
            <if test="dmsfunctname != null ">
                DmsFunctName =#{dmsfunctname},
            </if>
            <if test="navid != null ">
                Navid =#{navid},
            </if>
            <if test="navcode != null ">
                NavCode =#{navcode},
            </if>
            <if test="navname != null ">
                NavName =#{navname},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiDmsFunctMenuApp
        where id = #{key}
    </delete>

    <select id="getListByFunction" resultType="inks.system.domain.pojo.PidmsfunctmenuappPojo">
        SELECT
            PiMenuWeb.NavPid,
            PiMenuWeb.NavType,
            PiDmsFunctMenuApp.id,
            PiDmsFunctMenuApp.DmsFunctid,
            PiDmsFunctMenuApp.DmsFunctCode,
            PiDmsFunctMenuApp.DmsFunctName,
            PiDmsFunctMenuApp.Navid,
            PiDmsFunctMenuApp.NavCode,
            PiDmsFunctMenuApp.NavName,
            PiDmsFunctMenuApp.Remark,
            PiDmsFunctMenuApp.CreateBy,
            PiDmsFunctMenuApp.CreateByid,
            PiDmsFunctMenuApp.CreateDate,
            PiDmsFunctMenuApp.Lister,
            PiDmsFunctMenuApp.Listerid,
            PiDmsFunctMenuApp.ModifyDate,
            PiDmsFunctMenuApp.Revision
        FROM
            PiMenuWeb
                RIGHT JOIN PiDmsFunctMenuApp ON PiMenuWeb.Navid = PiDmsFunctMenuApp.Navid
        where 1 = 1 and PiDmsFunctMenuApp.DmsFunctid =#{key}
        order by PiDmsFunctMenuApp.NavCode
    </select>
    <select id="getListByDmsFunctids" resultType="inks.system.domain.pojo.PimenuappPojo">
        SELECT DISTINCT PiMenuApp.Navid,
                        PiMenuApp.NavPid,
                        PiMenuApp.NavType,
                        PiMenuApp.NavCode,
                        PiMenuApp.NavName,
                        PiMenuApp.NavGroup,
                        PiMenuApp.RowNum,
                        PiMenuApp.ImageCss,
                        PiMenuApp.IconUrl,
                        PiMenuApp.NavigateUrl,
                        PiMenuApp.MvcUrl,
                        PiMenuApp.ModuleType,
                        PiMenuApp.ModuleCode,
                        PiMenuApp.RoleCode,
                        PiMenuApp.ImageIndex,
                        PiMenuApp.ImageStyle,
                        PiMenuApp.EnabledMark,
                        PiMenuApp.Remark,
                        PiMenuApp.PermissionCode,
                        PiMenuApp.Functionid,
                        PiMenuApp.FunctionCode,
                        PiMenuApp.FunctionName,
                        PiMenuApp.Lister,
                        PiMenuApp.CreateDate,
                        PiMenuApp.ModifyDate,
                        PiMenuApp.DeleteMark,
                        PiMenuApp.DeleteLister,
                        PiMenuApp.DeleteDate
        FROM PiMenuApp
                 LEFT JOIN PiDmsFunctMenuApp ON PiDmsFunctMenuApp.Navid = PiMenuApp.Navid
        WHERE PiDmsFunctMenuApp.DmsFunctid in (${ids})
        order by RowNum
    </select>

</mapper>

