<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PidmsjustauthMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PidmsjustauthPojo">
        select
          id, Userid, UserName, RealName, NickName, AuthType, AuthUuid, Unionid, AuthAvatar, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision        from PiDmsJustAuth
        where PiDmsJustAuth.id = #{key} and PiDmsJustAuth.Tenantid=#{tid}
    </select>
    <sql id="selectPidmsjustauthVo">
         select
          id, Userid, UserName, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>th<PERSON><PERSON>, Auth<PERSON><PERSON>, Union<PERSON>, <PERSON>thAvatar, <PERSON><PERSON><PERSON>y, <PERSON>reate<PERSON>yid, <PERSON>reate<PERSON>ate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision        from PiDmsJustAuth
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.PidmsjustauthPojo">
        <include refid="selectPidmsjustauthVo"/>
         where 1 = 1 and PiDmsJustAuth.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and PiDmsJustAuth.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.userid != null ">
   and PiDmsJustAuth.Userid like concat('%', #{SearchPojo.userid}, '%')
</if>
<if test="SearchPojo.username != null ">
   and PiDmsJustAuth.UserName like concat('%', #{SearchPojo.username}, '%')
</if>
<if test="SearchPojo.realname != null ">
   and PiDmsJustAuth.RealName like concat('%', #{SearchPojo.realname}, '%')
</if>
<if test="SearchPojo.nickname != null ">
   and PiDmsJustAuth.NickName like concat('%', #{SearchPojo.nickname}, '%')
</if>
<if test="SearchPojo.authtype != null ">
   and PiDmsJustAuth.AuthType like concat('%', #{SearchPojo.authtype}, '%')
</if>
<if test="SearchPojo.authuuid != null ">
   and PiDmsJustAuth.AuthUuid like concat('%', #{SearchPojo.authuuid}, '%')
</if>
<if test="SearchPojo.unionid != null ">
   and PiDmsJustAuth.Unionid like concat('%', #{SearchPojo.unionid}, '%')
</if>
<if test="SearchPojo.authavatar != null ">
   and PiDmsJustAuth.AuthAvatar like concat('%', #{SearchPojo.authavatar}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and PiDmsJustAuth.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and PiDmsJustAuth.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and PiDmsJustAuth.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and PiDmsJustAuth.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and PiDmsJustAuth.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and PiDmsJustAuth.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and PiDmsJustAuth.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and PiDmsJustAuth.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and PiDmsJustAuth.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and PiDmsJustAuth.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.userid != null ">
   or PiDmsJustAuth.Userid like concat('%', #{SearchPojo.userid}, '%')
</if>
<if test="SearchPojo.username != null ">
   or PiDmsJustAuth.UserName like concat('%', #{SearchPojo.username}, '%')
</if>
<if test="SearchPojo.realname != null ">
   or PiDmsJustAuth.RealName like concat('%', #{SearchPojo.realname}, '%')
</if>
<if test="SearchPojo.nickname != null ">
   or PiDmsJustAuth.NickName like concat('%', #{SearchPojo.nickname}, '%')
</if>
<if test="SearchPojo.authtype != null ">
   or PiDmsJustAuth.AuthType like concat('%', #{SearchPojo.authtype}, '%')
</if>
<if test="SearchPojo.authuuid != null ">
   or PiDmsJustAuth.AuthUuid like concat('%', #{SearchPojo.authuuid}, '%')
</if>
<if test="SearchPojo.unionid != null ">
   or PiDmsJustAuth.Unionid like concat('%', #{SearchPojo.unionid}, '%')
</if>
<if test="SearchPojo.authavatar != null ">
   or PiDmsJustAuth.AuthAvatar like concat('%', #{SearchPojo.authavatar}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or PiDmsJustAuth.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or PiDmsJustAuth.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or PiDmsJustAuth.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or PiDmsJustAuth.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or PiDmsJustAuth.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or PiDmsJustAuth.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or PiDmsJustAuth.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or PiDmsJustAuth.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or PiDmsJustAuth.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or PiDmsJustAuth.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into PiDmsJustAuth(id, Userid, UserName, RealName, NickName, AuthType, AuthUuid, Unionid, AuthAvatar, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{userid}, #{username}, #{realname}, #{nickname}, #{authtype}, #{authuuid}, #{unionid}, #{authavatar}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update PiDmsJustAuth
        <set>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="nickname != null ">
                NickName =#{nickname},
            </if>
            <if test="authtype != null ">
                AuthType =#{authtype},
            </if>
            <if test="authuuid != null ">
                AuthUuid =#{authuuid},
            </if>
            <if test="unionid != null ">
                Unionid =#{unionid},
            </if>
            <if test="authavatar != null ">
                AuthAvatar =#{authavatar},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from PiDmsJustAuth where id = #{key} and Tenantid=#{tid}
    </delete>
    <delete id="deleteByOpenid">
        delete from PiDmsJustAuth where AuthUuid = #{openid} and Tenantid=#{tid}
    </delete>
</mapper>

