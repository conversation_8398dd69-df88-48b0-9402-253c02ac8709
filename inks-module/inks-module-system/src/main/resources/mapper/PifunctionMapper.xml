<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PifunctionMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PifunctionPojo">
        <include refid="selectPifunctionVo"/>
        where PiFunction.Functionid = #{key}
    </select>
    <sql id="selectPifunctionVo">
        select Functionid,
               FunctionCode,
               FunctionName,
               Description,
               FrontPhoto,
               PublicMark,
               EnabledMark,
               ReleaseMark,
               RowNum,
               Weight,
               Remark,
               FunctionUrl,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               DeleteMark,
               DeleteLister,
               DeleteListerid,
               DeleteDate,
               Revision
        from PiFunction
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PifunctionPojo">
        <include refid="selectPifunctionVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiFunction.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.functioncode != null and SearchPojo.functioncode  != ''">
            and PiFunction.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null and SearchPojo.functionname  != ''">
            and PiFunction.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.description != null and SearchPojo.description  != ''">
            and PiFunction.Description like concat('%', #{SearchPojo.description}, '%')
        </if>
        <if test="SearchPojo.frontphoto != null and SearchPojo.frontphoto  != ''">
            and PiFunction.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark  != ''">
            and PiFunction.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.functionurl != null and SearchPojo.functionurl  != ''">
            and PiFunction.FunctionUrl like concat('%', #{SearchPojo.functionurl}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby  != ''">
            and PiFunction.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid  != ''">
            and PiFunction.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and PiFunction.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid  != ''">
            and PiFunction.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.deletelister != null and SearchPojo.deletelister  != ''">
            and PiFunction.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
        <if test="SearchPojo.deletelisterid != null and SearchPojo.deletelisterid  != ''">
            and PiFunction.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.functioncode != null and SearchPojo.functioncode != ''">
            or PiFunction.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null and SearchPojo.functionname != ''">
            or PiFunction.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.description != null and SearchPojo.description != ''">
            or PiFunction.Description like concat('%', #{SearchPojo.description}, '%')
        </if>
        <if test="SearchPojo.frontphoto != null and SearchPojo.frontphoto != ''">
            or PiFunction.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or PiFunction.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.functionurl != null and SearchPojo.functionurl != ''">
            or PiFunction.FunctionUrl like concat('%', #{SearchPojo.functionurl}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            or PiFunction.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            or PiFunction.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or PiFunction.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            or PiFunction.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.deletelister != null and SearchPojo.deletelister != ''">
            or PiFunction.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
        <if test="SearchPojo.deletelisterid != null and SearchPojo.deletelisterid != ''">
            or PiFunction.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiFunction(Functionid, FunctionCode, FunctionName, Description, FrontPhoto, PublicMark, EnabledMark,
                               ReleaseMark, RowNum, Weight, Remark, FunctionUrl, CreateBy, CreateByid, CreateDate,
                               Lister, Listerid, ModifyDate, DeleteMark, DeleteLister, DeleteListerid, DeleteDate,
                               Revision)
        values (#{functionid}, #{functioncode}, #{functionname}, #{description}, #{frontphoto}, #{publicmark},
                #{enabledmark}, #{releasemark}, #{rownum}, #{weight}, #{remark}, #{functionurl}, #{createby},
                #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{deletemark}, #{deletelister},
                #{deletelisterid}, #{deletedate}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiFunction
        <set>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="description != null ">
                Description =#{description},
            </if>
            <if test="frontphoto != null ">
                FrontPhoto =#{frontphoto},
            </if>
            <if test="publicmark != null">
                PublicMark =#{publicmark},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="releasemark != null">
                ReleaseMark =#{releasemark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="weight != null">
                Weight =#{weight},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="functionurl != null ">
                FunctionUrl =#{functionurl},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletelisterid != null ">
                DeleteListerid =#{deletelisterid},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
            Revision=Revision+1
        </set>
        where Functionid = #{functionid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiFunction
        where Functionid = #{key}
    </delete>

    <select id="getFunctionListBySelf" resultType="inks.system.domain.pojo.PifunctionPojo">
        <include refid="selectPifunctionVo"/>
        where PiFunction.Functionid in (select distinct Functionid
                                        from PiSubscriber
                                        where Tenantid = #{tenantid})
    </select>
</mapper>

