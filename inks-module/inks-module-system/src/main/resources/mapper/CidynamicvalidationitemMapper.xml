<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CidynamicvalidationitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CidynamicvalidationitemPojo">
        <include refid="selectCidynamicvalidationitemVo"/>
        where CiDynamicValidationItem.id = #{key} and CiDynamicValidationItem.Tenantid=#{tid}
    </select>
    <sql id="selectCidynamicvalidationitemVo">
         select
id, Pid, FieldName, Comment, RuleType, RuleValue, ErrorMessage, CheckType, Remark, RowNum, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from CiDynamicValidationItem
    </sql>

         <!--查询List-->
    <select id="getList" resultType="inks.system.domain.pojo.CidynamicvalidationitemPojo">
        <include refid="selectCidynamicvalidationitemVo"/>
        where CiDynamicValidationItem.Pid = #{Pid} and CiDynamicValidationItem.Tenantid=#{tid}
        order by RowNum
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.CidynamicvalidationitemPojo">
        <include refid="selectCidynamicvalidationitemVo"/>
         where 1 = 1 and CiDynamicValidationItem.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and CiDynamicValidationItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">
           <include refid="and"></include>
         </if>
         <if test="SearchType==1">
           <include refid="or"></include>
         </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and CiDynamicValidationItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.fieldname != null and SearchPojo.fieldname != ''">
   and CiDynamicValidationItem.fieldname like concat('%', #{SearchPojo.fieldname}, '%')
</if>
<if test="SearchPojo.comment != null and SearchPojo.comment != ''">
   and CiDynamicValidationItem.comment like concat('%', #{SearchPojo.comment}, '%')
</if>
<if test="SearchPojo.ruletype != null and SearchPojo.ruletype != ''">
   and CiDynamicValidationItem.ruletype like concat('%', #{SearchPojo.ruletype}, '%')
</if>
<if test="SearchPojo.rulevalue != null and SearchPojo.rulevalue != ''">
   and CiDynamicValidationItem.rulevalue like concat('%', #{SearchPojo.rulevalue}, '%')
</if>
<if test="SearchPojo.errormessage != null and SearchPojo.errormessage != ''">
   and CiDynamicValidationItem.errormessage like concat('%', #{SearchPojo.errormessage}, '%')
</if>
<if test="SearchPojo.checktype != null and SearchPojo.checktype != ''">
   and CiDynamicValidationItem.checktype like concat('%', #{SearchPojo.checktype}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and CiDynamicValidationItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and CiDynamicValidationItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and CiDynamicValidationItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and CiDynamicValidationItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and CiDynamicValidationItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and CiDynamicValidationItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and CiDynamicValidationItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and CiDynamicValidationItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and CiDynamicValidationItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and CiDynamicValidationItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and CiDynamicValidationItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or CiDynamicValidationItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.fieldname != null and SearchPojo.fieldname != ''">
   or CiDynamicValidationItem.FieldName like concat('%', #{SearchPojo.fieldname}, '%')
</if>
<if test="SearchPojo.comment != null and SearchPojo.comment != ''">
   or CiDynamicValidationItem.Comment like concat('%', #{SearchPojo.comment}, '%')
</if>
<if test="SearchPojo.ruletype != null and SearchPojo.ruletype != ''">
   or CiDynamicValidationItem.RuleType like concat('%', #{SearchPojo.ruletype}, '%')
</if>
<if test="SearchPojo.rulevalue != null and SearchPojo.rulevalue != ''">
   or CiDynamicValidationItem.RuleValue like concat('%', #{SearchPojo.rulevalue}, '%')
</if>
<if test="SearchPojo.errormessage != null and SearchPojo.errormessage != ''">
   or CiDynamicValidationItem.ErrorMessage like concat('%', #{SearchPojo.errormessage}, '%')
</if>
<if test="SearchPojo.checktype != null and SearchPojo.checktype != ''">
   or CiDynamicValidationItem.CheckType like concat('%', #{SearchPojo.checktype}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or CiDynamicValidationItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or CiDynamicValidationItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or CiDynamicValidationItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or CiDynamicValidationItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or CiDynamicValidationItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or CiDynamicValidationItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or CiDynamicValidationItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or CiDynamicValidationItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or CiDynamicValidationItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or CiDynamicValidationItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or CiDynamicValidationItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>


    <!--新增所有列-->
    <insert id="insert" >
        insert into CiDynamicValidationItem(id, Pid, FieldName, Comment, RuleType, RuleValue, ErrorMessage, CheckType, Remark, RowNum, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{fieldname}, #{comment}, #{ruletype}, #{rulevalue}, #{errormessage}, #{checktype}, #{remark}, #{rownum}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiDynamicValidationItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="fieldname != null ">
                FieldName = #{fieldname},
            </if>
            <if test="comment != null ">
                Comment = #{comment},
            </if>
            <if test="ruletype != null ">
                RuleType = #{ruletype},
            </if>
            <if test="rulevalue != null ">
                RuleValue = #{rulevalue},
            </if>
            <if test="errormessage != null ">
                ErrorMessage = #{errormessage},
            </if>
            <if test="checktype != null ">
                CheckType = #{checktype},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from CiDynamicValidationItem where id = #{key} and Tenantid=#{tid}
    </delete>

    <select id="getListByValidationCode" resultType="inks.system.domain.pojo.CidynamicvalidationitemPojo">
        select CiDynamicValidationItem.*
        from CiDynamicValidationItem
                 join CiDynamicValidation on CiDynamicValidationItem.Pid = CiDynamicValidation.id
        where CiDynamicValidation.ValidationCode = #{validationcode}
          and CiDynamicValidationItem.Tenantid = #{tid}
        order by RowNum
    </select>
</mapper>

