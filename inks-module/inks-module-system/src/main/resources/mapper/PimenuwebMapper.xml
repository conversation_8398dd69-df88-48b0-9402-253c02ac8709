<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PimenuwebMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PimenuwebPojo">
        <include refid="selectPimenuwebVo"/>
        where PiMenuWeb.Navid = #{key}
    </select>
    <sql id="selectPimenuwebVo">
         select
Navid, NavPid, NavType, NavCode, NavName, NavGroup, RowNum, ImageCss, IconUrl, NavigateUrl, MvcUrl, RouteComp, RouteName, RoutePath, ModuleType, ModuleCode, RoleCode, ImageIndex, ImageStyle, EnabledMark, HiddenMark, Remark, PermissionCode, Functionid, FunctionCode, FunctionName, IsMicroApp, MicroAppName, MicroAppEntry, MicroAppRule, MicroAppLocal, Lister, CreateDate, ModifyDate, DeleteMark, DeleteLister, DeleteDate        from PiMenuWeb
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PimenuwebPojo">
        <include refid="selectPimenuwebVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiMenuWeb.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.navpid != null and SearchPojo.navpid  != ''">
            and PiMenuWeb.NavPid like concat('%', #{SearchPojo.navpid}, '%')
        </if>
        <if test="SearchPojo.navtype != null and SearchPojo.navtype  != ''">
            and PiMenuWeb.NavType like concat('%', #{SearchPojo.navtype}, '%')
        </if>
        <if test="SearchPojo.navcode != null and SearchPojo.navcode  != ''">
            and PiMenuWeb.NavCode like concat('%', #{SearchPojo.navcode}, '%')
        </if>
        <if test="SearchPojo.navname != null and SearchPojo.navname  != ''">
            and PiMenuWeb.NavName like concat('%', #{SearchPojo.navname}, '%')
        </if>
        <if test="SearchPojo.navgroup != null and SearchPojo.navgroup  != ''">
            and PiMenuWeb.NavGroup like concat('%', #{SearchPojo.navgroup}, '%')
        </if>
        <if test="SearchPojo.imagecss != null and SearchPojo.imagecss  != ''">
            and PiMenuWeb.ImageCss like concat('%', #{SearchPojo.imagecss}, '%')
        </if>
        <if test="SearchPojo.iconurl != null and SearchPojo.iconurl  != ''">
            and PiMenuWeb.IconUrl like concat('%', #{SearchPojo.iconurl}, '%')
        </if>
        <if test="SearchPojo.navigateurl != null and SearchPojo.navigateurl  != ''">
            and PiMenuWeb.NavigateUrl like concat('%', #{SearchPojo.navigateurl}, '%')
        </if>
        <if test="SearchPojo.mvcurl != null and SearchPojo.mvcurl  != ''">
            and PiMenuWeb.MvcUrl like concat('%', #{SearchPojo.mvcurl}, '%')
        </if>
        <if test="SearchPojo.moduletype != null and SearchPojo.moduletype  != ''">
            and PiMenuWeb.ModuleType like concat('%', #{SearchPojo.moduletype}, '%')
        </if>
        <if test="SearchPojo.modulecode != null and SearchPojo.modulecode  != ''">
            and PiMenuWeb.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.rolecode != null and SearchPojo.rolecode  != ''">
            and PiMenuWeb.RoleCode like concat('%', #{SearchPojo.rolecode}, '%')
        </if>
        <if test="SearchPojo.imageindex != null and SearchPojo.imageindex  != ''">
            and PiMenuWeb.ImageIndex like concat('%', #{SearchPojo.imageindex}, '%')
        </if>
        <if test="SearchPojo.imagestyle != null and SearchPojo.imagestyle  != ''">
            and PiMenuWeb.ImageStyle like concat('%', #{SearchPojo.imagestyle}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark  != ''">
            and PiMenuWeb.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.permissioncode != null and SearchPojo.permissioncode  != ''">
            and PiMenuWeb.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
        </if>
        <if test="SearchPojo.functionid != null and SearchPojo.functionid  != ''">
            and PiMenuWeb.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null and SearchPojo.functioncode  != ''">
            and PiMenuWeb.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null and SearchPojo.functionname  != ''">
            and PiMenuWeb.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and PiMenuWeb.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.deletelister != null and SearchPojo.deletelister  != ''">
            and PiMenuWeb.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.navpid != null and SearchPojo.navpid != ''">
            or PiMenuWeb.NavPid like concat('%', #{SearchPojo.navpid}, '%')
        </if>
        <if test="SearchPojo.navtype != null and SearchPojo.navtype != ''">
            or PiMenuWeb.NavType like concat('%', #{SearchPojo.navtype}, '%')
        </if>
        <if test="SearchPojo.navcode != null and SearchPojo.navcode != ''">
            or PiMenuWeb.NavCode like concat('%', #{SearchPojo.navcode}, '%')
        </if>
        <if test="SearchPojo.navname != null and SearchPojo.navname != ''">
            or PiMenuWeb.NavName like concat('%', #{SearchPojo.navname}, '%')
        </if>
        <if test="SearchPojo.navgroup != null and SearchPojo.navgroup != ''">
            or PiMenuWeb.NavGroup like concat('%', #{SearchPojo.navgroup}, '%')
        </if>
        <if test="SearchPojo.imagecss != null and SearchPojo.imagecss != ''">
            or PiMenuWeb.ImageCss like concat('%', #{SearchPojo.imagecss}, '%')
        </if>
        <if test="SearchPojo.iconurl != null and SearchPojo.iconurl != ''">
            or PiMenuWeb.IconUrl like concat('%', #{SearchPojo.iconurl}, '%')
        </if>
        <if test="SearchPojo.navigateurl != null and SearchPojo.navigateurl != ''">
            or PiMenuWeb.NavigateUrl like concat('%', #{SearchPojo.navigateurl}, '%')
        </if>
        <if test="SearchPojo.mvcurl != null and SearchPojo.mvcurl != ''">
            or PiMenuWeb.MvcUrl like concat('%', #{SearchPojo.mvcurl}, '%')
        </if>
        <if test="SearchPojo.moduletype != null and SearchPojo.moduletype != ''">
            or PiMenuWeb.ModuleType like concat('%', #{SearchPojo.moduletype}, '%')
        </if>
        <if test="SearchPojo.modulecode != null and SearchPojo.modulecode != ''">
            or PiMenuWeb.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.rolecode != null and SearchPojo.rolecode != ''">
            or PiMenuWeb.RoleCode like concat('%', #{SearchPojo.rolecode}, '%')
        </if>
        <if test="SearchPojo.imageindex != null and SearchPojo.imageindex != ''">
            or PiMenuWeb.ImageIndex like concat('%', #{SearchPojo.imageindex}, '%')
        </if>
        <if test="SearchPojo.imagestyle != null and SearchPojo.imagestyle != ''">
            or PiMenuWeb.ImageStyle like concat('%', #{SearchPojo.imagestyle}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or PiMenuWeb.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.permissioncode != null and SearchPojo.permissioncode != ''">
            or PiMenuWeb.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
        </if>
        <if test="SearchPojo.functionid != null and SearchPojo.functionid != ''">
            or PiMenuWeb.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null and SearchPojo.functioncode != ''">
            or PiMenuWeb.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null and SearchPojo.functionname != ''">
            or PiMenuWeb.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or PiMenuWeb.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.deletelister != null and SearchPojo.deletelister != ''">
            or PiMenuWeb.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiMenuWeb(Navid, NavPid, NavType, NavCode, NavName, NavGroup, RowNum, ImageCss, IconUrl, NavigateUrl, MvcUrl, RouteComp, RouteName, RoutePath, ModuleType, ModuleCode, RoleCode, ImageIndex, ImageStyle, EnabledMark, HiddenMark, Remark, PermissionCode, Functionid, FunctionCode, FunctionName, IsMicroApp, MicroAppName, MicroAppEntry, MicroAppRule, MicroAppLocal, Lister, CreateDate, ModifyDate, DeleteMark, DeleteLister, DeleteDate)
        values (#{navid}, #{navpid}, #{navtype}, #{navcode}, #{navname}, #{navgroup}, #{rownum}, #{imagecss}, #{iconurl}, #{navigateurl}, #{mvcurl}, #{routecomp}, #{routename}, #{routepath}, #{moduletype}, #{modulecode}, #{rolecode}, #{imageindex}, #{imagestyle}, #{enabledmark}, #{hiddenmark}, #{remark}, #{permissioncode}, #{functionid}, #{functioncode}, #{functionname}, #{ismicroapp}, #{microappname}, #{microappentry}, #{microapprule}, #{microapplocal}, #{lister}, #{createdate}, #{modifydate}, #{deletemark}, #{deletelister}, #{deletedate})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiMenuWeb
        <set>
            <if test="navpid != null ">
                NavPid =#{navpid},
            </if>
            <if test="navtype != null ">
                NavType =#{navtype},
            </if>
            <if test="navcode != null ">
                NavCode =#{navcode},
            </if>
            <if test="navname != null ">
                NavName =#{navname},
            </if>
            <if test="navgroup != null ">
                NavGroup =#{navgroup},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="imagecss != null ">
                ImageCss =#{imagecss},
            </if>
            <if test="iconurl != null ">
                IconUrl =#{iconurl},
            </if>
            <if test="navigateurl != null ">
                NavigateUrl =#{navigateurl},
            </if>
            <if test="mvcurl != null ">
                MvcUrl =#{mvcurl},
            </if>
            <if test="routecomp != null ">
                RouteComp =#{routecomp},
            </if>
            <if test="routename != null ">
                RouteName =#{routename},
            </if>
            <if test="routepath != null ">
                RoutePath =#{routepath},
            </if>
            <if test="moduletype != null ">
                ModuleType =#{moduletype},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="rolecode != null ">
                RoleCode =#{rolecode},
            </if>
            <if test="imageindex != null ">
                ImageIndex =#{imageindex},
            </if>
            <if test="imagestyle != null ">
                ImageStyle =#{imagestyle},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="hiddenmark != null">
                HiddenMark =#{hiddenmark},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="permissioncode != null ">
                PermissionCode =#{permissioncode},
            </if>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="ismicroapp != null">
                IsMicroApp =#{ismicroapp},
            </if>
            <if test="microappname != null ">
                MicroAppName =#{microappname},
            </if>
            <if test="microappentry != null ">
                MicroAppEntry =#{microappentry},
            </if>
            <if test="microapprule != null ">
                MicroAppRule =#{microapprule},
            </if>
            <if test="microapplocal != null ">
                MicroAppLocal =#{microapplocal},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null ">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
        </set>
        where Navid = #{navid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiMenuWeb
        where Navid = #{key}
    </delete>

    <!--查询单个-->
    <select id="getListByPid" resultType="inks.system.domain.pojo.PimenuwebPojo">
        <include refid="selectPimenuwebVo"/>
        where PiMenuWeb.NavPid = #{key}
        Order by RowNum
    </select>

    <select id="getListByNavids" resultType="inks.system.domain.pojo.PimenuwebPojo">
        <include refid="selectPimenuwebVo"/>
        where PiMenuWeb.Navid in
        <foreach collection="navids" item="navid" open="(" separator="," close=")">
            #{navid}
        </foreach>
        Order by RowNum
    </select>
</mapper>

