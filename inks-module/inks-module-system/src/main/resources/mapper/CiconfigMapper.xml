<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CiconfigMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CiconfigPojo">
        select id,
               Parentid,
               CfgName,
               CfgKey,
               CfgValue,
               CfgType,
               CfgLevel,
               CtrlType,
               CfgIcon,
               CfgOption,
               RowNum,
               AllowUi,
               EnabledMark,
               AllowDelete,
               SpecialMark,
               AppSetting,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Userid,
               Tenantid,
               TenantName,
               Revision
        from CiConfig
        where CiConfig.id = #{key}
          and CiConfig.Tenantid = #{tid}
    </select>
    <sql id="selectCiconfigVo">
        select id,
               Parentid,
               CfgName,
               CfgKey,
               CfgValue,
               CfgType,
               CfgLevel,
               CtrlType,
               CfgIcon,
               CfgOption,
               AllowUi,
               RowNum,
               EnabledMark,
               AllowDelete,
               SpecialMark,
               AppSetting,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Userid,
               Tenantid,
               TenantName,
               Revision
        from CiConfig
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CiconfigPojo">
        <include refid="selectCiconfigVo"/>
        where 1 = 1 and CiConfig.Tenantid = #{tenantid}
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and CiConfig.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.parentid != null">
            and CiConfig.Parentid like concat('%', #{SearchPojo.parentid}, '%')
        </if>
        <if test="SearchPojo.cfgname != null">
            and CiConfig.CfgName like concat('%',
                #{SearchPojo.cfgname}, '%')
        </if>
        <if test="SearchPojo.cfgkey != null">
            and CiConfig.CfgKey like concat('%',
                #{SearchPojo.cfgkey}, '%')
        </if>
        <if test="SearchPojo.cfgvalue != null">
            and CiConfig.CfgValue like concat('%',
                #{SearchPojo.cfgvalue}, '%')
        </if>
        <if test="SearchPojo.cfgoption != null">
            and CiConfig.CfgOption like concat('%',
                #{SearchPojo.cfgoption}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and CiConfig.Remark like concat('%',
                #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and CiConfig.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and CiConfig.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and CiConfig.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and CiConfig.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and CiConfig.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and CiConfig.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and CiConfig.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and CiConfig.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and CiConfig.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.userid != null">
            and CiConfig.Userid like concat('%',
                #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and CiConfig.TenantName like concat('%',
                #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.parentid != null">
                or CiConfig.Parentid like concat('%', #{SearchPojo.parentid}, '%')
            </if>
            <if test="SearchPojo.cfgname != null">
                or CiConfig.CfgName like concat('%', #{SearchPojo.cfgname}, '%')
            </if>
            <if test="SearchPojo.cfgkey != null">
                or CiConfig.CfgKey like concat('%', #{SearchPojo.cfgkey}, '%')
            </if>
            <if test="SearchPojo.cfgvalue != null">
                or CiConfig.CfgValue like concat('%', #{SearchPojo.cfgvalue}, '%')
            </if>
            <if test="SearchPojo.cfgoption != null">
                or CiConfig.CfgOption like concat('%', #{SearchPojo.cfgoption}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or CiConfig.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or CiConfig.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or CiConfig.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or CiConfig.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or CiConfig.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or CiConfig.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or CiConfig.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or CiConfig.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or CiConfig.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or CiConfig.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.userid != null">
                or CiConfig.Userid like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or CiConfig.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into CiConfig(id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgIcon, CfgOption,
                             AllowUi, RowNum, EnabledMark,
                             AllowDelete, SpecialMark, AppSetting, Remark, CreateBy, CreateByid, CreateDate, Lister,
                             Listerid, ModifyDate,
                             Custom1, Custom2, Custom3, Custom4, Custom5, Userid, Tenantid, TenantName, Revision)
        values (#{id}, #{parentid}, #{cfgname}, #{cfgkey}, #{cfgvalue}, #{cfgtype}, #{cfglevel}, #{ctrltype},
                #{cfgicon}, #{cfgoption}, #{allowui},
                #{rownum}, #{enabledmark}, #{allowdelete}, #{specialmark}, #{appsetting}, #{remark}, #{createby},
                #{createbyid}, #{createdate},
                #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5},
                #{userid}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiConfig
        <set>
            <if test="parentid != null">
                Parentid =#{parentid},
            </if>
            <if test="cfgname != null">
                CfgName =#{cfgname},
            </if>
            <if test="cfgkey != null">
                CfgKey =#{cfgkey},
            </if>
            <if test="cfgvalue != null">
                CfgValue =#{cfgvalue},
            </if>
            <if test="cfgtype != null">
                CfgType =#{cfgtype},
            </if>
            <if test="cfglevel != null">
                CfgLevel =#{cfglevel},
            </if>
            <if test="ctrltype != null">
                CtrlType =#{ctrltype},
            </if>
            <if test="cfgicon != null">
                Cfgicon =#{cfgicon},
            </if>
            <if test="cfgoption != null">
                CfgOption =#{cfgoption},
            </if>
            <if test="allowui != null">
                AllowUi =#{allowui},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="allowdelete != null">
                AllowDelete =#{allowdelete},
            </if>
            <if test="specialmark != null">
                SpecialMark =#{specialmark},
            </if>
            <if test="appsetting != null">
                AppSetting =#{appsetting},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="userid != null">
                Userid =#{userid},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiConfig
        where id = #{key}
    </delete>


    <!--查询单个-->
    <select id="getEntityByKey" resultType="inks.system.domain.pojo.CiconfigPojo">
        select id,
               Parentid,
               CfgName,
               CfgKey,
               CfgValue,
               CfgType,
               CfgLevel,
               CtrlType,
               CfgIcon,
               CfgOption,
               AllowUi,
               RowNum,
               EnabledMark,
               AllowDelete,
               SpecialMark,
               AppSetting,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Userid,
               Tenantid,
               TenantName,
               Revision
        from CiConfig
        where CiConfig.CfgKey = #{key}
          and CiConfig.Tenantid = #{tid}
    </select>
    <!--查询单个-->
    <select id="getEntityByKeyUser" resultType="inks.system.domain.pojo.CiconfigPojo">
        select id,
               Parentid,
               CfgName,
               CfgKey,
               CfgValue,
               CfgType,
               CfgLevel,
               CtrlType,
               CfgIcon,
               CfgOption,
               AllowUi,
               RowNum,
               EnabledMark,
               AllowDelete,
               SpecialMark,
               AppSetting,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Userid,
               Tenantid,
               TenantName,
               Revision
        from CiConfig
        where CiConfig.CfgKey = #{key}
          and CiConfig.Tenantid = #{tid}
          and CiConfig.Userid = #{userid}
    </select>
    <!--查询单个-->
    <select id="getListByTenant" resultType="inks.system.domain.pojo.CiconfigPojo">
        select id,
               Parentid,
               CfgName,
               CfgKey,
               CfgValue,
               CfgType,
               CfgLevel,
               CtrlType,
               CfgIcon,
               CfgOption,
               AllowUi,
               RowNum,
               EnabledMark,
               AllowDelete,
               SpecialMark,
               AppSetting,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Userid,
               Tenantid,
               TenantName,
               Revision
        from CiConfig
        where CiConfig.Tenantid = #{tid}
        Order by CfgKey
    </select>
    <!--查询单个-->
    <select id="getListByTenUi" resultType="inks.system.domain.pojo.CiconfigPojo">
        select id,
               Parentid,
               CfgName,
               CfgKey,
               CfgValue,
               CfgType,
               CfgLevel,
               CtrlType,
               CfgIcon,
               CfgOption,
               AllowUi,
               RowNum,
               EnabledMark,
               AllowDelete,
               SpecialMark,
               AppSetting,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Userid,
               Tenantid,
               TenantName,
               Revision
        from CiConfig
        where CiConfig.Tenantid = #{tid}
          and AllowUi = 1
        Order by CfgKey
    </select>

    <!--查询单个-->
    <select id="getListByUserUi" resultType="inks.system.domain.pojo.CiconfigPojo">
        select id,
               Parentid,
               CfgName,
               CfgKey,
               CfgValue,
               CfgType,
               CfgLevel,
               CtrlType,
               CfgIcon,
               CfgOption,
               AllowUi,
               RowNum,
               EnabledMark,
               AllowDelete,
               SpecialMark,
               AppSetting,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Userid,
               Tenantid,
               TenantName,
               Revision
        from CiConfig
        where CiConfig.Userid = #{userid}
          and CiConfig.Tenantid = #{tid}
          and AllowUi = 1
        Order by CfgKey
    </select>
    <!--查询单个-->
    <select id="getListByDefault" resultType="inks.system.domain.pojo.CiconfigPojo">
        select id,
               Parentid,
               CfgName,
               CfgKey,
               CfgValue,
               CfgType,
               CfgLevel,
               CtrlType,
               CfgIcon,
               CfgOption,
               AllowUi,
               RowNum,
               EnabledMark,
               AllowDelete,
               SpecialMark,
               AppSetting,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Userid,
               Tenantid,
               TenantName,
               Revision
        from CiConfig
        where CiConfig.Tenantid = 'default'
          and CiConfig.CfgLevel = 1
        Order by RowNum
    </select>

    <!--查询单个-->
    <select id="getListByPrefix" resultType="inks.system.domain.pojo.CiconfigPojo">
        select id,
               Parentid,
               CfgName,
               CfgKey,
               CfgValue,
               CfgType,
               CfgLevel,
               CtrlType,
               CfgIcon,
               CfgOption,
               AllowUi,
               RowNum,
               EnabledMark,
               AllowDelete,
               SpecialMark,
               AppSetting,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Userid,
               Tenantid,
               TenantName,
               Revision
        from CiConfig
        where CiConfig.Tenantid = #{tid}
          and CiConfig.CfgLevel = 1
          and CiConfig.CfgKey like concat(#{key}, '%')
        Order by RowNum
    </select>

    <update id="unCheckOnlineInTids">
        update CiConfig
        set CfgValue = '0'
        where CfgKey = 'system.checkonline'
          and Tenantid = #{tid}
    </update>

    <!-- 获取微应用配置列表 -->
    <select id="getMicroAppList" resultType="inks.system.domain.pojo.CiconfigPojo">
        <include refid="selectCiconfigVo"/>
        where CiConfig.CfgLevel = 0
          and CiConfig.CfgKey like 'platform.microapp.%'
          and CiConfig.EnabledMark = 1
        order by CiConfig.CfgKey
    </select>
</mapper>
