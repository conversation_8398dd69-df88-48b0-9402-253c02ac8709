<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PiauthcodeMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PiauthcodePojo">
        select
          id, AuthCode, AuthDesc, UserName, UserPassword, RowNum, EnabledMark, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision        from PiAuthCode
        where PiAuthCode.id = #{key} and PiAuthCode.Tenantid=#{tid}
    </select>
    <sql id="selectPiauthcodeVo">
         select
          id, AuthCode, Auth<PERSON>esc, User<PERSON><PERSON>, User<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, EnabledMark, <PERSON>mark, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Lister, <PERSON>erid, <PERSON>difyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision        from PiAuthCode
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.PiauthcodePojo">
        <include refid="selectPiauthcodeVo"/>
         where 1 = 1 and PiAuthCode.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and PiAuthCode.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">
           <include refid="and"></include>
         </if>
         <if test="SearchType==1">
           <include refid="or"></include>
         </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.authcode != null ">
   and PiAuthCode.AuthCode like concat('%', #{SearchPojo.authcode}, '%')
</if>
<if test="SearchPojo.authdesc != null ">
   and PiAuthCode.AuthDesc like concat('%', #{SearchPojo.authdesc}, '%')
</if>
<if test="SearchPojo.username != null ">
   and PiAuthCode.UserName like concat('%', #{SearchPojo.username}, '%')
</if>
<if test="SearchPojo.userpassword != null ">
   and PiAuthCode.UserPassword like concat('%', #{SearchPojo.userpassword}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and PiAuthCode.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and PiAuthCode.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and PiAuthCode.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and PiAuthCode.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and PiAuthCode.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and PiAuthCode.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and PiAuthCode.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and PiAuthCode.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and PiAuthCode.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and PiAuthCode.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and PiAuthCode.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.authcode != null ">
   or PiAuthCode.AuthCode like concat('%', #{SearchPojo.authcode}, '%')
</if>
<if test="SearchPojo.authdesc != null ">
   or PiAuthCode.AuthDesc like concat('%', #{SearchPojo.authdesc}, '%')
</if>
<if test="SearchPojo.username != null ">
   or PiAuthCode.UserName like concat('%', #{SearchPojo.username}, '%')
</if>
<if test="SearchPojo.userpassword != null ">
   or PiAuthCode.UserPassword like concat('%', #{SearchPojo.userpassword}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or PiAuthCode.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or PiAuthCode.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or PiAuthCode.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or PiAuthCode.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or PiAuthCode.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or PiAuthCode.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or PiAuthCode.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or PiAuthCode.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or PiAuthCode.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or PiAuthCode.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or PiAuthCode.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into PiAuthCode(id, AuthCode, AuthDesc, UserName, UserPassword, RowNum, EnabledMark, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{authcode}, #{authdesc}, #{username}, #{userpassword}, #{rownum}, #{enabledmark}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiAuthCode
        <set>
            <if test="authcode != null ">
                AuthCode =#{authcode},
            </if>
            <if test="authdesc != null ">
                AuthDesc =#{authdesc},
            </if>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="userpassword != null ">
                UserPassword =#{userpassword},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from PiAuthCode where id = #{key} and Tenantid=#{tid}
    </delete>
                                                                                        </mapper>

