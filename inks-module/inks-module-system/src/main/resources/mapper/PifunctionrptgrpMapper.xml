<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PifunctionrptgrpMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PifunctionrptgrpPojo">
        <include refid="selectPifunctionrptgrpVo"/>
        where PiFunctionRptGrp.id = #{key} and PiFunctionRptGrp.Tenantid=#{tid}
    </select>
    <sql id="selectPifunctionrptgrpVo">
         select
id, Parentid, ModuleCode, RptName, Functionid, FunctionCode, FunctionName, RowNum, Remark, CreateBy, CreateByid, <PERSON>reateDate, <PERSON>er, <PERSON>erid, <PERSON>difyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, <PERSON>anti<PERSON>, Tenant<PERSON>ame, Revision        from PiFunctionRptGrp
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.PifunctionrptgrpPojo">
        <include refid="selectPifunctionrptgrpVo"/>
         where 1 = 1 and PiFunctionRptGrp.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and PiFunctionRptGrp.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>


<!--    忽略tid,通过functionid-->
    <select id="getPageListByFunctionid" resultType="inks.system.domain.pojo.PifunctionrptgrpPojo">
        <include refid="selectPifunctionrptgrpVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiFunctionRptGrp.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>


     <sql id="and">
<if test="SearchPojo.parentid != null ">
   and PiFunctionRptGrp.Parentid like concat('%', #{SearchPojo.parentid}, '%')
</if>
<if test="SearchPojo.modulecode != null ">
   and PiFunctionRptGrp.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.rptname != null ">
   and PiFunctionRptGrp.RptName like concat('%', #{SearchPojo.rptname}, '%')
</if>
<if test="SearchPojo.functionid != null ">
   and PiFunctionRptGrp.Functionid like concat('%', #{SearchPojo.functionid}, '%')
</if>
<if test="SearchPojo.functioncode != null ">
   and PiFunctionRptGrp.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
</if>
<if test="SearchPojo.functionname != null ">
   and PiFunctionRptGrp.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and PiFunctionRptGrp.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and PiFunctionRptGrp.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and PiFunctionRptGrp.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and PiFunctionRptGrp.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and PiFunctionRptGrp.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and PiFunctionRptGrp.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and PiFunctionRptGrp.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and PiFunctionRptGrp.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and PiFunctionRptGrp.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and PiFunctionRptGrp.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and PiFunctionRptGrp.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and PiFunctionRptGrp.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and PiFunctionRptGrp.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and PiFunctionRptGrp.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and PiFunctionRptGrp.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and PiFunctionRptGrp.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.parentid != null ">
   or PiFunctionRptGrp.Parentid like concat('%', #{SearchPojo.parentid}, '%')
</if>
<if test="SearchPojo.modulecode != null ">
   or PiFunctionRptGrp.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.rptname != null ">
   or PiFunctionRptGrp.RptName like concat('%', #{SearchPojo.rptname}, '%')
</if>
<if test="SearchPojo.functionid != null ">
   or PiFunctionRptGrp.Functionid like concat('%', #{SearchPojo.functionid}, '%')
</if>
<if test="SearchPojo.functioncode != null ">
   or PiFunctionRptGrp.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
</if>
<if test="SearchPojo.functionname != null ">
   or PiFunctionRptGrp.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or PiFunctionRptGrp.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or PiFunctionRptGrp.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or PiFunctionRptGrp.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or PiFunctionRptGrp.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or PiFunctionRptGrp.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or PiFunctionRptGrp.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or PiFunctionRptGrp.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or PiFunctionRptGrp.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or PiFunctionRptGrp.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or PiFunctionRptGrp.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or PiFunctionRptGrp.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or PiFunctionRptGrp.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or PiFunctionRptGrp.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or PiFunctionRptGrp.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or PiFunctionRptGrp.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or PiFunctionRptGrp.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into PiFunctionRptGrp(id, Parentid, ModuleCode, RptName, Functionid, FunctionCode, FunctionName, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{parentid}, #{modulecode}, #{rptname}, #{functionid}, #{functioncode}, #{functionname}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update PiFunctionRptGrp
        <set>
            <if test="parentid != null ">
                Parentid =#{parentid},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="rptname != null ">
                RptName =#{rptname},
            </if>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from PiFunctionRptGrp where id = #{key} and Tenantid=#{tid}
    </delete>

    <select id="getList" resultType="inks.system.domain.pojo.PifunctionrptgrpPojo">
        <include refid="selectPifunctionrptgrpVo"/>
        where PiFunctionRptGrp.Tenantid =#{tid}
    </select>
</mapper>

