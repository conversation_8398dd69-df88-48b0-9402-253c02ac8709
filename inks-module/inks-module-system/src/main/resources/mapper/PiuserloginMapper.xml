<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PiuserloginMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PiuserloginPojo">
        select id,
               Userid,
               UserPassword,
               CheckIpAddr,
               Ipaddress,
               MacAddress,
               FirstVisit,
               PreviouVisit,
               BrowserName,
               HostSystem,
               Lister,
               CreateDate,
               ModifyDate
        from PiUserLogin
        where PiUserLogin.id = #{key}
    </select>
    <sql id="selectPiuserloginVo">
        select id,
               Userid,
               UserPassword,
               CheckIpAddr,
               Ipaddress,
               MacAddress,
               FirstVisit,
               PreviouVisit,
               BrowserName,
               HostSystem,
               Lister,
               <PERSON>reate<PERSON>ate,
               ModifyDate
        from PiUserLogin
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.<PERSON>erloginPojo">
        <include refid="selectPiuserloginVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiUserLogin.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.userid != null and SearchPojo.userid  != ''">
            and PiUserLogin.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.userpassword != null and SearchPojo.userpassword  != ''">
            and PiUserLogin.UserPassword like concat('%', #{SearchPojo.userpassword}, '%')
        </if>
        <if test="SearchPojo.ipaddress != null and SearchPojo.ipaddress  != ''">
            and PiUserLogin.Ipaddress like concat('%', #{SearchPojo.ipaddress}, '%')
        </if>
        <if test="SearchPojo.macaddress != null and SearchPojo.macaddress  != ''">
            and PiUserLogin.MacAddress like concat('%', #{SearchPojo.macaddress}, '%')
        </if>
        <if test="SearchPojo.browsername != null and SearchPojo.browsername  != ''">
            and PiUserLogin.BrowserName like concat('%', #{SearchPojo.browsername}, '%')
        </if>
        <if test="SearchPojo.hostsystem != null and SearchPojo.hostsystem  != ''">
            and PiUserLogin.HostSystem like concat('%', #{SearchPojo.hostsystem}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and PiUserLogin.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.userid != null and SearchPojo.userid != ''">
            or PiUserLogin.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.userpassword != null and SearchPojo.userpassword != ''">
            or PiUserLogin.UserPassword like concat('%', #{SearchPojo.userpassword}, '%')
        </if>
        <if test="SearchPojo.ipaddress != null and SearchPojo.ipaddress != ''">
            or PiUserLogin.Ipaddress like concat('%', #{SearchPojo.ipaddress}, '%')
        </if>
        <if test="SearchPojo.macaddress != null and SearchPojo.macaddress != ''">
            or PiUserLogin.MacAddress like concat('%', #{SearchPojo.macaddress}, '%')
        </if>
        <if test="SearchPojo.browsername != null and SearchPojo.browsername != ''">
            or PiUserLogin.BrowserName like concat('%', #{SearchPojo.browsername}, '%')
        </if>
        <if test="SearchPojo.hostsystem != null and SearchPojo.hostsystem != ''">
            or PiUserLogin.HostSystem like concat('%', #{SearchPojo.hostsystem}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or PiUserLogin.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiUserLogin(id, Userid, UserPassword, CheckIpAddr, Ipaddress, MacAddress, FirstVisit, PreviouVisit,
                                BrowserName, HostSystem, Lister, CreateDate, ModifyDate)
        values (#{id}, #{userid}, #{userpassword}, #{checkipaddr}, #{ipaddress}, #{macaddress}, #{firstvisit},
                #{previouvisit}, #{browsername}, #{hostsystem}, #{lister}, #{createdate}, #{modifydate})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiUserLogin
        <set>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="userpassword != null ">
                UserPassword =#{userpassword},
            </if>
            <if test="checkipaddr != null">
                CheckIpAddr =#{checkipaddr},
            </if>
            <if test="ipaddress != null ">
                Ipaddress =#{ipaddress},
            </if>
            <if test="macaddress != null ">
                MacAddress =#{macaddress},
            </if>
            <if test="firstvisit != null">
                FirstVisit =#{firstvisit},
            </if>
            <if test="previouvisit != null">
                PreviouVisit =#{previouvisit},
            </if>
            <if test="browsername != null ">
                BrowserName =#{browsername},
            </if>
            <if test="hostsystem != null ">
                HostSystem =#{hostsystem},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiUserLogin
        where id = #{key}
    </delete>

    <!--查询单个-->
    <select id="getEntityByUserid" resultType="inks.system.domain.pojo.PiuserloginPojo">
        select id,
               Userid,
               UserPassword,
               CheckIpAddr,
               Ipaddress,
               MacAddress,
               FirstVisit,
               PreviouVisit,
               BrowserName,
               HostSystem,
               Lister,
               CreateDate,
               ModifyDate
        from PiUserLogin
        where PiUserLogin.Userid = #{key}
    </select>
    <select id="getListByUserid" resultType="inks.system.domain.pojo.PiuserloginPojo">
        select PiUserLogin.id,
               PiUserLogin.Userid,
               PiUserLogin.UserPassword,
               PiUserLogin.CheckIpAddr,
               PiUserLogin.Ipaddress,
               PiUserLogin.MacAddress,
               PiUserLogin.FirstVisit,
               PiUserLogin.PreviouVisit,
               PiUserLogin.BrowserName,
               PiUserLogin.HostSystem,
               PiUserLogin.Lister,
               PiUserLogin.CreateDate,
               PiUserLogin.ModifyDate
        from PiUserLogin
        Left Join PiTenantUser on PiUserLogin.Userid = PiTenantUser.Userid
        where PiUserLogin.Userid = #{key}
        and PiTenantUser.Tenantid = #{tenantid}
    </select>

    <select id="getPasswordByUserid" resultType="java.lang.String">
        select UserPassword from PiUserLogin where Userid=#{userid}
    </select>
</mapper>

