<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CidgformatitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CidgformatitemPojo">
        <include refid="selectCidgformatitemVo"/>
        where CiDgFormatItem.id = #{key}
          and CiDgFormatItem.Tenantid = #{tid}
    </select>
    <sql id="selectCidgformatitemVo">
        select id,
               Pid,
               ItemCode,
               ItemName,
               DefWidth,
               MinWidth,
               DisplayMark,
               Fixed,
               Sortable,
               OrderField,
               Overflow,
               Formatter,
               ClassName,
               AlignType,
               EventName,
               EditMark,
               OperationMark,
               DisplayIndex,
               RowNum,
               Remark,
               Tenantid,
               Revision
        from CiDgFormatItem
    </sql>

         <!--查询List-->
    <select id="getList" resultType="inks.system.domain.pojo.CidgformatitemPojo">
        <include refid="selectCidgformatitemVo"/>
        where CiDgFormatItem.Pid = #{Pid} and CiDgFormatItem.Tenantid=#{tid}
        order by RowNum
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CidgformatitemPojo">
        <include refid="selectCidgformatitemVo"/>
        where 1 = 1 and CiDgFormatItem.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiDgFormatItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and CiDgFormatItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
            and CiDgFormatItem.itemcode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
            and CiDgFormatItem.itemname like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.defwidth != null and SearchPojo.defwidth != ''">
            and CiDgFormatItem.defwidth like concat('%', #{SearchPojo.defwidth}, '%')
        </if>
        <if test="SearchPojo.minwidth != null and SearchPojo.minwidth != ''">
            and CiDgFormatItem.minwidth like concat('%', #{SearchPojo.minwidth}, '%')
        </if>
        <if test="SearchPojo.orderfield != null and SearchPojo.orderfield != ''">
            and CiDgFormatItem.orderfield like concat('%', #{SearchPojo.orderfield}, '%')
        </if>
        <if test="SearchPojo.formatter != null and SearchPojo.formatter != ''">
            and CiDgFormatItem.formatter like concat('%', #{SearchPojo.formatter}, '%')
        </if>
        <if test="SearchPojo.classname != null and SearchPojo.classname != ''">
            and CiDgFormatItem.classname like concat('%', #{SearchPojo.classname}, '%')
        </if>
        <if test="SearchPojo.aligntype != null and SearchPojo.aligntype != ''">
            and CiDgFormatItem.aligntype like concat('%', #{SearchPojo.aligntype}, '%')
        </if>
        <if test="SearchPojo.eventname != null and SearchPojo.eventname != ''">
            and CiDgFormatItem.eventname like concat('%', #{SearchPojo.eventname}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and CiDgFormatItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or CiDgFormatItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null and SearchPojo.itemcode != ''">
                or CiDgFormatItem.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null and SearchPojo.itemname != ''">
                or CiDgFormatItem.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.defwidth != null and SearchPojo.defwidth != ''">
                or CiDgFormatItem.DefWidth like concat('%', #{SearchPojo.defwidth}, '%')
            </if>
            <if test="SearchPojo.minwidth != null and SearchPojo.minwidth != ''">
                or CiDgFormatItem.MinWidth like concat('%', #{SearchPojo.minwidth}, '%')
            </if>
            <if test="SearchPojo.orderfield != null and SearchPojo.orderfield != ''">
                or CiDgFormatItem.OrderField like concat('%', #{SearchPojo.orderfield}, '%')
            </if>
            <if test="SearchPojo.formatter != null and SearchPojo.formatter != ''">
                or CiDgFormatItem.Formatter like concat('%', #{SearchPojo.formatter}, '%')
            </if>
            <if test="SearchPojo.classname != null and SearchPojo.classname != ''">
                or CiDgFormatItem.ClassName like concat('%', #{SearchPojo.classname}, '%')
            </if>
            <if test="SearchPojo.aligntype != null and SearchPojo.aligntype != ''">
                or CiDgFormatItem.AlignType like concat('%', #{SearchPojo.aligntype}, '%')
            </if>
            <if test="SearchPojo.eventname != null and SearchPojo.eventname != ''">
                or CiDgFormatItem.EventName like concat('%', #{SearchPojo.eventname}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or CiDgFormatItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
        </trim>
    </sql>



    <!--新增所有列-->
    <insert id="insert">
        insert into CiDgFormatItem(id, Pid, ItemCode, ItemName, DefWidth, MinWidth, DisplayMark, Fixed, Sortable, OrderField, Overflow, Formatter, ClassName, AlignType, EventName, EditMark, OperationMark, DisplayIndex, RowNum, Remark, Tenantid, Revision)
        values (#{id}, #{pid}, #{itemcode}, #{itemname}, #{defwidth}, #{minwidth}, #{displaymark}, #{fixed}, #{sortable}, #{orderfield}, #{overflow}, #{formatter}, #{classname}, #{aligntype}, #{eventname}, #{editmark}, #{operationmark}, #{displayindex}, #{rownum}, #{remark}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiDgFormatItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="itemcode != null ">
                ItemCode = #{itemcode},
            </if>
            <if test="itemname != null ">
                ItemName = #{itemname},
            </if>
            <if test="defwidth != null ">
                DefWidth = #{defwidth},
            </if>
            <if test="minwidth != null ">
                MinWidth = #{minwidth},
            </if>
            <if test="displaymark != null">
                DisplayMark = #{displaymark},
            </if>
            <if test="fixed != null">
                Fixed = #{fixed},
            </if>
            <if test="sortable != null">
                Sortable = #{sortable},
            </if>
            <if test="orderfield != null ">
                OrderField = #{orderfield},
            </if>
            <if test="overflow != null">
                Overflow = #{overflow},
            </if>
            <if test="formatter != null ">
                Formatter = #{formatter},
            </if>
            <if test="classname != null ">
                ClassName = #{classname},
            </if>
            <if test="aligntype != null ">
                AlignType = #{aligntype},
            </if>
            <if test="eventname != null ">
                EventName = #{eventname},
            </if>
            <if test="editmark != null">
                EditMark = #{editmark},
            </if>
            <if test="operationmark != null">
                OperationMark = #{operationmark},
            </if>
            <if test="displayindex != null">
                DisplayIndex = #{displayindex},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiDgFormatItem
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

</mapper>

