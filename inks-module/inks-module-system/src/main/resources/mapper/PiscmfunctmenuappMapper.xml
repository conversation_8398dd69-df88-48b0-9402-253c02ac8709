<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PiscmfunctmenuappMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PiscmfunctmenuappPojo">
        select id,
               ScmFunctid,
               ScmFunctCode,
               ScmFunctName,
               Navid,
               NavCode,
               NavName,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Revision
        from PiScmFunctMenuApp
        where PiScmFunctMenuApp.id = #{key}
    </select>
    <sql id="selectPiscmfunctmenuappVo">
        select id,
               ScmFunctid,
               <PERSON>mFunctCode,
               ScmFunctName,
               <PERSON><PERSON>,
               <PERSON>v<PERSON>ode,
               NavName,
               Remark,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               <PERSON>reate<PERSON><PERSON>,
               <PERSON>er,
               Listerid,
               ModifyDate,
               Revision
        from PiScmFunctMenuApp
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PiscmfunctmenuappPojo">
        <include refid="selectPiscmfunctmenuappVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiScmFunctMenuApp.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.scmfunctid != null ">
            and PiScmFunctMenuApp.ScmFunctid like concat('%', #{SearchPojo.scmfunctid}, '%')
        </if>
        <if test="SearchPojo.scmfunctcode != null ">
            and PiScmFunctMenuApp.ScmFunctCode like concat('%', #{SearchPojo.scmfunctcode}, '%')
        </if>
        <if test="SearchPojo.scmfunctname != null ">
            and PiScmFunctMenuApp.ScmFunctName like concat('%', #{SearchPojo.scmfunctname}, '%')
        </if>
        <if test="SearchPojo.navid != null ">
            and PiScmFunctMenuApp.Navid like concat('%', #{SearchPojo.navid}, '%')
        </if>
        <if test="SearchPojo.navcode != null ">
            and PiScmFunctMenuApp.NavCode like concat('%', #{SearchPojo.navcode}, '%')
        </if>
        <if test="SearchPojo.navname != null ">
            and PiScmFunctMenuApp.NavName like concat('%', #{SearchPojo.navname}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and PiScmFunctMenuApp.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and PiScmFunctMenuApp.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and PiScmFunctMenuApp.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and PiScmFunctMenuApp.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and PiScmFunctMenuApp.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.scmfunctid != null ">
                or PiScmFunctMenuApp.ScmFunctid like concat('%', #{SearchPojo.scmfunctid}, '%')
            </if>
            <if test="SearchPojo.scmfunctcode != null ">
                or PiScmFunctMenuApp.ScmFunctCode like concat('%', #{SearchPojo.scmfunctcode}, '%')
            </if>
            <if test="SearchPojo.scmfunctname != null ">
                or PiScmFunctMenuApp.ScmFunctName like concat('%', #{SearchPojo.scmfunctname}, '%')
            </if>
            <if test="SearchPojo.navid != null ">
                or PiScmFunctMenuApp.Navid like concat('%', #{SearchPojo.navid}, '%')
            </if>
            <if test="SearchPojo.navcode != null ">
                or PiScmFunctMenuApp.NavCode like concat('%', #{SearchPojo.navcode}, '%')
            </if>
            <if test="SearchPojo.navname != null ">
                or PiScmFunctMenuApp.NavName like concat('%', #{SearchPojo.navname}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or PiScmFunctMenuApp.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or PiScmFunctMenuApp.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or PiScmFunctMenuApp.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or PiScmFunctMenuApp.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or PiScmFunctMenuApp.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiScmFunctMenuApp(id, ScmFunctid, ScmFunctCode, ScmFunctName, Navid, NavCode, NavName, Remark,
                                      CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Revision)
        values (#{id}, #{scmfunctid}, #{scmfunctcode}, #{scmfunctname}, #{navid}, #{navcode}, #{navname}, #{remark},
                #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiScmFunctMenuApp
        <set>
            <if test="scmfunctid != null ">
                ScmFunctid =#{scmfunctid},
            </if>
            <if test="scmfunctcode != null ">
                ScmFunctCode =#{scmfunctcode},
            </if>
            <if test="scmfunctname != null ">
                ScmFunctName =#{scmfunctname},
            </if>
            <if test="navid != null ">
                Navid =#{navid},
            </if>
            <if test="navcode != null ">
                NavCode =#{navcode},
            </if>
            <if test="navname != null ">
                NavName =#{navname},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiScmFunctMenuApp
        where id = #{key}
    </delete>

    <select id="getListByFunction" resultType="inks.system.domain.pojo.PiscmfunctmenuappPojo">
        SELECT
            PiMenuWeb.NavPid,
            PiMenuWeb.NavType,
            PiScmFunctMenuApp.id,
            PiScmFunctMenuApp.ScmFunctid,
            PiScmFunctMenuApp.ScmFunctCode,
            PiScmFunctMenuApp.ScmFunctName,
            PiScmFunctMenuApp.Navid,
            PiScmFunctMenuApp.NavCode,
            PiScmFunctMenuApp.NavName,
            PiScmFunctMenuApp.Remark,
            PiScmFunctMenuApp.CreateBy,
            PiScmFunctMenuApp.CreateByid,
            PiScmFunctMenuApp.CreateDate,
            PiScmFunctMenuApp.Lister,
            PiScmFunctMenuApp.Listerid,
            PiScmFunctMenuApp.ModifyDate,
            PiScmFunctMenuApp.Revision
        FROM
            PiMenuWeb
                RIGHT JOIN PiScmFunctMenuApp ON PiMenuWeb.Navid = PiScmFunctMenuApp.Navid
        where 1 = 1 and PiScmFunctMenuApp.ScmFunctid =#{key}
        order by PiScmFunctMenuApp.NavCode
    </select>
    <select id="getListByScmFunctids" resultType="inks.system.domain.pojo.PimenuappPojo">
        SELECT DISTINCT PiMenuApp.Navid,
                        PiMenuApp.NavPid,
                        PiMenuApp.NavType,
                        PiMenuApp.NavCode,
                        PiMenuApp.NavName,
                        PiMenuApp.NavGroup,
                        PiMenuApp.RowNum,
                        PiMenuApp.ImageCss,
                        PiMenuApp.IconUrl,
                        PiMenuApp.NavigateUrl,
                        PiMenuApp.MvcUrl,
                        PiMenuApp.ModuleType,
                        PiMenuApp.ModuleCode,
                        PiMenuApp.RoleCode,
                        PiMenuApp.ImageIndex,
                        PiMenuApp.ImageStyle,
                        PiMenuApp.EnabledMark,
                        PiMenuApp.Remark,
                        PiMenuApp.PermissionCode,
                        PiMenuApp.Functionid,
                        PiMenuApp.FunctionCode,
                        PiMenuApp.FunctionName,
                        PiMenuApp.Lister,
                        PiMenuApp.CreateDate,
                        PiMenuApp.ModifyDate,
                        PiMenuApp.DeleteMark,
                        PiMenuApp.DeleteLister,
                        PiMenuApp.DeleteDate
        FROM PiMenuApp
                 LEFT JOIN PiScmFunctMenuApp ON PiScmFunctMenuApp.Navid = PiMenuApp.Navid
        WHERE PiScmFunctMenuApp.ScmFunctid in (${ids})
        order by RowNum
    </select>

</mapper>

