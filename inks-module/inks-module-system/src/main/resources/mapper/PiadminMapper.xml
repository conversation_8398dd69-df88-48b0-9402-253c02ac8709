<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PiadminMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PiadminPojo">
        select Adminid,
               UserName,
               RealName,
               Mobile,
               Email,
               LangCode,
               Avatar,
               UserStatus,
               AdminCode,
               Remark,
               WxOpenid,
               RoleType,
               Lister,
               CreateDate,
               ModifyDate
        from PiAdmin
        where PiAdmin.Adminid = #{key}
    </select>
    <sql id="selectPiadminVo">
        select Adminid,
               UserName,
               RealName,
               Mobile,
               Email,
               LangCode,
               Avatar,
               UserStatus,
               AdminCode,
               Remark,
               WxOpenid,
               <PERSON>Type,
               Lister,
               CreateDate,
               ModifyDate
        from PiAdmin
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PiadminPojo">
        <include refid="selectPiadminVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiAdmin.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.username != null and SearchPojo.username  != ''">
            and PiAdmin.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null and SearchPojo.realname  != ''">
            and PiAdmin.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.mobile != null and SearchPojo.mobile  != ''">
            and PiAdmin.Mobile like concat('%', #{SearchPojo.mobile}, '%')
        </if>
        <if test="SearchPojo.email != null and SearchPojo.email  != ''">
            and PiAdmin.Email like concat('%', #{SearchPojo.email}, '%')
        </if>
        <if test="SearchPojo.langcode != null and SearchPojo.langcode  != ''">
            and PiAdmin.LangCode like concat('%', #{SearchPojo.langcode}, '%')
        </if>
        <if test="SearchPojo.avatar != null and SearchPojo.avatar  != ''">
            and PiAdmin.Avatar like concat('%', #{SearchPojo.avatar}, '%')
        </if>
        <if test="SearchPojo.admincode != null and SearchPojo.admincode  != ''">
            and PiAdmin.AdminCode like concat('%', #{SearchPojo.admincode}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark  != ''">
            and PiAdmin.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and PiAdmin.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.username != null and SearchPojo.username != ''">
            or PiAdmin.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null and SearchPojo.realname != ''">
            or PiAdmin.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.mobile != null and SearchPojo.mobile != ''">
            or PiAdmin.Mobile like concat('%', #{SearchPojo.mobile}, '%')
        </if>
        <if test="SearchPojo.email != null and SearchPojo.email != ''">
            or PiAdmin.Email like concat('%', #{SearchPojo.email}, '%')
        </if>
        <if test="SearchPojo.langcode != null and SearchPojo.langcode != ''">
            or PiAdmin.LangCode like concat('%', #{SearchPojo.langcode}, '%')
        </if>
        <if test="SearchPojo.avatar != null and SearchPojo.avatar != ''">
            or PiAdmin.Avatar like concat('%', #{SearchPojo.avatar}, '%')
        </if>
        <if test="SearchPojo.admincode != null and SearchPojo.admincode != ''">
            or PiAdmin.AdminCode like concat('%', #{SearchPojo.admincode}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            or PiAdmin.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or PiAdmin.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiAdmin(Adminid, UserName, RealName, Mobile, Email, LangCode, Avatar, UserStatus, AdminCode, Remark,WxOpenid,RoleType,
                            Lister, CreateDate, ModifyDate)
        values (#{adminid}, #{username}, #{realname}, #{mobile}, #{email}, #{langcode}, #{avatar}, #{userstatus},
                #{admincode}, #{remark},#{wxopenid},#{roletype}, #{lister}, #{createdate}, #{modifydate})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiAdmin
        <set>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="mobile != null ">
                Mobile =#{mobile},
            </if>
            <if test="email != null ">
                Email =#{email},
            </if>
            <if test="langcode != null ">
                LangCode =#{langcode},
            </if>
            <if test="avatar != null ">
                Avatar =#{avatar},
            </if>
            <if test="userstatus != null">
                UserStatus =#{userstatus},
            </if>
            <if test="admincode != null ">
                AdminCode =#{admincode},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="wxopenid != null ">
                WxOpenid =#{wxopenid},
            </if>
            <if test="roletype != null ">
                RoleType =#{roletype},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
        </set>
        where Adminid = #{adminid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiAdmin
        where Adminid = #{key}
    </delete>

    <!--通过主键修改数据-->
    <update id="initpassword">
        update PiAdminLogin  set UserPassword =#{password},
            Lister =#{lister},
            ModifyDate =#{modifydate}
        where Adminid = #{adminid}
    </update>
</mapper>

