<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PidmsuserMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PidmsuserPojo">
        <include refid="selectPidmsuserVo"/>
        where PiDmsUser.Userid = #{key}
          and PiDmsUser.Tenantid = #{tid}
    </select>
    <sql id="selectPidmsuserVo">
        select Userid,
               UserName,
               RealName,
               NickName,
               UserPassword,
               Mobile,
               Email,
               Sex,
               LangCode,
               Avatar,
               UserType,
               IsAdmin,
               Deptid,
               DeptCode,
               DeptName,
               IsDeptAdmin,
               DeptRowNum,
               RowNum,
               UserStatus,
               UserCode,
               Groupids,
               GroupNames,
               DmsFunctids,
               DmsFunctNames,
               Remark,
               DataLabel,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Tenantid,
               TenantName,
               Revision
        from PiDmsUser
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PidmsuserPojo">
        <include refid="selectPidmsuserVo"/>
        where 1 = 1 and PiDmsUser.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiDmsUser.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.username != null ">
            and PiDmsUser.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null ">
            and PiDmsUser.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.nickname != null ">
            and PiDmsUser.NickName like concat('%', #{SearchPojo.nickname}, '%')
        </if>
        <if test="SearchPojo.userpassword != null ">
            and PiDmsUser.UserPassword like concat('%', #{SearchPojo.userpassword}, '%')
        </if>
        <if test="SearchPojo.mobile != null ">
            and PiDmsUser.Mobile like concat('%', #{SearchPojo.mobile}, '%')
        </if>
        <if test="SearchPojo.email != null ">
            and PiDmsUser.Email like concat('%', #{SearchPojo.email}, '%')
        </if>
        <if test="SearchPojo.langcode != null ">
            and PiDmsUser.LangCode like concat('%', #{SearchPojo.langcode}, '%')
        </if>
        <if test="SearchPojo.avatar != null ">
            and PiDmsUser.Avatar like concat('%', #{SearchPojo.avatar}, '%')
        </if>
        <if test="SearchPojo.deptid != null ">
            and PiDmsUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.deptcode != null ">
            and PiDmsUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
        </if>
        <if test="SearchPojo.deptname != null ">
            and PiDmsUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
        </if>
        <if test="SearchPojo.usercode != null ">
            and PiDmsUser.UserCode like concat('%', #{SearchPojo.usercode}, '%')
        </if>
        <if test="SearchPojo.groupids != null ">
            and PiDmsUser.Groupids like concat('%', #{SearchPojo.groupids}, '%')
        </if>
        <if test="SearchPojo.groupnames != null ">
            and PiDmsUser.GroupNames like concat('%', #{SearchPojo.groupnames}, '%')
        </if>
        <if test="SearchPojo.dmsfunctids != null ">
            and PiDmsUser.DmsFunctids like concat('%', #{SearchPojo.dmsfunctids}, '%')
        </if>
        <if test="SearchPojo.dmsfunctnames != null ">
            and PiDmsUser.DmsFunctNames like concat('%', #{SearchPojo.dmsfunctnames}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and PiDmsUser.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and PiDmsUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and PiDmsUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and PiDmsUser.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and PiDmsUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and PiDmsUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.username != null ">
                or PiDmsUser.UserName like concat('%', #{SearchPojo.username}, '%')
            </if>
            <if test="SearchPojo.realname != null ">
                or PiDmsUser.RealName like concat('%', #{SearchPojo.realname}, '%')
            </if>
            <if test="SearchPojo.nickname != null ">
                or PiDmsUser.NickName like concat('%', #{SearchPojo.nickname}, '%')
            </if>
            <if test="SearchPojo.userpassword != null ">
                or PiDmsUser.UserPassword like concat('%', #{SearchPojo.userpassword}, '%')
            </if>
            <if test="SearchPojo.mobile != null ">
                or PiDmsUser.Mobile like concat('%', #{SearchPojo.mobile}, '%')
            </if>
            <if test="SearchPojo.email != null ">
                or PiDmsUser.Email like concat('%', #{SearchPojo.email}, '%')
            </if>
            <if test="SearchPojo.langcode != null ">
                or PiDmsUser.LangCode like concat('%', #{SearchPojo.langcode}, '%')
            </if>
            <if test="SearchPojo.avatar != null ">
                or PiDmsUser.Avatar like concat('%', #{SearchPojo.avatar}, '%')
            </if>
            <if test="SearchPojo.deptid != null ">
                or PiDmsUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.deptcode != null ">
                or PiDmsUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
            </if>
            <if test="SearchPojo.deptname != null ">
                or PiDmsUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
            </if>
            <if test="SearchPojo.usercode != null ">
                or PiDmsUser.UserCode like concat('%', #{SearchPojo.usercode}, '%')
            </if>
            <if test="SearchPojo.groupids != null ">
                or PiDmsUser.Groupids like concat('%', #{SearchPojo.groupids}, '%')
            </if>
            <if test="SearchPojo.groupnames != null ">
                or PiDmsUser.GroupNames like concat('%', #{SearchPojo.groupnames}, '%')
            </if>
            <if test="SearchPojo.dmsfunctids != null ">
                or PiDmsUser.DmsFunctids like concat('%', #{SearchPojo.dmsfunctids}, '%')
            </if>
            <if test="SearchPojo.dmsfunctnames != null ">
                or PiDmsUser.DmsFunctNames like concat('%', #{SearchPojo.dmsfunctnames}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or PiDmsUser.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or PiDmsUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or PiDmsUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or PiDmsUser.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or PiDmsUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or PiDmsUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiDmsUser(Userid, UserName, RealName, NickName, UserPassword, Mobile, Email, Sex, LangCode, Avatar, UserType, IsAdmin, Deptid, DeptCode, DeptName, IsDeptAdmin, DeptRowNum, RowNum, UserStatus, UserCode, Groupids, GroupNames, DmsFunctids, DmsFunctNames, Remark, DataLabel, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
        values (#{userid}, #{username}, #{realname}, #{nickname}, #{userpassword}, #{mobile}, #{email}, #{sex}, #{langcode}, #{avatar}, #{usertype}, #{isadmin}, #{deptid}, #{deptcode}, #{deptname}, #{isdeptadmin}, #{deptrownum}, #{rownum}, #{userstatus}, #{usercode}, #{groupids}, #{groupnames}, #{dmsfunctids}, #{dmsfunctnames}, #{remark}, #{datalabel}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiDmsUser
        <set>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="nickname != null ">
                NickName =#{nickname},
            </if>
            <if test="userpassword != null ">
                UserPassword =#{userpassword},
            </if>
            <if test="mobile != null ">
                Mobile =#{mobile},
            </if>
            <if test="email != null ">
                Email =#{email},
            </if>
            <if test="sex != null">
                Sex =#{sex},
            </if>
            <if test="langcode != null ">
                LangCode =#{langcode},
            </if>
            <if test="avatar != null ">
                Avatar =#{avatar},
            </if>
            <if test="usertype != null">
                UserType =#{usertype},
            </if>
            <if test="isadmin != null">
                IsAdmin =#{isadmin},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="deptcode != null ">
                DeptCode =#{deptcode},
            </if>
            <if test="deptname != null ">
                DeptName =#{deptname},
            </if>
            <if test="isdeptadmin != null">
                IsDeptAdmin =#{isdeptadmin},
            </if>
            <if test="deptrownum != null">
                DeptRowNum =#{deptrownum},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="userstatus != null">
                UserStatus =#{userstatus},
            </if>
            <if test="usercode != null ">
                UserCode =#{usercode},
            </if>
            <if test="groupids != null ">
                Groupids =#{groupids},
            </if>
            <if test="groupnames != null ">
                GroupNames =#{groupnames},
            </if>
            <if test="dmsfunctids != null ">
                DmsFunctids =#{dmsfunctids},
            </if>
            <if test="dmsfunctnames != null ">
                DmsFunctNames =#{dmsfunctnames},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="datalabel != null ">
                DataLabel =#{datalabel},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where Userid = #{userid} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiDmsUser
        where Userid = #{key}
          and Tenantid = #{tid}
    </delete>
    <select id="getEntityByUserName" resultType="inks.system.domain.pojo.PidmsuserPojo">
        select Userid,
               UserName,
               RealName,
               NickName,
               UserPassword,
               Mobile,
               Email,
               Sex,
               LangCode,
               Avatar,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Revision
        from PiDmsUser
        where PiDmsUser.Mobile = #{username} or PiDmsUser.Email = #{username}
    </select>
    <select id="getPageListByTen" resultType="inks.system.domain.pojo.PidmsuserPojo">
        select PiDmsUser.Userid,
               PiDmsUser.UserName,
               PiDmsUser.RealName,
               PiDmsUser.NickName,
               PiDmsUser.UserPassword,
               PiDmsUser.Mobile,
               PiDmsUser.Email,
               PiDmsUser.Sex,
               PiDmsUser.LangCode,
               PiDmsUser.Avatar,
               PiDmsUser.Remark,
               PiDmsUser.CreateBy,
               PiDmsUser.CreateByid,
               PiDmsUser.CreateDate,
               PiDmsUser.Lister,
               PiDmsUser.Listerid,
               PiDmsUser.ModifyDate,
               PiDmsUser.Revision,
               PiTenantDmsUser.TenantId
        from PiDmsUser Left JOIN PiTenantDmsUser ON PiDmsUser.Userid = PiTenantDmsUser.Userid
        where 1=1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        <if test="tenantid != null and tenantid != ''">
            and PiTenantDmsUser.TenantId=#{tenantid}
        </if>
    </select>
    <select id="getEntityByOpenid" resultType="inks.system.domain.pojo.PidmsuserPojo">
        select PiDmsUser.Userid,
               PiDmsUser.UserName,
               PiDmsUser.RealName,
               PiDmsUser.NickName,
               PiDmsUser.UserPassword,
               PiDmsUser.Mobile,
               PiDmsUser.Email,
               PiDmsUser.Sex,
               PiDmsUser.LangCode,
               PiDmsUser.Avatar,
               PiDmsUser.UserType,
               PiDmsUser.IsAdmin,
               PiDmsUser.Deptid,
               PiDmsUser.DeptCode,
               PiDmsUser.DeptName,
               PiDmsUser.IsDeptAdmin,
               PiDmsUser.DeptRowNum,
               PiDmsUser.RowNum,
               PiDmsUser.UserStatus,
               PiDmsUser.UserCode,
               PiDmsUser.Groupids,
               PiDmsUser.GroupNames,
               PiDmsUser.DmsFunctids,
               PiDmsUser.DmsFunctNames,
               PiDmsUser.Remark,
               PiDmsUser.CreateBy,
               PiDmsUser.CreateByid,
               PiDmsUser.CreateDate,
               PiDmsUser.Lister,
               PiDmsUser.Listerid,
               PiDmsUser.ModifyDate,
               PiDmsUser.Tenantid,
               PiDmsUser.TenantName,
               PiDmsUser.Revision
        from PiDmsUser
                 Left Join inkssaas.PiDmsJustAuth on PiDmsJustAuth.Userid = PiDmsUser.Userid
        where PiDmsUser.Tenantid = #{tid}
          and PiDmsJustAuth.AuthType = 'openid'
          and PiDmsJustAuth.AuthUuid = #{openid}
    </select>
    <select id="getListByOpenid" resultType="inks.system.domain.pojo.PidmsuserPojo">
        select PiDmsUser.Userid,
               PiDmsUser.UserName,
               PiDmsUser.RealName,
               PiDmsUser.NickName,
               PiDmsUser.UserPassword,
               PiDmsUser.Mobile,
               PiDmsUser.Email,
               PiDmsUser.Sex,
               PiDmsUser.LangCode,
               PiDmsUser.Avatar,
               PiDmsUser.UserType,
               PiDmsUser.IsAdmin,
               PiDmsUser.Deptid,
               PiDmsUser.DeptCode,
               PiDmsUser.DeptName,
               PiDmsUser.IsDeptAdmin,
               PiDmsUser.DeptRowNum,
               PiDmsUser.RowNum,
               PiDmsUser.UserStatus,
               PiDmsUser.UserCode,
               PiDmsUser.Groupids,
               PiDmsUser.GroupNames,
               PiDmsUser.DmsFunctids,
               PiDmsUser.DmsFunctNames,
               PiDmsUser.Remark,
               PiDmsUser.CreateBy,
               PiDmsUser.CreateByid,
               PiDmsUser.CreateDate,
               PiDmsUser.Lister,
               PiDmsUser.Listerid,
               PiDmsUser.ModifyDate,
               PiDmsUser.Tenantid,
               PiDmsUser.TenantName,
               PiDmsUser.Revision
        from PiDmsUser
                 Left Join inkssaas.PiDmsJustAuth on PiDmsJustAuth.Userid = PiDmsUser.Userid
        where  PiDmsJustAuth.AuthType = 'openid'
          and PiDmsJustAuth.AuthUuid = #{openid}
    </select>
    <select id="getEntityByUserid" resultType="inks.system.domain.pojo.PidmsuserPojo">
        select * from PiDmsUser where Userid = #{userid} and Tenantid = #{tid}
    </select>

    <delete id="deletePiTenantDmsUser">
        delete from PiTenantDmsUser where Userid = #{userid} and Tenantid = #{tid}
    </delete>
</mapper>

