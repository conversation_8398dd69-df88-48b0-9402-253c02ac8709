<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CibillexpressionMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CibillexpressionPojo">
        select id,
               ModuleCode,
               BillName,
               OrgColumns,
               ExprTemp,
               TgColumn,
               DecNum,
               ReturnType,
               EnabledMark,
               RowNum,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from CiBillExpression
        where CiBillExpression.id = #{key}
          and CiBillExpression.Tenantid = #{tid}
    </select>
    <sql id="selectCibillexpressionVo">
        select id,
               ModuleCode,
               BillName,
               OrgColumns,
               ExprTemp,
               TgColumn,
               DecNum,
               ReturnType,
               EnabledMark,
               RowNum,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from CiBillExpression
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CibillexpressionPojo">
        <include refid="selectCibillexpressionVo"/>
        where 1 = 1 and CiBillExpression.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiBillExpression.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.modulecode != null ">
            and CiBillExpression.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.billname != null ">
            and CiBillExpression.BillName like concat('%', #{SearchPojo.billname}, '%')
        </if>
        <if test="SearchPojo.orgcolumns != null ">
            and CiBillExpression.OrgColumns like concat('%', #{SearchPojo.orgcolumns}, '%')
        </if>
        <if test="SearchPojo.exprtemp != null ">
            and CiBillExpression.ExprTemp like concat('%', #{SearchPojo.exprtemp}, '%')
        </if>
        <if test="SearchPojo.tgcolumn != null ">
            and CiBillExpression.TgColumn like concat('%', #{SearchPojo.tgcolumn}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and CiBillExpression.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and CiBillExpression.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and CiBillExpression.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and CiBillExpression.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and CiBillExpression.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and CiBillExpression.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and CiBillExpression.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and CiBillExpression.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and CiBillExpression.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and CiBillExpression.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and CiBillExpression.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.modulecode != null ">
                or CiBillExpression.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.billname != null ">
                or CiBillExpression.BillName like concat('%', #{SearchPojo.billname}, '%')
            </if>
            <if test="SearchPojo.orgcolumns != null ">
                or CiBillExpression.OrgColumns like concat('%', #{SearchPojo.orgcolumns}, '%')
            </if>
            <if test="SearchPojo.exprtemp != null ">
                or CiBillExpression.ExprTemp like concat('%', #{SearchPojo.exprtemp}, '%')
            </if>
            <if test="SearchPojo.tgcolumn != null ">
                or CiBillExpression.TgColumn like concat('%', #{SearchPojo.tgcolumn}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or CiBillExpression.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or CiBillExpression.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or CiBillExpression.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or CiBillExpression.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or CiBillExpression.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or CiBillExpression.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or CiBillExpression.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or CiBillExpression.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or CiBillExpression.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or CiBillExpression.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or CiBillExpression.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into CiBillExpression(id, ModuleCode, BillName, OrgColumns, ExprTemp, TgColumn, DecNum, ReturnType,
                                     EnabledMark, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid,
                                     ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName,
                                     Revision)
        values (#{id}, #{modulecode}, #{billname}, #{orgcolumns}, #{exprtemp}, #{tgcolumn}, #{decnum}, #{returntype},
                #{enabledmark}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid},
                #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname},
                #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiBillExpression
        <set>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="billname != null ">
                BillName =#{billname},
            </if>
            <if test="orgcolumns != null ">
                OrgColumns =#{orgcolumns},
            </if>
            <if test="exprtemp != null ">
                ExprTemp =#{exprtemp},
            </if>
            <if test="tgcolumn != null ">
                TgColumn =#{tgcolumn},
            </if>
            <if test="decnum != null">
                DecNum =#{decnum},
            </if>
            <if test="returntype != null">
                ReturnType =#{returntype},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiBillExpression
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <select id="getListByCode" resultType="inks.system.domain.pojo.CibillexpressionPojo">
        select *
        from CiBillExpression
        where ModuleCode = #{key}
          and Tenantid = #{tid}
        order by RowNum
    </select>


</mapper>

