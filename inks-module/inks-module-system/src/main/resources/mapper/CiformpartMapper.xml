<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CiformpartMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CiformpartPojo">
        select
          id, GenGroupid, ModuleCode, PartType, PartCode, PartName, ParamJson, RowNum, EnabledMark, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision        from CiFormPart
        where CiFormPart.id = #{key} and CiFormPart.Tenantid=#{tid}
    </select>
    <sql id="selectCiformpartVo">
         select
          id, GenGroupid, ModuleCode, PartType, PartCode, PartName, ParamJson, RowNum, EnabledMark, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision        from CiFormPart
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.CiformpartPojo">
        <include refid="selectCiformpartVo"/>
         where 1 = 1 and CiFormPart.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and CiFormPart.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.gengroupid != null ">
   and CiFormPart.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
</if>
<if test="SearchPojo.modulecode != null ">
   and CiFormPart.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.parttype != null ">
   and CiFormPart.PartType like concat('%', #{SearchPojo.parttype}, '%')
</if>
<if test="SearchPojo.partcode != null ">
   and CiFormPart.PartCode like concat('%', #{SearchPojo.partcode}, '%')
</if>
<if test="SearchPojo.partname != null ">
   and CiFormPart.PartName like concat('%', #{SearchPojo.partname}, '%')
</if>
<if test="SearchPojo.paramjson != null ">
   and CiFormPart.ParamJson like concat('%', #{SearchPojo.paramjson}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and CiFormPart.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and CiFormPart.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and CiFormPart.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and CiFormPart.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and CiFormPart.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and CiFormPart.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and CiFormPart.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and CiFormPart.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and CiFormPart.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and CiFormPart.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and CiFormPart.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.gengroupid != null ">
   or CiFormPart.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
</if>
<if test="SearchPojo.modulecode != null ">
   or CiFormPart.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.parttype != null ">
   or CiFormPart.PartType like concat('%', #{SearchPojo.parttype}, '%')
</if>
<if test="SearchPojo.partcode != null ">
   or CiFormPart.PartCode like concat('%', #{SearchPojo.partcode}, '%')
</if>
<if test="SearchPojo.partname != null ">
   or CiFormPart.PartName like concat('%', #{SearchPojo.partname}, '%')
</if>
<if test="SearchPojo.paramjson != null ">
   or CiFormPart.ParamJson like concat('%', #{SearchPojo.paramjson}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or CiFormPart.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or CiFormPart.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or CiFormPart.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or CiFormPart.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or CiFormPart.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or CiFormPart.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or CiFormPart.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or CiFormPart.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or CiFormPart.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or CiFormPart.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or CiFormPart.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into CiFormPart(id, GenGroupid, ModuleCode, PartType, PartCode, PartName, ParamJson, RowNum, EnabledMark, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{gengroupid}, #{modulecode}, #{parttype}, #{partcode}, #{partname}, #{paramjson}, #{rownum}, #{enabledmark}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update CiFormPart
        <set>
            <if test="gengroupid != null ">
                GenGroupid =#{gengroupid},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="parttype != null ">
                PartType =#{parttype},
            </if>
            <if test="partcode != null ">
                PartCode =#{partcode},
            </if>
            <if test="partname != null ">
                PartName =#{partname},
            </if>
            <if test="paramjson != null ">
                ParamJson =#{paramjson},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from CiFormPart where id = #{key} and Tenantid=#{tid}
    </delete>

    <select id="getPartList" resultType="inks.system.domain.pojo.CiformpartPojo">
        <include refid="selectCiformpartVo"/>
        where CiFormPart.Tenantid = #{tid}
        <if test="code != null and code != ''">
            and CiFormPart.PartCode = #{code}
        </if>
        <if test="type != null and type != ''">
            and CiFormPart.PartType = #{type}
        </if>
        order by CiFormPart.RowNum asc
    </select>
</mapper>

