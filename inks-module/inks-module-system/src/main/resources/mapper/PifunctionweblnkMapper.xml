<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PifunctionweblnkMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PifunctionweblnkPojo">
        select PiFunctionWebLnk.id,
               PiFunctionWebLnk.Functionid,
               PiFunctionWebLnk.FunctionCode,
               PiFunctionWebLnk.FunctionName,
               PiFunctionWebLnk.Navid,
               PiFunctionWebLnk.NavCode,
               PiFunctionWebLnk.NavName,
               PiFunctionWebLnk.Remark,
               PiFunctionWebLnk.CreateBy,
               PiFunctionWebLnk.CreateDate,
               PiFunctionWebLnk.Lister,
               PiFunctionWebLnk.ModifyDate,
               PiFunctionWebLnk.Tenantid,
               PiFunctionWebLnk.Revision,
               PiWebLnk.NavType,
               PiWebLnk.NavPid
        from PiWebLnk
                 RIGHT JOIN PiFunctionWebLnk ON PiWebLnk.Navid = PiFunctionWebLnk.Navid
        where PiFunctionWebLnk.id = #{key}
          and PiFunctionWebLnk.Tenantid = #{tid}
    </select>
    <sql id="selectPifunctionweblnkVo">
        select PiFunctionWebLnk.id,
               PiFunctionWebLnk.Functionid,
               PiFunctionWebLnk.FunctionCode,
               PiFunctionWebLnk.FunctionName,
               PiFunctionWebLnk.Navid,
               PiFunctionWebLnk.NavCode,
               PiFunctionWebLnk.NavName,
               PiFunctionWebLnk.Remark,
               PiFunctionWebLnk.CreateBy,
               PiFunctionWebLnk.CreateDate,
               PiFunctionWebLnk.Lister,
               PiFunctionWebLnk.ModifyDate,
               PiFunctionWebLnk.Tenantid,
               PiFunctionWebLnk.Revision,
               PiWebLnk.NavType,
               PiWebLnk.NavPid
        from  PiWebLnk
                  RIGHT JOIN  PiFunctionWebLnk ON PiWebLnk.Navid = PiFunctionWebLnk.Navid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PifunctionweblnkPojo">
        <include refid="selectPifunctionweblnkVo"/>
        where 1 = 1 and PiFunctionWebLnk.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiFunctionWebLnk.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.functionid != null ">
            and PiFunctionWebLnk.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.functioncode != null ">
            and PiFunctionWebLnk.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null ">
            and PiFunctionWebLnk.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.navid != null ">
            and PiFunctionWebLnk.Navid like concat('%', #{SearchPojo.navid}, '%')
        </if>
        <if test="SearchPojo.navcode != null ">
            and PiFunctionWebLnk.NavCode like concat('%', #{SearchPojo.navcode}, '%')
        </if>
        <if test="SearchPojo.navname != null ">
            and PiFunctionWebLnk.NavName like concat('%', #{SearchPojo.navname}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and PiFunctionWebLnk.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and PiFunctionWebLnk.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and PiFunctionWebLnk.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.functionid != null ">
                or PiFunctionWebLnk.Functionid like concat('%', #{SearchPojo.functionid}, '%')
            </if>
            <if test="SearchPojo.functioncode != null ">
                or PiFunctionWebLnk.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
            </if>
            <if test="SearchPojo.functionname != null ">
                or PiFunctionWebLnk.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
            </if>
            <if test="SearchPojo.navid != null ">
                or PiFunctionWebLnk.Navid like concat('%', #{SearchPojo.navid}, '%')
            </if>
            <if test="SearchPojo.navcode != null ">
                or PiFunctionWebLnk.NavCode like concat('%', #{SearchPojo.navcode}, '%')
            </if>
            <if test="SearchPojo.navname != null ">
                or PiFunctionWebLnk.NavName like concat('%', #{SearchPojo.navname}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or PiFunctionWebLnk.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or PiFunctionWebLnk.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or PiFunctionWebLnk.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiFunctionWebLnk(id, Functionid, FunctionCode, FunctionName, Navid, NavCode, NavName, Remark,
                                     CreateBy, CreateDate, Lister, ModifyDate, Tenantid, Revision)
        values (#{id}, #{functionid}, #{functioncode}, #{functionname}, #{navid}, #{navcode}, #{navname}, #{remark},
                #{createby}, #{createdate}, #{lister}, #{modifydate}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiFunctionWebLnk
        <set>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="navid != null ">
                Navid =#{navid},
            </if>
            <if test="navcode != null ">
                NavCode =#{navcode},
            </if>
            <if test="navname != null ">
                NavName =#{navname},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiFunctionWebLnk
        where id = #{key}
          and Tenantid = #{tid}
    </delete>
    <!--查询List-->
    <select id="getListByFunction" resultType="inks.system.domain.pojo.PifunctionweblnkPojo">
        <include refid="selectPifunctionweblnkVo"/>
        where 1 = 1 and PiFunctionWebLnk.Functionid =#{key}
        order by PiFunctionWebLnk.NavCode
    </select>
</mapper>

