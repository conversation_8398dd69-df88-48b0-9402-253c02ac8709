<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CiformvailditemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CiformvailditemPojo">
        <include refid="selectCiformvailditemVo"/>
        where CiFormVaildItem.id = #{key} and CiFormVaildItem.Tenantid=#{tid}
    </select>
    <sql id="selectCiformvailditemVo">
         select
id, Pid, ItemField, ItemLabel, Required, MinLimit, MaxLimit, Regular, TipMsg, TipMsgEn, EnabledMark, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from CiFormVaildItem
    </sql>

         <!--查询List-->
    <select id="getList" resultType="inks.system.domain.pojo.CiformvailditemPojo">
        <include refid="selectCiformvailditemVo"/>
        where CiFormVaildItem.Pid = #{Pid} and CiFormVaildItem.Tenantid=#{tid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.CiformvailditemPojo">
        <include refid="selectCiformvailditemVo"/>
         where 1 = 1 and CiFormVaildItem.Tenantid =#{tenantid} 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and CiFormVaildItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and CiFormVaildItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.itemfield != null and SearchPojo.itemfield != ''">
   and CiFormVaildItem.itemfield like concat('%', #{SearchPojo.itemfield}, '%')
</if>
<if test="SearchPojo.itemlabel != null and SearchPojo.itemlabel != ''">
   and CiFormVaildItem.itemlabel like concat('%', #{SearchPojo.itemlabel}, '%')
</if>
<if test="SearchPojo.regular != null and SearchPojo.regular != ''">
   and CiFormVaildItem.regular like concat('%', #{SearchPojo.regular}, '%')
</if>
<if test="SearchPojo.tipmsg != null and SearchPojo.tipmsg != ''">
   and CiFormVaildItem.tipmsg like concat('%', #{SearchPojo.tipmsg}, '%')
</if>
<if test="SearchPojo.tipmsgen != null and SearchPojo.tipmsgen != ''">
   and CiFormVaildItem.tipmsgen like concat('%', #{SearchPojo.tipmsgen}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and CiFormVaildItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and CiFormVaildItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and CiFormVaildItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and CiFormVaildItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and CiFormVaildItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and CiFormVaildItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or CiFormVaildItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.itemfield != null and SearchPojo.itemfield != ''">
   or CiFormVaildItem.ItemField like concat('%', #{SearchPojo.itemfield}, '%')
</if>
<if test="SearchPojo.itemlabel != null and SearchPojo.itemlabel != ''">
   or CiFormVaildItem.ItemLabel like concat('%', #{SearchPojo.itemlabel}, '%')
</if>
<if test="SearchPojo.regular != null and SearchPojo.regular != ''">
   or CiFormVaildItem.Regular like concat('%', #{SearchPojo.regular}, '%')
</if>
<if test="SearchPojo.tipmsg != null and SearchPojo.tipmsg != ''">
   or CiFormVaildItem.TipMsg like concat('%', #{SearchPojo.tipmsg}, '%')
</if>
<if test="SearchPojo.tipmsgen != null and SearchPojo.tipmsgen != ''">
   or CiFormVaildItem.TipMsgEn like concat('%', #{SearchPojo.tipmsgen}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or CiFormVaildItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or CiFormVaildItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or CiFormVaildItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or CiFormVaildItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or CiFormVaildItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or CiFormVaildItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>
     
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into CiFormVaildItem(id, Pid, ItemField, ItemLabel, Required, MinLimit, MaxLimit, Regular, TipMsg, TipMsgEn, EnabledMark, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{pid}, #{itemfield}, #{itemlabel}, #{required}, #{minlimit}, #{maxlimit}, #{regular}, #{tipmsg}, #{tipmsgen}, #{enabledmark}, #{rownum}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update CiFormVaildItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="itemfield != null ">
                ItemField = #{itemfield},
            </if>
            <if test="itemlabel != null ">
                ItemLabel = #{itemlabel},
            </if>
            <if test="required != null">
                Required = #{required},
            </if>
            <if test="minlimit != null">
                MinLimit = #{minlimit},
            </if>
            <if test="maxlimit != null">
                MaxLimit = #{maxlimit},
            </if>
            <if test="regular != null ">
                Regular = #{regular},
            </if>
            <if test="tipmsg != null ">
                TipMsg = #{tipmsg},
            </if>
            <if test="tipmsgen != null ">
                TipMsgEn = #{tipmsgen},
            </if>
            <if test="enabledmark != null">
                EnabledMark = #{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from CiFormVaildItem where id = #{key} and Tenantid=#{tid}
    </delete>

</mapper>

