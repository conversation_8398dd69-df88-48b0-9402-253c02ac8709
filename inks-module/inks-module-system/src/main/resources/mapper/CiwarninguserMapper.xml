<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.CiwarninguserMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.CiwarninguserPojo">
        SELECT CiWarningUser.id,
               CiWarningUser.Warnid,
               CiWarningUser.DiffNum,
               CiWarningUser.RowNum,
               CiWarningUser.Remark,
               CiWarningUser.CreateBy,
               CiWarningUser.CreateByid,
               CiWarningUser.CreateDate,
               CiWarningUser.Lister,
               CiWarningUser.Listerid,
               CiWarningUser.ModifyDate,
               CiWarningUser.Userid,
               CiWarningUser.RealName,
               CiWarningUser.Tenantid,
               CiWarningUser.TenantName,
               CiWarningUser.Revision,
               CiWarning.WarnCode,
               CiWarning.WarnName,
               CiWarning.WarnField,
               CiWarning.SvcCode,
               CiWarning.WarnApi,
               CiWarning.WebPath,
               CiWarning.ImageCss,
               CiWarning.TagTitle,
               CiWarning.PermCode
        FROM CiWarningUser
                 LEFT JOIN CiWarning ON CiWarningUser.Warnid = CiWarning.id
        where CiWarningUser.id = #{key}
          and CiWarningUser.Tenantid = #{tid}
    </select>
    <sql id="selectCiwarninguserVo">
        SELECT CiWarningUser.id,
               CiWarningUser.Warnid,
               CiWarningUser.DiffNum,
               CiWarningUser.RowNum,
               CiWarningUser.Remark,
               CiWarningUser.CreateBy,
               CiWarningUser.CreateByid,
               CiWarningUser.CreateDate,
               CiWarningUser.Lister,
               CiWarningUser.Listerid,
               CiWarningUser.ModifyDate,
               CiWarningUser.Userid,
               CiWarningUser.RealName,
               CiWarningUser.Tenantid,
               CiWarningUser.TenantName,
               CiWarningUser.Revision,
               CiWarning.WarnCode,
               CiWarning.WarnName,
               CiWarning.WarnField,
               CiWarning.SvcCode,
               CiWarning.WarnApi,
               CiWarning.WebPath,
               CiWarning.ImageCss,
               CiWarning.TagTitle,
               CiWarning.PermCode
        FROM CiWarningUser
                 LEFT JOIN CiWarning ON CiWarningUser.Warnid = CiWarning.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CiwarninguserPojo">
        <include refid="selectCiwarninguserVo"/>
        where 1 = 1 and CiWarningUser.Tenantid =#{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and CiWarningUser.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.warnid != null ">
            and CiWarningUser.Warnid like concat('%', #{SearchPojo.warnid}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and CiWarningUser.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and CiWarningUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and CiWarningUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and CiWarningUser.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and CiWarningUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.userid != null ">
            and CiWarningUser.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.realname != null ">
            and CiWarningUser.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and CiWarningUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.warnid != null ">
                or CiWarningUser.Warnid like concat('%', #{SearchPojo.warnid}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or CiWarningUser.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or CiWarningUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or CiWarningUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or CiWarningUser.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or CiWarningUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.userid != null ">
                or CiWarningUser.Userid like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.realname != null ">
                or CiWarningUser.RealName like concat('%', #{SearchPojo.realname}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or CiWarningUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into CiWarningUser(id, Warnid, DiffNum, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister,
                                  Listerid, ModifyDate, Userid, RealName, Tenantid, TenantName, Revision)
        values (#{id}, #{warnid}, #{diffnum}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate},
                #{lister}, #{listerid}, #{modifydate}, #{userid}, #{realname}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update CiWarningUser
        <set>
            <if test="warnid != null ">
                Warnid =#{warnid},
            </if>
            <if test="diffnum != null">
                DiffNum =#{diffnum},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from CiWarningUser
        where id = #{key}
          and Tenantid = #{tid}
    </delete>

    <!--查询指定行数据  -->
    <select id="getListByUser" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.CiwarninguserPojo">
        <include refid="selectCiwarninguserVo"/>
        where CiWarningUser.Tenantid =#{tid}
        and CiWarningUser.Userid =#{userid}
        Order by RowNum
    </select>


    <!--查询指定行数据 resultType="java.util.Map" -->
    <select id="getBusMachList" resultType="java.util.Map">
        SELECT App_Workgroup.groupuid,
               App_Workgroup.groupname,
               App_Workgroup.abbreviate,
               Bus_MachiningItem.id,
               Bus_MachiningItem.pid,
               Bus_MachiningItem.goodsid,
               Bus_MachiningItem.quantity,
               Bus_MachiningItem.taxprice,
               Bus_MachiningItem.taxamount,
               Bus_MachiningItem.itemtaxrate,
               Bus_MachiningItem.taxtotal,
               Bus_MachiningItem.price,
               Bus_MachiningItem.amount,
               Bus_MachiningItem.itemorgdate,
               Bus_MachiningItem.itemplandate,
               Bus_MachiningItem.wkqty,
               Bus_MachiningItem.stoqty,
               Bus_MachiningItem.rownum,
               Bus_MachiningItem.remark,
               Bus_MachiningItem.engstatetext,
               Bus_MachiningItem.engstatedate,
               Bus_MachiningItem.wkstatetext,
               Bus_MachiningItem.wkstatedate,
               Bus_MachiningItem.busstatetext,
               Bus_MachiningItem.busstatedate,
               Bus_MachiningItem.buyquantity,
               Bus_MachiningItem.wkquantity,
               Bus_MachiningItem.inquantity,
               Bus_MachiningItem.pickqty,
               Bus_MachiningItem.finishqty,
               Bus_MachiningItem.outquantity,
               Bus_MachiningItem.outsecqty,
               Bus_MachiningItem.editioninfo,
               Bus_MachiningItem.itemcompdate,
               Bus_MachiningItem.virtualitem,
               Bus_MachiningItem.closed,
               Bus_MachiningItem.stdprice,
               Bus_MachiningItem.stdamount,
               Bus_MachiningItem.rebate,
               Bus_MachiningItem.mrpuid,
               Bus_MachiningItem.mrpid,
               Bus_MachiningItem.maxqty,
               Bus_MachiningItem.location,
               Bus_MachiningItem.batchno,
               Bus_MachiningItem.disannulmark,
               Bus_MachiningItem.wipused,
               Bus_MachiningItem.wkwpid,
               Bus_MachiningItem.wkwpcode,
               Bus_MachiningItem.wkwpname,
               Bus_MachiningItem.wkrownum,
               Bus_MachiningItem.ordercostuid,
               Bus_MachiningItem.ordercostitemid,
               Bus_MachiningItem.bomtype,
               Bus_MachiningItem.bomid,
               Bus_MachiningItem.bomuid,
               Bus_MachiningItem.bomstate,
               Bus_MachiningItem.attributejson,
               Bus_MachiningItem.custom1,
               Bus_MachiningItem.custom2,
               Bus_MachiningItem.custom3,
               Bus_MachiningItem.custom4,
               Bus_MachiningItem.custom5,
               Bus_MachiningItem.custom6,
               Bus_MachiningItem.custom7,
               Bus_MachiningItem.custom8,
               Bus_MachiningItem.custom9,
               Bus_MachiningItem.custom10,
               Bus_MachiningItem.custom11,
               Bus_MachiningItem.custom12,
               Bus_MachiningItem.custom13,
               Bus_MachiningItem.custom14,
               Bus_MachiningItem.custom15,
               Bus_MachiningItem.custom16,
               Bus_MachiningItem.custom17,
               Bus_MachiningItem.custom18,
               Bus_MachiningItem.tenantid,
               Bus_MachiningItem.revision,
               Mat_Goods.goodsuid,
               Mat_Goods.goodsname,
               Mat_Goods.goodsspec,
               Mat_Goods.goodsunit,
               Mat_Goods.partid,
               Bus_Machining.refno,
               Bus_Machining.billtype,
               Bus_Machining.billtitle,
               Bus_Machining.billdate,
               Bus_Machining.custorderid,
               Bus_Machining.logisticsmode,
               Bus_Machining.logistirmsort,
               Bus_Machining.country,
               Bus_Machining.advaamount,
               Bus_Machining.salesman,
               Bus_Machining.taxrate,
               Bus_Machining.summary,
               Bus_Machining.createby,
               Bus_Machining.lister,
               Bus_Machining.assessor,
               Bus_Machining.itemcount,
               Bus_Machining.pickcount,
               Bus_Machining.finishcount,
               Bus_Machining.printcount,
               TIMESTAMPDIFF(DAY,NOW(), ${warnfield}) as diffnum
        FROM App_Workgroup
                 RIGHT JOIN Bus_Machining ON App_Workgroup.id = Bus_Machining.Groupid
                 RIGHT JOIN Bus_MachiningItem ON Bus_MachiningItem.Pid = Bus_Machining.id
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Bus_MachiningItem.Goodsid
        where Bus_Machining.Tenantid = #{tid}
          and TIMESTAMPDIFF(DAY,NOW(), ${warnfield}) &lt; #{diffnum}
          and Bus_MachiningItem.FinishQty &lt; Bus_MachiningItem.Quantity
          and Bus_MachiningItem.DisannulMark = 0
          and Bus_MachiningItem.Closed = 0
        Order by diffnum
    </select>

    <select id="getBusMachCanOutList" resultType="java.util.Map">
        SELECT App_Workgroup.groupuid,
               App_Workgroup.groupname,
               App_Workgroup.abbreviate,
               Bus_MachiningItem.id,
               Bus_MachiningItem.pid,
               Bus_MachiningItem.goodsid,
               Bus_MachiningItem.quantity,
               Bus_MachiningItem.taxprice,
               Bus_MachiningItem.taxamount,
               Bus_MachiningItem.itemtaxrate,
               Bus_MachiningItem.taxtotal,
               Bus_MachiningItem.price,
               Bus_MachiningItem.amount,
               Bus_MachiningItem.itemorgdate,
               Bus_MachiningItem.itemplandate,
               Bus_MachiningItem.wkqty,
               Bus_MachiningItem.stoqty,
               Bus_MachiningItem.rownum,
               Bus_MachiningItem.remark,
               Bus_MachiningItem.engstatetext,
               Bus_MachiningItem.engstatedate,
               Bus_MachiningItem.wkstatetext,
               Bus_MachiningItem.wkstatedate,
               Bus_MachiningItem.busstatetext,
               Bus_MachiningItem.busstatedate,
               Bus_MachiningItem.buyquantity,
               Bus_MachiningItem.wkquantity,
               Bus_MachiningItem.inquantity,
               Bus_MachiningItem.pickqty,
               Bus_MachiningItem.finishqty,
               Bus_MachiningItem.outquantity,
               Bus_MachiningItem.outsecqty,
               Bus_MachiningItem.editioninfo,
               Bus_MachiningItem.itemcompdate,
               Bus_MachiningItem.virtualitem,
               Bus_MachiningItem.closed,
               Bus_MachiningItem.stdprice,
               Bus_MachiningItem.stdamount,
               Bus_MachiningItem.rebate,
               Bus_MachiningItem.mrpuid,
               Bus_MachiningItem.mrpid,
               Bus_MachiningItem.maxqty,
               Bus_MachiningItem.location,
               Bus_MachiningItem.batchno,
               Bus_MachiningItem.disannulmark,
               Bus_MachiningItem.wipused,
               Bus_MachiningItem.wkwpid,
               Bus_MachiningItem.wkwpcode,
               Bus_MachiningItem.wkwpname,
               Bus_MachiningItem.wkrownum,
               Bus_MachiningItem.ordercostuid,
               Bus_MachiningItem.ordercostitemid,
               Bus_MachiningItem.bomtype,
               Bus_MachiningItem.bomid,
               Bus_MachiningItem.bomuid,
               Bus_MachiningItem.bomstate,
               Bus_MachiningItem.attributejson,
               Bus_MachiningItem.custom1,
               Bus_MachiningItem.custom2,
               Bus_MachiningItem.custom3,
               Bus_MachiningItem.custom4,
               Bus_MachiningItem.custom5,
               Bus_MachiningItem.custom6,
               Bus_MachiningItem.custom7,
               Bus_MachiningItem.custom8,
               Bus_MachiningItem.custom9,
               Bus_MachiningItem.custom10,
               Bus_MachiningItem.custom11,
               Bus_MachiningItem.custom12,
               Bus_MachiningItem.custom13,
               Bus_MachiningItem.custom14,
               Bus_MachiningItem.custom15,
               Bus_MachiningItem.custom16,
               Bus_MachiningItem.custom17,
               Bus_MachiningItem.custom18,
               Bus_MachiningItem.tenantid,
               Bus_MachiningItem.revision,
               Mat_Goods.goodsuid,
               Mat_Goods.goodsname,
               Mat_Goods.goodsspec,
               Mat_Goods.goodsunit,
               Mat_Goods.partid,
               Bus_Machining.refno,
               Bus_Machining.billtype,
               Bus_Machining.billtitle,
               Bus_Machining.billdate,
               Bus_Machining.custorderid,
               Bus_Machining.logisticsmode,
               Bus_Machining.logistirmsort,
               Bus_Machining.country,
               Bus_Machining.advaamount,
               Bus_Machining.salesman,
               Bus_Machining.taxrate,
               Bus_Machining.summary,
               Bus_Machining.createby,
               Bus_Machining.lister,
               Bus_Machining.assessor,
               Bus_Machining.itemcount,
               Bus_Machining.pickcount,
               Bus_Machining.finishcount,
               Bus_Machining.printcount,
               TIMESTAMPDIFF(DAY, NOW(), ${warnfield}) as diffnum
        FROM App_Workgroup
                 RIGHT JOIN Bus_Machining ON App_Workgroup.id = Bus_Machining.Groupid
                 RIGHT JOIN Bus_MachiningItem ON Bus_MachiningItem.Pid = Bus_Machining.id
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Bus_MachiningItem.Goodsid
        where Bus_Machining.Tenantid = #{tid}
          and TIMESTAMPDIFF(DAY, NOW(), ${warnfield}) &lt; #{diffnum}
          and Bus_MachiningItem.FinishQty &lt; Bus_MachiningItem.Quantity
          and (Bus_MachiningItem.InQuantity <![CDATA[ >= ]]> Bus_MachiningItem.WkQty
            or Bus_MachiningItem.WkQty = 0)
          and Bus_MachiningItem.DisannulMark = 0
          and Bus_MachiningItem.Closed = 0
        Order by diffnum
    </select>




    <!--查询指定行数据 resultType="java.util.Map" -->
    <select id="getBusInvoList" resultType="java.util.Map">
        SELECT Bus_Invoice.id,
               Bus_Invoice.refno,
               Bus_Invoice.billtype,
               Bus_Invoice.billtitle,
               Bus_Invoice.billdate,
               Bus_Invoice.groupid,
               Bus_Invoice.taxamount,
               Bus_Invoice.amount,
               Bus_Invoice.taxtotal,
               Bus_Invoice.taxrate,
               Bus_Invoice.invocode,
               Bus_Invoice.invodate,
               Bus_Invoice.aimdate,
               Bus_Invoice.receipted,
               Bus_Invoice.summary,
               Bus_Invoice.createby,
               Bus_Invoice.createbyid,
               Bus_Invoice.createdate,
               Bus_Invoice.lister,
               Bus_Invoice.listerid,
               Bus_Invoice.modifydate,
               Bus_Invoice.assessor,
               Bus_Invoice.assessorid,
               Bus_Invoice.assessdate,
               Bus_Invoice.statecode,
               Bus_Invoice.statedate,
               Bus_Invoice.closed,
               Bus_Invoice.disannulmark,
               Bus_Invoice.custom1,
               Bus_Invoice.custom2,
               Bus_Invoice.custom3,
               Bus_Invoice.custom4,
               Bus_Invoice.custom5,
               Bus_Invoice.custom6,
               Bus_Invoice.custom7,
               Bus_Invoice.custom8,
               Bus_Invoice.custom9,
               Bus_Invoice.custom10,
               Bus_Invoice.tenantid,
               Bus_Invoice.revision,
               App_Workgroup.groupuid,
               App_Workgroup.groupname,
               App_Workgroup.abbreviate,
               TIMESTAMPDIFF(DAY,NOW(), ${warnfield}) as diffnum
        FROM Bus_Invoice
                 LEFT JOIN App_Workgroup ON Bus_Invoice.Groupid = App_Workgroup.id
        where Bus_Invoice.Tenantid = #{tid}
          and TIMESTAMPDIFF(DAY,NOW(), ${warnfield}) &lt; #{diffnum}
          and Bus_Invoice.DisannulMark = 0
          and Bus_Invoice.Closed = 0
          and Bus_Invoice.Receipted &lt; Bus_Invoice.TaxAmount
        Order by diffnum
    </select>

    <!--查询指定行数据 resultType="java.util.Map" -->
    <select id="getBuyOrderList" resultType="java.util.Map">
        SELECT App_Workgroup.groupuid,
               App_Workgroup.groupname,
               App_Workgroup.abbreviate,
               Mat_Goods.goodsuid,
               Mat_Goods.goodsname,
               Mat_Goods.goodsspec,
               Mat_Goods.goodsunit,
               Mat_Goods.partid,
               Buy_OrderItem.id,
               Buy_OrderItem.pid,
               Buy_OrderItem.goodsid,
               Buy_OrderItem.itemcode,
               Buy_OrderItem.itemname,
               Buy_OrderItem.itemspec,
               Buy_OrderItem.itemunit,
               Buy_OrderItem.quantity,
               Buy_OrderItem.taxprice,
               Buy_OrderItem.taxamount,
               Buy_OrderItem.itemtaxrate,
               Buy_OrderItem.taxtotal,
               Buy_OrderItem.price,
               Buy_OrderItem.amount,
               Buy_OrderItem.plandate,
               Buy_OrderItem.maxqty,
               Buy_OrderItem.finishqty,
               Buy_OrderItem.remark,
               Buy_OrderItem.citeuid,
               Buy_OrderItem.citeitemid,
               Buy_OrderItem.statecode,
               Buy_OrderItem.statedate,
               Buy_OrderItem.closed,
               Buy_OrderItem.rownum,
               Buy_OrderItem.orggoodsid,
               Buy_OrderItem.subrate,
               Buy_OrderItem.machuid,
               Buy_OrderItem.machitemid,
               Buy_OrderItem.machgroupid,
               Buy_OrderItem.mainplanuid,
               Buy_OrderItem.mainplanitemid,
               Buy_OrderItem.mrpuid,
               Buy_OrderItem.mrpitemid,
               Buy_OrderItem.virtualitem,
               Buy_OrderItem.customer,
               Buy_OrderItem.custpo,
               Buy_OrderItem.batchno,
               Buy_OrderItem.disannulmark,
               Buy_OrderItem.disannullisterid,
               Buy_OrderItem.disannullister,
               Buy_OrderItem.disannuldate,
               Buy_OrderItem.attributejson,
               Buy_OrderItem.custom1,
               Buy_OrderItem.custom2,
               Buy_OrderItem.custom3,
               Buy_OrderItem.custom4,
               Buy_OrderItem.custom5,
               Buy_OrderItem.custom6,
               Buy_OrderItem.custom7,
               Buy_OrderItem.custom8,
               Buy_OrderItem.custom9,
               Buy_OrderItem.custom10,
               Buy_OrderItem.custom11,
               Buy_OrderItem.custom12,
               Buy_OrderItem.custom13,
               Buy_OrderItem.custom14,
               Buy_OrderItem.custom15,
               Buy_OrderItem.custom16,
               Buy_OrderItem.custom17,
               Buy_OrderItem.custom18,
               Buy_OrderItem.tenantid,
               Buy_OrderItem.revision,
               Buy_Order.refno,
               Buy_Order.billtype,
               Buy_Order.billtitle,
               Buy_Order.billdate,
               Buy_Order.payment,
               Buy_Order.orderno,
               Buy_Order.arrivaladd,
               Buy_Order.transport,
               Buy_Order.linkman,
               Buy_Order.linktel,
               Buy_Order.operator,
               Buy_Order.prepayments,
               Buy_Order.createby,
               Buy_Order.createdate,
               Buy_Order.modifydate,
               Buy_Order.lister,
               Buy_Order.assessor,
               Buy_Order.assessdate,
               TIMESTAMPDIFF(DAY,NOW(), ${warnfield}) as diffnum
        FROM App_Workgroup
                 RIGHT JOIN Buy_Order ON Buy_Order.Groupid = App_Workgroup.id
                 RIGHT JOIN Buy_OrderItem ON Buy_OrderItem.Pid = Buy_Order.id
                 LEFT JOIN Mat_Goods ON Mat_Goods.id = Buy_OrderItem.Goodsid
        where Buy_Order.Tenantid = #{tid}
          and TIMESTAMPDIFF(DAY,NOW(), ${warnfield}) &lt; #{diffnum}
          and Buy_OrderItem.FinishQty &lt; Buy_OrderItem.Quantity
          and Buy_OrderItem.DisannulMark = 0
          and Buy_OrderItem.Closed = 0
        Order by diffnum
    </select>

    <!--查询指定行数据 resultType="java.util.Map" -->
    <select id="getBuyInvoList" resultType="java.util.Map">
        select App_Workgroup.groupuid,
               App_Workgroup.groupname,
               App_Workgroup.abbreviate,
               Buy_Invoice.id,
               Buy_Invoice.refno,
               Buy_Invoice.billtype,
               Buy_Invoice.billtitle,
               Buy_Invoice.billdate,
               Buy_Invoice.groupid,
               Buy_Invoice.taxrate,
               Buy_Invoice.taxamount,
               Buy_Invoice.taxtotal,
               Buy_Invoice.amount,
               Buy_Invoice.invodate,
               Buy_Invoice.invocode,
               Buy_Invoice.aimdate,
               Buy_Invoice.paid,
               Buy_Invoice.summary,
               Buy_Invoice.createby,
               Buy_Invoice.createbyid,
               Buy_Invoice.createdate,
               Buy_Invoice.lister,
               Buy_Invoice.listerid,
               Buy_Invoice.modifydate,
               Buy_Invoice.assessor,
               Buy_Invoice.assessorid,
               Buy_Invoice.assessdate,
               Buy_Invoice.statecode,
               Buy_Invoice.statedate,
               Buy_Invoice.closed,
               Buy_Invoice.disannulmark,
               Buy_Invoice.custom1,
               Buy_Invoice.custom2,
               Buy_Invoice.custom3,
               Buy_Invoice.custom4,
               Buy_Invoice.custom5,
               Buy_Invoice.custom6,
               Buy_Invoice.custom7,
               Buy_Invoice.custom8,
               Buy_Invoice.custom9,
               Buy_Invoice.custom10,
               Buy_Invoice.tenantid,
               Buy_Invoice.revision,
               TIMESTAMPDIFF(DAY,NOW(), ${warnfield}) as diffnum
        FROM App_Workgroup
                 RIGHT JOIN Buy_Invoice ON App_Workgroup.id = Buy_Invoice.Groupid
        where Buy_Invoice.Tenantid = #{tid}
          and TIMESTAMPDIFF(DAY,NOW(), ${warnfield}) &lt; #{diffnum}
          and Buy_Invoice.DisannulMark = 0
          and Buy_Invoice.Closed = 0
          and Buy_Invoice.Paid &lt; Buy_Invoice.TaxAmount
        Order by diffnum
    </select>
</mapper>

