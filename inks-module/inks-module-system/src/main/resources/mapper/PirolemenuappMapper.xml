<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PirolemenuappMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PirolemenuappPojo">
        <include refid="selectPirolemenuappVo"/>
        where PiRoleMenuApp.id = #{key} and PiRoleMenuApp.Tenantid=#{tid}
    </select>
    <sql id="selectPirolemenuappVo">
         select
id, <PERSON>id, <PERSON>vid, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>y, <PERSON><PERSON><PERSON>yid, <PERSON>reate<PERSON>ate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision        from PiRoleMenuApp
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.system.domain.pojo.PirolemenuappPojo">
        <include refid="selectPirolemenuappVo"/>
         where 1 = 1 and PiRoleMenuApp.Tenantid =#{tenantid}
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and PiRoleMenuApp.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.roleid != null ">
   and PiRoleMenuApp.Roleid like concat('%', #{SearchPojo.roleid}, '%')
</if>
<if test="SearchPojo.navid != null ">
   and PiRoleMenuApp.Navid like concat('%', #{SearchPojo.navid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and PiRoleMenuApp.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and PiRoleMenuApp.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and PiRoleMenuApp.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and PiRoleMenuApp.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and PiRoleMenuApp.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.roleid != null ">
   or PiRoleMenuApp.Roleid like concat('%', #{SearchPojo.roleid}, '%')
</if>
<if test="SearchPojo.navid != null ">
   or PiRoleMenuApp.Navid like concat('%', #{SearchPojo.navid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or PiRoleMenuApp.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or PiRoleMenuApp.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or PiRoleMenuApp.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or PiRoleMenuApp.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or PiRoleMenuApp.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into PiRoleMenuApp(id, Roleid, Navid, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
        values (#{id}, #{roleid}, #{navid}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update PiRoleMenuApp
        <set>
            <if test="roleid != null ">
                Roleid =#{roleid},
            </if>
            <if test="navid != null ">
                Navid =#{navid},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} and Tenantid =#{tenantid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from PiRoleMenuApp where id = #{key} and Tenantid=#{tid}
    </delete>

    <select id="getNavidsByUserid" resultType="java.lang.String">
        SELECT DISTINCT PiRoleMenuApp.Navid
        FROM PiRoleMenuApp
                 JOIN PiUserRole ON PiRoleMenuApp.Roleid = PiUserRole.Roleid
        WHERE PiUserRole.Userid = #{userid}
          AND PiRoleMenuApp.Tenantid = #{tid}
    </select>

    <select id="getNavidsByRoleid" resultType="java.lang.String">
        SELECT DISTINCT Navid
        FROM PiRoleMenuApp
        WHERE Roleid = #{roleid}
          AND Tenantid = #{tid}
    </select>

    <delete id="deleteByRoleidAndNavid">
        delete from PiRoleMenuApp where Roleid = #{roleid} and Navid = #{navid} and Tenantid=#{tid}
    </delete>

    <delete id="batchDelete">
        delete from PiRoleMenuApp where Navid in
        <foreach collection="deleteNavids" item="navid" open="(" separator="," close=")">
            #{navid}
        </foreach>
        and Roleid = #{roleid}
        and Tenantid=#{tid}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into PiRoleMenuApp(id, Roleid, Navid, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
        values
        <foreach collection="pirolemenuappPojoList" item="pojo" separator=",">
            (
            #{pojo.id}, #{pojo.roleid}, #{pojo.navid}, #{pojo.rownum}, #{pojo.createby}, #{pojo.createbyid}, #{pojo.createdate}, #{pojo.lister}, #{pojo.listerid}, #{pojo.modifydate}, #{pojo.tenantid}, #{pojo.tenantname}, #{pojo.revision}
            )
        </foreach>
    </insert>

    <select id="getListByRole" resultType="inks.system.domain.pojo.PirolemenuappPojo">
        <include refid="selectPirolemenuappVo"/>
        where PiRoleMenuApp.Roleid = #{key} and PiRoleMenuApp.Tenantid=#{tid}
    </select>
</mapper>

