<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.system.mapper.PisubscriberMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.system.domain.pojo.PisubscriberPojo">
        select id,
               Tenantid,
               Functionid,
               Quantity,
               StartDate,
               EndDate,
               Lister,
               CreateDate,
               ModifyDate,
               TenantCode,
               TenantName,
               Company,
               FunctionCode,
               FunctionName,
               FrontPhoto,
               CycleCode,
               Container,
               TaxPrice,
               TaxAmount,
               OrderNo,
               OrderItemid,
               EnabledMark
        from PiSubscriber
        where PiSubscriber.id = #{key}
    </select>
    <sql id="selectPisubscriberVo">
        select id,
               Tenantid,
               Functionid,
               Quantity,
               StartDate,
               EndDate,
               Lister,
               <PERSON>reateDate,
               <PERSON>difyDate,
               TenantCode,
               TenantName,
               Company,
               FunctionCode,
               FunctionName,
               FrontPhoto,
               CycleCode,
               Container,
               TaxPrice,
               TaxAmount,
               OrderNo,
               OrderItemid,
               EnabledMark
        from PiSubscriber
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PisubscriberPojo">
        <include refid="selectPisubscriberVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiSubscriber.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.functionid != null and SearchPojo.functionid  != ''">
            and PiSubscriber.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister  != ''">
            and PiSubscriber.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.tenantcode != null and SearchPojo.tenantcode  != ''">
            and PiSubscriber.TenantCode like concat('%', #{SearchPojo.tenantcode}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname  != ''">
            and PiSubscriber.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.company != null and SearchPojo.company  != ''">
            and PiSubscriber.Company like concat('%', #{SearchPojo.company}, '%')
        </if>
        <if test="SearchPojo.functioncode != null and SearchPojo.functioncode  != ''">
            and PiSubscriber.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null and SearchPojo.functionname  != ''">
            and PiSubscriber.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.frontphoto != null and SearchPojo.frontphoto  != ''">
            and PiSubscriber.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
        </if>
        <if test="SearchPojo.cyclecode != null and SearchPojo.cyclecode  != ''">
            and PiSubscriber.CycleCode like concat('%', #{SearchPojo.cyclecode}, '%')
        </if>
        <if test="SearchPojo.orderno != null and SearchPojo.orderno  != ''">
            and PiSubscriber.OrderNo like concat('%', #{SearchPojo.orderno}, '%')
        </if>
        <if test="SearchPojo.orderitemid != null and SearchPojo.orderitemid  != ''">
            and PiSubscriber.OrderItemid like concat('%', #{SearchPojo.orderitemid}, '%')
        </if>
    </sql>
    <sql id="or">
        and (1=0
        <if test="SearchPojo.functionid != null and SearchPojo.functionid != ''">
            or PiSubscriber.Functionid like concat('%', #{SearchPojo.functionid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            or PiSubscriber.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.tenantcode != null and SearchPojo.tenantcode != ''">
            or PiSubscriber.TenantCode like concat('%', #{SearchPojo.tenantcode}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
            or PiSubscriber.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.company != null and SearchPojo.company != ''">
            or PiSubscriber.Company like concat('%', #{SearchPojo.company}, '%')
        </if>
        <if test="SearchPojo.functioncode != null and SearchPojo.functioncode != ''">
            or PiSubscriber.FunctionCode like concat('%', #{SearchPojo.functioncode}, '%')
        </if>
        <if test="SearchPojo.functionname != null and SearchPojo.functionname != ''">
            or PiSubscriber.FunctionName like concat('%', #{SearchPojo.functionname}, '%')
        </if>
        <if test="SearchPojo.frontphoto != null and SearchPojo.frontphoto != ''">
            or PiSubscriber.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
        </if>
        <if test="SearchPojo.cyclecode != null and SearchPojo.cyclecode != ''">
            or PiSubscriber.CycleCode like concat('%', #{SearchPojo.cyclecode}, '%')
        </if>
        <if test="SearchPojo.orderno != null and SearchPojo.orderno != ''">
            or PiSubscriber.OrderNo like concat('%', #{SearchPojo.orderno}, '%')
        </if>
        <if test="SearchPojo.orderitemid != null and SearchPojo.orderitemid != ''">
            or PiSubscriber.OrderItemid like concat('%', #{SearchPojo.orderitemid}, '%')
        </if>
        )
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into PiSubscriber(id, Tenantid, Functionid, Quantity, StartDate, EndDate, Lister, CreateDate, ModifyDate,
                                 TenantCode, TenantName, Company, FunctionCode, FunctionName, FrontPhoto, CycleCode,
                                 Container, TaxPrice, TaxAmount, OrderNo, OrderItemid, EnabledMark)
        values (#{id}, #{tenantid}, #{functionid}, #{quantity}, #{startdate}, #{enddate}, #{lister}, #{createdate},
                #{modifydate}, #{tenantcode}, #{tenantname}, #{company}, #{functioncode}, #{functionname},
                #{frontphoto}, #{cyclecode}, #{container}, #{taxprice}, #{taxamount}, #{orderno}, #{orderitemid},
                #{enabledmark})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update PiSubscriber
        <set>
            <if test="functionid != null ">
                Functionid =#{functionid},
            </if>
            <if test="quantity != null">
                Quantity =#{quantity},
            </if>
            <if test="startdate != null">
                StartDate =#{startdate},
            </if>
            <if test="enddate != null">
                EndDate =#{enddate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantcode != null ">
                TenantCode =#{tenantcode},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            <if test="company != null ">
                Company =#{company},
            </if>
            <if test="functioncode != null ">
                FunctionCode =#{functioncode},
            </if>
            <if test="functionname != null ">
                FunctionName =#{functionname},
            </if>
            <if test="frontphoto != null ">
                FrontPhoto =#{frontphoto},
            </if>
            <if test="cyclecode != null ">
                CycleCode =#{cyclecode},
            </if>
            <if test="container != null">
                Container =#{container},
            </if>
            <if test="taxprice != null">
                TaxPrice =#{taxprice},
            </if>
            <if test="taxamount != null">
                TaxAmount =#{taxamount},
            </if>
            <if test="orderno != null ">
                OrderNo =#{orderno},
            </if>
            <if test="orderitemid != null ">
                OrderItemid =#{orderitemid},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from PiSubscriber
        where id = #{key}
    </delete>


    <!--查询指定行数据-->
    <select id="getPageListByTenant" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.system.domain.pojo.PisubscriberPojo">
        <include refid="selectPisubscriberVo"/>
        where 1 = 1 and PiSubscriber.Tenantid= #{tenantid}
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">

            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and PiSubscriber.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>

            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>

    <!--查询单个-->
    <select id="getEntityByFunction" resultType="inks.system.domain.pojo.PisubscriberPojo">
        select id,
               Tenantid,
               Functionid,
               Quantity,
               StartDate,
               EndDate,
               Lister,
               CreateDate,
               ModifyDate,
               TenantCode,
               TenantName,
               Company,
               FunctionCode,
               FunctionName,
               FrontPhoto,
               CycleCode,
               Container,
               TaxPrice,
               TaxAmount,
               OrderNo,
               OrderItemid,
               EnabledMark
        from PiSubscriber
        WHERE #{date} >= PiSubscriber.StartDate
          and PiSubscriber.EndDate >= #{date}
          and PiSubscriber.Tenantid = #{tid}
          and PiSubscriber.Functionid = #{key}
    </select>

    <!--查询List-->
    <select id="getMenuWebListByTenant" resultType="inks.system.domain.pojo.PimenuwebPojo">
        SELECT DISTINCT PiMenuWeb.Navid,
                        PiMenuWeb.NavPid,
                        PiMenuWeb.NavType,
                        PiMenuWeb.NavCode,
                        PiMenuWeb.NavName,
                        PiMenuWeb.NavGroup,
                        PiMenuWeb.RowNum,
                        PiMenuWeb.ImageCss,
                        PiMenuWeb.IconUrl,
                        PiMenuWeb.NavigateUrl,
                        PiMenuWeb.MvcUrl,
                        PiMenuWeb.ModuleType,
                        PiMenuWeb.ModuleCode,
                        PiMenuWeb.RoleCode,
                        PiMenuWeb.ImageIndex,
                        PiMenuWeb.ImageStyle,
                        PiMenuWeb.EnabledMark,
                        PiMenuWeb.Remark,
                        PiMenuWeb.PermissionCode,
                        PiMenuWeb.Functionid,
                        PiMenuWeb.FunctionCode,
                        PiMenuWeb.FunctionName,
                        PiMenuWeb.IsMicroApp,
                        PiMenuWeb.MicroAppName,
                        PiMenuWeb.MicroAppEntry,
                        PiMenuWeb.MicroAppRule,
                        PiMenuWeb.MicroAppLocal,
                        PiMenuWeb.Lister,
                        PiMenuWeb.CreateDate,
                        PiMenuWeb.ModifyDate,
                        PiMenuWeb.DeleteMark,
                        PiMenuWeb.DeleteLister,
                        PiMenuWeb.DeleteDate,
                        PiMenuWeb.RouteComp,
                        PiMenuWeb.RouteName,
                        PiMenuWeb.RoutePath,
                        PiMenuWeb.HiddenMark,
                        PiSubscriber.Tenantid
        FROM PiSubscriber
                 RIGHT JOIN PiFunctionMenuWeb ON PiSubscriber.Functionid = PiFunctionMenuWeb.Functionid
                 RIGHT JOIN PiMenuWeb ON PiMenuWeb.Navid = PiFunctionMenuWeb.Navid
        WHERE #{date} >= PiSubscriber.StartDate
          and PiSubscriber.EndDate >= #{date}
          and PiSubscriber.Tenantid = #{key}
        order by RowNum
    </select>
    <!--查询List-->
    <select id="getMenuWebListByTenFnCode" resultType="inks.system.domain.pojo.PimenuwebPojo">
        SELECT DISTINCT PiMenuWeb.Navid,
                        PiMenuWeb.NavPid,
                        PiMenuWeb.NavType,
                        PiMenuWeb.NavCode,
                        PiMenuWeb.NavName,
                        PiMenuWeb.NavGroup,
                        PiMenuWeb.RowNum,
                        PiMenuWeb.ImageCss,
                        PiMenuWeb.IconUrl,
                        PiMenuWeb.NavigateUrl,
                        PiMenuWeb.MvcUrl,
                        PiMenuWeb.ModuleType,
                        PiMenuWeb.ModuleCode,
                        PiMenuWeb.RoleCode,
                        PiMenuWeb.ImageIndex,
                        PiMenuWeb.ImageStyle,
                        PiMenuWeb.EnabledMark,
                        PiMenuWeb.Remark,
                        PiMenuWeb.PermissionCode,
                        PiMenuWeb.Functionid,
                        PiMenuWeb.FunctionCode,
                        PiMenuWeb.FunctionName,
                        PiMenuWeb.IsMicroApp,
                        PiMenuWeb.MicroAppName,
                        PiMenuWeb.MicroAppEntry,
                        PiMenuWeb.MicroAppRule,
                        PiMenuWeb.MicroAppLocal,
                        PiMenuWeb.Lister,
                        PiMenuWeb.CreateDate,
                        PiMenuWeb.ModifyDate,
                        PiMenuWeb.DeleteMark,
                        PiMenuWeb.DeleteLister,
                        PiMenuWeb.DeleteDate,
                        PiMenuWeb.RouteComp,
                        PiMenuWeb.RouteName,
                        PiMenuWeb.RoutePath,
                        PiMenuWeb.HiddenMark,
                        PiSubscriber.Tenantid
        FROM PiSubscriber
                 RIGHT JOIN PiFunctionMenuWeb ON PiSubscriber.Functionid = PiFunctionMenuWeb.Functionid
                 RIGHT JOIN PiMenuWeb ON PiMenuWeb.Navid = PiFunctionMenuWeb.Navid
        WHERE #{date} >= PiSubscriber.StartDate
          and PiSubscriber.EndDate >= #{date}
          and PiSubscriber.Tenantid = #{key}
          and PiSubscriber.FunctionCode = #{fncode}
        order by RowNum
    </select>


    <select id="getMenuFrmListByTenant" resultType="inks.system.domain.pojo.PimenufrmPojo">
        SELECT DISTINCT PiMenuFrm.*,
                        PiSubscriber.Tenantid
        FROM PiSubscriber
                 RIGHT JOIN PiFunctionMenuFrm ON PiSubscriber.Functionid = PiFunctionMenuFrm.Functionid
                 RIGHT JOIN PiMenuFrm ON PiMenuFrm.Navid = PiFunctionMenuFrm.Navid
        WHERE #{date} >= PiSubscriber.StartDate
          and PiSubscriber.EndDate >= #{date}
          and PiSubscriber.Tenantid = #{key}
        order by RowNum
    </select>

    <select id="getMenuFrmListByTenFnCode" resultType="inks.system.domain.pojo.PimenufrmPojo">
        SELECT DISTINCT PiMenuFrm.*,
                        PiSubscriber.Tenantid
        FROM PiSubscriber
                 RIGHT JOIN PiFunctionMenuFrm ON PiSubscriber.Functionid = PiFunctionMenuFrm.Functionid
                 RIGHT JOIN PiMenuFrm ON PiMenuFrm.Navid = PiFunctionMenuFrm.Navid
        WHERE #{date} >= PiSubscriber.StartDate
          and PiSubscriber.EndDate >= #{date}
          and PiSubscriber.Tenantid = #{key}
          and PiSubscriber.FunctionCode = #{fncode}
        order by RowNum
    </select>


    <select id="getWebLnkListByTenant" resultType="inks.system.domain.pojo.PiweblnkPojo">
        SELECT DISTINCT PiWebLnk.Navid,
                        PiWebLnk.NavPid,
                        PiWebLnk.NavType,
                        PiWebLnk.NavCode,
                        PiWebLnk.NavName,
                        PiWebLnk.NavGroup,
                        PiWebLnk.RowNum,
                        PiWebLnk.ImageCss,
                        PiWebLnk.IconUrl,
                        PiWebLnk.NavigateUrl,
                        PiWebLnk.MvcUrl,
                        PiWebLnk.ModuleType,
                        PiWebLnk.ModuleCode,
                        PiWebLnk.RoleCode,
                        PiWebLnk.ImageIndex,
                        PiWebLnk.ImageStyle,
                        PiWebLnk.EnabledMark,
                        PiWebLnk.Remark,
                        PiWebLnk.PermissionCode,
                        PiWebLnk.Functionid,
                        PiWebLnk.FunctionCode,
                        PiWebLnk.FunctionName,
                        PiWebLnk.Lister,
                        PiWebLnk.CreateDate,
                        PiWebLnk.ModifyDate,
                        PiWebLnk.DeleteMark,
                        PiWebLnk.DeleteLister,
                        PiWebLnk.DeleteDate,
                        PiSubscriber.Tenantid
        FROM PiSubscriber
                 RIGHT JOIN PiFunctionWebLnk ON PiSubscriber.Functionid = PiFunctionWebLnk.Functionid
                 RIGHT JOIN PiWebLnk ON PiWebLnk.Navid = PiFunctionWebLnk.Navid
        WHERE #{date} >= PiSubscriber.StartDate
          and PiSubscriber.EndDate >= #{date}
          and PiSubscriber.Tenantid = #{key}
        order by RowNum
    </select>


    <select id="getWebNavListByTenant" resultType="inks.system.domain.pojo.PiwebnavPojo">
        SELECT DISTINCT PiWebNav.Navid,
                        PiWebNav.NavCode,
                        PiWebNav.NavName,
                        PiWebNav.NavContent,
                        PiWebNav.RowNum,
                        PiWebNav.EnabledMark,
                        PiWebNav.PermissionCode,
                        PiWebNav.Remark,
                        PiWebNav.CreateBy,
                        PiWebNav.CreateByid,
                        PiWebNav.CreateDate,
                        PiWebNav.Lister,
                        PiWebNav.Listerid,
                        PiWebNav.ModifyDate,
                        PiWebNav.Revision,
                        PiSubscriber.Tenantid
        FROM PiSubscriber
                 RIGHT JOIN PiFunctionWebNav ON PiSubscriber.Functionid = PiFunctionWebNav.Functionid
                 RIGHT JOIN PiWebNav ON PiWebNav.Navid = PiFunctionWebNav.Navid
        WHERE #{date} >= PiSubscriber.StartDate
          and PiSubscriber.EndDate >= #{date}
          and PiSubscriber.Tenantid = #{key}
        order by RowNum
    </select>






    <select id="getWebLnkListByTenFnCode" resultType="inks.system.domain.pojo.PiweblnkPojo">
        SELECT DISTINCT PiWebLnk.Navid,
                        PiWebLnk.NavPid,
                        PiWebLnk.NavType,
                        PiWebLnk.NavCode,
                        PiWebLnk.NavName,
                        PiWebLnk.NavGroup,
                        PiWebLnk.RowNum,
                        PiWebLnk.ImageCss,
                        PiWebLnk.IconUrl,
                        PiWebLnk.NavigateUrl,
                        PiWebLnk.MvcUrl,
                        PiWebLnk.ModuleType,
                        PiWebLnk.ModuleCode,
                        PiWebLnk.RoleCode,
                        PiWebLnk.ImageIndex,
                        PiWebLnk.ImageStyle,
                        PiWebLnk.EnabledMark,
                        PiWebLnk.Remark,
                        PiWebLnk.PermissionCode,
                        PiWebLnk.Functionid,
                        PiWebLnk.FunctionCode,
                        PiWebLnk.FunctionName,
                        PiWebLnk.Lister,
                        PiWebLnk.CreateDate,
                        PiWebLnk.ModifyDate,
                        PiWebLnk.DeleteMark,
                        PiWebLnk.DeleteLister,
                        PiWebLnk.DeleteDate,
                        PiSubscriber.Tenantid
        FROM PiSubscriber
                 RIGHT JOIN PiFunctionWebLnk ON PiSubscriber.Functionid = PiFunctionWebLnk.Functionid
                 RIGHT JOIN PiWebLnk ON PiWebLnk.Navid = PiFunctionWebLnk.Navid
        WHERE #{date} >= PiSubscriber.StartDate
          and PiSubscriber.EndDate >= #{date}
          and PiSubscriber.Tenantid = #{key}
          and PiSubscriber.FunctionCode = #{fncode}
        order by RowNum
    </select>



    <select id="getWebNavListByTenFnCode" resultType="inks.system.domain.pojo.PiwebnavPojo">
        SELECT DISTINCT PiWebNav.Navid,
                        PiWebNav.NavCode,
                        PiWebNav.NavName,
                        PiWebNav.NavContent,
                        PiWebNav.RowNum,
                        PiWebNav.EnabledMark,
                        PiWebNav.PermissionCode,
                        PiWebNav.Remark,
                        PiWebNav.CreateBy,
                        PiWebNav.CreateByid,
                        PiWebNav.CreateDate,
                        PiWebNav.Lister,
                        PiWebNav.Listerid,
                        PiWebNav.ModifyDate,
                        PiWebNav.Revision,
                        PiSubscriber.Tenantid
        FROM PiSubscriber
                 RIGHT JOIN PiFunctionWebNav
                            ON PiSubscriber.Functionid = PiFunctionWebNav.Functionid
                 RIGHT JOIN PiWebNav ON PiWebNav.Navid = PiFunctionWebNav.Navid
        WHERE #{date} >= PiSubscriber.StartDate
          and PiSubscriber.EndDate >= #{date}
          and PiSubscriber.Tenantid = #{key}
          and PiSubscriber.FunctionCode = #{fncode}
        order by RowNum
    </select>



    <!--查询List-->
    <select id="getMenuAppListByTenant" resultType="inks.system.domain.pojo.PimenuappPojo">
        SELECT DISTINCT PiMenuApp.Navid,
                        PiMenuApp.NavPid,
                        PiMenuApp.NavType,
                        PiMenuApp.NavCode,
                        PiMenuApp.NavName,
                        PiMenuApp.NavGroup,
                        PiMenuApp.RowNum,
                        PiMenuApp.ImageCss,
                        PiMenuApp.IconUrl,
                        PiMenuApp.NavigateUrl,
                        PiMenuApp.MvcUrl,
                        PiMenuApp.ModuleType,
                        PiMenuApp.ModuleCode,
                        PiMenuApp.RoleCode,
                        PiMenuApp.ImageIndex,
                        PiMenuApp.ImageStyle,
                        PiMenuApp.EnabledMark,
                        PiMenuApp.Remark,
                        PiMenuApp.PermissionCode,
                        PiMenuApp.Functionid,
                        PiMenuApp.FunctionCode,
                        PiMenuApp.FunctionName,
                        PiMenuApp.Lister,
                        PiMenuApp.CreateDate,
                        PiMenuApp.ModifyDate,
                        PiMenuApp.DeleteMark,
                        PiMenuApp.DeleteLister,
                        PiMenuApp.DeleteDate,
                        PiSubscriber.Tenantid
        FROM PiSubscriber
                 RIGHT JOIN PiFunctionMenuApp ON PiSubscriber.Functionid = PiFunctionMenuApp.Functionid
                 RIGHT JOIN PiMenuApp ON PiMenuApp.Navid = PiFunctionMenuApp.Navid
        WHERE #{date} >= PiSubscriber.StartDate
          and PiSubscriber.EndDate >= #{date}
          and PiSubscriber.Tenantid = #{key}
        order by RowNum
    </select>

    <!--查询List-->
    <select id="getPermAllByTenant" resultType="inks.system.domain.pojo.PipermcodePojo">
        SELECT DISTINCT PiSubscriber.Tenantid,
                        PiPermCode.Permid,
                        PiPermCode.Parentid,
                        PiPermCode.PermCode,
                        PiPermCode.PermName,
                        PiPermCode.RowNum,
                        PiPermCode.IsPublic,
                        PiPermCode.EnabledMark,
                        PiPermCode.AllowDelete,
                        PiPermCode.Remark,
                        PiPermCode.CreateBy,
                        PiPermCode.CreateDate,
                        PiPermCode.Lister,
                        PiPermCode.ModifyDate,
                        PiPermCode.Revision
        FROM PiPermCode
                 LEFT JOIN PiFunctionPerm ON PiFunctionPerm.Permid = PiPermCode.Permid
                 LEFT JOIN PiSubscriber ON PiSubscriber.Functionid = PiFunctionPerm.Functionid
        WHERE #{date} >= PiSubscriber.StartDate
          and PiSubscriber.EndDate >= #{date}
          and PiSubscriber.Tenantid = #{key}
        order by RowNum
    </select>

    <!--查询List-->
    <select id="getDefConfigByTenant" resultType="inks.system.domain.pojo.CiconfigPojo">
        SELECT DISTINCT CiConfig.id,
                        CiConfig.Parentid,
                        CiConfig.CfgName,
                        CiConfig.CfgKey,
                        CiConfig.CfgValue,
                        CiConfig.CfgType,
                        CiConfig.CfgLevel,
                        CiConfig.CtrlType,
                        CiConfig.CfgIcon,
                        CiConfig.CfgOption,
                        CiConfig.AllowUi,
                        CiConfig.RowNum,
                        CiConfig.EnabledMark,
                        CiConfig.AllowDelete,
                        CiConfig.Remark,
                        CiConfig.CreateBy,
                        CiConfig.CreateByid,
                        CiConfig.CreateDate,
                        CiConfig.Lister,
                        CiConfig.Listerid,
                        CiConfig.ModifyDate,
                        CiConfig.Custom1,
                        CiConfig.Custom2,
                        CiConfig.Custom3,
                        CiConfig.Custom4,
                        CiConfig.Custom5,
                        CiConfig.Userid,
                        CiConfig.Tenantid,
                        CiConfig.TenantName,
                        CiConfig.Revision
        FROM PiSubscriber
                 RIGHT JOIN PiFunctionConfig ON PiSubscriber.Functionid = PiFunctionConfig.Functionid
                 RIGHT JOIN CiConfig ON PiFunctionConfig.Cfgid = CiConfig.id
        WHERE #{date} >= PiSubscriber.StartDate
          and PiSubscriber.EndDate >= #{date}
          and PiSubscriber.Tenantid = #{key}
          and CiConfig.Tenantid = 'default'
        order by RowNum
    </select>

    <!--查询List-->
    <select id="getTenConfigByTenant" resultType="inks.system.domain.pojo.CiconfigPojo">
        SELECT DISTINCT CiConfig.id,
                        CiConfig.Parentid,
                        CiConfig.CfgName,
                        CiConfig.CfgKey,
                        CiConfig.CfgValue,
                        CiConfig.CfgType,
                        CiConfig.CfgLevel,
                        CiConfig.CtrlType,
                        CiConfig.CfgIcon,
                        CiConfig.CfgOption,
                        CiConfig.RowNum,
                        CiConfig.AllowUi,
                        CiConfig.EnabledMark,
                        CiConfig.AllowDelete,
                        CiConfig.Remark,
                        CiConfig.CreateBy,
                        CiConfig.CreateByid,
                        CiConfig.CreateDate,
                        CiConfig.Lister,
                        CiConfig.Listerid,
                        CiConfig.ModifyDate,
                        CiConfig.Custom1,
                        CiConfig.Custom2,
                        CiConfig.Custom3,
                        CiConfig.Custom4,
                        CiConfig.Custom5,
                        CiConfig.Userid,
                        CiConfig.Tenantid,
                        CiConfig.TenantName,
                        CiConfig.Revision
        FROM PiSubscriber
                 RIGHT JOIN PiFunctionConfig ON PiSubscriber.Functionid = PiFunctionConfig.Functionid
                 RIGHT JOIN CiConfig ON PiFunctionConfig.Cfgid = CiConfig.id
        WHERE #{date} >= PiSubscriber.StartDate
          and PiSubscriber.EndDate >= #{date}
          and PiSubscriber.Tenantid = #{key}
          and CiConfig.Tenantid = #{key}
        order by RowNum
    </select>
    <!--查询List-->
    <select id="getDefRoleByTenant" resultType="inks.system.domain.pojo.PirolePojo">
        SELECT DISTINCT PiRole.Roleid,
                        PiRole.RoleCode,
                        PiRole.RoleName,
                        PiRole.Functionid,
                        PiRole.FunctionCode,
                        PiRole.FunctionName,
                        PiRole.EnabledMark,
                        PiRole.RowNum,
                        PiRole.Remark,
                        PiRole.CreateBy,
                        PiRole.CreateDate,
                        PiRole.Lister,
                        PiRole.ModifyDate,
                        PiRole.Tenantid,
                        PiRole.Revision,
                        PiSubscriber.Tenantid
        FROM PiSubscriber
                 RIGHT JOIN PiRole ON PiRole.Functionid = PiSubscriber.Functionid
        WHERE #{date} >= PiSubscriber.StartDate
          and PiSubscriber.EndDate >= #{date}
          and PiSubscriber.Tenantid = #{key}
        order by RowNum
    </select>

    <!--查询List-->
    <select id="getBigDataByTenant" resultType="inks.system.domain.pojo.CibigdataPojo">
        SELECT DISTINCT CiBigData.id,
                        CiBigData.BdType,
                        CiBigData.BdCode,
                        CiBigData.BdName,
                        CiBigData.BdTitle,
                        CiBigData.FrontPhoto,
                        CiBigData.ImageCss,
                        CiBigData.MvcUrl,
                        CiBigData.RowNum,
                        CiBigData.EnabledMark,
                        CiBigData.IsPublic,
                        CiBigData.PermissionCode,
                        CiBigData.Functionid,
                        CiBigData.FunctionCode,
                        CiBigData.FunctionName,
                        CiBigData.Tenantid,
                        CiBigData.Remark,
                        CiBigData.CreateBy,
                        CiBigData.CreateDate,
                        CiBigData.Lister,
                        CiBigData.ModifyDate,
                        CiBigData.Revision
        FROM PiSubscriber
                 RIGHT JOIN CiBigData ON PiSubscriber.Functionid = CiBigData.Functionid
        WHERE #{date} >= PiSubscriber.StartDate
          and PiSubscriber.EndDate >= #{date}
          and PiSubscriber.Tenantid = #{key}
        order by CiBigData.RowNum
    </select>

    <!--查询List-->
    <select id="getWarnByTenant" resultType="inks.system.domain.pojo.CiwarningPojo">
        SELECT DISTINCT CiWarning.id,
                        CiWarning.GenGroupid,
                        CiWarning.ModuleCode,
                        CiWarning.WarnCode,
                        CiWarning.WarnName,
                        CiWarning.WarnField,
                        CiWarning.SvcCode,
                        CiWarning.WarnApi,
                        CiWarning.WebPath,
                        CiWarning.ImageCss,
                        CiWarning.TagTitle,
                        CiWarning.PermCode,
                        CiWarning.RowNum,
                        CiWarning.EnabledMark,
                        CiWarning.Remark,
                        CiWarning.CreateBy,
                        CiWarning.CreateByid,
                        CiWarning.CreateDate,
                        CiWarning.Lister,
                        CiWarning.Listerid,
                        CiWarning.ModifyDate,
                        CiWarning.Revision
        FROM PiFunctionWarn
                 LEFT JOIN PiSubscriber ON PiSubscriber.Functionid = PiFunctionWarn.Functionid
                 RIGHT JOIN CiWarning ON CiWarning.id = PiFunctionWarn.Warnid
        WHERE #{date} >= PiSubscriber.StartDate
          and PiSubscriber.EndDate >= #{date}
          and PiSubscriber.Tenantid = #{key}
        order by RowNum
    </select>

    <!--查询List-->
    <select id="getDashByTenant" resultType="inks.system.domain.pojo.CidashboardPojo">
        SELECT DISTINCT CiDashboard.id,
                        CiDashboard.GenGroupid,
                        CiDashboard.DashCode,
                        CiDashboard.DashName,
                        CiDashboard.DashDesc,
                        CiDashboard.FrontPhoto,
                        CiDashboard.MvcUrl,
                        CiDashboard.PermCode,
                        CiDashboard.EnabledMark,
                        CiDashboard.Remark,
                        CiDashboard.CreateBy,
                        CiDashboard.CreateByid,
                        CiDashboard.CreateDate,
                        CiDashboard.Lister,
                        CiDashboard.Listerid,
                        CiDashboard.ModifyDate,
                        CiDashboard.Tenantid,
                        CiDashboard.TenantName,
                        CiDashboard.Revision
        FROM PiFunctionDash
                 LEFT JOIN PiSubscriber ON PiSubscriber.Functionid = PiFunctionDash.Functionid
                 RIGHT JOIN CiDashboard ON CiDashboard.id = PiFunctionDash.Dashid
        WHERE #{date} >= PiSubscriber.StartDate
          and PiSubscriber.EndDate >= #{date}
          and PiSubscriber.Tenantid = #{key}
        order by DashCode
    </select>


    <!--查询List-->
    <select id="getReportsByTenant" resultType="inks.system.domain.pojo.CireportsPojo">
        SELECT DISTINCT CiReports.id,
                        CiReports.GenGroupid,
                        CiReports.ModuleCode,
                        CiReports.RptType,
                        CiReports.RptName,
                        CiReports.RptData,
                        CiReports.PageRow,
                        CiReports.TempUrl,
                        CiReports.FileName,
                        CiReports.PrinterSn,
                        CiReports.RowNum,
                        CiReports.EnabledMark,
                        CiReports.GrfData,
                        CiReports.PaperLength,
                        CiReports.PaperWidth,
                        CiReports.Remark,
                        CiReports.CreateBy,
                        CiReports.CreateByid,
                        CiReports.CreateDate,
                        CiReports.Lister,
                        CiReports.Listerid,
                        CiReports.ModifyDate,
                        CiReports.Custom1,
                        CiReports.Custom2,
                        CiReports.Custom3,
                        CiReports.Custom4,
                        CiReports.Custom5,
                        CiReports.Tenantid,
                        CiReports.TenantName,
                        CiReports.Revision
        FROM PiFunctionReports
                 LEFT JOIN PiSubscriber ON PiSubscriber.Functionid = PiFunctionReports.Functionid
                 RIGHT JOIN CiReports ON CiReports.id = PiFunctionReports.Reportid
        WHERE #{date} >= PiSubscriber.StartDate
          and PiSubscriber.EndDate >= #{date}
          and PiSubscriber.Tenantid = #{key}
        order by RowNum
    </select>


</mapper>

