//package inks.system.config.face;
//
//import org.apache.ibatis.type.BaseTypeHandler;
//import org.apache.ibatis.type.JdbcType;
//import org.apache.ibatis.type.MappedTypes;
//
//import java.sql.CallableStatement;
//import java.sql.PreparedStatement;
//import java.sql.ResultSet;
//import java.sql.SQLException;
///**
// * 人脸识别 mapper.xml中人脸向量类型转换器
// */
//@MappedTypes(float[].class)
//public class VectorTypeHandler extends BaseTypeHandler<float[]> {
//
//    @Override
//    public void setNonNullParameter(PreparedStatement ps, int i, float[] parameter, JdbcType jdbcType) throws SQLException {
//        StringBuilder sb = new StringBuilder();
//        sb.append("[");
//        for (int j = 0; j < parameter.length; j++) {
//            if (j > 0) {
//                sb.append(",");
//            }
//            sb.append(parameter[j]);
//        }
//        sb.append("]");
//        ps.setString(i, sb.toString());
//    }
//
//    @Override
//    public float[] getNullableResult(ResultSet rs, String columnName) throws SQLException {
//        return parseVectorString(rs.getString(columnName));
//    }
//
//    @Override
//    public float[] getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
//        return parseVectorString(rs.getString(columnIndex));
//    }
//
//    @Override
//    public float[] getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
//        return parseVectorString(cs.getString(columnIndex));
//    }
//
//    private float[] parseVectorString(String vectorString) {
//        if (vectorString == null) {
//            return null;
//        }
//        vectorString = vectorString.substring(1, vectorString.length() - 1);
//        String[] parts = vectorString.split(",");
//        float[] vector = new float[parts.length];
//        for (int i = 0; i < parts.length; i++) {
//            vector[i] = Float.parseFloat(parts[i].trim());
//        }
//        return vector;
//    }
//}