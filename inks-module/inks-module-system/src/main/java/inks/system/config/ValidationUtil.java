package inks.system.config;

import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 校验接口：每个校验规则都实现该接口，只负责返回校验结果
 *
 * 校验规则：
 * // Null 校验：值必须为 null
 * // NotNull  校验：值不为 null 且（若为字符串）不为空字符串
 * // NotBlank 校验：值不为 null 且（若为字符串）不为空字符串,去除空格后也不为空
 * // NotEmpty 校验：值不为空（支持字符串、数组、List 等）
 * // Min 数值校验：若值非 null，则值必须大于等于配置的最小值
 * // Max 数值校验：若值非 null，则值必须小于等于配置的最大值
 * // Range 数值范围校验：值必须在指定的范围内
 * // Email 格式校验：验证值是否为合法的电子邮件地址
 * // Phone 格式校验：验证值是否为合法的手机号码
 * // Regex 自定义正则校验：根据正则表达式进行校验
 * // DateFormat 日期格式校验：使用线程安全的 DateTimeFormatter 校验日期格式
 * // Enum 枚举校验：验证值是否为指定枚举的有效值
 * // Digits 数字字符串校验：验证值是否为有效的数字字符串
 * // IdCard 身份证格式校验：验证值是否为合法的身份证号码
 * // Size 长度区间校验：用于容器类型（如 List、Array 等），验证大小是否在指定范围内
 * // Length 固定长度校验：针对字符串，验证长度是否符合指定要求
 */

interface Validator {
    boolean isValid(RuleVO rule, Object value);
}

/**
 * 辅助工具类，提供公共方法
 */
class ValidatorUtil {
    public static boolean isEmpty(Object value) {
        if (value == null) return true;
        if (value instanceof String) return ((String) value).isEmpty();
        if (value instanceof java.util.List) return ((java.util.List<?>) value).isEmpty();
        if (value instanceof Object[]) return ((Object[]) value).length == 0;
        return false;
    }

    public static boolean isBlank(Object value) {
        return value == null || (value instanceof String && ((String) value).trim().isEmpty());
    }

    public static Double parseDouble(Object value) throws NumberFormatException {
        return Double.parseDouble(String.valueOf(value));
    }
}

/*-------------------各校验规则实现---------------------*/

/**
 * NotNull 校验：值不为 null 且（若为字符串）不为空字符串
 */
class NotNullValidator implements Validator {
    @Override
    public boolean isValid(RuleVO rule, Object value) {
        return value != null && !(value instanceof String && ((String) value).isEmpty());
    }
}

/**
 * Null 校验：值必须为 null
 */
class NullValidator implements Validator {
    @Override
    public boolean isValid(RuleVO rule, Object value) {
        return value == null;
    }
}

/**
 * NotEmpty 校验：值不为空（支持字符串、数组、List 等）
 */
class NotEmptyValidator implements Validator {
    @Override
    public boolean isValid(RuleVO rule, Object value) {
        return !ValidatorUtil.isEmpty(value);
    }
}

/**
 * NotBlank 校验：若为字符串，去除空白后不为空
 */
class NotBlankValidator implements Validator {
    @Override
    public boolean isValid(RuleVO rule, Object value) {
        return !ValidatorUtil.isBlank(value);
    }
}

/**
 * Min 数值校验：若值非 null，则值必须大于等于配置的最小值
 */
class MinValidator implements Validator {
    @Override
    public boolean isValid(RuleVO rule, Object value) {
        if (value == null) return true;
        try {
            double num = ValidatorUtil.parseDouble(value);
            double min = Double.parseDouble(rule.getRulevalue());
            return num >= min;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}

/**
 * Max 数值校验：若值非 null，则值必须小于等于配置的最大值
 */
class MaxValidator implements Validator {
    @Override
    public boolean isValid(RuleVO rule, Object value) {
        if (value == null) return true;
        try {
            double num = ValidatorUtil.parseDouble(value);
            double max = Double.parseDouble(rule.getRulevalue());
            return num <= max;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}

/**
 * Range 数值区间校验
 */
class RangeValidator implements Validator {
    @Override
    public boolean isValid(RuleVO rule, Object value) {
        if (value == null) return true;
        try {
            double num = ValidatorUtil.parseDouble(value);
            String[] parts = rule.getRulevalue().split(",");
            double min = Double.parseDouble(parts[0]);
            double max = Double.parseDouble(parts[1]);
            return num >= min && num <= max;
        } catch (Exception e) {
            return false;
        }
    }
}

/**
 * Email 格式校验
 */
class EmailValidator implements Validator {
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$");
    @Override
    public boolean isValid(RuleVO rule, Object value) {
        // 允许 null 值，由 NotNull 校验控制必填性
        return value == null || EMAIL_PATTERN.matcher(String.valueOf(value)).matches();
    }
}

/**
 * Phone 格式校验
 */
class PhoneValidator implements Validator {
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    @Override
    public boolean isValid(RuleVO rule, Object value) {
        return value == null || PHONE_PATTERN.matcher(String.valueOf(value)).matches();
    }
}

/**
 * Regex 自定义正则校验
 */
class RegexValidator implements Validator {
    @Override
    public boolean isValid(RuleVO rule, Object value) {
        return value != null && String.valueOf(value).matches(rule.getRulevalue());
    }
}

/**
 * DateFormat 日期格式校验（使用线程安全的 DateTimeFormatter）
 */
class DateFormatValidator implements Validator {
    @Override
    public boolean isValid(RuleVO rule, Object value) {
        if (value == null) return true;
        try {
            DateTimeFormatter.ofPattern(rule.getRulevalue()).parse(String.valueOf(value));
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }
}

/**
 * Enum 枚举校验
 */
class EnumValidator implements Validator {
    @Override
    public boolean isValid(RuleVO rule, Object value) {
        if (value == null) return true;
        return Arrays.asList(rule.getRulevalue().split(",")).contains(String.valueOf(value));
    }
}

/**
 * Digits 数字字符校验
 */
class DigitsValidator implements Validator {
    @Override
    public boolean isValid(RuleVO rule, Object value) {
        return value == null || String.valueOf(value).matches("^\\d+$");
    }
}

/**
 * IdCard 身份证格式校验
 */
class IdCardValidator implements Validator {
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("^[1-9]\\d{5}(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]$");
    @Override
    public boolean isValid(RuleVO rule, Object value) {
        return value == null || ID_CARD_PATTERN.matcher(String.valueOf(value)).matches();
    }
}

/**
 * Size 长度区间校验（针对字符串）
 */
class SizeValidator implements Validator {
    @Override
    public boolean isValid(RuleVO rule, Object value) {
        if (value == null) return true;
        String str = String.valueOf(value);
        String[] parts = rule.getRulevalue().split(",");
        int min = Integer.parseInt(parts[0]);
        int max = Integer.parseInt(parts[1]);
        return str.length() >= min && str.length() <= max;
    }
}

/**
 * Length 固定长度校验（针对字符串）
 */
class LengthValidator implements Validator {
    @Override
    public boolean isValid(RuleVO rule, Object value) {
        if (value == null) return true;
        String str = String.valueOf(value);
        int requiredLength = Integer.parseInt(rule.getRulevalue());
        return str.length() == requiredLength;
    }
}

/*-------------------核心校验工具类---------------------*/

/**
 * 核心校验工具类：根据规则类型选择对应的校验器，只负责返回校验结果
 */
public class ValidationUtil {
    private static final Map<String, Validator> validators = new HashMap<>();
    static {
        validators.put("NotNull", new NotNullValidator());   // NotNull 校验：值不为 null 且（若为字符串）不为空字符串
        validators.put("Null", new NullValidator());           // Null 校验：值必须为 null
        validators.put("NotEmpty", new NotEmptyValidator());  // NotEmpty 校验：值不为空（支持字符串、数组、List 等）
        validators.put("NotBlank", new NotBlankValidator());  // NotBlank 校验：值不为 null 且（若为字符串）不为空字符串,去除空格后也不为空
        validators.put("Min", new MinValidator());            // Min 数值校验：若值非 null，则值必须大于等于配置的最小值
        validators.put("Max", new MaxValidator());            // Max 数值校验：若值非 null，则值必须小于等于配置的最大值
        validators.put("Range", new RangeValidator());         // Range 数值范围校验
        validators.put("Email", new EmailValidator());        // Email 格式校验
        validators.put("Phone", new PhoneValidator());        // Phone 格式校验
        validators.put("Regex", new RegexValidator());            // Regex 自定义正则校验
        validators.put("DateFormat", new DateFormatValidator());   // DateFormat 日期格式校验（使用线程安全的 DateTimeFormatter）
        validators.put("Enum", new EnumValidator());           // Enum 枚举校验
        validators.put("Digits", new DigitsValidator());        // Digits 数字字符串校验
        validators.put("IdCard", new IdCardValidator());        // IdCard 身份证格式校验
        validators.put("Size", new SizeValidator());          // Size 长度区间校验（针对容器类）
        validators.put("Length", new LengthValidator());       // Length 固定长度校验（针对字符串）
    }


    /**
     * 获取对应的校验器
     */
    public static Validator getValidator(String ruleType) {
        return validators.get(ruleType);
    }

    /**
     * 根据规则和值进行校验，返回 true 表示通过，false 表示失败
     */
    public static boolean isValid(RuleVO rule, Object value) {
        Validator validator = getValidator(rule.getRuletype());
        if (validator != null) {
            return validator.isValid(rule, value);
        } else {
            // 对于不支持的规则类型，统一返回 false
            return false;
        }
    }
}
