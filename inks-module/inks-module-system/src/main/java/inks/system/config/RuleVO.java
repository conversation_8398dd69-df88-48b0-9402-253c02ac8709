package inks.system.config;

/**
 * 规则对象(动态校验子表)
 */
public class RuleVO {
        private final String fieldname;
        private final String comment;
        private final String rulevalue;
        private final String ruletype;
        private final String errormessage;
        private final String operation;

        public RuleVO(String fieldname, String comment, String rulevalue, String ruletype, String errormessage, String operation) {
            this.fieldname = fieldname;
            this.comment = comment;
            this.rulevalue = rulevalue;
            this.ruletype = ruletype;
            this.errormessage = errormessage;
            this.operation = operation;
        }

        public String getFieldname() {
            return fieldname;
        }
        public String getComment() {
            return comment;
        }
        public String getRulevalue() {
            return rulevalue;
        }
        public String getRuletype() {
            return ruletype;
        }
        public String getErrormessage() {
            return errormessage;
        }
        public String getOperation() {
            return operation;
        }
    }