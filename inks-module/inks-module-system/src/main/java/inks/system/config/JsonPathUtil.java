package inks.system.config;

import com.jayway.jsonpath.JsonPath;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class JsonPathUtil {
    public static Object buildJsonPath(String jsonInput, String fieldPath) {
        // 解析 JSON 得到原始对象
        Object jsonObj = JsonPath.parse(jsonInput).json();
        String[] segments = fieldPath.split("\\.");
        StringBuilder pathBuilder = new StringBuilder("$");
        Object currentNode = jsonObj;

        // 遍历每个字段
        for (int i = 0; i < segments.length; i++) {
            String segment = segments[i];
            boolean isArrayField = false;
            // 如果当前节点是集合，则遍历集合中的每个元素
            if (currentNode instanceof List) {
                List<Object> tempNodes = new ArrayList<>();
                for (Object node : (List<?>) currentNode) {
                    if (node instanceof Map) {
                        Object child = ((Map<?, ?>) node).get(segment);
                        tempNodes.add(child);
                        if (child instanceof List) {
                            isArrayField = true;
                        }
                    }
                }
                currentNode = tempNodes;
            } else if (currentNode instanceof Map) {
                Object child = ((Map<?, ?>) currentNode).get(segment);
                if (child instanceof List) {
                    isArrayField = true;
                }
                currentNode = child;
            }

            // 如果不是最后一级字段，并且当前真实字段为数组，则拼接 [*]
            if (i < segments.length - 1 && isArrayField) {
                pathBuilder.append("['").append(segment).append("'][*]");
            } else {
                pathBuilder.append("['").append(segment).append("']");
            }
        }
        //return pathBuilder.toString();
        try {
            return JsonPath.read(jsonInput, pathBuilder.toString());
        } catch (Exception e) {
            throw new RuntimeException("wuwuwuwu");
        }
    }


    public static void main(String[] args) {
        String json = "{\n" +
                "  \"refno\": \"BS2025\",\n" +
                "  \"item\": [\n" +
                "    { \"item2\": [ { \"goodsname\": 1 }, { \"uid\": \"001\" } ] },\n" +
                "    { \"name\": \"小A\" },\n" +
                "    { \"item2\": [ { \"goodsname\": \"orange\" }, { \"uid\": \"002\" } ] }\n" +
                "  ]\n" +
                "}";

        // 测试路径 $['item'][*]['item2'][*]['goodsname']
        Object path = buildJsonPath(json, "item.item2.goodsname");
        System.out.println("path:" + path);

        // 测试路径 $['refno']
        Object path2 = buildJsonPath(json, "refno");
        System.out.println("path2:" + path2);

        // 测试路径 $['refno']
        Object path3 = buildJsonPath(json, "item.name");
        System.out.println("path3:" + path3);

        // 测试路径 $['refno.name']
        Object path4 = buildJsonPath(json, "refno.name");
        System.out.println("path4:" + path4);

        // 测试路径 $['item'][*]['qqq']
        Object path5 = buildJsonPath(json, "item.qqq");
        System.out.println("path5:" + path5);

        // 测试路径 $['item']['goodsname']
        Object path6 = buildJsonPath(json, "item.goodsname");
        System.out.println("path6:" + path6);
    }
}
