//package inks.system.config.face;
//
//import org.apache.ibatis.type.BaseTypeHandler;
//import org.apache.ibatis.type.JdbcType;
//import org.apache.ibatis.type.MappedTypes;
//
//import java.sql.CallableStatement;
//import java.sql.PreparedStatement;
//import java.sql.ResultSet;
//import java.sql.SQLException;
///**
// * 人脸识别 mapper.xml中关键5点坐标类型处理器
// */
//@MappedTypes(double[][].class)
//public class PointsTypeHandler extends BaseTypeHandler<double[][]> {
//
//    @Override
//    public void setNonNullParameter(PreparedStatement ps, int i, double[][] parameter, JdbcType jdbcType) throws SQLException {
//        StringBuilder sb = new StringBuilder("{");
//        for (double[] row : parameter) {
//            sb.append("{");
//            for (double value : row) {
//                sb.append(value).append(",");
//            }
//            sb.setLength(sb.length() - 1); // Remove last comma
//            sb.append("},");
//        }
//        sb.setLength(sb.length() - 1); // Remove last comma
//        sb.append("}");
//        ps.setString(i, sb.toString());
//    }
//
//    @Override
//    public double[][] getNullableResult(ResultSet rs, String columnName) throws SQLException {
//        return parsePointsString(rs.getString(columnName));
//    }
//
//    @Override
//    public double[][] getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
//        return parsePointsString(rs.getString(columnIndex));
//    }
//
//    @Override
//    public double[][] getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
//        return parsePointsString(cs.getString(columnIndex));
//    }
//
//    private double[][] parsePointsString(String value) {
//        if (value == null) {
//            return null;
//        }
//        value = value.substring(1, value.length() - 1);
//        String[] rows = value.split("\\},\\{");
//        double[][] array = new double[rows.length][];
//
//        for (int i = 0; i < rows.length; i++) {
//            String row = rows[i];
//            row = row.trim();
//            if (row.startsWith("{")) {
//                row = row.substring(1);
//            }
//            if (row.endsWith("}")) {
//                row = row.substring(0, row.length() - 1);
//            }
//
//            String[] values = row.split(",");
//            array[i] = new double[values.length];
//            for (int j = 0; j < values.length; j++) {
//                array[i][j] = Double.parseDouble(values[j].trim());
//            }
//        }
//        return array;
//    }
//}