package inks.system.config;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DynamicValidation {
    // 对应动态校验规则主表的 validationcode
    String code();
    // 方法类型 对应动态校验规则子表的 checktype
    Operation type();
}
