//package inks.system.config.face;
//
//import javax.persistence.AttributeConverter;
//import javax.persistence.Converter;
///**
// * 人脸识别 PifaceinfoPojo关键5点坐标转换器
// */
//@Converter
//public class PointsConverter implements AttributeConverter<double[][], String> {
//    @Override
//    public String convertToDatabaseColumn(double[][] attribute) {
//        if (attribute == null) {
//            return null;
//        }
//        StringBuilder sb = new StringBuilder("{");
//        for (double[] row : attribute) {
//            sb.append("{");
//            for (double value : row) {
//                sb.append(value).append(",");
//            }
//            sb.setLength(sb.length() - 1); // Remove last comma
//            sb.append("},");
//        }
//        sb.setLength(sb.length() - 1); // Remove last comma
//        sb.append("}");
//        return sb.toString();
//    }
//
//    @Override
//    public double[][] convertToEntityAttribute(String dbData) {
//        if (dbData == null) {
//            return null;
//        }
//        String value = dbData.substring(1, dbData.length() - 1); // Remove outer braces
//        String[] rows = value.split("\\},\\{");
//        double[][] array = new double[rows.length][];
//
//        for (int i = 0; i < rows.length; i++) {
//            String row = rows[i];
//            row = row.trim();
//            if (row.startsWith("{")) {
//                row = row.substring(1);
//            }
//            if (row.endsWith("}")) {
//                row = row.substring(0, row.length() - 1);
//            }
//
//            String[] values = row.split(",");
//            array[i] = new double[values.length];
//            for (int j = 0; j < values.length; j++) {
//                array[i][j] = Double.parseDouble(values[j].trim());
//            }
//        }
//        return array;
//    }
//}