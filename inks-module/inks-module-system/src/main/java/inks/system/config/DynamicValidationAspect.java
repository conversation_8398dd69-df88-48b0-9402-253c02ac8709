package inks.system.config;

import com.jayway.jsonpath.JsonPath;
import inks.common.core.domain.LoginUser;
import inks.common.security.service.TokenService;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Aspect
@Component
public class DynamicValidationAspect {

    @Resource
    private JdbcTemplate jdbcTemplate;
    @Resource
    private TokenService tokenService;
    private final static Logger logger = LoggerFactory.getLogger(DynamicValidationAspect.class);

    @Around("@annotation(dynamicValidation)")
    public Object doValidation(ProceedingJoinPoint joinPoint, DynamicValidation dynamicValidation) throws Throwable {
        LoginUser loginUser = tokenService.getLoginUser();
        String validationCode = dynamicValidation.code();
        Operation type = dynamicValidation.type(); // 获取操作类型 (CREATE, READ, UPDATE, DELETE, ALL)

        // 根据校验编码查询校验规则（返回 RuleVO 列表）
        List<RuleVO> rules = getValidationListByCode(validationCode, type, loginUser.getTenantid());

        if (CollectionUtils.isEmpty(rules)) {
            logger.info("没有找到对应的动态校验规则: {}", validationCode);
            return joinPoint.proceed(); // 无规则则直接放行
        }

        // 获取 JSON 输入字符串并预解析，避免重复解析
        String jsonInput = (String) joinPoint.getArgs()[0];
        Object jsonObj = JsonPath.parse(jsonInput).json();

        List<String> errors = new ArrayList<>();
        int i = 0;
        for (RuleVO rule : rules) {
            i++;
            logger.info("开始校验第 {} 条规则，字段：{} 规则：{} 规则值：{}", i, rule.getFieldname(), rule.getRuletype(), rule.getRulevalue());
            validateField(jsonObj, rule, errors);  // 校验字段
        }

        // 如果有错误，则抛出异常
        if (!errors.isEmpty()) {
            throw new RuntimeException(String.join(" ; ", errors));
        }

        // 校验通过，继续执行目标方法
        return joinPoint.proceed();
    }


    /**
     * 使用 JsonPath 对指定字段进行校验（支持数组/集合），将错误信息收集到 errors 中
     */
    private void validateField(Object jsonObj, RuleVO rule, List<String> errors) {
        Object pathValue = buildJsonPath(jsonObj, rule.getFieldname());
        // List类型需要循环校验
        if (pathValue instanceof List) {
            List<?> list = (List<?>) pathValue;
            // 空列表则直接返回
            if (CollectionUtils.isEmpty(list)) {
                errors.add(String.format("列表[%s]值[null]校验失败: %s", rule.getFieldname(), rule.getErrormessage()));
            }
            for (Object obj : list) {
                checkValue(rule, obj, errors);
            }
        } else {
            // 非List类型直接校验
            checkValue(rule, pathValue, errors);
        }
    }


    /**
     * 根据校验类型调用工具类方法进行校验，
     * 若校验失败则组装错误信息并添加到 errors 中
     */
    private void checkValue(RuleVO rule, Object value, java.util.List<String> errors) {
        String valueStr = value == null ? "null" : String.valueOf(value);
        String comment = rule.getComment();
        String errormessage = rule.getErrormessage();

        // 获取对应校验器(如：NotNull、Regex、Size...)
        Validator validator = ValidationUtil.getValidator(rule.getRuletype());
        if (validator == null) {
            errors.add(String.format("不支持的校验类型: %s", rule.getRuletype()));
        } else if (!validator.isValid(rule, value)) {
            errors.add(String.format("字段[%s]值[%s]校验失败: %s", comment, valueStr, errormessage));
        }
    }


    /**
     * 构建 JsonPath 路径，并读取指定字段的值
     * 如：fieldName = "refno"，则构建路径 "$[refno];
     * fieldName = "item.goodsname"，则构建路径 "$[item][*].[goodsname]"
     */
    public static Object buildJsonPath(Object jsonObj, String fieldPath) {
        String[] segments = fieldPath.split("\\.");
        StringBuilder pathBuilder = new StringBuilder("$");
        Object currentNode = jsonObj;

        // 遍历每个字段
        for (int i = 0; i < segments.length; i++) {
            String segment = segments[i];
            boolean isArrayField = false;
            if (currentNode instanceof List) {
                List<Object> tempNodes = new ArrayList<>();
                for (Object node : (List<?>) currentNode) {
                    if (node instanceof Map) {
                        Object child = ((Map<?, ?>) node).get(segment);
                        tempNodes.add(child);
                        if (child instanceof List) {
                            isArrayField = true;
                        }
                    }
                }
                currentNode = tempNodes;
            } else if (currentNode instanceof Map) {
                Object child = ((Map<?, ?>) currentNode).get(segment);
                if (child instanceof List) {
                    isArrayField = true;
                }
                currentNode = child;
            }

            // 如果不是最后一级字段，并且当前真实字段为数组，则拼接 [*]
            if (i < segments.length - 1 && isArrayField) {
                pathBuilder.append("['").append(segment).append("'][*]");
            } else {
                pathBuilder.append("['").append(segment).append("']");
            }
        }
        try {
            return JsonPath.read(jsonObj, pathBuilder.toString());
        } catch (Exception e) {
            throw new RuntimeException("入参中无此字段：" + fieldPath, e);
        }
    }

    public List<RuleVO> getValidationListByCode(String validationCode, Operation type, String tid) {

        List<Object> params = new ArrayList<>();
        // 查询需要校验的规则
        // 1.ValidationCode匹配
        StringBuilder sql = new StringBuilder(
                "SELECT FieldName, Comment, RuleValue, RuleType, ErrorMessage, CheckType " +
                        "FROM CiDynamicValidationItem " +
                        "JOIN CiDynamicValidation ON CiDynamicValidationItem.Pid = CiDynamicValidation.id " +
                        "WHERE CiDynamicValidation.ValidationCode = ? "
        );
        params.add(validationCode);

        // 2.CheckType匹配
        // 注解上的类型是Operation.ALL,全检查；否则只检查CheckType包含注解上的类型或者CheckType为空
        if (type != Operation.ALL) {
            sql.append("AND (CiDynamicValidationItem.CheckType LIKE ? OR CiDynamicValidationItem.CheckType = '') ");
            params.add("%" + type.toString() + "%");
        }

        // 3.Tenantid匹配
        sql.append("AND CiDynamicValidationItem.Tenantid = ? ORDER BY RowNum");
        params.add(tid);

        List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql.toString(), params.toArray());


        return rows.stream()
                .map(row -> new RuleVO(
                        String.valueOf(row.get("FieldName")),
                        String.valueOf(row.get("Comment")),
                        String.valueOf(row.get("RuleValue")),
                        String.valueOf(row.get("RuleType")),
                        String.valueOf(row.get("ErrorMessage")),
                        String.valueOf(row.get("Operation"))
                ))
                .collect(Collectors.toList());
    }


}
