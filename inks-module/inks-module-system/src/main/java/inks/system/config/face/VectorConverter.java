//package inks.system.config.face;
//
//import javax.persistence.AttributeConverter;
//import javax.persistence.Converter;
//
///**
// * 人脸识别 PifaceinfoPojo人脸向量转换器
// */
//@Converter
//public class VectorConverter implements AttributeConverter<float[], String> {
//    @Override
//    public String convertToDatabaseColumn(float[] attribute) {
//        if (attribute == null) {
//            return null;
//        }
//        StringBuilder sb = new StringBuilder();
//        sb.append("[");
//        for (int i = 0; i < attribute.length; i++) {
//            if (i > 0) {
//                sb.append(",");
//            }
//            sb.append(attribute[i]);
//        }
//        sb.append("]");
//        return sb.toString();
//    }
//
//    @Override
//    public float[] convertToEntityAttribute(String dbData) {
//        if (dbData == null) {
//            return null;
//        }
//        String cleaned = dbData.substring(1, dbData.length() - 1);
//        String[] parts = cleaned.split(",");
//        float[] result = new float[parts.length];
//        for (int i = 0; i < parts.length; i++) {
//            result[i] = Float.parseFloat(parts[i].trim());
//        }
//        return result;
//    }
//}
