package inks.system.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

//配合META-INF/spring.factories 实现自动配置：作为依赖时，省去启动类扫包配置
@Configuration
@ComponentScan("inks.system") // 自动扫描 Component
@MapperScan("inks.system.mapper") // 自动扫描 Mapper
public class SystemAutoConfiguration {
    // 可以在此添加其他自动配置的 Bean
}