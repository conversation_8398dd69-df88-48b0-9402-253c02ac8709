package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiprojectitemPojo;

import java.util.List;
/**
 * 项目服务(Piprojectitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-20 12:52:14
 */
public interface PiprojectitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiprojectitemPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PiprojectitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<PiprojectitemPojo> getList(String Pid);
    
    /**
     * 新增数据
     *
     * @param piprojectitemPojo 实例对象
     * @return 实例对象
     */
    PiprojectitemPojo insert(PiprojectitemPojo piprojectitemPojo);

    /**
     * 修改数据
     *
     * @param piprojectitempojo 实例对象
     * @return 实例对象
     */
    PiprojectitemPojo update(PiprojectitemPojo piprojectitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

     /**
     * 修改数据
     *
     * @param piprojectitempojo 实例对象
     * @return 实例对象
     */
    PiprojectitemPojo clearNull(PiprojectitemPojo piprojectitempojo);
}
