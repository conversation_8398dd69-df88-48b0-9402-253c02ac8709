package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CiwebprinterEntity;
import inks.system.domain.pojo.CiwebprinterPojo;
import inks.system.mapper.CiwebprinterMapper;
import inks.system.service.CiwebprinterService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 网络打印机(Ciwebprinter)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-08 15:45:15
 */
@Service("ciwebprinterService")
public class CiwebprinterServiceImpl implements CiwebprinterService {
    @Resource
    private CiwebprinterMapper ciwebprinterMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CiwebprinterPojo getEntity(String key) {
        return this.ciwebprinterMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CiwebprinterPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiwebprinterPojo> lst = ciwebprinterMapper.getPageList(queryParam);
            PageInfo<CiwebprinterPojo> pageInfo = new PageInfo<CiwebprinterPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param ciwebprinterPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiwebprinterPojo insert(CiwebprinterPojo ciwebprinterPojo) {
    //初始化NULL字段
     if(ciwebprinterPojo.getModulecode()==null) ciwebprinterPojo.setModulecode("");
     if(ciwebprinterPojo.getPrintersn()==null) ciwebprinterPojo.setPrintersn("");
     if(ciwebprinterPojo.getPrintername()==null) ciwebprinterPojo.setPrintername("");
     if(ciwebprinterPojo.getPrinterspec()==null) ciwebprinterPojo.setPrinterspec("");
     if(ciwebprinterPojo.getFrontphoto()==null) ciwebprinterPojo.setFrontphoto("");
     if(ciwebprinterPojo.getRownum()==null) ciwebprinterPojo.setRownum(0);
     if(ciwebprinterPojo.getEnabledmark()==null) ciwebprinterPojo.setEnabledmark(0);
     if(ciwebprinterPojo.getRemark()==null) ciwebprinterPojo.setRemark("");
     if(ciwebprinterPojo.getCustom1()==null) ciwebprinterPojo.setCustom1("");
     if(ciwebprinterPojo.getCustom2()==null) ciwebprinterPojo.setCustom2("");
     if(ciwebprinterPojo.getCustom3()==null) ciwebprinterPojo.setCustom3("");
     if(ciwebprinterPojo.getCustom4()==null) ciwebprinterPojo.setCustom4("");
     if(ciwebprinterPojo.getCustom5()==null) ciwebprinterPojo.setCustom5("");
     if(ciwebprinterPojo.getCreateby()==null) ciwebprinterPojo.setCreateby("");
     if(ciwebprinterPojo.getCreatebyid()==null) ciwebprinterPojo.setCreatebyid("");
     if(ciwebprinterPojo.getCreatedate()==null) ciwebprinterPojo.setCreatedate(new Date());
     if(ciwebprinterPojo.getLister()==null) ciwebprinterPojo.setLister("");
     if(ciwebprinterPojo.getListerid()==null) ciwebprinterPojo.setListerid("");
     if(ciwebprinterPojo.getModifydate()==null) ciwebprinterPojo.setModifydate(new Date());
     if(ciwebprinterPojo.getTenantid()==null) ciwebprinterPojo.setTenantid("");
     if(ciwebprinterPojo.getTenantname()==null) ciwebprinterPojo.setTenantname("");
     if(ciwebprinterPojo.getRevision()==null) ciwebprinterPojo.setRevision(0);
        CiwebprinterEntity ciwebprinterEntity = new CiwebprinterEntity(); 
        BeanUtils.copyProperties(ciwebprinterPojo,ciwebprinterEntity);
        
          ciwebprinterEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          ciwebprinterEntity.setRevision(1);  //乐观锁
          this.ciwebprinterMapper.insert(ciwebprinterEntity);
        return this.getEntity(ciwebprinterEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param ciwebprinterPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiwebprinterPojo update(CiwebprinterPojo ciwebprinterPojo) {
        CiwebprinterEntity ciwebprinterEntity = new CiwebprinterEntity(); 
        BeanUtils.copyProperties(ciwebprinterPojo,ciwebprinterEntity);
        this.ciwebprinterMapper.update(ciwebprinterEntity);
        return this.getEntity(ciwebprinterEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.ciwebprinterMapper.delete(key) ;
    }


    /**
     * 通过功能号获得报表样式
     *
     * @param moduleCode 主键
     * @return 是否成功
     */
    @Override
    public List<CiwebprinterPojo> getListByModuleCode(String moduleCode, String tid){
        return this.ciwebprinterMapper.getListByModuleCode(moduleCode,tid) ;
    }
                                                                                                                       
}
