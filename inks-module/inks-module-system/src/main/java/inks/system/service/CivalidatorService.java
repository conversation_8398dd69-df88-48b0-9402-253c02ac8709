package inks.system.service;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CivalidatorPojo;
import com.github.pagehelper.PageInfo;
import inks.system.domain.vo.ValidationResponse;
import org.xmlunit.validation.ValidationResult;

import java.util.Map;

/**
 * 数据验证(CiValidator)表服务接口
 *
 * <AUTHOR>
 * @since 2024-11-15 10:02:13
 */
public interface CivalidatorService {

    CivalidatorPojo getEntity(String key,String tid);

    PageInfo<CivalidatorPojo> getPageList(QueryParam queryParam);

    CivalidatorPojo insert(CivalidatorPojo civalidatorPojo);

    CivalidatorPojo update(CivalidatorPojo civalidatorpojo);

    int delete(String key,String tid);

    ValidationResponse validate(String valicode, Map<String, Object> dataObj, LoginUser loginUser);
}
