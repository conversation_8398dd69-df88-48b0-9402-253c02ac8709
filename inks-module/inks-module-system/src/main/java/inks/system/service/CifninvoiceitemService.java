package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CifninvoiceitemPojo;
import inks.system.domain.CifninvoiceitemEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 发票项目(Cifninvoiceitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-24 13:32:09
 */
public interface CifninvoiceitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CifninvoiceitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CifninvoiceitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<CifninvoiceitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param cifninvoiceitemPojo 实例对象
     * @return 实例对象
     */
    CifninvoiceitemPojo insert(CifninvoiceitemPojo cifninvoiceitemPojo);

    /**
     * 修改数据
     *
     * @param cifninvoiceitempojo 实例对象
     * @return 实例对象
     */
    CifninvoiceitemPojo update(CifninvoiceitemPojo cifninvoiceitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param cifninvoiceitempojo 实例对象
     * @return 实例对象
     */
    CifninvoiceitemPojo clearNull(CifninvoiceitemPojo cifninvoiceitempojo);
}
