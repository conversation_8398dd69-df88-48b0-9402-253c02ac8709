package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.pojo.PifunctionmenuappPojo;
import inks.system.domain.PifunctionmenuappEntity;
import inks.system.domain.pojo.PifunctionpermPojo;
import inks.system.mapper.PifunctionmenuappMapper;
import inks.system.service.PifunctionmenuappService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 服务菜单关系app(Pifunctionmenuapp)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-11 17:34:41
 */
@Service("pifunctionmenuappService")
public class PifunctionmenuappServiceImpl implements PifunctionmenuappService {
    @Resource
    private PifunctionmenuappMapper pifunctionmenuappMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PifunctionmenuappPojo getEntity(String key, String tid) {
        return this.pifunctionmenuappMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PifunctionmenuappPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PifunctionmenuappPojo> lst = pifunctionmenuappMapper.getPageList(queryParam);
            PageInfo<PifunctionmenuappPojo> pageInfo = new PageInfo<PifunctionmenuappPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param pifunctionmenuappPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PifunctionmenuappPojo insert(PifunctionmenuappPojo pifunctionmenuappPojo) {
    //初始化NULL字段
     if(pifunctionmenuappPojo.getFunctionid()==null) pifunctionmenuappPojo.setFunctionid("");
     if(pifunctionmenuappPojo.getFunctioncode()==null) pifunctionmenuappPojo.setFunctioncode("");
     if(pifunctionmenuappPojo.getFunctionname()==null) pifunctionmenuappPojo.setFunctionname("");
     if(pifunctionmenuappPojo.getNavid()==null) pifunctionmenuappPojo.setNavid("");
     if(pifunctionmenuappPojo.getNavcode()==null) pifunctionmenuappPojo.setNavcode("");
     if(pifunctionmenuappPojo.getNavname()==null) pifunctionmenuappPojo.setNavname("");
     if(pifunctionmenuappPojo.getLister()==null) pifunctionmenuappPojo.setLister("");
     if(pifunctionmenuappPojo.getCreatedate()==null) pifunctionmenuappPojo.setCreatedate(new Date());
     if(pifunctionmenuappPojo.getModifydate()==null) pifunctionmenuappPojo.setModifydate(new Date());
     if(pifunctionmenuappPojo.getRemark()==null) pifunctionmenuappPojo.setRemark("");
     if(pifunctionmenuappPojo.getCreateby()==null) pifunctionmenuappPojo.setCreateby("");
     if(pifunctionmenuappPojo.getTenantid()==null) pifunctionmenuappPojo.setTenantid("");
     if(pifunctionmenuappPojo.getRevision()==null) pifunctionmenuappPojo.setRevision(0);
        PifunctionmenuappEntity pifunctionmenuappEntity = new PifunctionmenuappEntity(); 
        BeanUtils.copyProperties(pifunctionmenuappPojo,pifunctionmenuappEntity);
        
          pifunctionmenuappEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          pifunctionmenuappEntity.setRevision(1);  //乐观锁
          this.pifunctionmenuappMapper.insert(pifunctionmenuappEntity);
        return this.getEntity(pifunctionmenuappEntity.getId(),pifunctionmenuappEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param pifunctionmenuappPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PifunctionmenuappPojo update(PifunctionmenuappPojo pifunctionmenuappPojo) {
        PifunctionmenuappEntity pifunctionmenuappEntity = new PifunctionmenuappEntity(); 
        BeanUtils.copyProperties(pifunctionmenuappPojo,pifunctionmenuappEntity);
        this.pifunctionmenuappMapper.update(pifunctionmenuappEntity);
        return this.getEntity(pifunctionmenuappEntity.getId(),pifunctionmenuappEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.pifunctionmenuappMapper.delete(key,tid) ;
    }


    @Override
    public List<PifunctionmenuappPojo> getListByFunction(String key){
        return this.pifunctionmenuappMapper.getListByFunction(key);
    }

}
