package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PimenufrmPojo;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * Frm导航(Pimenufrm)表服务接口
 *
 * <AUTHOR>
 * @since 2024-05-05 16:28:27
 */
public interface PimenufrmService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PimenufrmPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PimenufrmPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pimenufrmPojo 实例对象
     * @return 实例对象
     */
    PimenufrmPojo insert(PimenufrmPojo pimenufrmPojo);

    /**
     * 修改数据
     *
     * @param pimenufrmpojo 实例对象
     * @return 实例对象
     */
    PimenufrmPojo update(PimenufrmPojo pimenufrmpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<PimenufrmPojo> getListByPid(String key);

    List<PimenufrmPojo> getAllMenus();
}
