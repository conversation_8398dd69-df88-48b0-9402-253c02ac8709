package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiemailPojo;

import java.util.List;

/**
 * 邮件模板(Ciemail)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-29 15:00:43
 */
public interface CiemailService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiemailPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CiemailPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param ciemailPojo 实例对象
     * @return 实例对象
     */
    CiemailPojo insert(CiemailPojo ciemailPojo);

    /**
     * 修改数据
     *
     * @param ciemailpojo 实例对象
     * @return 实例对象
     */
    CiemailPojo update(CiemailPojo ciemailpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 通过功能号获得报表样式
     *
     * @param moduleCode 主键
     * @return 是否成功
     */
    List<CiemailPojo> getListByModuleCode(String moduleCode, String tid);
}
