package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CifnorderEntity;
import inks.system.domain.CifnorderitemEntity;
import inks.system.domain.pojo.CifnorderPojo;
import inks.system.domain.pojo.CifnorderitemPojo;
import inks.system.domain.pojo.CifnorderitemdetailPojo;
import inks.system.mapper.CifnorderMapper;
import inks.system.mapper.CifnorderitemMapper;
import inks.system.service.CifnorderService;
import inks.system.service.CifnorderitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 销售订单(Cifnorder)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-09 20:42:14
 */
@Service("cifnorderService")
public class CifnorderServiceImpl implements CifnorderService {
    @Resource
    private CifnorderMapper cifnorderMapper;

    @Resource
    private CifnorderitemMapper cifnorderitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private CifnorderitemService cifnorderitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CifnorderPojo getEntity(String key, String tid) {
        return this.cifnorderMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CifnorderitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CifnorderitemdetailPojo> lst = cifnorderMapper.getPageList(queryParam);
            PageInfo<CifnorderitemdetailPojo> pageInfo = new PageInfo<CifnorderitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CifnorderPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            CifnorderPojo cifnorderPojo = this.cifnorderMapper.getEntity(key, tid);
            //读取子表
            cifnorderPojo.setItem(cifnorderitemMapper.getList(cifnorderPojo.getId()));
            return cifnorderPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CifnorderPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CifnorderPojo> lst = cifnorderMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(cifnorderitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<CifnorderPojo> pageInfo = new PageInfo<CifnorderPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CifnorderPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CifnorderPojo> lst = cifnorderMapper.getPageTh(queryParam);
            PageInfo<CifnorderPojo> pageInfo = new PageInfo<CifnorderPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param cifnorderPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public CifnorderPojo insert(CifnorderPojo cifnorderPojo) {
        //初始化NULL字段
        if (cifnorderPojo.getRefno() == null) cifnorderPojo.setRefno("");
        if (cifnorderPojo.getBilltype() == null) cifnorderPojo.setBilltype("");
        if (cifnorderPojo.getBilltitle() == null) cifnorderPojo.setBilltitle("");
        if (cifnorderPojo.getBilldate() == null) cifnorderPojo.setBilldate(new Date());
        if (cifnorderPojo.getSellerid() == null) cifnorderPojo.setSellerid("");
        if (cifnorderPojo.getUserid() == null) cifnorderPojo.setUserid("");
        if (cifnorderPojo.getUsername() == null) cifnorderPojo.setUsername("");
        if (cifnorderPojo.getRealname() == null) cifnorderPojo.setRealname("");
        if (cifnorderPojo.getTenantid() == null) cifnorderPojo.setTenantid("");
        if (cifnorderPojo.getTenantcode() == null) cifnorderPojo.setTenantcode("");
        if (cifnorderPojo.getTenantname() == null) cifnorderPojo.setTenantname("");
        if (cifnorderPojo.getSummary() == null) cifnorderPojo.setSummary("");
        if (cifnorderPojo.getLister() == null) cifnorderPojo.setLister("");
        if (cifnorderPojo.getCreatedate() == null) cifnorderPojo.setCreatedate(new Date());
        if (cifnorderPojo.getModifydate() == null) cifnorderPojo.setModifydate(new Date());
        if (cifnorderPojo.getBilltaxamount() == null) cifnorderPojo.setBilltaxamount(0D);
        if (cifnorderPojo.getPayamount() == null) cifnorderPojo.setPayamount(0D);
        if (cifnorderPojo.getPaybillcode() == null) cifnorderPojo.setPaybillcode("");
        if (cifnorderPojo.getStatecode() == null) cifnorderPojo.setStatecode("");
        if (cifnorderPojo.getStatedate() == null) cifnorderPojo.setStatedate(new Date());
        if (cifnorderPojo.getDisannulmark() == null) cifnorderPojo.setDisannulmark(0);
        if (cifnorderPojo.getDisannullister() == null) cifnorderPojo.setDisannullister("");
        if (cifnorderPojo.getDisannuldate() == null) cifnorderPojo.setDisannuldate(new Date());
        if (cifnorderPojo.getRevision()==null) cifnorderPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        CifnorderEntity cifnorderEntity = new CifnorderEntity();
        BeanUtils.copyProperties(cifnorderPojo, cifnorderEntity);
        //设置id和新建日期
        cifnorderEntity.setId(id);
        //插入主表
        this.cifnorderMapper.insert(cifnorderEntity);
        //Item子表处理
        List<CifnorderitemPojo> lst = cifnorderPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                CifnorderitemPojo itemPojo = this.cifnorderitemService.clearNull(lst.get(i));
                CifnorderitemEntity cifnorderitemEntity = new CifnorderitemEntity();
                BeanUtils.copyProperties(itemPojo, cifnorderitemEntity);
                //设置id和Pid
                cifnorderitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                cifnorderitemEntity.setPid(id);
                // cifnorderitemEntity.setTenantid(cifnorderPojo.getTenantid());
                //插入子表
                this.cifnorderitemMapper.insert(cifnorderitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(cifnorderEntity.getId(), cifnorderEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param cifnorderPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public CifnorderPojo update(CifnorderPojo cifnorderPojo) {
        //主表更改
        CifnorderEntity cifnorderEntity = new CifnorderEntity();
        BeanUtils.copyProperties(cifnorderPojo, cifnorderEntity);
        this.cifnorderMapper.update(cifnorderEntity);
        //Item子表处理
        List<CifnorderitemPojo> lst = cifnorderPojo.getItem();
        //获取被删除的Item
        List<String> lstDelIds = cifnorderMapper.getDelItemIds(cifnorderPojo);
        if (lstDelIds != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstDelIds.size(); i++) {
                this.cifnorderitemMapper.delete(lstDelIds.get(i));
            }
        }
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                CifnorderitemEntity cifnorderitemEntity = new CifnorderitemEntity();
                if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                    //初始化item的NULL
                    CifnorderitemPojo itemPojo = this.cifnorderitemService.clearNull(lst.get(i));
                    BeanUtils.copyProperties(itemPojo, cifnorderitemEntity);
                    //设置id和Pid
                    cifnorderitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                    cifnorderitemEntity.setPid(cifnorderEntity.getId());
                    // cifnorderitemEntity.setTenantid(cifnorderPojo.getTenantid());
                    //插入子表
                    this.cifnorderitemMapper.insert(cifnorderitemEntity);
                } else {
                    BeanUtils.copyProperties(lst.get(i), cifnorderitemEntity);
                    //cifnorderitemEntity.setTenantid(cifnorderPojo.getTenantid());
                    this.cifnorderitemMapper.update(cifnorderitemEntity);
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(cifnorderEntity.getId(), cifnorderEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        CifnorderPojo cifnorderPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<CifnorderitemPojo> lst = cifnorderPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.cifnorderitemMapper.delete(lst.get(i).getId());
            }
        }
        return this.cifnorderMapper.delete(key, tid);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CifnorderPojo getEntityByRefno(String key) {
        return this.cifnorderMapper.getEntityByRefno(key);
    }


}
