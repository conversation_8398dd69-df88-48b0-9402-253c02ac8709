package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CixlsinputPojo;

import java.util.List;

/**
 * xls导入格式(Cixlsinput)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-29 10:40:17
 */
public interface CixlsinputService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CixlsinputPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CixlsinputPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param cixlsinputPojo 实例对象
     * @return 实例对象
     */
    CixlsinputPojo insert(CixlsinputPojo cixlsinputPojo);

    /**
     * 修改数据
     *
     * @param cixlsinputpojo 实例对象
     * @return 实例对象
     */
    CixlsinputPojo update(CixlsinputPojo cixlsinputpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);


    /**
     * 通过功能号获得导入样式
     *
     * @param moduleCode 主键
     * @return 是否成功
     */
    List<CixlsinputPojo> getListByModuleCode(String moduleCode, String tid);

}
