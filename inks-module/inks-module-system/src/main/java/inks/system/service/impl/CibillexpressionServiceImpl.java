package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.system.domain.pojo.CibillexpressionPojo;
import inks.system.domain.CibillexpressionEntity;
import inks.system.mapper.CibillexpressionMapper;
import inks.system.service.CibillexpressionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 单据公式(Cibillexpression)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-21 17:01:47
 */
@Service("cibillexpressionService")
public class CibillexpressionServiceImpl implements CibillexpressionService {
    @Resource
    private CibillexpressionMapper cibillexpressionMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CibillexpressionPojo getEntity(String key, String tid) {
        return this.cibillexpressionMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CibillexpressionPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CibillexpressionPojo> lst = cibillexpressionMapper.getPageList(queryParam);
            PageInfo<CibillexpressionPojo> pageInfo = new PageInfo<CibillexpressionPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param cibillexpressionPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CibillexpressionPojo insert(CibillexpressionPojo cibillexpressionPojo) {
    //初始化NULL字段
     if(cibillexpressionPojo.getModulecode()==null) cibillexpressionPojo.setModulecode("");
     if(cibillexpressionPojo.getBillname()==null) cibillexpressionPojo.setBillname("");
     if(cibillexpressionPojo.getOrgcolumns()==null) cibillexpressionPojo.setOrgcolumns("");
     if(cibillexpressionPojo.getExprtemp()==null) cibillexpressionPojo.setExprtemp("");
     if(cibillexpressionPojo.getTgcolumn()==null) cibillexpressionPojo.setTgcolumn("");
     if(cibillexpressionPojo.getDecnum()==null) cibillexpressionPojo.setDecnum(0);
     if(cibillexpressionPojo.getReturntype()==null) cibillexpressionPojo.setReturntype(0);
     if(cibillexpressionPojo.getEnabledmark()==null) cibillexpressionPojo.setEnabledmark(0);
     if(cibillexpressionPojo.getRownum()==null) cibillexpressionPojo.setRownum(0);
     if(cibillexpressionPojo.getRemark()==null) cibillexpressionPojo.setRemark("");
     if(cibillexpressionPojo.getCreateby()==null) cibillexpressionPojo.setCreateby("");
     if(cibillexpressionPojo.getCreatebyid()==null) cibillexpressionPojo.setCreatebyid("");
     if(cibillexpressionPojo.getCreatedate()==null) cibillexpressionPojo.setCreatedate(new Date());
     if(cibillexpressionPojo.getLister()==null) cibillexpressionPojo.setLister("");
     if(cibillexpressionPojo.getListerid()==null) cibillexpressionPojo.setListerid("");
     if(cibillexpressionPojo.getModifydate()==null) cibillexpressionPojo.setModifydate(new Date());
     if(cibillexpressionPojo.getCustom1()==null) cibillexpressionPojo.setCustom1("");
     if(cibillexpressionPojo.getCustom2()==null) cibillexpressionPojo.setCustom2("");
     if(cibillexpressionPojo.getCustom3()==null) cibillexpressionPojo.setCustom3("");
     if(cibillexpressionPojo.getCustom4()==null) cibillexpressionPojo.setCustom4("");
     if(cibillexpressionPojo.getCustom5()==null) cibillexpressionPojo.setCustom5("");
     if(cibillexpressionPojo.getTenantid()==null) cibillexpressionPojo.setTenantid("");
     if(cibillexpressionPojo.getTenantname()==null) cibillexpressionPojo.setTenantname("");
     if(cibillexpressionPojo.getRevision()==null) cibillexpressionPojo.setRevision(0);
        CibillexpressionEntity cibillexpressionEntity = new CibillexpressionEntity(); 
        BeanUtils.copyProperties(cibillexpressionPojo,cibillexpressionEntity);
          //生成雪花id
          cibillexpressionEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          cibillexpressionEntity.setRevision(1);  //乐观锁
          this.cibillexpressionMapper.insert(cibillexpressionEntity);
        return this.getEntity(cibillexpressionEntity.getId(),cibillexpressionEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param cibillexpressionPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CibillexpressionPojo update(CibillexpressionPojo cibillexpressionPojo) {
        CibillexpressionEntity cibillexpressionEntity = new CibillexpressionEntity(); 
        BeanUtils.copyProperties(cibillexpressionPojo,cibillexpressionEntity);
        this.cibillexpressionMapper.update(cibillexpressionEntity);
        return this.getEntity(cibillexpressionEntity.getId(),cibillexpressionEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.cibillexpressionMapper.delete(key,tid) ;
    }

    @Override
    public List<CibillexpressionPojo> getListByCode(String key, String tid) {
        return this.cibillexpressionMapper.getListByCode(key,tid);
    }
}
