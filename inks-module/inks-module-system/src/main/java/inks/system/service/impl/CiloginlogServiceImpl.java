package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CiloginlogEntity;
import inks.system.domain.pojo.CiloginlogPojo;
import inks.system.mapper.CiloginlogMapper;
import inks.system.service.CiloginlogService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 登录日志(Ciloginlog)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-08 14:50:05
 */
@Service("ciloginlogService")
public class CiloginlogServiceImpl implements CiloginlogService {
    @Resource
    private CiloginlogMapper ciloginlogMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CiloginlogPojo getEntity(String key) {
        return this.ciloginlogMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CiloginlogPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiloginlogPojo> lst = ciloginlogMapper.getPageList(queryParam);
            PageInfo<CiloginlogPojo> pageInfo = new PageInfo<CiloginlogPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param ciloginlogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiloginlogPojo insert(CiloginlogPojo ciloginlogPojo) {
        //初始化NULL字段
        if (ciloginlogPojo.getUserid() == null) ciloginlogPojo.setUserid("");
        if (ciloginlogPojo.getUsername() == null) ciloginlogPojo.setUsername("");
        if (ciloginlogPojo.getRealname() == null) ciloginlogPojo.setRealname("");
        if (ciloginlogPojo.getIpaddr() == null) ciloginlogPojo.setIpaddr("");
        if (ciloginlogPojo.getLoginlocation() == null) ciloginlogPojo.setLoginlocation("");
        if (ciloginlogPojo.getBrowsername() == null) ciloginlogPojo.setBrowsername("");
        if (ciloginlogPojo.getHostsystem() == null) ciloginlogPojo.setHostsystem("");
        if (ciloginlogPojo.getDirection() == null) ciloginlogPojo.setDirection("");
        if (ciloginlogPojo.getLoginstatus() == null) ciloginlogPojo.setLoginstatus(0);
        if (ciloginlogPojo.getLoginmsg() == null) ciloginlogPojo.setLoginmsg("");
        if (ciloginlogPojo.getLogintime() == null) ciloginlogPojo.setLogintime(new Date());
        if (ciloginlogPojo.getTenantid() == null) ciloginlogPojo.setTenantid("");
        if (ciloginlogPojo.getTenantname() == null) ciloginlogPojo.setTenantname("");
        CiloginlogEntity ciloginlogEntity = new CiloginlogEntity();
        BeanUtils.copyProperties(ciloginlogPojo, ciloginlogEntity);

        ciloginlogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.ciloginlogMapper.insert(ciloginlogEntity);
        return this.getEntity(ciloginlogEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param ciloginlogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiloginlogPojo update(CiloginlogPojo ciloginlogPojo) {
        CiloginlogEntity ciloginlogEntity = new CiloginlogEntity();
        BeanUtils.copyProperties(ciloginlogPojo, ciloginlogEntity);
        this.ciloginlogMapper.update(ciloginlogEntity);
        return this.getEntity(ciloginlogEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.ciloginlogMapper.delete(key);
    }


    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public List<ChartPojo> getCountListByPro(QueryParam queryParam) {
        try {
            List<ChartPojo> lst = ciloginlogMapper.getCountListByPro(queryParam);
            //去掉 省 市
            for (ChartPojo pojo : lst) {
                pojo.setName(pojo.getName().replace('省', ' ').replace('市', ' ').trim());
            }
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public List<ChartPojo> getCountListByCity(QueryParam queryParam) {
        try {
            List<ChartPojo> lst = ciloginlogMapper.getCountListByCity(queryParam);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getEveryAdminLogin(Date startdate, Date enddate) {
        try {
            return ciloginlogMapper.getEveryAdminLogin(startdate, enddate);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getEveryUserLogin(Date startdate, Date enddate) {
        try {
            return ciloginlogMapper.getEveryUserLogin(startdate, enddate);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getEveryTenantLogin(Date startdate, Date enddate) {
        try {
            return ciloginlogMapper.getEveryTenantLogin(startdate, enddate);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<CiloginlogPojo> getAllAdminLogin(Date startDate, Date endDate) {
        try {
            return ciloginlogMapper.getAllAdminLogin(startDate, endDate);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<CiloginlogPojo> getAllUserLogin(Date startDate, Date endDate) {
        try {
            return ciloginlogMapper.getAllUserLogin(startDate, endDate);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getCountPageListByTen(QueryParam queryParam) {
        try {
            return ciloginlogMapper.getCountPageListByTen(queryParam);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public int deleteByTime(QueryParam queryParam) {
        return ciloginlogMapper.deleteByTime(queryParam);
    }

    @Override
    public List<Map<String, Object>> getCountSuccessByUser(Date startDate, Date endDate, String tenantid) {
        return ciloginlogMapper.getCountSuccessByUser(startDate, endDate, tenantid);
    }
}
