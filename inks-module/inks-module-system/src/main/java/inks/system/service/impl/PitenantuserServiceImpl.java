package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DeptinfoPojo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PitenantuserEntity;
import inks.system.domain.pojo.PitenantuserPojo;
import inks.system.mapper.PitenantuserMapper;
import inks.system.service.PideptService;
import inks.system.service.PitenantuserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 租户关系表(Pitenantuser)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-28 20:42:28
 */
@Service("pitenantuserService")
public class PitenantuserServiceImpl implements PitenantuserService {
    @Resource
    private PitenantuserMapper pitenantuserMapper;

    @Resource
    private PideptService pideptService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PitenantuserPojo getEntity(String key, String tid) {
        return this.pitenantuserMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PitenantuserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PitenantuserPojo> lst = pitenantuserMapper.getPageList(queryParam);
            PageInfo<PitenantuserPojo> pageInfo = new PageInfo<PitenantuserPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param pitenantuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PitenantuserPojo insert(PitenantuserPojo pitenantuserPojo) {
        //初始化NULL字段
        if (pitenantuserPojo.getTenantid() == null) pitenantuserPojo.setTenantid("");
        if (pitenantuserPojo.getTenantname() == null) pitenantuserPojo.setTenantname("");
        if (pitenantuserPojo.getUserid() == null) pitenantuserPojo.setUserid("");
        if (pitenantuserPojo.getUsername() == null) pitenantuserPojo.setUsername("");
        if (pitenantuserPojo.getRealname() == null) pitenantuserPojo.setRealname("");
        if (pitenantuserPojo.getIsadmin() == null) pitenantuserPojo.setIsadmin(0);
        if (pitenantuserPojo.getDeptid() == null) pitenantuserPojo.setDeptid("");
        if (pitenantuserPojo.getDeptcode() == null) pitenantuserPojo.setDeptcode("");
        if (pitenantuserPojo.getDeptname() == null) pitenantuserPojo.setDeptname("");
        if (pitenantuserPojo.getIsdeptadmin() == null) pitenantuserPojo.setIsdeptadmin(0);
        if (pitenantuserPojo.getDeptrownum() == null) pitenantuserPojo.setDeptrownum(0);
        if (pitenantuserPojo.getRownum() == null) pitenantuserPojo.setRownum(0);
        if (pitenantuserPojo.getCreateby() == null) pitenantuserPojo.setCreateby("");
        if (pitenantuserPojo.getCreatebyid() == null) pitenantuserPojo.setCreatebyid("");
        if (pitenantuserPojo.getCreatedate() == null) pitenantuserPojo.setCreatedate(new Date());
        if (pitenantuserPojo.getLister() == null) pitenantuserPojo.setLister("");
        if (pitenantuserPojo.getListerid() == null) pitenantuserPojo.setListerid("");
        if (pitenantuserPojo.getModifydate() == null) pitenantuserPojo.setModifydate(new Date());
        if (pitenantuserPojo.getCustom1() == null) pitenantuserPojo.setCustom1("");
        if (pitenantuserPojo.getCustom2() == null) pitenantuserPojo.setCustom2("");
        if (pitenantuserPojo.getCustom3() == null) pitenantuserPojo.setCustom3("");
        if (pitenantuserPojo.getCustom4() == null) pitenantuserPojo.setCustom4("");
        if (pitenantuserPojo.getCustom5() == null) pitenantuserPojo.setCustom5("");
        if (pitenantuserPojo.getRevision() == null) pitenantuserPojo.setRevision(0);
        PitenantuserEntity pitenantuserEntity = new PitenantuserEntity();
        BeanUtils.copyProperties(pitenantuserPojo, pitenantuserEntity);

        pitenantuserEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        pitenantuserEntity.setRevision(1);  //乐观锁
        this.pitenantuserMapper.insert(pitenantuserEntity);
        return this.getEntity(pitenantuserEntity.getId(), pitenantuserEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param pitenantuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PitenantuserPojo update(PitenantuserPojo pitenantuserPojo) {
        PitenantuserEntity pitenantuserEntity = new PitenantuserEntity();
        BeanUtils.copyProperties(pitenantuserPojo, pitenantuserEntity);
        this.pitenantuserMapper.update(pitenantuserEntity);
        return this.getEntity(pitenantuserEntity.getId(), pitenantuserEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.pitenantuserMapper.delete(key, tid);
    }


    @Override
    public List<PitenantuserPojo> getListByTenant(String key) {
        return this.pitenantuserMapper.getListByTenant(key);
    }

    @Override
    public List<PitenantuserPojo> getListByUser(String key) {
        return this.pitenantuserMapper.getListByUser(key);
    }


    /**
     * 分页查询
     *
     * @param userid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<DeptinfoPojo> getDeptinfoList(String userid, String tid) {
        List<DeptinfoPojo> lst = new ArrayList<>();
        PitenantuserPojo pitenantuserPojo =this.pitenantuserMapper.getEntityByUser(userid,tid);
        if (pitenantuserPojo == null || pitenantuserPojo.getDeptid()==null) {
            return null;
        } else {
            DeptinfoPojo deptinfoPojo = new DeptinfoPojo();
            BeanUtils.copyProperties(pitenantuserPojo, deptinfoPojo);
            lst.add(deptinfoPojo);
            pideptService.getSubinfoAll(lst, tid);
            return lst;
        }
    }


}
