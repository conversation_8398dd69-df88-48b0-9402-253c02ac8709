package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.pojo.CifninvoiceitemPojo;
import inks.system.domain.CifninvoiceitemEntity;
import inks.system.mapper.CifninvoiceitemMapper;
import inks.system.service.CifninvoiceitemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 发票项目(Cifninvoiceitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-24 13:32:09
 */
@Service("cifninvoiceitemService")
public class CifninvoiceitemServiceImpl implements CifninvoiceitemService {
    @Resource
    private CifninvoiceitemMapper cifninvoiceitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CifninvoiceitemPojo getEntity(String key,String tid) {
        return this.cifninvoiceitemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CifninvoiceitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CifninvoiceitemPojo> lst = cifninvoiceitemMapper.getPageList(queryParam);
            PageInfo<CifninvoiceitemPojo> pageInfo = new PageInfo<CifninvoiceitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<CifninvoiceitemPojo> getList(String Pid,String tid) { 
        try {
            List<CifninvoiceitemPojo> lst = cifninvoiceitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param cifninvoiceitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CifninvoiceitemPojo insert(CifninvoiceitemPojo cifninvoiceitemPojo) {
        //初始化item的NULL
        CifninvoiceitemPojo itempojo =this.clearNull(cifninvoiceitemPojo);
        CifninvoiceitemEntity cifninvoiceitemEntity = new CifninvoiceitemEntity(); 
        BeanUtils.copyProperties(itempojo,cifninvoiceitemEntity);
        
          cifninvoiceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          cifninvoiceitemEntity.setRevision(1);  //乐观锁      
          this.cifninvoiceitemMapper.insert(cifninvoiceitemEntity);
        return this.getEntity(cifninvoiceitemEntity.getId(),cifninvoiceitemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param cifninvoiceitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CifninvoiceitemPojo update(CifninvoiceitemPojo cifninvoiceitemPojo) {
        CifninvoiceitemEntity cifninvoiceitemEntity = new CifninvoiceitemEntity(); 
        BeanUtils.copyProperties(cifninvoiceitemPojo,cifninvoiceitemEntity);
        this.cifninvoiceitemMapper.update(cifninvoiceitemEntity);
        return this.getEntity(cifninvoiceitemEntity.getId(),cifninvoiceitemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.cifninvoiceitemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param cifninvoiceitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public CifninvoiceitemPojo clearNull(CifninvoiceitemPojo cifninvoiceitemPojo){
     //初始化NULL字段
     if(cifninvoiceitemPojo.getPid()==null) cifninvoiceitemPojo.setPid("");
     if(cifninvoiceitemPojo.getOrderuid()==null) cifninvoiceitemPojo.setOrderuid("");
     if(cifninvoiceitemPojo.getOrdertype()==null) cifninvoiceitemPojo.setOrdertype("");
     if(cifninvoiceitemPojo.getOrdertitle()==null) cifninvoiceitemPojo.setOrdertitle("");
     if(cifninvoiceitemPojo.getOrderdate()==null) cifninvoiceitemPojo.setOrderdate(new Date());
     if(cifninvoiceitemPojo.getFunctionid()==null) cifninvoiceitemPojo.setFunctionid("");
     if(cifninvoiceitemPojo.getFunctioncode()==null) cifninvoiceitemPojo.setFunctioncode("");
     if(cifninvoiceitemPojo.getFunctionname()==null) cifninvoiceitemPojo.setFunctionname("");
     if(cifninvoiceitemPojo.getCyclecode()==null) cifninvoiceitemPojo.setCyclecode("");
     if(cifninvoiceitemPojo.getContainer()==null) cifninvoiceitemPojo.setContainer(0);
     if(cifninvoiceitemPojo.getQuantity()==null) cifninvoiceitemPojo.setQuantity(0D);
     if(cifninvoiceitemPojo.getTaxprice()==null) cifninvoiceitemPojo.setTaxprice(0D);
     if(cifninvoiceitemPojo.getTaxamount()==null) cifninvoiceitemPojo.setTaxamount(0D);
     if(cifninvoiceitemPojo.getRownum()==null) cifninvoiceitemPojo.setRownum(0);
     if(cifninvoiceitemPojo.getRemark()==null) cifninvoiceitemPojo.setRemark("");
     if(cifninvoiceitemPojo.getRevision()==null) cifninvoiceitemPojo.setRevision(0);
     if(cifninvoiceitemPojo.getTenantid()==null) cifninvoiceitemPojo.setTenantid("");
     if(cifninvoiceitemPojo.getOrderitemid()==null) cifninvoiceitemPojo.setOrderitemid("");
     return cifninvoiceitemPojo;
     }
}
