package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CidashboardPojo;
import inks.system.domain.CidashboardEntity;

import com.github.pagehelper.PageInfo;

/**
 * 工作台(Cidashboard)表服务接口
 *
 * <AUTHOR>
 * @since 2022-07-15 21:12:58
 */
public interface CidashboardService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CidashboardPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CidashboardPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param cidashboardPojo 实例对象
     * @return 实例对象
     */
    CidashboardPojo insert(CidashboardPojo cidashboardPojo);

    /**
     * 修改数据
     *
     * @param cidashboardpojo 实例对象
     * @return 实例对象
     */
    CidashboardPojo update(CidashboardPojo cidashboardpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);
                                                                                          }
