package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PirmsuserPojo;

import java.util.List;

/**
 * RMS用户(Pirmsuser)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-21 19:08:54
 */
public interface PirmsuserService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PirmsuserPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PirmsuserPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pirmsuserPojo 实例对象
     * @return 实例对象
     */
    PirmsuserPojo insert(PirmsuserPojo pirmsuserPojo);

    /**
     * 修改数据
     *
     * @param pirmsuserpojo 实例对象
     * @return 实例对象
     */
    PirmsuserPojo update(PirmsuserPojo pirmsuserpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    PirmsuserPojo getEntityByUserName(String username);

    PageInfo<PirmsuserPojo> getPageListByTen(QueryParam queryParam);

    PirmsuserPojo getEntityByOpenid(String openid, String tid);

    List<PirmsuserPojo> getListByOpenid(String openid);

    PirmsuserPojo getEntityByUserid(String userid, String tenantid);
}
