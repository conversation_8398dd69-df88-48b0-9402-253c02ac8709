package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CibilldraftPojo;

import java.util.List;

/**
 * 单据草稿(CiBillDraft)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-26 10:17:54
 */
public interface CibilldraftService {

    CibilldraftPojo getEntity(String key, String tid);

    PageInfo<CibilldraftPojo> getPageList(QueryParam queryParam);

    CibilldraftPojo insert(CibilldraftPojo cibilldraftPojo);

    CibilldraftPojo update(CibilldraftPojo cibilldraftpojo);

    int delete(String key, String tid);

    CibilldraftPojo getEntityByModuleCodeAndListerId(String modulecode, String userid, String tid);

    Integer getDraftCountByListerId(String userid, String tid);

    List<CibilldraftPojo> getDraftList(String userid, String code, String tenantid);
}
