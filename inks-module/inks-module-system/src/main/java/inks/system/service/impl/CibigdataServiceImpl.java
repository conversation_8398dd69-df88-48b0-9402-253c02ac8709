package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.system.domain.CibigdataEntity;
import inks.system.domain.CibigdataitemEntity;
import inks.system.domain.pojo.CibigdataPojo;
import inks.system.domain.pojo.CibigdataitemPojo;
import inks.system.domain.pojo.CibigdataitemdetailPojo;
import inks.system.mapper.CibigdataMapper;
import inks.system.mapper.CibigdataitemMapper;
import inks.system.service.CibigdataService;
import inks.system.service.CibigdataitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 数据大屏(Cibigdata)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-25 10:32:03
 */
@Service("cibigdataService")
public class CibigdataServiceImpl implements CibigdataService {
    @Resource
    private CibigdataMapper cibigdataMapper;
    
    @Resource
    private CibigdataitemMapper cibigdataitemMapper;
    
     /**
     * 服务对象Item
     */
    @Resource
    private CibigdataitemService cibigdataitemService;
    
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CibigdataPojo getEntity(String key) {
        return this.cibigdataMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CibigdataitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CibigdataitemdetailPojo> lst = cibigdataMapper.getPageList(queryParam);
            PageInfo<CibigdataitemdetailPojo> pageInfo = new PageInfo<CibigdataitemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    
     /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CibigdataPojo getBillEntity(String key) {
       try {
        //读取主表
        CibigdataPojo cibigdataPojo = this.cibigdataMapper.getEntity(key);
        //读取子表
        cibigdataPojo.setItem(cibigdataitemMapper.getList(cibigdataPojo.getId()));
        return cibigdataPojo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CibigdataPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CibigdataPojo> lst = cibigdataMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(int i=0;i<lst.size();i++){
                lst.get(i).setItem(cibigdataitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<CibigdataPojo> pageInfo = new PageInfo<CibigdataPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    
       /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CibigdataPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CibigdataPojo> lst = cibigdataMapper.getPageTh(queryParam);
            PageInfo<CibigdataPojo> pageInfo = new PageInfo<CibigdataPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param cibigdataPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public CibigdataPojo insert(CibigdataPojo cibigdataPojo) {
//初始化NULL字段
     if(cibigdataPojo.getBdtype()==null) cibigdataPojo.setBdtype("");
     if(cibigdataPojo.getBdcode()==null) cibigdataPojo.setBdcode("");
     if(cibigdataPojo.getBdname()==null) cibigdataPojo.setBdname("");
     if(cibigdataPojo.getBdtitle()==null) cibigdataPojo.setBdtitle("");
     if(cibigdataPojo.getFrontphoto()==null) cibigdataPojo.setFrontphoto("");
     if(cibigdataPojo.getImagecss()==null) cibigdataPojo.setImagecss("");
     if(cibigdataPojo.getMvcurl()==null) cibigdataPojo.setMvcurl("");
     if(cibigdataPojo.getRownum()==null) cibigdataPojo.setRownum(0);
     if(cibigdataPojo.getEnabledmark()==null) cibigdataPojo.setEnabledmark(0);
     if(cibigdataPojo.getIspublic()==null) cibigdataPojo.setIspublic(0);
     if(cibigdataPojo.getPermissioncode()==null) cibigdataPojo.setPermissioncode("");
     if(cibigdataPojo.getTenantid()==null) cibigdataPojo.setTenantid("");
     if(cibigdataPojo.getRemark()==null) cibigdataPojo.setRemark("");
     if(cibigdataPojo.getCreateby()==null) cibigdataPojo.setCreateby("");
     if(cibigdataPojo.getCreatebyid()==null) cibigdataPojo.setCreatebyid("");
     if(cibigdataPojo.getCreatedate()==null) cibigdataPojo.setCreatedate(new Date());
     if(cibigdataPojo.getLister()==null) cibigdataPojo.setLister("");
     if(cibigdataPojo.getListerid()==null) cibigdataPojo.setListerid("");
     if(cibigdataPojo.getModifydate()==null) cibigdataPojo.setModifydate(new Date());
     if(cibigdataPojo.getRevision()==null) cibigdataPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        CibigdataEntity cibigdataEntity = new CibigdataEntity(); 
        BeanUtils.copyProperties(cibigdataPojo,cibigdataEntity);
        //设置id和新建日期
        cibigdataEntity.setId(id);
        cibigdataEntity.setRevision(1);  //乐观锁
        //插入主表
        this.cibigdataMapper.insert(cibigdataEntity);
        //Item子表处理
        List<CibigdataitemPojo> lst = cibigdataPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               //初始化item的NULL
               CibigdataitemPojo itemPojo =this.cibigdataitemService.clearNull(lst.get(i));
               CibigdataitemEntity cibigdataitemEntity = new CibigdataitemEntity(); 
               BeanUtils.copyProperties(itemPojo,cibigdataitemEntity);
               //设置id和Pid
               cibigdataitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               cibigdataitemEntity.setPid(id);
               cibigdataitemEntity.setRevision(1);  //乐观锁
               //插入子表
               this.cibigdataitemMapper.insert(cibigdataitemEntity);
            }
        } 
        //返回Bill实例
        return this.getBillEntity(cibigdataEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param cibigdataPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public CibigdataPojo update(CibigdataPojo cibigdataPojo) {
        //主表更改
        CibigdataEntity cibigdataEntity = new CibigdataEntity(); 
        BeanUtils.copyProperties(cibigdataPojo,cibigdataEntity);
        this.cibigdataMapper.update(cibigdataEntity);
        //Item子表处理
        List<CibigdataitemPojo> lst = cibigdataPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =cibigdataMapper.getDelItemIds(cibigdataPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for(int i=0;i<lstDelIds.size();i++){
             this.cibigdataitemMapper.delete(lstDelIds.get(i));
            }
        }
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               CibigdataitemEntity cibigdataitemEntity = new CibigdataitemEntity(); 
               if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null){
                //初始化item的NULL
               CibigdataitemPojo itemPojo =this.cibigdataitemService.clearNull(lst.get(i));
               BeanUtils.copyProperties(itemPojo,cibigdataitemEntity);
               //设置id和Pid
               cibigdataitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               cibigdataitemEntity.setPid(cibigdataEntity.getId());  // 主表 id
               cibigdataitemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.cibigdataitemMapper.insert(cibigdataitemEntity);
               }
               else
               {
               BeanUtils.copyProperties(lst.get(i),cibigdataitemEntity);
               this.cibigdataitemMapper.update(cibigdataitemEntity);
               }
            }
        } 
        //返回Bill实例
        return this.getBillEntity(cibigdataEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key) {
       CibigdataPojo cibigdataPojo =  this.getBillEntity(key);
        //Item子表处理
        List<CibigdataitemPojo> lst = cibigdataPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for(int i=0;i<lst.size();i++){
             this.cibigdataitemMapper.delete(lst.get(i).getId());
            }
        }        
        return this.cibigdataMapper.delete(key) ;
    }


    // 查询租户下的大屏
    @Override
    public  List<CibigdataPojo> getListByTenant(String key) {

        return this.cibigdataMapper.getListByTenant(key, DateUtils.getTime()) ;
    }
                                                                                                         
}
