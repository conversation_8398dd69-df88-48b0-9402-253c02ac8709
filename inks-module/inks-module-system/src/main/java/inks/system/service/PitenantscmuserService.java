package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PitenantscmuserPojo;

import java.util.List;

/**
 * SCM租户关系表(Pitenantscmuser)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
public interface PitenantscmuserService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PitenantscmuserPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PitenantscmuserPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pitenantscmuserPojo 实例对象
     * @return 实例对象
     */
    PitenantscmuserPojo insert(PitenantscmuserPojo pitenantscmuserPojo);

    /**
     * 修改数据
     *
     * @param pitenantscmuserpojo 实例对象
     * @return 实例对象
     */
    PitenantscmuserPojo update(PitenantscmuserPojo pitenantscmuserpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 分页查询
     * @return 查询结果
     */
    List<PitenantscmuserPojo> getListByUser(String Userid);

    PitenantscmuserPojo createScmUser(PitenantscmuserPojo pitenantscmuserPojo) throws Exception;

    PitenantscmuserPojo getEntityByUserid(String key, String tid);

}
