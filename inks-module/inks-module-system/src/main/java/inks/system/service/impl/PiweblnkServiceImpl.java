package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.system.domain.pojo.PiweblnkPojo;
import inks.system.domain.PiweblnkEntity;
import inks.system.mapper.PiweblnkMapper;
import inks.system.service.PiweblnkService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 快捷方式(Piweblnk)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-08 14:11:24
 */
@Service("piweblnkService")
public class PiweblnkServiceImpl implements PiweblnkService {
    @Resource
    private PiweblnkMapper piweblnkMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PiweblnkPojo getEntity(String key) {
        return this.piweblnkMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PiweblnkPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PiweblnkPojo> lst = piweblnkMapper.getPageList(queryParam);
            PageInfo<PiweblnkPojo> pageInfo = new PageInfo<PiweblnkPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param piweblnkPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiweblnkPojo insert(PiweblnkPojo piweblnkPojo) {
    //初始化NULL字段
     if(piweblnkPojo.getNavpid()==null) piweblnkPojo.setNavpid("");
     if(piweblnkPojo.getNavtype()==null) piweblnkPojo.setNavtype("");
     if(piweblnkPojo.getNavcode()==null) piweblnkPojo.setNavcode("");
     if(piweblnkPojo.getNavname()==null) piweblnkPojo.setNavname("");
     if(piweblnkPojo.getNavgroup()==null) piweblnkPojo.setNavgroup("");
     if(piweblnkPojo.getRownum()==null) piweblnkPojo.setRownum(0);
     if(piweblnkPojo.getImagecss()==null) piweblnkPojo.setImagecss("");
     if(piweblnkPojo.getIconurl()==null) piweblnkPojo.setIconurl("");
     if(piweblnkPojo.getNavigateurl()==null) piweblnkPojo.setNavigateurl("");
     if(piweblnkPojo.getMvcurl()==null) piweblnkPojo.setMvcurl("");
     if(piweblnkPojo.getModuletype()==null) piweblnkPojo.setModuletype("");
     if(piweblnkPojo.getModulecode()==null) piweblnkPojo.setModulecode("");
     if(piweblnkPojo.getRolecode()==null) piweblnkPojo.setRolecode("");
     if(piweblnkPojo.getImageindex()==null) piweblnkPojo.setImageindex("");
     if(piweblnkPojo.getImagestyle()==null) piweblnkPojo.setImagestyle("");
     if(piweblnkPojo.getEnabledmark()==null) piweblnkPojo.setEnabledmark(0);
     if(piweblnkPojo.getRemark()==null) piweblnkPojo.setRemark("");
     if(piweblnkPojo.getPermissioncode()==null) piweblnkPojo.setPermissioncode("");
     if(piweblnkPojo.getFunctionid()==null) piweblnkPojo.setFunctionid("");
     if(piweblnkPojo.getFunctioncode()==null) piweblnkPojo.setFunctioncode("");
     if(piweblnkPojo.getFunctionname()==null) piweblnkPojo.setFunctionname("");
     if(piweblnkPojo.getLister()==null) piweblnkPojo.setLister("");
     if(piweblnkPojo.getCreatedate()==null) piweblnkPojo.setCreatedate(new Date());
     if(piweblnkPojo.getModifydate()==null) piweblnkPojo.setModifydate(new Date());
     if(piweblnkPojo.getDeletemark()==null) piweblnkPojo.setDeletemark(0);
     if(piweblnkPojo.getDeletelister()==null) piweblnkPojo.setDeletelister("");
     if(piweblnkPojo.getDeletedate()==null) piweblnkPojo.setDeletedate(new Date());
        PiweblnkEntity piweblnkEntity = new PiweblnkEntity(); 
        BeanUtils.copyProperties(piweblnkPojo,piweblnkEntity);
        //生成雪花id
          piweblnkEntity.setNavid(inksSnowflake.getSnowflake().nextIdStr());
//          piweblnkEntity.setRevision(1);  //乐观锁
          this.piweblnkMapper.insert(piweblnkEntity);
        return this.getEntity(piweblnkEntity.getNavid());
  
    }

    /**
     * 修改数据
     *
     * @param piweblnkPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiweblnkPojo update(PiweblnkPojo piweblnkPojo) {
        PiweblnkEntity piweblnkEntity = new PiweblnkEntity(); 
        BeanUtils.copyProperties(piweblnkPojo,piweblnkEntity);
        this.piweblnkMapper.update(piweblnkEntity);
        return this.getEntity(piweblnkEntity.getNavid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.piweblnkMapper.delete(key) ;
    }

    @Override
    public List<PiweblnkPojo> getListByPid(String key) {
        return  this.piweblnkMapper.getListByPid(key);
    }
}
