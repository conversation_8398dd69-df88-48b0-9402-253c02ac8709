package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.AESUtil;
import inks.system.domain.PitenantscmuserEntity;
import inks.system.domain.pojo.PiscmuserPojo;
import inks.system.domain.pojo.PitenantscmuserPojo;
import inks.system.mapper.PitenantscmuserMapper;
import inks.system.service.PiscmuserService;
import inks.system.service.PitenantscmuserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * SCM租户关系表(Pitenantscmuser)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@Service("pitenantscmuserService")
public class PitenantscmuserServiceImpl implements PitenantscmuserService {
    @Resource
    private PitenantscmuserMapper pitenantscmuserMapper;
    @Resource
    private PiscmuserService piscmuserService;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PitenantscmuserPojo getEntity(String key) {
        return this.pitenantscmuserMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PitenantscmuserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PitenantscmuserPojo> lst = pitenantscmuserMapper.getPageList(queryParam);
            PageInfo<PitenantscmuserPojo> pageInfo = new PageInfo<PitenantscmuserPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param pitenantscmuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PitenantscmuserPojo insert(PitenantscmuserPojo pitenantscmuserPojo) {
    //初始化NULL字段
        if(pitenantscmuserPojo.getTenantid()==null) pitenantscmuserPojo.setTenantid("");
        if(pitenantscmuserPojo.getTenantname()==null) pitenantscmuserPojo.setTenantname("");
        if(pitenantscmuserPojo.getUserid()==null) pitenantscmuserPojo.setUserid("");
        if(pitenantscmuserPojo.getUsername()==null) pitenantscmuserPojo.setUsername("");
        if(pitenantscmuserPojo.getRealname()==null) pitenantscmuserPojo.setRealname("");
        if(pitenantscmuserPojo.getUsertype()==null) pitenantscmuserPojo.setUsertype(0);
        if(pitenantscmuserPojo.getIsadmin()==null) pitenantscmuserPojo.setIsadmin(0);
        if(pitenantscmuserPojo.getDeptid()==null) pitenantscmuserPojo.setDeptid("");
        if(pitenantscmuserPojo.getDeptcode()==null) pitenantscmuserPojo.setDeptcode("");
        if(pitenantscmuserPojo.getDeptname()==null) pitenantscmuserPojo.setDeptname("");
        if(pitenantscmuserPojo.getIsdeptadmin()==null) pitenantscmuserPojo.setIsdeptadmin(0);
        if(pitenantscmuserPojo.getDeptrownum()==null) pitenantscmuserPojo.setDeptrownum(0);
        if(pitenantscmuserPojo.getRownum()==null) pitenantscmuserPojo.setRownum(0);
        if(pitenantscmuserPojo.getUserstatus()==null) pitenantscmuserPojo.setUserstatus(0);
        if(pitenantscmuserPojo.getUsercode()==null) pitenantscmuserPojo.setUsercode("");
        if(pitenantscmuserPojo.getGroupids()==null) pitenantscmuserPojo.setGroupids("");
        if(pitenantscmuserPojo.getGroupnames()==null) pitenantscmuserPojo.setGroupnames("");
        if(pitenantscmuserPojo.getScmfunctids()==null) pitenantscmuserPojo.setScmfunctids("");
        if(pitenantscmuserPojo.getScmfunctnames()==null) pitenantscmuserPojo.setScmfunctnames("");
        if(pitenantscmuserPojo.getCreateby()==null) pitenantscmuserPojo.setCreateby("");
        if(pitenantscmuserPojo.getCreatebyid()==null) pitenantscmuserPojo.setCreatebyid("");
        if(pitenantscmuserPojo.getCreatedate()==null) pitenantscmuserPojo.setCreatedate(new Date());
        if(pitenantscmuserPojo.getLister()==null) pitenantscmuserPojo.setLister("");
        if(pitenantscmuserPojo.getListerid()==null) pitenantscmuserPojo.setListerid("");
        if(pitenantscmuserPojo.getModifydate()==null) pitenantscmuserPojo.setModifydate(new Date());
        if(pitenantscmuserPojo.getCustom1()==null) pitenantscmuserPojo.setCustom1("");
        if(pitenantscmuserPojo.getCustom2()==null) pitenantscmuserPojo.setCustom2("");
        if(pitenantscmuserPojo.getCustom3()==null) pitenantscmuserPojo.setCustom3("");
        if(pitenantscmuserPojo.getCustom4()==null) pitenantscmuserPojo.setCustom4("");
        if(pitenantscmuserPojo.getCustom5()==null) pitenantscmuserPojo.setCustom5("");
        if(pitenantscmuserPojo.getRevision()==null) pitenantscmuserPojo.setRevision(0);
        PitenantscmuserEntity pitenantscmuserEntity = new PitenantscmuserEntity(); 
        BeanUtils.copyProperties(pitenantscmuserPojo,pitenantscmuserEntity);
  //生成雪花id
          pitenantscmuserEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          pitenantscmuserEntity.setRevision(1);  //乐观锁
          this.pitenantscmuserMapper.insert(pitenantscmuserEntity);
        return this.getEntity(pitenantscmuserEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param pitenantscmuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PitenantscmuserPojo update(PitenantscmuserPojo pitenantscmuserPojo) {
        PitenantscmuserEntity pitenantscmuserEntity = new PitenantscmuserEntity(); 
        BeanUtils.copyProperties(pitenantscmuserPojo,pitenantscmuserEntity);
        this.pitenantscmuserMapper.update(pitenantscmuserEntity);
        return this.getEntity(pitenantscmuserEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pitenantscmuserMapper.delete(key) ;
    }

    /**
     * 分页查询
     * @return 查询结果
     */
    @Override
    public  List<PitenantscmuserPojo> getListByUser(String Userid) {
        return this.pitenantscmuserMapper.getListByUser(Userid) ;
    }

    @Override
    public PitenantscmuserPojo getEntityByUserid(String key, String tid) {
        return this.pitenantscmuserMapper.getEntityByUserid(key,tid) ;
    }



    @Override
    public PitenantscmuserPojo createScmUser(PitenantscmuserPojo pitenantscmuserPojo) throws Exception {
        //PitenantscmuserPojo的用户信息拷贝到PiscmuserPojo
        PiscmuserPojo piscmuserPojo = new PiscmuserPojo();
        BeanUtils.copyProperties(pitenantscmuserPojo,piscmuserPojo);
        //1.判断是否有传入userid;无,新增ScmUser
        if (StringUtils.isBlank(pitenantscmuserPojo.getUserid())) {
            //新建ScmUser用户,并获取userid(给个默认密码)
            piscmuserPojo.setUserpassword(AESUtil.Encrypt("123456")); //加密
            PiscmuserPojo insert = piscmuserService.insert(piscmuserPojo);
            pitenantscmuserPojo.setUserid(insert.getUserid());
            //绑定租户,更新groupids,functids
            return this.insert(pitenantscmuserPojo);
        }
        //1.判断是否有传入userid;有,修改ScmUser
        else {
            piscmuserService.update(piscmuserPojo);
            //判断是否绑定当前租户
            PitenantscmuserPojo entityByUserid = this.getEntityByUserid(pitenantscmuserPojo.getUserid(), pitenantscmuserPojo.getTenantid());
            if (entityByUserid == null) {
                //未绑定:绑定租户,更新groupids,functids
                return this.insert(pitenantscmuserPojo);
            }else {
                //已绑定:更新groupids,functids
                return this.update(pitenantscmuserPojo);
            }
        }
    }

}
