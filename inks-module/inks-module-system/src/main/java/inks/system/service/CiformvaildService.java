package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiformvaildPojo;
import inks.system.domain.pojo.CiformvailditemdetailPojo;
import com.github.pagehelper.PageInfo;

/**
 * 窗体验证(Ciformvaild)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-31 17:03:53
 */
public interface CiformvaildService {

    CiformvaildPojo getEntity(String key,String tid);

    PageInfo<CiformvailditemdetailPojo> getPageList(QueryParam queryParam);

    CiformvaildPojo getBillEntity(String key,String tid);

    PageInfo<CiformvaildPojo> getBillList(QueryParam queryParam);

    PageInfo<CiformvaildPojo> getPageTh(QueryParam queryParam);

    CiformvaildPojo insert(CiformvaildPojo ciformvaildPojo);

    CiformvaildPojo update(CiformvaildPojo ciformvaildpojo);

    int delete(String key,String tid);

    CiformvaildPojo getBillEntityByFormCode(String key, String tenantid);
}
