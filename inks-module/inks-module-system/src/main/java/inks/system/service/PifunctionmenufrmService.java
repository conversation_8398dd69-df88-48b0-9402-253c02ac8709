package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PifunctionmenufrmPojo;
import com.github.pagehelper.PageInfo;
import inks.system.domain.pojo.PifunctionmenuwebPojo;

import java.util.List;

/**
 * 服务Frm关系(PiFunctionMenuFrm)表服务接口
 *
 * <AUTHOR>
 * @since 2024-05-05 16:28:10
 */
public interface PifunctionmenufrmService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionmenufrmPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PifunctionmenufrmPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pifunctionmenufrmPojo 实例对象
     * @return 实例对象
     */
    PifunctionmenufrmPojo insert(PifunctionmenufrmPojo pifunctionmenufrmPojo);

    /**
     * 修改数据
     *
     * @param pifunctionmenufrmpojo 实例对象
     * @return 实例对象
     */
    PifunctionmenufrmPojo update(PifunctionmenufrmPojo pifunctionmenufrmpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

    List<PifunctionmenuwebPojo> getListByFunction(String key);
}
