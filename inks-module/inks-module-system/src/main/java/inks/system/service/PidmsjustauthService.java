package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PidmsjustauthPojo;
import inks.system.domain.PidmsjustauthEntity;

import com.github.pagehelper.PageInfo;

/**
 * DMS第三方登录(Pidmsjustauth)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-21 21:25:20
 */
public interface PidmsjustauthService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PidmsjustauthPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PidmsjustauthPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pidmsjustauthPojo 实例对象
     * @return 实例对象
     */
    PidmsjustauthPojo insert(PidmsjustauthPojo pidmsjustauthPojo);

    /**
     * 修改数据
     *
     * @param pidmsjustauthpojo 实例对象
     * @return 实例对象
     */
    PidmsjustauthPojo update(PidmsjustauthPojo pidmsjustauthpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

    int deleteByOpenid(String openid, String tid);
}
