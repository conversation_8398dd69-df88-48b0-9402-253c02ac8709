package inks.system.service;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PipermissionPojo;
import inks.system.domain.PipermissionEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 权限关系表(Pipermission)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-08 14:34:15
 */
public interface PipermissionService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PipermissionPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PipermissionPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pipermissionPojo 实例对象
     * @return 实例对象
     */
    PipermissionPojo insert(PipermissionPojo pipermissionPojo);

    /**
     * 修改数据
     *
     * @param pipermissionpojo 实例对象
     * @return 实例对象
     */
    PipermissionPojo update(PipermissionPojo pipermissionpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

    /**
     * 按角色查询权限
     *
     * @param key 主键
     * @return 是否成功
     */
    public List<PipermissionPojo> getListByRole(String key);

    List<PipermissionPojo> getUserAllPerm(String key);

    String batchCreateDelete(List<PipermissionPojo> pipermissionList, LoginUser loginUser);
}
