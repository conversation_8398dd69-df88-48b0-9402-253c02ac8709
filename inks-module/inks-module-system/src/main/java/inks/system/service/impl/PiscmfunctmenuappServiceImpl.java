package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.SnowflakeConfig;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.pojo.PimenuappPojo;
import inks.system.domain.pojo.PiscmfunctmenuappPojo;
import inks.system.domain.PiscmfunctmenuappEntity;
import inks.system.mapper.PiscmfunctmenuappMapper;
import inks.system.service.PiscmfunctmenuappService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.List;
/**
 * SCMAPP关系(Piscmfunctmenuapp)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@Service("piscmfunctmenuappService")
public class PiscmfunctmenuappServiceImpl implements PiscmfunctmenuappService {
    @Resource
    private PiscmfunctmenuappMapper piscmfunctmenuappMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PiscmfunctmenuappPojo getEntity(String key) {
        return this.piscmfunctmenuappMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PiscmfunctmenuappPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PiscmfunctmenuappPojo> lst = piscmfunctmenuappMapper.getPageList(queryParam);
            PageInfo<PiscmfunctmenuappPojo> pageInfo = new PageInfo<PiscmfunctmenuappPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param piscmfunctmenuappPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiscmfunctmenuappPojo insert(PiscmfunctmenuappPojo piscmfunctmenuappPojo) {
    //初始化NULL字段
     if(piscmfunctmenuappPojo.getScmfunctid()==null) piscmfunctmenuappPojo.setScmfunctid("");
     if(piscmfunctmenuappPojo.getScmfunctcode()==null) piscmfunctmenuappPojo.setScmfunctcode("");
     if(piscmfunctmenuappPojo.getScmfunctname()==null) piscmfunctmenuappPojo.setScmfunctname("");
     if(piscmfunctmenuappPojo.getNavid()==null) piscmfunctmenuappPojo.setNavid("");
     if(piscmfunctmenuappPojo.getNavcode()==null) piscmfunctmenuappPojo.setNavcode("");
     if(piscmfunctmenuappPojo.getNavname()==null) piscmfunctmenuappPojo.setNavname("");
     if(piscmfunctmenuappPojo.getRemark()==null) piscmfunctmenuappPojo.setRemark("");
     if(piscmfunctmenuappPojo.getCreateby()==null) piscmfunctmenuappPojo.setCreateby("");
     if(piscmfunctmenuappPojo.getCreatebyid()==null) piscmfunctmenuappPojo.setCreatebyid("");
     if(piscmfunctmenuappPojo.getCreatedate()==null) piscmfunctmenuappPojo.setCreatedate(new Date());
     if(piscmfunctmenuappPojo.getLister()==null) piscmfunctmenuappPojo.setLister("");
     if(piscmfunctmenuappPojo.getListerid()==null) piscmfunctmenuappPojo.setListerid("");
     if(piscmfunctmenuappPojo.getModifydate()==null) piscmfunctmenuappPojo.setModifydate(new Date());
     if(piscmfunctmenuappPojo.getRevision()==null) piscmfunctmenuappPojo.setRevision(0);
        PiscmfunctmenuappEntity piscmfunctmenuappEntity = new PiscmfunctmenuappEntity(); 
        BeanUtils.copyProperties(piscmfunctmenuappPojo,piscmfunctmenuappEntity);
  //生成雪花id
          piscmfunctmenuappEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          piscmfunctmenuappEntity.setRevision(1);  //乐观锁
          this.piscmfunctmenuappMapper.insert(piscmfunctmenuappEntity);
        return this.getEntity(piscmfunctmenuappEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param piscmfunctmenuappPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiscmfunctmenuappPojo update(PiscmfunctmenuappPojo piscmfunctmenuappPojo) {
        PiscmfunctmenuappEntity piscmfunctmenuappEntity = new PiscmfunctmenuappEntity(); 
        BeanUtils.copyProperties(piscmfunctmenuappPojo,piscmfunctmenuappEntity);
        this.piscmfunctmenuappMapper.update(piscmfunctmenuappEntity);
        return this.getEntity(piscmfunctmenuappEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.piscmfunctmenuappMapper.delete(key) ;
    }

    @Override
    public List<PiscmfunctmenuappPojo> getListByFunction(String key) {
        return this.piscmfunctmenuappMapper.getListByFunction(key);
    }

    @Override
    public List<PimenuappPojo> getListByScmFunctids(String scmfunctids) {
        return this.piscmfunctmenuappMapper.getListByScmFunctids(scmfunctids);
    }
}
