package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiadminPojo;
import inks.system.domain.PiadminEntity;

import com.github.pagehelper.PageInfo;

/**
 * 平台管理员(Piadmin)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-08 13:55:55
 */
public interface PiadminService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiadminPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PiadminPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param piadminPojo 实例对象
     * @return 实例对象
     */
    PiadminPojo insert(PiadminPojo piadminPojo);

    /**
     * 修改数据
     *
     * @param piadminpojo 实例对象
     * @return 实例对象
     */
    PiadminPojo update(PiadminPojo piadminpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 通过用户id初始化密码
     *
     * @param piadminpojo 主键
     * @return 是否成功
     */
    int initpassword(PiadminPojo piadminpojo);
}
