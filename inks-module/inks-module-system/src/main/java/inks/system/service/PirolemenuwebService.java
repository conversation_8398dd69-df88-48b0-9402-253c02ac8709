package inks.system.service;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PirolemenuwebPojo;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 角色菜单Web(PiRoleMenuWeb)表服务接口
 *
 * <AUTHOR>
 * @since 2024-06-11 13:55:54
 */
public interface PirolemenuwebService {

    PirolemenuwebPojo getEntity(String key,String tid);

    PageInfo<PirolemenuwebPojo> getPageList(QueryParam queryParam);

    PirolemenuwebPojo insert(PirolemenuwebPojo pirolemenuwebPojo);

    PirolemenuwebPojo update(PirolemenuwebPojo pirolemenuwebpojo);

    int delete(String key,String tid);

    int deleteByRoleidAndNavid(String roleid, String navid, String tenantid);

    Integer batchCreateDelete(String roleid, List<String> deleteNavids, List<String> createNavids, LoginUser loginUser);

    List<PirolemenuwebPojo> getListByRole(String key, String tenantid);
}
