package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PifunctionpermPojo;

import java.util.HashSet;
import java.util.List;

/**
 * 服务权限关系(Pifunctionperm)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-11 17:34:56
 */
public interface PifunctionpermService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionpermPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PifunctionpermPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pifunctionpermPojo 实例对象
     * @return 实例对象
     */
    PifunctionpermPojo insert(PifunctionpermPojo pifunctionpermPojo);

    /**
     * 修改数据
     *
     * @param pifunctionpermpojo 实例对象
     * @return 实例对象
     */
    PifunctionpermPojo update(PifunctionpermPojo pifunctionpermpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

    List<PifunctionpermPojo> getListByFunction(String key);

    HashSet<String> getFunctionPermsByFunctionCode(String functioncode,String tid);
}
