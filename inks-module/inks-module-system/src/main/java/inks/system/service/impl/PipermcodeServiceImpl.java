package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PipermcodeEntity;
import inks.system.domain.pojo.PipermcodePojo;
import inks.system.mapper.PipermcodeMapper;
import inks.system.service.PipermcodeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 权限编码表(Pipermcode)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-27 10:01:54
 */
@Service("pipermcodeService")
public class PipermcodeServiceImpl implements PipermcodeService {
    @Resource
    private PipermcodeMapper pipermcodeMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PipermcodePojo getEntity(String key) {
        return this.pipermcodeMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PipermcodePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PipermcodePojo> lst = pipermcodeMapper.getPageList(queryParam);
            PageInfo<PipermcodePojo> pageInfo = new PageInfo<PipermcodePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param pipermcodePojo 实例对象
     * @return 实例对象
     */
    @Override
    public PipermcodePojo insert(PipermcodePojo pipermcodePojo) {
    //初始化NULL字段
     if(pipermcodePojo.getParentid()==null) pipermcodePojo.setParentid("");
     if(pipermcodePojo.getPermtype()==null) pipermcodePojo.setPermtype("");
     if(pipermcodePojo.getPermcode()==null) pipermcodePojo.setPermcode("");
     if(pipermcodePojo.getPermname()==null) pipermcodePojo.setPermname("");
     if(pipermcodePojo.getRownum()==null) pipermcodePojo.setRownum(0);
     if(pipermcodePojo.getIspublic()==null) pipermcodePojo.setIspublic(0);
     if(pipermcodePojo.getEnabledmark()==null) pipermcodePojo.setEnabledmark(0);
     if(pipermcodePojo.getAllowdelete()==null) pipermcodePojo.setAllowdelete(0);
     if(pipermcodePojo.getRemark()==null) pipermcodePojo.setRemark("");
     if(pipermcodePojo.getCreateby()==null) pipermcodePojo.setCreateby("");
     if(pipermcodePojo.getCreatebyid()==null) pipermcodePojo.setCreatebyid("");
     if(pipermcodePojo.getCreatedate()==null) pipermcodePojo.setCreatedate(new Date());
     if(pipermcodePojo.getLister()==null) pipermcodePojo.setLister("");
     if(pipermcodePojo.getListerid()==null) pipermcodePojo.setListerid("");
     if(pipermcodePojo.getModifydate()==null) pipermcodePojo.setModifydate(new Date());
     if(pipermcodePojo.getRevision()==null) pipermcodePojo.setRevision(0);
        PipermcodeEntity pipermcodeEntity = new PipermcodeEntity(); 
        BeanUtils.copyProperties(pipermcodePojo,pipermcodeEntity);
        
          pipermcodeEntity.setPermid(inksSnowflake.getSnowflake().nextIdStr());
          pipermcodeEntity.setRevision(1);  //乐观锁
          this.pipermcodeMapper.insert(pipermcodeEntity);
        return this.getEntity(pipermcodeEntity.getPermid());
  
    }

    /**
     * 修改数据
     *
     * @param pipermcodePojo 实例对象
     * @return 实例对象
     */
    @Override
    public PipermcodePojo update(PipermcodePojo pipermcodePojo) {
        PipermcodeEntity pipermcodeEntity = new PipermcodeEntity(); 
        BeanUtils.copyProperties(pipermcodePojo,pipermcodeEntity);
        this.pipermcodeMapper.update(pipermcodeEntity);
        return this.getEntity(pipermcodeEntity.getPermid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成
     */
    @Override
    public int delete(String key) {
        return this.pipermcodeMapper.delete(key) ;
    }
}
