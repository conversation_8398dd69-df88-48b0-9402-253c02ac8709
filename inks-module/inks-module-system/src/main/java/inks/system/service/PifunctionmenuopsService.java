package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PifunctionmenuopsPojo;
import com.github.pagehelper.PageInfo;
import inks.system.domain.pojo.PifunctionmenuwebPojo;

import java.util.List;

/**
 * 服务Form关系(PiFunctionMenuOps)表服务接口
 *
 * <AUTHOR>
 * @since 2024-04-24 17:03:52
 */
public interface PifunctionmenuopsService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionmenuopsPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PifunctionmenuopsPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pifunctionmenuopsPojo 实例对象
     * @return 实例对象
     */
    PifunctionmenuopsPojo insert(PifunctionmenuopsPojo pifunctionmenuopsPojo);

    /**
     * 修改数据
     *
     * @param pifunctionmenuopspojo 实例对象
     * @return 实例对象
     */
    PifunctionmenuopsPojo update(PifunctionmenuopsPojo pifunctionmenuopspojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

    List<PifunctionmenuwebPojo> getListByFunction(String key);
}
