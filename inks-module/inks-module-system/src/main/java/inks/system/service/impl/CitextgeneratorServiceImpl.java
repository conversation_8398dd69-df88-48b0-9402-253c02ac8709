package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.system.domain.pojo.CitextgeneratorPojo;
import inks.system.domain.CitextgeneratorEntity;
import inks.system.mapper.CitextgeneratorMapper;
import inks.system.service.CitextgeneratorService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import inks.common.core.text.inksSnowflake;

/**
 * 文本生成器(Citextgenerator)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-18 15:00:23
 */
@Service("citextgeneratorService")
public class CitextgeneratorServiceImpl implements CitextgeneratorService {
    @Resource
    private CitextgeneratorMapper citextgeneratorMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CitextgeneratorPojo getEntity(String key, String tid) {
        return this.citextgeneratorMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CitextgeneratorPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CitextgeneratorPojo> lst = citextgeneratorMapper.getPageList(queryParam);
            PageInfo<CitextgeneratorPojo> pageInfo = new PageInfo<CitextgeneratorPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param citextgeneratorPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CitextgeneratorPojo insert(CitextgeneratorPojo citextgeneratorPojo) {
        //初始化NULL字段
        if (citextgeneratorPojo.getParentid() == null) citextgeneratorPojo.setParentid("");
        if (citextgeneratorPojo.getTgcode() == null) citextgeneratorPojo.setTgcode("");
        if (citextgeneratorPojo.getTgvalue() == null) citextgeneratorPojo.setTgvalue("");
        if (citextgeneratorPojo.getTglevel() == null) citextgeneratorPojo.setTglevel(0);
        if (citextgeneratorPojo.getEssential() == null) citextgeneratorPojo.setEssential(0);
        if (citextgeneratorPojo.getTgbillcode() == null) citextgeneratorPojo.setTgbillcode("");

        if (citextgeneratorPojo.getCssclass() == null) citextgeneratorPojo.setCssclass("");
        if (citextgeneratorPojo.getRownum() == null) citextgeneratorPojo.setRownum(0);
        if (citextgeneratorPojo.getDefaultmark() == null) citextgeneratorPojo.setDefaultmark(0);
        if (citextgeneratorPojo.getEnabledmark() == null) citextgeneratorPojo.setEnabledmark(0);
        if (citextgeneratorPojo.getRemark() == null) citextgeneratorPojo.setRemark("");
        if (citextgeneratorPojo.getCreateby() == null) citextgeneratorPojo.setCreateby("");
        if (citextgeneratorPojo.getCreatebyid() == null) citextgeneratorPojo.setCreatebyid("");
        if (citextgeneratorPojo.getCreatedate() == null) citextgeneratorPojo.setCreatedate(new Date());
        if (citextgeneratorPojo.getLister() == null) citextgeneratorPojo.setLister("");
        if (citextgeneratorPojo.getListerid() == null) citextgeneratorPojo.setListerid("");
        if (citextgeneratorPojo.getModifydate() == null) citextgeneratorPojo.setModifydate(new Date());
        if (citextgeneratorPojo.getCustom1() == null) citextgeneratorPojo.setCustom1("");
        if (citextgeneratorPojo.getCustom2() == null) citextgeneratorPojo.setCustom2("");
        if (citextgeneratorPojo.getCustom3() == null) citextgeneratorPojo.setCustom3("");
        if (citextgeneratorPojo.getCustom4() == null) citextgeneratorPojo.setCustom4("");
        if (citextgeneratorPojo.getCustom5() == null) citextgeneratorPojo.setCustom5("");
        if (citextgeneratorPojo.getTenantid() == null) citextgeneratorPojo.setTenantid("");
        if (citextgeneratorPojo.getRevision() == null) citextgeneratorPojo.setRevision(0);
        CitextgeneratorEntity citextgeneratorEntity = new CitextgeneratorEntity();
        BeanUtils.copyProperties(citextgeneratorPojo, citextgeneratorEntity);
        //生成雪花id
        citextgeneratorEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        citextgeneratorEntity.setRevision(1);  //乐观锁
        this.citextgeneratorMapper.insert(citextgeneratorEntity);
        return this.getEntity(citextgeneratorEntity.getId(), citextgeneratorEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param citextgeneratorPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CitextgeneratorPojo update(CitextgeneratorPojo citextgeneratorPojo) {
        CitextgeneratorEntity citextgeneratorEntity = new CitextgeneratorEntity();
        BeanUtils.copyProperties(citextgeneratorPojo, citextgeneratorEntity);
        this.citextgeneratorMapper.update(citextgeneratorEntity);
        return this.getEntity(citextgeneratorEntity.getId(), citextgeneratorEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        // 校验是否存在子节点
        List<CitextgeneratorPojo> children = getChildrenByParentId(key, tid);
        if (!children.isEmpty()) {
            throw new BaseBusinessException("存在子节点，无法删除");
        }
        return this.citextgeneratorMapper.delete(key, tid);
    }

    @Override
    public List<CitextgeneratorPojo> orderByRowNum(List<String> ids, String tid) {
        //从0开始给行号赋值
        int count = this.citextgeneratorMapper.orderByRowNum(ids, tid);

        return citextgeneratorMapper.getListByid(ids.get(0), tid);
    }


    public List<CitextgeneratorPojo> getTree(String tid) {
        // 首先获取第一层的节点（Parentid为""的，且tglevel=1）
        List<CitextgeneratorPojo> firstLevelNodes = getChildrenByParentId("", tid);
        // 递归构建树状结构
        for (CitextgeneratorPojo node : firstLevelNodes) {
            buildTree(node, tid);
        }
        return firstLevelNodes;
    }

    private void buildTree(CitextgeneratorPojo parentNode, String tid) {
        // 获取当前节点的子节点列表
        List<CitextgeneratorPojo> children = getChildrenByParentId(parentNode.getId(), tid);
        // 递归构建子树
        for (CitextgeneratorPojo child : children) {
            buildTree(child, tid);
        }
        // 将子节点列表设置到父节点中
        parentNode.setChildren(children);
    }

    // 假设这个方法用于从数据库或其他数据源中获取指定父级ID的子级列表
    private List<CitextgeneratorPojo> getChildrenByParentId(String parentId, String tid) {
        // 在这里实现获取子级列表的逻辑
        return citextgeneratorMapper.getListByParentid(parentId, tid);
    }
}
