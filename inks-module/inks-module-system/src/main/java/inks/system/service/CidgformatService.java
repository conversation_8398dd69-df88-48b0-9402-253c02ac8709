package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CidgformatPojo;
import inks.system.domain.pojo.CidgformatitemdetailPojo;

/**
 * 列表格式(Cidgformat)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-21 15:17:44
 */
public interface CidgformatService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CidgformatPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CidgformatitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CidgformatPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CidgformatPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CidgformatPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param cidgformatPojo 实例对象
     * @return 实例对象
     */
    CidgformatPojo insert(CidgformatPojo cidgformatPojo);

    /**
     * 修改数据
     *
     * @param cidgformatpojo 实例对象
     * @return 实例对象
     */
    CidgformatPojo update(CidgformatPojo cidgformatpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);


    /**
     * 通过ID查询单条数据
     *
     * @param code 主键
     * @return 实例对象
     */
    CidgformatPojo getBillEntityByCode(String code,String userid, String tid);

    CidgformatPojo getTenBillEntityByCode(String code,String tid);

    CidgformatPojo getEntityByCodeUser(String formCode, String userid, String tenantid);

    int deleteByCode(String code, String userid, String tid);
}
