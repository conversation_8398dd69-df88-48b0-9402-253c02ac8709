package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PirmsuserEntity;
import inks.system.domain.pojo.PirmsjustauthPojo;
import inks.system.domain.pojo.PirmsuserPojo;
import inks.system.mapper.PirmsjustauthMapper;
import inks.system.mapper.PirmsuserMapper;
import inks.system.service.PirmsuserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * RMS用户(Pirmsuser)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-21 19:05:49
 */
@Service("pirmsuserService")
public class PirmsuserServiceImpl implements PirmsuserService {
    @Resource
    private PirmsuserMapper pirmsuserMapper;
    @Resource
    private PirmsjustauthMapper pirmsjustauthMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PirmsuserPojo getEntity(String key, String tid) {
        PirmsuserPojo pirmsuserPojo = this.pirmsuserMapper.getEntity(key, tid);
        //加入是否有绑定openid
        PirmsjustauthPojo pirmsjustauthPojo = pirmsjustauthMapper.getEntityByUseridAndAuthtype(key, "openid", tid);
        if (pirmsjustauthPojo!= null) {
            pirmsuserPojo.setOpenid(pirmsjustauthPojo.getAuthuuid());
        }
        return pirmsuserPojo;
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PirmsuserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PirmsuserPojo> lst = pirmsuserMapper.getPageList(queryParam);
            PageInfo<PirmsuserPojo> pageInfo = new PageInfo<PirmsuserPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<PirmsuserPojo> getPageListByTen(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PirmsuserPojo> lst = pirmsuserMapper.getPageListByTen(queryParam);
            PageInfo<PirmsuserPojo> pageInfo = new PageInfo<PirmsuserPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param pirmsuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PirmsuserPojo insert(PirmsuserPojo pirmsuserPojo) {
        //初始化NULL字段
        if (pirmsuserPojo.getUsername() == null) pirmsuserPojo.setUsername("");
        if (pirmsuserPojo.getRealname() == null) pirmsuserPojo.setRealname("");
        if (pirmsuserPojo.getNickname() == null) pirmsuserPojo.setNickname("");
        if (pirmsuserPojo.getUserpassword() == null) pirmsuserPojo.setUserpassword("");
        if (pirmsuserPojo.getMobile() == null) pirmsuserPojo.setMobile("");
        if (pirmsuserPojo.getEmail() == null) pirmsuserPojo.setEmail("");
        if (pirmsuserPojo.getSex() == null) pirmsuserPojo.setSex(0);
        if (pirmsuserPojo.getLangcode() == null) pirmsuserPojo.setLangcode("");
        if (pirmsuserPojo.getAvatar() == null) pirmsuserPojo.setAvatar("");
        if (pirmsuserPojo.getUsertype() == null) pirmsuserPojo.setUsertype(0);
        if (pirmsuserPojo.getIsadmin() == null) pirmsuserPojo.setIsadmin(0);
        if (pirmsuserPojo.getDeptid() == null) pirmsuserPojo.setDeptid("");
        if (pirmsuserPojo.getDeptcode() == null) pirmsuserPojo.setDeptcode("");
        if (pirmsuserPojo.getDeptname() == null) pirmsuserPojo.setDeptname("");
        if (pirmsuserPojo.getIsdeptadmin() == null) pirmsuserPojo.setIsdeptadmin(0);
        if (pirmsuserPojo.getDeptrownum() == null) pirmsuserPojo.setDeptrownum(0);
        if (pirmsuserPojo.getRownum() == null) pirmsuserPojo.setRownum(0);
        if (pirmsuserPojo.getUserstatus() == null) pirmsuserPojo.setUserstatus(0);
        if (pirmsuserPojo.getUsercode() == null) pirmsuserPojo.setUsercode("");
        if (pirmsuserPojo.getGroupids() == null) pirmsuserPojo.setGroupids("");
        if (pirmsuserPojo.getGroupnames() == null) pirmsuserPojo.setGroupnames("");
        if (pirmsuserPojo.getRmsfunctids() == null) pirmsuserPojo.setRmsfunctids("");
        if (pirmsuserPojo.getRmsfunctnames() == null) pirmsuserPojo.setRmsfunctnames("");
        if (pirmsuserPojo.getRemark() == null) pirmsuserPojo.setRemark("");
        if(pirmsuserPojo.getDatalabel()==null) pirmsuserPojo.setDatalabel("");
        if(pirmsuserPojo.getCreateby()==null) pirmsuserPojo.setCreateby("");
        if (pirmsuserPojo.getCreatebyid() == null) pirmsuserPojo.setCreatebyid("");
        if (pirmsuserPojo.getCreatedate() == null) pirmsuserPojo.setCreatedate(new Date());
        if (pirmsuserPojo.getLister() == null) pirmsuserPojo.setLister("");
        if (pirmsuserPojo.getListerid() == null) pirmsuserPojo.setListerid("");
        if (pirmsuserPojo.getModifydate() == null) pirmsuserPojo.setModifydate(new Date());
        if (pirmsuserPojo.getTenantid() == null) pirmsuserPojo.setTenantid("");
        if (pirmsuserPojo.getTenantname() == null) pirmsuserPojo.setTenantname("");
        if (pirmsuserPojo.getRevision() == null) pirmsuserPojo.setRevision(0);
        PirmsuserEntity pirmsuserEntity = new PirmsuserEntity();
        BeanUtils.copyProperties(pirmsuserPojo, pirmsuserEntity);
        //生成雪花id
        pirmsuserEntity.setUserid(inksSnowflake.getSnowflake().nextIdStr());
        pirmsuserEntity.setRevision(1);  //乐观锁
        this.pirmsuserMapper.insert(pirmsuserEntity);
        return this.getEntity(pirmsuserEntity.getUserid(), pirmsuserEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param pirmsuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PirmsuserPojo update(PirmsuserPojo pirmsuserPojo) {
        PirmsuserEntity pirmsuserEntity = new PirmsuserEntity();
        BeanUtils.copyProperties(pirmsuserPojo, pirmsuserEntity);
        this.pirmsuserMapper.update(pirmsuserEntity);
        return this.getEntity(pirmsuserEntity.getUserid(), pirmsuserEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        // 还得删除关联表 PiTenantRmsUser
        this.pirmsuserMapper.deletePiTenantRmsUser(key, tid);
        return this.pirmsuserMapper.delete(key, tid);
    }

    @Override
    public PirmsuserPojo getEntityByUserName(String username) {
        return this.pirmsuserMapper.getEntityByUserName(username);
    }

    @Override
    public PirmsuserPojo getEntityByOpenid(String openid, String tenantid) {
        return this.pirmsuserMapper.getEntityByOpenid(openid, tenantid);
    }

    @Override
    public List<PirmsuserPojo> getListByOpenid(String openid) {
        return this.pirmsuserMapper.getListByOpenid(openid);
    }

    @Override
    public PirmsuserPojo getEntityByUserid(String userid, String tid) {
        return this.pirmsuserMapper.getEntityByUserid(userid, tid);
    }
}
