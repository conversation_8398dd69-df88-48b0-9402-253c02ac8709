package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.system.domain.pojo.PidmsjustauthPojo;
import inks.system.domain.PidmsjustauthEntity;
import inks.system.mapper.PidmsjustauthMapper;
import inks.system.service.PidmsjustauthService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * DMS第三方登录(Pidmsjustauth)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-21 21:25:20
 */
@Service("pidmsjustauthService")
public class PidmsjustauthServiceImpl implements PidmsjustauthService {
    @Resource
    private PidmsjustauthMapper pidmsjustauthMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PidmsjustauthPojo getEntity(String key, String tid) {
        return this.pidmsjustauthMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PidmsjustauthPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PidmsjustauthPojo> lst = pidmsjustauthMapper.getPageList(queryParam);
            PageInfo<PidmsjustauthPojo> pageInfo = new PageInfo<PidmsjustauthPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param pidmsjustauthPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PidmsjustauthPojo insert(PidmsjustauthPojo pidmsjustauthPojo) {
    //初始化NULL字段
     if(pidmsjustauthPojo.getUserid()==null) pidmsjustauthPojo.setUserid("");
     if(pidmsjustauthPojo.getUsername()==null) pidmsjustauthPojo.setUsername("");
     if(pidmsjustauthPojo.getRealname()==null) pidmsjustauthPojo.setRealname("");
     if(pidmsjustauthPojo.getNickname()==null) pidmsjustauthPojo.setNickname("");
     if(pidmsjustauthPojo.getAuthtype()==null) pidmsjustauthPojo.setAuthtype("");
     if(pidmsjustauthPojo.getAuthuuid()==null) pidmsjustauthPojo.setAuthuuid("");
     if(pidmsjustauthPojo.getUnionid()==null) pidmsjustauthPojo.setUnionid("");
     if(pidmsjustauthPojo.getAuthavatar()==null) pidmsjustauthPojo.setAuthavatar("");
     if(pidmsjustauthPojo.getCreateby()==null) pidmsjustauthPojo.setCreateby("");
     if(pidmsjustauthPojo.getCreatebyid()==null) pidmsjustauthPojo.setCreatebyid("");
     if(pidmsjustauthPojo.getCreatedate()==null) pidmsjustauthPojo.setCreatedate(new Date());
     if(pidmsjustauthPojo.getLister()==null) pidmsjustauthPojo.setLister("");
     if(pidmsjustauthPojo.getListerid()==null) pidmsjustauthPojo.setListerid("");
     if(pidmsjustauthPojo.getModifydate()==null) pidmsjustauthPojo.setModifydate(new Date());
     if(pidmsjustauthPojo.getCustom1()==null) pidmsjustauthPojo.setCustom1("");
     if(pidmsjustauthPojo.getCustom2()==null) pidmsjustauthPojo.setCustom2("");
     if(pidmsjustauthPojo.getCustom3()==null) pidmsjustauthPojo.setCustom3("");
     if(pidmsjustauthPojo.getCustom4()==null) pidmsjustauthPojo.setCustom4("");
     if(pidmsjustauthPojo.getCustom5()==null) pidmsjustauthPojo.setCustom5("");
     if(pidmsjustauthPojo.getTenantid()==null) pidmsjustauthPojo.setTenantid("");
     if(pidmsjustauthPojo.getTenantname()==null) pidmsjustauthPojo.setTenantname("");
     if(pidmsjustauthPojo.getRevision()==null) pidmsjustauthPojo.setRevision(0);
        PidmsjustauthEntity pidmsjustauthEntity = new PidmsjustauthEntity(); 
        BeanUtils.copyProperties(pidmsjustauthPojo,pidmsjustauthEntity);
          //生成雪花id
          pidmsjustauthEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          pidmsjustauthEntity.setRevision(1);  //乐观锁
          this.pidmsjustauthMapper.insert(pidmsjustauthEntity);
        return this.getEntity(pidmsjustauthEntity.getId(),pidmsjustauthEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param pidmsjustauthPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PidmsjustauthPojo update(PidmsjustauthPojo pidmsjustauthPojo) {
        PidmsjustauthEntity pidmsjustauthEntity = new PidmsjustauthEntity(); 
        BeanUtils.copyProperties(pidmsjustauthPojo,pidmsjustauthEntity);
        this.pidmsjustauthMapper.update(pidmsjustauthEntity);
        return this.getEntity(pidmsjustauthEntity.getId(),pidmsjustauthEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.pidmsjustauthMapper.delete(key,tid) ;
    }

    @Override
    public int deleteByOpenid(String openid, String tid) {
        return this.pidmsjustauthMapper.deleteByOpenid(openid,tid) ;
    }
}
