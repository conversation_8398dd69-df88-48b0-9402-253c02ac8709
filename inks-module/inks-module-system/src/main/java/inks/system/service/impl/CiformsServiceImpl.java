package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CiformsEntity;
import inks.system.domain.pojo.CiformsPojo;
import inks.system.mapper.CiformsMapper;
import inks.system.service.CiformsService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 窗体中心(Ciforms)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-18 15:43:44
 */
@Service("ciformsService")
public class CiformsServiceImpl implements CiformsService {
    @Resource
    private CiformsMapper ciformsMapper;

    @Override
    public CiformsPojo getEntity(String key) {
        return this.ciformsMapper.getEntity(key);
    }


    @Override
    public PageInfo<CiformsPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiformsPojo> lst = ciformsMapper.getPageList(queryParam);
            PageInfo<CiformsPojo> pageInfo = new PageInfo<CiformsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public CiformsPojo insert(CiformsPojo ciformsPojo) {
        // 校验modulecode下只有一个FormType = 1
        if (Objects.equals(ciformsPojo.getFormtype(), 0)) {
            CiformsPojo existingForm = this.ciformsMapper.getEntityByCode(ciformsPojo.getModulecode(), 1, ciformsPojo.getTenantid());
            if (existingForm != null) {
                throw new BaseBusinessException("ModuleCode下只能有一个FormType为0的窗体");
            }
        }
        //初始化NULL字段
        cleanNull(ciformsPojo);
        CiformsEntity ciformsEntity = new CiformsEntity();
        BeanUtils.copyProperties(ciformsPojo, ciformsEntity);
        //生成雪花id
        ciformsEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        ciformsEntity.setRevision(1);  //乐观锁
        this.ciformsMapper.insert(ciformsEntity);
        return this.getEntity(ciformsEntity.getId());
    }


    @Override
    public CiformsPojo update(CiformsPojo ciformsPojo) {
        // 校验modulecode下只有一个FormType = 1
        if (Objects.equals(ciformsPojo.getFormtype(), 0)) {
            CiformsPojo existingForm = this.ciformsMapper.getEntityByCode(ciformsPojo.getModulecode(), 1, ciformsPojo.getTenantid());
            if (existingForm != null && !existingForm.getId().equals(ciformsPojo.getId())) {
                throw new BaseBusinessException("ModuleCode下只能有一个FormType为0的窗体");
            }
        }
        CiformsEntity ciformsEntity = new CiformsEntity();
        BeanUtils.copyProperties(ciformsPojo, ciformsEntity);
        this.ciformsMapper.update(ciformsEntity);
        return this.getEntity(ciformsEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.ciformsMapper.delete(key);
    }


    private static void cleanNull(CiformsPojo ciformsPojo) {
        if (ciformsPojo.getModulecode() == null) ciformsPojo.setModulecode("");
        if (ciformsPojo.getFormtype() == null) ciformsPojo.setFormtype(0);
        if (ciformsPojo.getFormentry() == null) ciformsPojo.setFormentry("");
        if (ciformsPojo.getEnabledmark() == null) ciformsPojo.setEnabledmark(0);
        if (ciformsPojo.getRownum() == null) ciformsPojo.setRownum(0);
        if (ciformsPojo.getRemark() == null) ciformsPojo.setRemark("");
        if (ciformsPojo.getCreateby() == null) ciformsPojo.setCreateby("");
        if (ciformsPojo.getCreatebyid() == null) ciformsPojo.setCreatebyid("");
        if (ciformsPojo.getCreatedate() == null) ciformsPojo.setCreatedate(new Date());
        if (ciformsPojo.getLister() == null) ciformsPojo.setLister("");
        if (ciformsPojo.getListerid() == null) ciformsPojo.setListerid("");
        if (ciformsPojo.getModifydate() == null) ciformsPojo.setModifydate(new Date());
        if (ciformsPojo.getCustom1() == null) ciformsPojo.setCustom1("");
        if (ciformsPojo.getCustom2() == null) ciformsPojo.setCustom2("");
        if (ciformsPojo.getCustom3() == null) ciformsPojo.setCustom3("");
        if (ciformsPojo.getCustom4() == null) ciformsPojo.setCustom4("");
        if (ciformsPojo.getCustom5() == null) ciformsPojo.setCustom5("");
        if (ciformsPojo.getTenantid() == null) ciformsPojo.setTenantid("");
        if (ciformsPojo.getTenantname() == null) ciformsPojo.setTenantname("");
        if (ciformsPojo.getRevision() == null) ciformsPojo.setRevision(0);
    }

    @Override
    public List<CiformsPojo> getListByCode(String key, String tenantid) {
        return this.ciformsMapper.getListByCode(key, tenantid);
    }

    @Override
    public CiformsPojo getEntityByCode(String key, int type, String tenantid) {
        return this.ciformsMapper.getEntityByCode(key, type, tenantid);
    }

    @Override
    public PageInfo<CiformsPojo> getPageListAll(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiformsPojo> lst = ciformsMapper.getPageListAll(queryParam);
            PageInfo<CiformsPojo> pageInfo = new PageInfo<CiformsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }
}
