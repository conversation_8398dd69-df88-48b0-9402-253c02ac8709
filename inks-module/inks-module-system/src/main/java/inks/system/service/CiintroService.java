package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiintroPojo;

/**
 * 功能简介(Ciintro)表服务接口
 *
 * <AUTHOR>
 * @since 2022-07-07 20:44:33
 */
public interface CiintroService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiintroPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CiintroPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param ciintroPojo 实例对象
     * @return 实例对象
     */
    CiintroPojo insert(CiintroPojo ciintroPojo);

    /**
     * 修改数据
     *
     * @param ciintropojo 实例对象
     * @return 实例对象
     */
    CiintroPojo update(CiintroPojo ciintropojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiintroPojo getEntityByCode(String key);
}
