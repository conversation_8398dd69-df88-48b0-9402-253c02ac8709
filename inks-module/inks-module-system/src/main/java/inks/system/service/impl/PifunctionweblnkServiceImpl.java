package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.system.domain.pojo.PifunctionweblnkPojo;
import inks.system.domain.PifunctionweblnkEntity;
import inks.system.mapper.PifunctionweblnkMapper;
import inks.system.service.PifunctionweblnkService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * Web快捷方式(Pifunctionweblnk)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-08 14:48:40
 */
@Service("pifunctionweblnkService")
public class PifunctionweblnkServiceImpl implements PifunctionweblnkService {
    @Resource
    private PifunctionweblnkMapper pifunctionweblnkMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PifunctionweblnkPojo getEntity(String key, String tid) {
        return this.pifunctionweblnkMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PifunctionweblnkPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PifunctionweblnkPojo> lst = pifunctionweblnkMapper.getPageList(queryParam);
            PageInfo<PifunctionweblnkPojo> pageInfo = new PageInfo<PifunctionweblnkPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param pifunctionweblnkPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PifunctionweblnkPojo insert(PifunctionweblnkPojo pifunctionweblnkPojo) {
    //初始化NULL字段
     if(pifunctionweblnkPojo.getFunctionid()==null) pifunctionweblnkPojo.setFunctionid("");
     if(pifunctionweblnkPojo.getFunctioncode()==null) pifunctionweblnkPojo.setFunctioncode("");
     if(pifunctionweblnkPojo.getFunctionname()==null) pifunctionweblnkPojo.setFunctionname("");
     if(pifunctionweblnkPojo.getNavid()==null) pifunctionweblnkPojo.setNavid("");
     if(pifunctionweblnkPojo.getNavcode()==null) pifunctionweblnkPojo.setNavcode("");
     if(pifunctionweblnkPojo.getNavname()==null) pifunctionweblnkPojo.setNavname("");
     if(pifunctionweblnkPojo.getRemark()==null) pifunctionweblnkPojo.setRemark("");
     if(pifunctionweblnkPojo.getCreateby()==null) pifunctionweblnkPojo.setCreateby("");
     if(pifunctionweblnkPojo.getCreatedate()==null) pifunctionweblnkPojo.setCreatedate(new Date());
     if(pifunctionweblnkPojo.getLister()==null) pifunctionweblnkPojo.setLister("");
     if(pifunctionweblnkPojo.getModifydate()==null) pifunctionweblnkPojo.setModifydate(new Date());
     if(pifunctionweblnkPojo.getTenantid()==null) pifunctionweblnkPojo.setTenantid("");
     if(pifunctionweblnkPojo.getRevision()==null) pifunctionweblnkPojo.setRevision(0);
        PifunctionweblnkEntity pifunctionweblnkEntity = new PifunctionweblnkEntity();
        BeanUtils.copyProperties(pifunctionweblnkPojo,pifunctionweblnkEntity);
          //生成雪花id
          pifunctionweblnkEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          pifunctionweblnkEntity.setRevision(1);  //乐观锁
          this.pifunctionweblnkMapper.insert(pifunctionweblnkEntity);
        return this.getEntity(pifunctionweblnkEntity.getId(),pifunctionweblnkEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param pifunctionweblnkPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PifunctionweblnkPojo update(PifunctionweblnkPojo pifunctionweblnkPojo) {
        PifunctionweblnkEntity pifunctionweblnkEntity = new PifunctionweblnkEntity();
        BeanUtils.copyProperties(pifunctionweblnkPojo,pifunctionweblnkEntity);
        this.pifunctionweblnkMapper.update(pifunctionweblnkEntity);
        return this.getEntity(pifunctionweblnkEntity.getId(),pifunctionweblnkEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.pifunctionweblnkMapper.delete(key,tid) ;
    }

    @Override
    public List<PifunctionweblnkPojo> getListByFunction(String key) {
        return this.pifunctionweblnkMapper.getListByFunction(key);
    }
}
