package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CidynamicvalidationPojo;
import inks.system.domain.pojo.CidynamicvalidationitemdetailPojo;
import com.github.pagehelper.PageInfo;

/**
 * 动态校验规则(Cidynamicvalidation)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-04 15:54:38
 */
public interface CidynamicvalidationService {

    CidynamicvalidationPojo getEntity(String key,String tid);

    PageInfo<CidynamicvalidationitemdetailPojo> getPageList(QueryParam queryParam);

    CidynamicvalidationPojo getBillEntity(String key,String tid);

    PageInfo<CidynamicvalidationPojo> getBillList(QueryParam queryParam);

    PageInfo<CidynamicvalidationPojo> getPageTh(QueryParam queryParam);

    CidynamicvalidationPojo insert(CidynamicvalidationPojo cidynamicvalidationPojo);

    CidynamicvalidationPojo update(CidynamicvalidationPojo cidynamicvalidationpojo);

    int delete(String key,String tid);

}
