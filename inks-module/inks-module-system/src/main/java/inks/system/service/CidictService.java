package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CidictPojo;
import inks.system.domain.pojo.CidictitemdetailPojo;

/**
 * 数据字典(Cidict)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-12 07:38:32
 */
public interface CidictService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CidictPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CidictitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CidictPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CidictPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CidictPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param cidictPojo 实例对象
     * @return 实例对象
     */
    CidictPojo insert(CidictPojo cidictPojo);

    /**
     * 修改数据
     *
     * @param cidictpojo 实例对象
     * @return 实例对象
     */
    CidictPojo update(CidictPojo cidictpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CidictPojo getBillEntityByDictCode(String key, String tid);

}
