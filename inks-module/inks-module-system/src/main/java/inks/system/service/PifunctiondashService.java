package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PifunctiondashPojo;

import java.util.List;

/**
 * 服务工作台(Pifunctiondash)表服务接口
 *
 * <AUTHOR>
 * @since 2022-07-15 21:13:36
 */
public interface PifunctiondashService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctiondashPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PifunctiondashPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pifunctiondashPojo 实例对象
     * @return 实例对象
     */
    PifunctiondashPojo insert(PifunctiondashPojo pifunctiondashPojo);

    /**
     * 修改数据
     *
     * @param pifunctiondashpojo 实例对象
     * @return 实例对象
     */
    PifunctiondashPojo update(PifunctiondashPojo pifunctiondashpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<PifunctiondashPojo> getListByFunction(String key);
}
