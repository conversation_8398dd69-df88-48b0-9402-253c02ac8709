package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.system.domain.pojo.PifunctionrptgrpPojo;
import inks.system.domain.PifunctionrptgrpEntity;
import inks.system.mapper.PifunctionrptgrpMapper;
import inks.system.service.PifunctionrptgrpService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 服务报表分组数据(Pifunctionrptgrp)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-22 15:56:27
 */
@Service("pifunctionrptgrpService")
public class PifunctionrptgrpServiceImpl implements PifunctionrptgrpService {
    @Resource
    private PifunctionrptgrpMapper pifunctionrptgrpMapper;

    @Override
    public PifunctionrptgrpPojo getEntity(String key, String tid) {
        return this.pifunctionrptgrpMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<PifunctionrptgrpPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PifunctionrptgrpPojo> lst = pifunctionrptgrpMapper.getPageList(queryParam);
            PageInfo<PifunctionrptgrpPojo> pageInfo = new PageInfo<PifunctionrptgrpPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public PageInfo<PifunctionrptgrpPojo> getPageListByFunctionid(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PifunctionrptgrpPojo> lst = pifunctionrptgrpMapper.getPageListByFunctionid(queryParam);
            PageInfo<PifunctionrptgrpPojo> pageInfo = new PageInfo<PifunctionrptgrpPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PifunctionrptgrpPojo insert(PifunctionrptgrpPojo pifunctionrptgrpPojo) {
        //初始化NULL字段
        cleanNull(pifunctionrptgrpPojo);
        PifunctionrptgrpEntity pifunctionrptgrpEntity = new PifunctionrptgrpEntity(); 
        BeanUtils.copyProperties(pifunctionrptgrpPojo,pifunctionrptgrpEntity);
          //生成雪花id
          pifunctionrptgrpEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          pifunctionrptgrpEntity.setRevision(1);  //乐观锁
          this.pifunctionrptgrpMapper.insert(pifunctionrptgrpEntity);
        return this.getEntity(pifunctionrptgrpEntity.getId(),pifunctionrptgrpEntity.getTenantid());
    }


    @Override
    public PifunctionrptgrpPojo update(PifunctionrptgrpPojo pifunctionrptgrpPojo) {
        PifunctionrptgrpEntity pifunctionrptgrpEntity = new PifunctionrptgrpEntity(); 
        BeanUtils.copyProperties(pifunctionrptgrpPojo,pifunctionrptgrpEntity);
        this.pifunctionrptgrpMapper.update(pifunctionrptgrpEntity);
        return this.getEntity(pifunctionrptgrpEntity.getId(),pifunctionrptgrpEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.pifunctionrptgrpMapper.delete(key,tid) ;
    }
    
    @Override
    public List<PifunctionrptgrpPojo> getList(String tid) {
        try {
            return this.pifunctionrptgrpMapper.getList(tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    private static void cleanNull(PifunctionrptgrpPojo pifunctionrptgrpPojo) {
        if(pifunctionrptgrpPojo.getParentid()==null) pifunctionrptgrpPojo.setParentid("");
        if(pifunctionrptgrpPojo.getModulecode()==null) pifunctionrptgrpPojo.setModulecode("");
        if(pifunctionrptgrpPojo.getRptname()==null) pifunctionrptgrpPojo.setRptname("");
        if(pifunctionrptgrpPojo.getFunctionid()==null) pifunctionrptgrpPojo.setFunctionid("");
        if(pifunctionrptgrpPojo.getFunctioncode()==null) pifunctionrptgrpPojo.setFunctioncode("");
        if(pifunctionrptgrpPojo.getFunctionname()==null) pifunctionrptgrpPojo.setFunctionname("");
        if(pifunctionrptgrpPojo.getRownum()==null) pifunctionrptgrpPojo.setRownum(0);
        if(pifunctionrptgrpPojo.getRemark()==null) pifunctionrptgrpPojo.setRemark("");
        if(pifunctionrptgrpPojo.getCreateby()==null) pifunctionrptgrpPojo.setCreateby("");
        if(pifunctionrptgrpPojo.getCreatebyid()==null) pifunctionrptgrpPojo.setCreatebyid("");
        if(pifunctionrptgrpPojo.getCreatedate()==null) pifunctionrptgrpPojo.setCreatedate(new Date());
        if(pifunctionrptgrpPojo.getLister()==null) pifunctionrptgrpPojo.setLister("");
        if(pifunctionrptgrpPojo.getListerid()==null) pifunctionrptgrpPojo.setListerid("");
        if(pifunctionrptgrpPojo.getModifydate()==null) pifunctionrptgrpPojo.setModifydate(new Date());
        if(pifunctionrptgrpPojo.getCustom1()==null) pifunctionrptgrpPojo.setCustom1("");
        if(pifunctionrptgrpPojo.getCustom2()==null) pifunctionrptgrpPojo.setCustom2("");
        if(pifunctionrptgrpPojo.getCustom3()==null) pifunctionrptgrpPojo.setCustom3("");
        if(pifunctionrptgrpPojo.getCustom4()==null) pifunctionrptgrpPojo.setCustom4("");
        if(pifunctionrptgrpPojo.getCustom5()==null) pifunctionrptgrpPojo.setCustom5("");
        if(pifunctionrptgrpPojo.getCustom6()==null) pifunctionrptgrpPojo.setCustom6("");
        if(pifunctionrptgrpPojo.getCustom7()==null) pifunctionrptgrpPojo.setCustom7("");
        if(pifunctionrptgrpPojo.getCustom8()==null) pifunctionrptgrpPojo.setCustom8("");
        if(pifunctionrptgrpPojo.getCustom9()==null) pifunctionrptgrpPojo.setCustom9("");
        if(pifunctionrptgrpPojo.getCustom10()==null) pifunctionrptgrpPojo.setCustom10("");
        if(pifunctionrptgrpPojo.getTenantid()==null) pifunctionrptgrpPojo.setTenantid("");
        if(pifunctionrptgrpPojo.getTenantname()==null) pifunctionrptgrpPojo.setTenantname("");
        if(pifunctionrptgrpPojo.getRevision()==null) pifunctionrptgrpPojo.setRevision(0);
   }

}
