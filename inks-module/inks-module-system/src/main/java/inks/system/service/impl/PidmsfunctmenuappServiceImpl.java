package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.SnowflakeConfig;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.pojo.PimenuappPojo;
import inks.system.domain.pojo.PidmsfunctmenuappPojo;
import inks.system.domain.PidmsfunctmenuappEntity;
import inks.system.mapper.PidmsfunctmenuappMapper;
import inks.system.service.PidmsfunctmenuappService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.List;
/**
 * DMSAPP关系(Pidmsfunctmenuapp)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@Service("pidmsfunctmenuappService")
public class PidmsfunctmenuappServiceImpl implements PidmsfunctmenuappService {
    @Resource
    private PidmsfunctmenuappMapper pidmsfunctmenuappMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PidmsfunctmenuappPojo getEntity(String key) {
        return this.pidmsfunctmenuappMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PidmsfunctmenuappPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PidmsfunctmenuappPojo> lst = pidmsfunctmenuappMapper.getPageList(queryParam);
            PageInfo<PidmsfunctmenuappPojo> pageInfo = new PageInfo<PidmsfunctmenuappPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param pidmsfunctmenuappPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PidmsfunctmenuappPojo insert(PidmsfunctmenuappPojo pidmsfunctmenuappPojo) {
    //初始化NULL字段
     if(pidmsfunctmenuappPojo.getDmsfunctid()==null) pidmsfunctmenuappPojo.setDmsfunctid("");
     if(pidmsfunctmenuappPojo.getDmsfunctcode()==null) pidmsfunctmenuappPojo.setDmsfunctcode("");
     if(pidmsfunctmenuappPojo.getDmsfunctname()==null) pidmsfunctmenuappPojo.setDmsfunctname("");
     if(pidmsfunctmenuappPojo.getNavid()==null) pidmsfunctmenuappPojo.setNavid("");
     if(pidmsfunctmenuappPojo.getNavcode()==null) pidmsfunctmenuappPojo.setNavcode("");
     if(pidmsfunctmenuappPojo.getNavname()==null) pidmsfunctmenuappPojo.setNavname("");
     if(pidmsfunctmenuappPojo.getRemark()==null) pidmsfunctmenuappPojo.setRemark("");
     if(pidmsfunctmenuappPojo.getCreateby()==null) pidmsfunctmenuappPojo.setCreateby("");
     if(pidmsfunctmenuappPojo.getCreatebyid()==null) pidmsfunctmenuappPojo.setCreatebyid("");
     if(pidmsfunctmenuappPojo.getCreatedate()==null) pidmsfunctmenuappPojo.setCreatedate(new Date());
     if(pidmsfunctmenuappPojo.getLister()==null) pidmsfunctmenuappPojo.setLister("");
     if(pidmsfunctmenuappPojo.getListerid()==null) pidmsfunctmenuappPojo.setListerid("");
     if(pidmsfunctmenuappPojo.getModifydate()==null) pidmsfunctmenuappPojo.setModifydate(new Date());
     if(pidmsfunctmenuappPojo.getRevision()==null) pidmsfunctmenuappPojo.setRevision(0);
        PidmsfunctmenuappEntity pidmsfunctmenuappEntity = new PidmsfunctmenuappEntity(); 
        BeanUtils.copyProperties(pidmsfunctmenuappPojo,pidmsfunctmenuappEntity);
  //生成雪花id
          pidmsfunctmenuappEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          pidmsfunctmenuappEntity.setRevision(1);  //乐观锁
          this.pidmsfunctmenuappMapper.insert(pidmsfunctmenuappEntity);
        return this.getEntity(pidmsfunctmenuappEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param pidmsfunctmenuappPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PidmsfunctmenuappPojo update(PidmsfunctmenuappPojo pidmsfunctmenuappPojo) {
        PidmsfunctmenuappEntity pidmsfunctmenuappEntity = new PidmsfunctmenuappEntity(); 
        BeanUtils.copyProperties(pidmsfunctmenuappPojo,pidmsfunctmenuappEntity);
        this.pidmsfunctmenuappMapper.update(pidmsfunctmenuappEntity);
        return this.getEntity(pidmsfunctmenuappEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pidmsfunctmenuappMapper.delete(key) ;
    }

    @Override
    public List<PidmsfunctmenuappPojo> getListByFunction(String key) {
        return this.pidmsfunctmenuappMapper.getListByFunction(key);
    }

    @Override
    public List<PimenuappPojo> getListByDmsFunctids(String dmsfunctids) {
        return this.pidmsfunctmenuappMapper.getListByDmsFunctids(dmsfunctids);
    }
}
