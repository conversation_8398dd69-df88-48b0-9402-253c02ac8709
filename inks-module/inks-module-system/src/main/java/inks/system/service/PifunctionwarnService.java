package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PifunctionwarnPojo;

import java.util.List;

/**
 * 服务预警关系(Pifunctionwarn)表服务接口
 *
 * <AUTHOR>
 * @since 2022-07-05 20:11:43
 */
public interface PifunctionwarnService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionwarnPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PifunctionwarnPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pifunctionwarnPojo 实例对象
     * @return 实例对象
     */
    PifunctionwarnPojo insert(PifunctionwarnPojo pifunctionwarnPojo);

    /**
     * 修改数据
     *
     * @param pifunctionwarnpojo 实例对象
     * @return 实例对象
     */
    PifunctionwarnPojo update(PifunctionwarnPojo pifunctionwarnpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);


    List<PifunctionwarnPojo> getListByFunction(String key);
}
