package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.redis.service.RedisService;
import inks.system.domain.PiuserroleEntity;
import inks.system.domain.pojo.PipermcodePojo;
import inks.system.domain.pojo.PiuserrolePojo;
import inks.system.mapper.PiuserroleMapper;
import inks.system.service.PiuserroleService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 角色关系表(Piuserrole)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-17 10:33:18
 */
@Service("piuserroleService")
public class PiuserroleServiceImpl implements PiuserroleService {
    @Resource
    private PiuserroleMapper piuserroleMapper;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PiuserrolePojo getEntity(String key, String tid) {
        return this.piuserroleMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PiuserrolePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PiuserrolePojo> lst = piuserroleMapper.getPageList(queryParam);
            PageInfo<PiuserrolePojo> pageInfo = new PageInfo<PiuserrolePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param piuserrolePojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiuserrolePojo insert(PiuserrolePojo piuserrolePojo) {
        //初始化NULL字段
        if (piuserrolePojo.getRoleid() == null) piuserrolePojo.setRoleid("");
        if (piuserrolePojo.getUserid() == null) piuserrolePojo.setUserid("");
        if (piuserrolePojo.getRownum() == null) piuserrolePojo.setRownum(0);
        if (piuserrolePojo.getCreateby() == null) piuserrolePojo.setCreateby("");
        if (piuserrolePojo.getCreatebyid() == null) piuserrolePojo.setCreatebyid("");
        if (piuserrolePojo.getCreatedate() == null) piuserrolePojo.setCreatedate(new Date());
        if (piuserrolePojo.getLister() == null) piuserrolePojo.setLister("");
        if (piuserrolePojo.getListerid() == null) piuserrolePojo.setListerid("");
        if (piuserrolePojo.getModifydate() == null) piuserrolePojo.setModifydate(new Date());
        if (piuserrolePojo.getTenantid() == null) piuserrolePojo.setTenantid("");
        if (piuserrolePojo.getTenantname() == null) piuserrolePojo.setTenantname("");
        if (piuserrolePojo.getRevision() == null) piuserrolePojo.setRevision(0);
        PiuserroleEntity piuserroleEntity = new PiuserroleEntity();
        BeanUtils.copyProperties(piuserrolePojo, piuserroleEntity);

        piuserroleEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        piuserroleEntity.setRevision(1);  //乐观锁
        this.piuserroleMapper.insert(piuserroleEntity);
        // 清除Redis
        this.redisService.deleteObject("user_permset:" + piuserrolePojo.getTenantid() + "-" + piuserrolePojo.getUserid());
        return this.getEntity(piuserroleEntity.getId(), piuserroleEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param piuserrolePojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiuserrolePojo update(PiuserrolePojo piuserrolePojo) {
        // 清除Redis
        this.redisService.deleteObject("user_permset:" + piuserrolePojo.getTenantid() + "-" + piuserrolePojo.getUserid());
        PiuserroleEntity piuserroleEntity = new PiuserroleEntity();
        BeanUtils.copyProperties(piuserrolePojo, piuserroleEntity);
        this.piuserroleMapper.update(piuserroleEntity);
        return this.getEntity(piuserroleEntity.getId(), piuserroleEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        PiuserrolePojo piuserrolePojo=getEntity(key,tid);
        // 清除Redis
        this.redisService.deleteObject("user_permset:" + piuserrolePojo.getTenantid()+"-" +piuserrolePojo.getUserid());
        return this.piuserroleMapper.delete(key, tid);
    }

    @Override
    public List<PiuserrolePojo> getListByRole(String key) {
        return this.piuserroleMapper.getListByRole(key);
    }

    @Override
    public List<PiuserrolePojo> getListByUser(String key, String tid) {
        return this.piuserroleMapper.getListByUser(key, tid);
    }

    @Override
    public List<PipermcodePojo> getPermByUser(String key, String tid) {
        return this.piuserroleMapper.getPermByUser(key, tid);
    }

    @Override
    public HashSet<String> getPermSetByUser(String key, String tid) {//key是userid
        //从redis中获取Reprot内容
        HashSet<String> setPerm = this.redisService.getCacheObject("user_permset:" + tid + "-" + key);
        if (setPerm == null) {
            setPerm = new HashSet<>();
            List<PipermcodePojo> lst = this.piuserroleMapper.getPermByUser(key, tid);
            for (PipermcodePojo pipermcodePojo : lst) {
                if (pipermcodePojo.getPermtype() != null) {
                    if (pipermcodePojo.getPermtype().equals("3")) setPerm.add(pipermcodePojo.getPermcode());  //按键作用
                }
            }
            this.redisService.setCacheObject("user_permset:" + tid + "-" + key, setPerm, (long) (30), TimeUnit.DAYS);
        }

        return setPerm;
    }

}
