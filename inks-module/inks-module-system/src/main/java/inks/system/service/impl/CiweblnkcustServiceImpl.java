package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.system.domain.pojo.CiweblnkcustPojo;
import inks.system.domain.CiweblnkcustEntity;
import inks.system.mapper.CiweblnkcustMapper;
import inks.system.service.CiweblnkcustService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 自定义webLnk(Ciweblnkcust)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-08 14:57:09
 */
@Service("ciweblnkcustService")
public class CiweblnkcustServiceImpl implements CiweblnkcustService {
    @Resource
    private CiweblnkcustMapper ciweblnkcustMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CiweblnkcustPojo getEntity(String key, String tid) {
        return this.ciweblnkcustMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CiweblnkcustPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiweblnkcustPojo> lst = ciweblnkcustMapper.getPageList(queryParam);
            PageInfo<CiweblnkcustPojo> pageInfo = new PageInfo<CiweblnkcustPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param ciweblnkcustPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiweblnkcustPojo insert(CiweblnkcustPojo ciweblnkcustPojo) {
    //初始化NULL字段
     if(ciweblnkcustPojo.getUserid()==null) ciweblnkcustPojo.setUserid("");
     if(ciweblnkcustPojo.getRealname()==null) ciweblnkcustPojo.setRealname("");
     if(ciweblnkcustPojo.getLnkcontent()==null) ciweblnkcustPojo.setLnkcontent("");
        if(ciweblnkcustPojo.getFncode()==null) ciweblnkcustPojo.setFncode("");
     if(ciweblnkcustPojo.getCreateby()==null) ciweblnkcustPojo.setCreateby("");
     if(ciweblnkcustPojo.getCreatebyid()==null) ciweblnkcustPojo.setCreatebyid("");
     if(ciweblnkcustPojo.getCreatedate()==null) ciweblnkcustPojo.setCreatedate(new Date());
     if(ciweblnkcustPojo.getLister()==null) ciweblnkcustPojo.setLister("");
     if(ciweblnkcustPojo.getListerid()==null) ciweblnkcustPojo.setListerid("");
     if(ciweblnkcustPojo.getModifydate()==null) ciweblnkcustPojo.setModifydate(new Date());
     if(ciweblnkcustPojo.getCustom1()==null) ciweblnkcustPojo.setCustom1("");
     if(ciweblnkcustPojo.getCustom2()==null) ciweblnkcustPojo.setCustom2("");
     if(ciweblnkcustPojo.getCustom3()==null) ciweblnkcustPojo.setCustom3("");
     if(ciweblnkcustPojo.getCustom4()==null) ciweblnkcustPojo.setCustom4("");
     if(ciweblnkcustPojo.getCustom5()==null) ciweblnkcustPojo.setCustom5("");
     if(ciweblnkcustPojo.getTenantid()==null) ciweblnkcustPojo.setTenantid("");
     if(ciweblnkcustPojo.getTenantname()==null) ciweblnkcustPojo.setTenantname("");
     if(ciweblnkcustPojo.getRevision()==null) ciweblnkcustPojo.setRevision(0);
        CiweblnkcustEntity ciweblnkcustEntity = new CiweblnkcustEntity(); 
        BeanUtils.copyProperties(ciweblnkcustPojo,ciweblnkcustEntity);
          //生成雪花id
          ciweblnkcustEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          ciweblnkcustEntity.setRevision(1);  //乐观锁
          this.ciweblnkcustMapper.insert(ciweblnkcustEntity);
        return this.getEntity(ciweblnkcustEntity.getId(),ciweblnkcustEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param ciweblnkcustPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiweblnkcustPojo update(CiweblnkcustPojo ciweblnkcustPojo) {
        CiweblnkcustEntity ciweblnkcustEntity = new CiweblnkcustEntity(); 
        BeanUtils.copyProperties(ciweblnkcustPojo,ciweblnkcustEntity);
        this.ciweblnkcustMapper.update(ciweblnkcustEntity);
        return this.getEntity(ciweblnkcustEntity.getId(),ciweblnkcustEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.ciweblnkcustMapper.delete(key,tid) ;
    }

    @Override
    public CiweblnkcustPojo getEntityBySelf(String userid, String tid) {
        return this.ciweblnkcustMapper.getEntityBySelf(userid,tid);
    }

    @Override
    public List<CiweblnkcustPojo> getListBySelf(String fncode, String userid, String tenantid) {
        return this.ciweblnkcustMapper.getListBySelf(fncode,userid,tenantid);
    }
}
