package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PifunctionmenuopsEntity;
import inks.system.domain.pojo.PifunctionmenuopsPojo;
import inks.system.domain.pojo.PifunctionmenuwebPojo;
import inks.system.mapper.PifunctionmenuopsMapper;
import inks.system.service.PifunctionmenuopsService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 服务Form关系(Pifunctionmenuops)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-04-24 17:03:52
 */
@Service("pifunctionmenuopsService")
public class PifunctionmenuopsServiceImpl implements PifunctionmenuopsService {
    @Resource
    private PifunctionmenuopsMapper pifunctionmenuopsMapper;

    @Override
    public PifunctionmenuopsPojo getEntity(String key, String tid) {
        return this.pifunctionmenuopsMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<PifunctionmenuopsPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PifunctionmenuopsPojo> lst = pifunctionmenuopsMapper.getPageList(queryParam);
            PageInfo<PifunctionmenuopsPojo> pageInfo = new PageInfo<PifunctionmenuopsPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public PifunctionmenuopsPojo insert(PifunctionmenuopsPojo pifunctionmenuopsPojo) {
        //初始化NULL字段
        cleanNull(pifunctionmenuopsPojo);
        PifunctionmenuopsEntity pifunctionmenuopsEntity = new PifunctionmenuopsEntity(); 
        BeanUtils.copyProperties(pifunctionmenuopsPojo,pifunctionmenuopsEntity);
          //生成雪花id
          pifunctionmenuopsEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          pifunctionmenuopsEntity.setRevision(1);  //乐观锁
          this.pifunctionmenuopsMapper.insert(pifunctionmenuopsEntity);
        return this.getEntity(pifunctionmenuopsEntity.getId(),pifunctionmenuopsEntity.getTenantid());
    }


    @Override
    public PifunctionmenuopsPojo update(PifunctionmenuopsPojo pifunctionmenuopsPojo) {
        PifunctionmenuopsEntity pifunctionmenuopsEntity = new PifunctionmenuopsEntity(); 
        BeanUtils.copyProperties(pifunctionmenuopsPojo,pifunctionmenuopsEntity);
        this.pifunctionmenuopsMapper.update(pifunctionmenuopsEntity);
        return this.getEntity(pifunctionmenuopsEntity.getId(),pifunctionmenuopsEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.pifunctionmenuopsMapper.delete(key,tid) ;
    }
    

    private static void cleanNull(PifunctionmenuopsPojo pifunctionmenuopsPojo) {
        if(pifunctionmenuopsPojo.getFunctionid()==null) pifunctionmenuopsPojo.setFunctionid("");
        if(pifunctionmenuopsPojo.getFunctioncode()==null) pifunctionmenuopsPojo.setFunctioncode("");
        if(pifunctionmenuopsPojo.getFunctionname()==null) pifunctionmenuopsPojo.setFunctionname("");
        if(pifunctionmenuopsPojo.getNavid()==null) pifunctionmenuopsPojo.setNavid("");
        if(pifunctionmenuopsPojo.getNavcode()==null) pifunctionmenuopsPojo.setNavcode("");
        if(pifunctionmenuopsPojo.getNavname()==null) pifunctionmenuopsPojo.setNavname("");
        if(pifunctionmenuopsPojo.getRemark()==null) pifunctionmenuopsPojo.setRemark("");
        if(pifunctionmenuopsPojo.getCreateby()==null) pifunctionmenuopsPojo.setCreateby("");
        if(pifunctionmenuopsPojo.getCreatebyid()==null) pifunctionmenuopsPojo.setCreatebyid("");
        if(pifunctionmenuopsPojo.getCreatedate()==null) pifunctionmenuopsPojo.setCreatedate(new Date());
        if(pifunctionmenuopsPojo.getLister()==null) pifunctionmenuopsPojo.setLister("");
        if(pifunctionmenuopsPojo.getListerid()==null) pifunctionmenuopsPojo.setListerid("");
        if(pifunctionmenuopsPojo.getModifydate()==null) pifunctionmenuopsPojo.setModifydate(new Date());
        if(pifunctionmenuopsPojo.getTenantid()==null) pifunctionmenuopsPojo.setTenantid("");
        if(pifunctionmenuopsPojo.getRevision()==null) pifunctionmenuopsPojo.setRevision(0);
   }

    @Override
    public List<PifunctionmenuwebPojo> getListByFunction(String key) {
        return this.pifunctionmenuopsMapper.getListByFunction(key);
    }
}
