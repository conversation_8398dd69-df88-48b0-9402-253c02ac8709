package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PidmsfunctPojo;
import inks.system.domain.PidmsfunctEntity;

import com.github.pagehelper.PageInfo;

/**
 * DMS功能(Pidmsfunct)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
public interface PidmsfunctService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PidmsfunctPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PidmsfunctPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pidmsfunctPojo 实例对象
     * @return 实例对象
     */
    PidmsfunctPojo insert(PidmsfunctPojo pidmsfunctPojo);

    /**
     * 修改数据
     *
     * @param pidmsfunctpojo 实例对象
     * @return 实例对象
     */
    PidmsfunctPojo update(PidmsfunctPojo pidmsfunctpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
                                                                                     }
