package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiformpartPojo;
import inks.system.domain.CiformpartEntity;

import com.github.pagehelper.PageInfo;

/**
 * 表单组件(Ciformpart)表服务接口
 *
 * <AUTHOR>
 * @since 2024-01-19 13:41:52
 */
public interface CiformpartService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiformpartPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CiformpartPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param ciformpartPojo 实例对象
     * @return 实例对象
     */
    CiformpartPojo insert(CiformpartPojo ciformpartPojo);

    /**
     * 修改数据
     *
     * @param ciformpartpojo 实例对象
     * @return 实例对象
     */
    CiformpartPojo update(CiformpartPojo ciformpartpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);
                                                                                                                        }
