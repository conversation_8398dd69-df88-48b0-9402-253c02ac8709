package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CitablecustomPojo;

import java.util.List;

/**
 * 自定义字段(Citablecustom)表服务接口
 *
 * <AUTHOR>
 * @since 2022-08-10 14:26:33
 */
public interface CitablecustomService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CitablecustomPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CitablecustomPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param citablecustomPojo 实例对象
     * @return 实例对象
     */
    CitablecustomPojo insert(CitablecustomPojo citablecustomPojo);

    /**
     * 修改数据
     *
     * @param citablecustompojo 实例对象
     * @return 实例对象
     */
    CitablecustomPojo update(CitablecustomPojo citablecustompojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    List<CitablecustomPojo> getListByCode(String key, String tid);

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    List<CitablecustomPojo> getListByGroupid(String key, String tid);

    /**
     * 修改数据
     *
     * @return 实例对象
     */
    List<CitablecustomPojo> updateList(List<CitablecustomPojo> lst);

}
