package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiloginlogPojo;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 登录日志(Ciloginlog)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-08 13:56:41
 */
public interface CiloginlogService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiloginlogPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CiloginlogPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param ciloginlogPojo 实例对象
     * @return 实例对象
     */
    CiloginlogPojo insert(CiloginlogPojo ciloginlogPojo);

    /**
     * 修改数据
     *
     * @param ciloginlogpojo 实例对象
     * @return 实例对象
     */
    CiloginlogPojo update(CiloginlogPojo ciloginlogpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    List<ChartPojo> getCountListByPro(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    List<ChartPojo> getCountListByCity(QueryParam queryParam);

//    统计时间段内每个admin用户登录情况
    List<Map<String,Object>> getEveryAdminLogin(Date startdate, Date enddate);

    List<Map<String,Object>> getEveryUserLogin(Date startdate, Date enddate);

    List<Map<String, Object>> getEveryTenantLogin(Date startDate, Date endDate);

    List<CiloginlogPojo> getAllAdminLogin(Date startDate, Date endDate);

    List<CiloginlogPojo> getAllUserLogin(Date startDate, Date endDate);

    List<Map<String,Object>> getCountPageListByTen(QueryParam queryParam);


    int deleteByTime(QueryParam queryParam);

    List<Map<String, Object>> getCountSuccessByUser(Date startDate, Date endDate, String tenantid);
}
