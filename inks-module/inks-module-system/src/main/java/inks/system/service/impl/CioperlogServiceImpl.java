package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.pojo.CioperlogPojo;
import inks.system.domain.CioperlogEntity;
import inks.system.mapper.CioperlogMapper;
import inks.system.service.CioperlogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 操作日志(Cioperlog)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-22 13:38:48
 */
@Service("cioperlogService")
public class CioperlogServiceImpl implements CioperlogService {
    @Resource
    private CioperlogMapper cioperlogMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CioperlogPojo getEntity(String key, String tid) {
        return this.cioperlogMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CioperlogPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CioperlogPojo> lst = cioperlogMapper.getPageList(queryParam);
            PageInfo<CioperlogPojo> pageInfo = new PageInfo<CioperlogPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param cioperlogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CioperlogPojo insert(CioperlogPojo cioperlogPojo) {
    //初始化NULL字段
     if(cioperlogPojo.getOpertitle()==null) cioperlogPojo.setOpertitle("");
     if(cioperlogPojo.getBusinesstype()==null) cioperlogPojo.setBusinesstype(0);
     if(cioperlogPojo.getMethod()==null) cioperlogPojo.setMethod("");
     if(cioperlogPojo.getRequestmethod()==null) cioperlogPojo.setRequestmethod("");
     if(cioperlogPojo.getOperatortype()==null) cioperlogPojo.setOperatortype(0);
     if(cioperlogPojo.getOperuserid()==null) cioperlogPojo.setOperuserid("");
     if(cioperlogPojo.getOpername()==null) cioperlogPojo.setOpername("");
     if(cioperlogPojo.getDeptname()==null) cioperlogPojo.setDeptname("");
     if(cioperlogPojo.getOperurl()==null) cioperlogPojo.setOperurl("");
     if(cioperlogPojo.getOperip()==null) cioperlogPojo.setOperip("");
     if(cioperlogPojo.getOperlocation()==null) cioperlogPojo.setOperlocation("");
     if(cioperlogPojo.getOperparam()==null) cioperlogPojo.setOperparam("");
     if(cioperlogPojo.getJsonresult()==null) cioperlogPojo.setJsonresult("");
     if(cioperlogPojo.getStatus()==null) cioperlogPojo.setStatus(0);
     if(cioperlogPojo.getErrormsg()==null) cioperlogPojo.setErrormsg("");
     if(cioperlogPojo.getOpertime()==null) cioperlogPojo.setOpertime(new Date());
     if(cioperlogPojo.getTenantid()==null) cioperlogPojo.setTenantid("");
        CioperlogEntity cioperlogEntity = new CioperlogEntity(); 
        BeanUtils.copyProperties(cioperlogPojo,cioperlogEntity);
          cioperlogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          this.cioperlogMapper.insert(cioperlogEntity);
        return this.getEntity(cioperlogEntity.getId(),cioperlogEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param cioperlogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CioperlogPojo update(CioperlogPojo cioperlogPojo) {
        CioperlogEntity cioperlogEntity = new CioperlogEntity(); 
        BeanUtils.copyProperties(cioperlogPojo,cioperlogEntity);
        this.cioperlogMapper.update(cioperlogEntity);
        return this.getEntity(cioperlogEntity.getId(),cioperlogEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.cioperlogMapper.delete(key,tid) ;
    }

    @Override
    public int deleteByTime(QueryParam queryParam) {
        return cioperlogMapper.deleteByTime(queryParam);
    }
}
