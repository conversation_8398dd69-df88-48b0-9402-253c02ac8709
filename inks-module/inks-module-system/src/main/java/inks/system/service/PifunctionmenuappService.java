package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PifunctionmenuappPojo;

import java.util.List;

/**
 * 服务菜单关系app(Pifunctionmenuapp)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-11 17:34:41
 */
public interface PifunctionmenuappService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionmenuappPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PifunctionmenuappPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pifunctionmenuappPojo 实例对象
     * @return 实例对象
     */
    PifunctionmenuappPojo insert(PifunctionmenuappPojo pifunctionmenuappPojo);

    /**
     * 修改数据
     *
     * @param pifunctionmenuapppojo 实例对象
     * @return 实例对象
     */
    PifunctionmenuappPojo update(PifunctionmenuappPojo pifunctionmenuapppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

    List<PifunctionmenuappPojo> getListByFunction(String key);

}
