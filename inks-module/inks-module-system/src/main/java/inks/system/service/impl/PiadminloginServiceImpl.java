package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.pojo.PiadminloginPojo;
import inks.system.domain.PiadminloginEntity;
import inks.system.mapper.PiadminloginMapper;
import inks.system.service.PiadminloginService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 管理员登录(Piadminlogin)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-08 13:55:42
 */
@Service("piadminloginService")
public class PiadminloginServiceImpl implements PiadminloginService {
    @Resource
    private PiadminloginMapper piadminloginMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PiadminloginPojo getEntity(String key) {
        return this.piadminloginMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PiadminloginPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PiadminloginPojo> lst = piadminloginMapper.getPageList(queryParam);
            PageInfo<PiadminloginPojo> pageInfo = new PageInfo<PiadminloginPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param piadminloginPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiadminloginPojo insert(PiadminloginPojo piadminloginPojo) {
        //初始化NULL字段
        if (piadminloginPojo.getAdminid() == null) piadminloginPojo.setAdminid("");
        if (piadminloginPojo.getUserpassword() == null) piadminloginPojo.setUserpassword("");
        if (piadminloginPojo.getCheckipaddr() == null) piadminloginPojo.setCheckipaddr(0);
        if (piadminloginPojo.getIpaddress() == null) piadminloginPojo.setIpaddress("");
        if (piadminloginPojo.getMacaddress() == null) piadminloginPojo.setMacaddress("");
        if (piadminloginPojo.getFirstvisit() == null) piadminloginPojo.setFirstvisit(new Date());
        if (piadminloginPojo.getPreviouvisit() == null) piadminloginPojo.setPreviouvisit(new Date());
        if (piadminloginPojo.getBrowsername() == null) piadminloginPojo.setBrowsername("");
        if (piadminloginPojo.getHostsystem() == null) piadminloginPojo.setHostsystem("");
        if (piadminloginPojo.getLister() == null) piadminloginPojo.setLister("");
        if (piadminloginPojo.getCreatedate() == null) piadminloginPojo.setCreatedate(new Date());
        if (piadminloginPojo.getModifydate() == null) piadminloginPojo.setModifydate(new Date());
        PiadminloginEntity piadminloginEntity = new PiadminloginEntity();
        BeanUtils.copyProperties(piadminloginPojo, piadminloginEntity);

        piadminloginEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.piadminloginMapper.insert(piadminloginEntity);
        return this.getEntity(piadminloginEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param piadminloginPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiadminloginPojo update(PiadminloginPojo piadminloginPojo) {
        PiadminloginEntity piadminloginEntity = new PiadminloginEntity();
        BeanUtils.copyProperties(piadminloginPojo, piadminloginEntity);
        this.piadminloginMapper.update(piadminloginEntity);
        return this.getEntity(piadminloginEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.piadminloginMapper.delete(key);
    }
}
