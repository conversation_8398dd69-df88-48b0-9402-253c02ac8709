package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PiprojectitemEntity;
import inks.system.domain.pojo.PiprojectitemPojo;
import inks.system.mapper.PiprojectitemMapper;
import inks.system.service.PiprojectitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;
/**
 * 项目服务(Piprojectitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-20 12:52:14
 */
@Service("piprojectitemService")
public class PiprojectitemServiceImpl implements PiprojectitemService {
    @Resource
    private PiprojectitemMapper piprojectitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PiprojectitemPojo getEntity(String key) {
        return this.piprojectitemMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PiprojectitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PiprojectitemPojo> lst = piprojectitemMapper.getPageList(queryParam);
            PageInfo<PiprojectitemPojo> pageInfo = new PageInfo<PiprojectitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<PiprojectitemPojo> getList(String Pid) {
        try {
            List<PiprojectitemPojo> lst = piprojectitemMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param piprojectitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiprojectitemPojo insert(PiprojectitemPojo piprojectitemPojo) {
        //初始化item的NULL
        PiprojectitemPojo itempojo =this.clearNull(piprojectitemPojo);
        PiprojectitemEntity piprojectitemEntity = new PiprojectitemEntity(); 
        BeanUtils.copyProperties(itempojo,piprojectitemEntity);
        
          piprojectitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          piprojectitemEntity.setRevision(1);  //乐观锁      
          this.piprojectitemMapper.insert(piprojectitemEntity);
        return this.getEntity(piprojectitemEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param piprojectitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiprojectitemPojo update(PiprojectitemPojo piprojectitemPojo) {
        PiprojectitemEntity piprojectitemEntity = new PiprojectitemEntity(); 
        BeanUtils.copyProperties(piprojectitemPojo,piprojectitemEntity);
        this.piprojectitemMapper.update(piprojectitemEntity);
        return this.getEntity(piprojectitemEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.piprojectitemMapper.delete(key) ;
    }
    
     /**
     * 修改数据
     *
     * @param piprojectitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public PiprojectitemPojo clearNull(PiprojectitemPojo piprojectitemPojo){
     //初始化NULL字段
     if(piprojectitemPojo.getPid()==null) piprojectitemPojo.setPid("");
     if(piprojectitemPojo.getFunctionid()==null) piprojectitemPojo.setFunctionid("");
     if(piprojectitemPojo.getFunctioncode()==null) piprojectitemPojo.setFunctioncode("");
     if(piprojectitemPojo.getFunctionname()==null) piprojectitemPojo.setFunctionname("");
     if(piprojectitemPojo.getRownum()==null) piprojectitemPojo.setRownum(0);
     if(piprojectitemPojo.getRemark()==null) piprojectitemPojo.setRemark("");
     if(piprojectitemPojo.getRevision()==null) piprojectitemPojo.setRevision(0);
     return piprojectitemPojo;
     }
}
