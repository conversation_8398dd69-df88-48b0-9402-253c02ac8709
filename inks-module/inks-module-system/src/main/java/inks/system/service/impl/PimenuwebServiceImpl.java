package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PimenuwebEntity;
import inks.system.domain.pojo.PimenuwebPojo;
import inks.system.mapper.PimenuwebMapper;
import inks.system.service.PimenuwebService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 后台导航(Pimenuweb)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-08 14:26:00
 */
@Service("pimenuwebService")
public class PimenuwebServiceImpl implements PimenuwebService {
    @Resource
    private PimenuwebMapper pimenuwebMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PimenuwebPojo getEntity(String key) {
        return this.pimenuwebMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PimenuwebPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PimenuwebPojo> lst = pimenuwebMapper.getPageList(queryParam);
            PageInfo<PimenuwebPojo> pageInfo = new PageInfo<PimenuwebPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param pimenuwebPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PimenuwebPojo insert(PimenuwebPojo pimenuwebPojo) {
        //初始化NULL字段
        if (pimenuwebPojo.getNavpid() == null) pimenuwebPojo.setNavpid("");
        if (pimenuwebPojo.getNavtype() == null) pimenuwebPojo.setNavtype("");
        if (pimenuwebPojo.getNavcode() == null) pimenuwebPojo.setNavcode("");
        if (pimenuwebPojo.getNavname() == null) pimenuwebPojo.setNavname("");
        if (pimenuwebPojo.getNavgroup() == null) pimenuwebPojo.setNavgroup("");
        if (pimenuwebPojo.getRownum() == null) pimenuwebPojo.setRownum(0);
        if (pimenuwebPojo.getImagecss() == null) pimenuwebPojo.setImagecss("");
        if (pimenuwebPojo.getIconurl() == null) pimenuwebPojo.setIconurl("");
        if (pimenuwebPojo.getNavigateurl() == null) pimenuwebPojo.setNavigateurl("");
        if (pimenuwebPojo.getMvcurl() == null) pimenuwebPojo.setMvcurl("");
        if (pimenuwebPojo.getRoutecomp() == null) pimenuwebPojo.setRoutecomp("");
        if (pimenuwebPojo.getRoutename() == null) pimenuwebPojo.setRoutename("");
        if (pimenuwebPojo.getRoutepath() == null) pimenuwebPojo.setRoutepath("");
        if (pimenuwebPojo.getModuletype() == null) pimenuwebPojo.setModuletype("");
        if (pimenuwebPojo.getModulecode() == null) pimenuwebPojo.setModulecode("");
        if (pimenuwebPojo.getRolecode() == null) pimenuwebPojo.setRolecode("");
        if (pimenuwebPojo.getImageindex() == null) pimenuwebPojo.setImageindex("");
        if (pimenuwebPojo.getImagestyle() == null) pimenuwebPojo.setImagestyle("");
        if (pimenuwebPojo.getEnabledmark() == null) pimenuwebPojo.setEnabledmark(0);
        if (pimenuwebPojo.getHiddenmark() == null) pimenuwebPojo.setHiddenmark(0);
        if (pimenuwebPojo.getRemark() == null) pimenuwebPojo.setRemark("");
        if (pimenuwebPojo.getPermissioncode() == null) pimenuwebPojo.setPermissioncode("");
        if (pimenuwebPojo.getFunctionid() == null) pimenuwebPojo.setFunctionid("");
        if (pimenuwebPojo.getFunctioncode() == null) pimenuwebPojo.setFunctioncode("");
        if (pimenuwebPojo.getFunctionname() == null) pimenuwebPojo.setFunctionname("");
        if(pimenuwebPojo.getIsmicroapp()==null) pimenuwebPojo.setIsmicroapp(0);
        if(pimenuwebPojo.getMicroappname()==null) pimenuwebPojo.setMicroappname("");
        if(pimenuwebPojo.getMicroappentry()==null) pimenuwebPojo.setMicroappentry("");
        if(pimenuwebPojo.getMicroapprule()==null) pimenuwebPojo.setMicroapprule("");
        if(pimenuwebPojo.getMicroapplocal()==null) pimenuwebPojo.setMicroapplocal("");
        if(pimenuwebPojo.getLister()==null) pimenuwebPojo.setLister("");
        if (pimenuwebPojo.getCreatedate() == null) pimenuwebPojo.setCreatedate(new Date());
        if (pimenuwebPojo.getModifydate() == null) pimenuwebPojo.setModifydate(new Date());
        if (pimenuwebPojo.getDeletemark() == null) pimenuwebPojo.setDeletemark(0);
        if (pimenuwebPojo.getDeletelister() == null) pimenuwebPojo.setDeletelister("");
        if (pimenuwebPojo.getDeletedate() == null) pimenuwebPojo.setDeletedate(new Date());
        PimenuwebEntity pimenuwebEntity = new PimenuwebEntity();
        BeanUtils.copyProperties(pimenuwebPojo, pimenuwebEntity);

        pimenuwebEntity.setNavid(inksSnowflake.getSnowflake().nextIdStr());
        this.pimenuwebMapper.insert(pimenuwebEntity);
        return this.getEntity(pimenuwebEntity.getNavid());

    }

    /**
     * 修改数据
     *
     * @param pimenuwebPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PimenuwebPojo update(PimenuwebPojo pimenuwebPojo) {
        PimenuwebEntity pimenuwebEntity = new PimenuwebEntity();
        BeanUtils.copyProperties(pimenuwebPojo, pimenuwebEntity);
        this.pimenuwebMapper.update(pimenuwebEntity);
        return this.getEntity(pimenuwebEntity.getNavid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pimenuwebMapper.delete(key);
    }


    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public List<PimenuwebPojo> getListByPid(String key) {
        return this.pimenuwebMapper.getListByPid(key);
    }

    @Override
    public List<PimenuwebPojo> getListByNavids(List<String> navids) {
        return this.pimenuwebMapper.getListByNavids(navids);
    }
}
