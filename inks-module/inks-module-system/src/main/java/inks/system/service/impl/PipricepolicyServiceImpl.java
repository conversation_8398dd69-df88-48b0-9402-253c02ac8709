package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PipricepolicyEntity;
import inks.system.domain.PipricepolicyitemEntity;
import inks.system.domain.pojo.PipricepolicyPojo;
import inks.system.domain.pojo.PipricepolicyitemPojo;
import inks.system.domain.pojo.PipricepolicyitemdetailPojo;
import inks.system.mapper.PipricepolicyMapper;
import inks.system.mapper.PipricepolicyitemMapper;
import inks.system.service.PipricepolicyService;
import inks.system.service.PipricepolicyitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 功能价格(Pipricepolicy)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-09 14:04:44
 */
@Service("pipricepolicyService")
public class PipricepolicyServiceImpl implements PipricepolicyService {
    @Resource
    private PipricepolicyMapper pipricepolicyMapper;

    @Resource
    private PipricepolicyitemMapper pipricepolicyitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private PipricepolicyitemService pipricepolicyitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PipricepolicyPojo getEntity(String key) {
        return this.pipricepolicyMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PipricepolicyitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PipricepolicyitemdetailPojo> lst = pipricepolicyMapper.getPageList(queryParam);
            PageInfo<PipricepolicyitemdetailPojo> pageInfo = new PageInfo<PipricepolicyitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PipricepolicyPojo getBillEntity(String key) {
        try {
            //读取主表
            PipricepolicyPojo pipricepolicyPojo = this.pipricepolicyMapper.getEntity(key);
            //读取子表
            pipricepolicyPojo.setItem(pipricepolicyitemMapper.getList(pipricepolicyPojo.getId()));
            return pipricepolicyPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PipricepolicyPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PipricepolicyPojo> lst = pipricepolicyMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(pipricepolicyitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<PipricepolicyPojo> pageInfo = new PageInfo<PipricepolicyPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PipricepolicyPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PipricepolicyPojo> lst = pipricepolicyMapper.getPageTh(queryParam);
            PageInfo<PipricepolicyPojo> pageInfo = new PageInfo<PipricepolicyPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param pipricepolicyPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public PipricepolicyPojo insert(PipricepolicyPojo pipricepolicyPojo) {
//初始化NULL字段
        if (pipricepolicyPojo.getFunctionid() == null) pipricepolicyPojo.setFunctionid("");
        if (pipricepolicyPojo.getReleasedomain() == null) pipricepolicyPojo.setReleasedomain(0);
        if (pipricepolicyPojo.getObjectlevel() == null) pipricepolicyPojo.setObjectlevel("");
        if (pipricepolicyPojo.getExpirationdate() == null) pipricepolicyPojo.setExpirationdate(new Date());
        if (pipricepolicyPojo.getEnabledmark() == null) pipricepolicyPojo.setEnabledmark(0);
        if (pipricepolicyPojo.getRownum() == null) pipricepolicyPojo.setRownum(0);
        if (pipricepolicyPojo.getLevelnum() == null) pipricepolicyPojo.setLevelnum(0);
        if (pipricepolicyPojo.getDeletemark() == null) pipricepolicyPojo.setDeletemark(0);
        if (pipricepolicyPojo.getDeletelister() == null) pipricepolicyPojo.setDeletelister("");
        if (pipricepolicyPojo.getDeletedate() == null) pipricepolicyPojo.setDeletedate(new Date());
        if (pipricepolicyPojo.getSummary() == null) pipricepolicyPojo.setSummary("");
        if (pipricepolicyPojo.getCreateby() == null) pipricepolicyPojo.setCreateby("");
        if (pipricepolicyPojo.getCreatedate() == null) pipricepolicyPojo.setCreatedate(new Date());
        if (pipricepolicyPojo.getLister() == null) pipricepolicyPojo.setLister("");
        if (pipricepolicyPojo.getModifydate() == null) pipricepolicyPojo.setModifydate(new Date());
        if (pipricepolicyPojo.getAssessor() == null) pipricepolicyPojo.setAssessor("");
        if (pipricepolicyPojo.getAssessdate() == null) pipricepolicyPojo.setAssessdate(new Date());
        if (pipricepolicyPojo.getRevision() == null) pipricepolicyPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        PipricepolicyEntity pipricepolicyEntity = new PipricepolicyEntity();
        BeanUtils.copyProperties(pipricepolicyPojo, pipricepolicyEntity);
        //设置id和新建日期
        pipricepolicyEntity.setId(id);
        pipricepolicyEntity.setRevision(1);  //乐观锁
        //插入主表
        this.pipricepolicyMapper.insert(pipricepolicyEntity);
        //Item子表处理
        List<PipricepolicyitemPojo> lst = pipricepolicyPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                PipricepolicyitemPojo itemPojo = this.pipricepolicyitemService.clearNull(lst.get(i));
                PipricepolicyitemEntity pipricepolicyitemEntity = new PipricepolicyitemEntity();
                BeanUtils.copyProperties(itemPojo, pipricepolicyitemEntity);
                //设置id和Pid
                pipricepolicyitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                pipricepolicyitemEntity.setPid(id);
                pipricepolicyitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.pipricepolicyitemMapper.insert(pipricepolicyitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(pipricepolicyEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param pipricepolicyPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public PipricepolicyPojo update(PipricepolicyPojo pipricepolicyPojo) {
        //主表更改
        PipricepolicyEntity pipricepolicyEntity = new PipricepolicyEntity();
        BeanUtils.copyProperties(pipricepolicyPojo, pipricepolicyEntity);
        this.pipricepolicyMapper.update(pipricepolicyEntity);
        //Item子表处理
        List<PipricepolicyitemPojo> lst = pipricepolicyPojo.getItem();
        //获取被删除的Item
        List<String> lstDelIds = pipricepolicyMapper.getDelItemIds(pipricepolicyPojo);
        if (lstDelIds != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstDelIds.size(); i++) {
                this.pipricepolicyitemMapper.delete(lstDelIds.get(i));
            }
        }
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                PipricepolicyitemEntity pipricepolicyitemEntity = new PipricepolicyitemEntity();
                if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                    //初始化item的NULL
                    PipricepolicyitemPojo itemPojo = this.pipricepolicyitemService.clearNull(lst.get(i));
                    BeanUtils.copyProperties(itemPojo, pipricepolicyitemEntity);
                    //设置id和Pid
                    pipricepolicyitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    pipricepolicyitemEntity.setPid(pipricepolicyEntity.getId());  // 主表 id
                    pipricepolicyitemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.pipricepolicyitemMapper.insert(pipricepolicyitemEntity);
                } else {
                    BeanUtils.copyProperties(lst.get(i), pipricepolicyitemEntity);
                    this.pipricepolicyitemMapper.update(pipricepolicyitemEntity);
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(pipricepolicyEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key) {
        PipricepolicyPojo pipricepolicyPojo = this.getBillEntity(key);
        //Item子表处理
        List<PipricepolicyitemPojo> lst = pipricepolicyPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.pipricepolicyitemMapper.delete(lst.get(i).getId());
            }
        }
        return this.pipricepolicyMapper.delete(key);
    }

    /**
     * 审核数据
     *
     * @param pipricepolicyPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public PipricepolicyPojo approval(PipricepolicyPojo pipricepolicyPojo) {
        //主表更改
        PipricepolicyEntity pipricepolicyEntity = new PipricepolicyEntity();
        BeanUtils.copyProperties(pipricepolicyPojo,pipricepolicyEntity);
        this.pipricepolicyMapper.approval(pipricepolicyEntity);
        //返回Bill实例
        return this.getBillEntity(pipricepolicyEntity.getId());
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PipricepolicyPojo getBillEntityByFunction(String key) {
        try {
            //读取主表
            PipricepolicyPojo pipricepolicyPojo = this.pipricepolicyMapper.getEntityByFunction(key);
            //读取子表
            pipricepolicyPojo.setItem(pipricepolicyitemMapper.getList(pipricepolicyPojo.getId()));
            return pipricepolicyPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

}
