package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiuseronlinePojo;
import inks.system.domain.PiuseronlineEntity;

import com.github.pagehelper.PageInfo;

/**
 * 用户在线(Piuseronline)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-13 10:37:29
 */
public interface PiuseronlineService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiuseronlinePojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PiuseronlinePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param piuseronlinePojo 实例对象
     * @return 实例对象
     */
    PiuseronlinePojo insert(PiuseronlinePojo piuseronlinePojo);

    /**
     * 修改数据
     *
     * @param piuseronlinepojo 实例对象
     * @return 实例对象
     */
    PiuseronlinePojo update(PiuseronlinePojo piuseronlinepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);
}
