package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CibilldraftEntity;
import inks.system.domain.pojo.CibilldraftPojo;
import inks.system.mapper.CibilldraftMapper;
import inks.system.service.CibilldraftService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 单据草稿(Cibilldraft)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-26 10:17:54
 */
@Service("cibilldraftService")
public class CibilldraftServiceImpl implements CibilldraftService {
    @Resource
    private CibilldraftMapper cibilldraftMapper;

    @Override
    public CibilldraftPojo getEntity(String key, String tid) {
        return this.cibilldraftMapper.getEntity(key, tid);
    }


    @Override
    public PageInfo<CibilldraftPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CibilldraftPojo> lst = cibilldraftMapper.getPageList(queryParam);
            PageInfo<CibilldraftPojo> pageInfo = new PageInfo<CibilldraftPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public CibilldraftPojo insert(CibilldraftPojo cibilldraftPojo) {
        //初始化NULL字段
        cleanNull(cibilldraftPojo);
        CibilldraftEntity cibilldraftEntity = new CibilldraftEntity();
        BeanUtils.copyProperties(cibilldraftPojo, cibilldraftEntity);
        //生成雪花id
        cibilldraftEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        cibilldraftEntity.setRevision(1);  //乐观锁
        this.cibilldraftMapper.insert(cibilldraftEntity);
        return this.getEntity(cibilldraftEntity.getId(), cibilldraftEntity.getTenantid());
    }


    @Override
    public CibilldraftPojo update(CibilldraftPojo cibilldraftPojo) {
        CibilldraftEntity cibilldraftEntity = new CibilldraftEntity();
        BeanUtils.copyProperties(cibilldraftPojo, cibilldraftEntity);
        this.cibilldraftMapper.update(cibilldraftEntity);
        return this.getEntity(cibilldraftEntity.getId(), cibilldraftEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.cibilldraftMapper.delete(key, tid);
    }


    private static void cleanNull(CibilldraftPojo cibilldraftPojo) {
        if (cibilldraftPojo.getModulecode() == null) cibilldraftPojo.setModulecode("");
        if (cibilldraftPojo.getDrafttitle() == null) cibilldraftPojo.setDrafttitle("");
        if (cibilldraftPojo.getBilldata() == null) cibilldraftPojo.setBilldata("");
        if (cibilldraftPojo.getRownum() == null) cibilldraftPojo.setRownum(0);
        if (cibilldraftPojo.getRemark() == null) cibilldraftPojo.setRemark("");
        if (cibilldraftPojo.getCreateby() == null) cibilldraftPojo.setCreateby("");
        if (cibilldraftPojo.getCreatebyid() == null) cibilldraftPojo.setCreatebyid("");
        if (cibilldraftPojo.getCreatedate() == null) cibilldraftPojo.setCreatedate(new Date());
        if (cibilldraftPojo.getLister() == null) cibilldraftPojo.setLister("");
        if (cibilldraftPojo.getListerid() == null) cibilldraftPojo.setListerid("");
        if (cibilldraftPojo.getModifydate() == null) cibilldraftPojo.setModifydate(new Date());
        if (cibilldraftPojo.getCustom1() == null) cibilldraftPojo.setCustom1("");
        if (cibilldraftPojo.getCustom2() == null) cibilldraftPojo.setCustom2("");
        if (cibilldraftPojo.getCustom3() == null) cibilldraftPojo.setCustom3("");
        if (cibilldraftPojo.getCustom4() == null) cibilldraftPojo.setCustom4("");
        if (cibilldraftPojo.getCustom5() == null) cibilldraftPojo.setCustom5("");
        if (cibilldraftPojo.getTenantid() == null) cibilldraftPojo.setTenantid("");
        if (cibilldraftPojo.getTenantname() == null) cibilldraftPojo.setTenantname("");
        if (cibilldraftPojo.getRevision() == null) cibilldraftPojo.setRevision(0);
    }

    @Override
    public CibilldraftPojo getEntityByModuleCodeAndListerId(String modulecode, String userid, String tid) {
        return this.cibilldraftMapper.getEntityByModuleCodeAndListerId(modulecode, userid, tid);
    }

    @Override
    public Integer getDraftCountByListerId(String userid, String tid) {
        return this.cibilldraftMapper.getDraftCountByListerId(userid, tid);
    }

    @Override
    public List<CibilldraftPojo> getDraftList(String userid, String code, String tid) {
        return this.cibilldraftMapper.getDraftList(userid, code, tid);
    }
}
