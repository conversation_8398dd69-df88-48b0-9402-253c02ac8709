package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiadminloginPojo;
import inks.system.domain.PiadminloginEntity;

import com.github.pagehelper.PageInfo;

/**
 * 管理员登录(Piadminlogin)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-08 13:55:42
 */
public interface PiadminloginService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiadminloginPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PiadminloginPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param piadminloginPojo 实例对象
     * @return 实例对象
     */
    PiadminloginPojo insert(PiadminloginPojo piadminloginPojo);

    /**
     * 修改数据
     *
     * @param piadminloginpojo 实例对象
     * @return 实例对象
     */
    PiadminloginPojo update(PiadminloginPojo piadminloginpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

}
