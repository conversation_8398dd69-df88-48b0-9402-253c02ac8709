package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CidgformatitemPojo;
import inks.system.domain.CidgformatitemEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 列表项目(Cidgformatitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-04-21 15:17:14
 */
public interface CidgformatitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CidgformatitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CidgformatitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<CidgformatitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param cidgformatitemPojo 实例对象
     * @return 实例对象
     */
    CidgformatitemPojo insert(CidgformatitemPojo cidgformatitemPojo);

    /**
     * 修改数据
     *
     * @param cidgformatitempojo 实例对象
     * @return 实例对象
     */
    CidgformatitemPojo update(CidgformatitemPojo cidgformatitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param cidgformatitempojo 实例对象
     * @return 实例对象
     */
    CidgformatitemPojo clearNull(CidgformatitemPojo cidgformatitempojo);
}
