package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.pojo.PiprojectPojo;
import inks.system.domain.pojo.PiprojectitemPojo;
import inks.system.domain.pojo.PiprojectitemdetailPojo;
import inks.system.domain.PiprojectEntity;
import inks.system.domain.PiprojectitemEntity;
import inks.system.mapper.PiprojectMapper;
import inks.system.service.PiprojectService;
import inks.system.service.PiprojectitemService;
import inks.system.mapper.PiprojectitemMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 项目(Piproject)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-20 12:51:53
 */
@Service("piprojectService")
public class PiprojectServiceImpl implements PiprojectService {
    @Resource
    private PiprojectMapper piprojectMapper;

    @Resource
    private PiprojectitemMapper piprojectitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private PiprojectitemService piprojectitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PiprojectPojo getEntity(String key) {
        return this.piprojectMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PiprojectitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PiprojectitemdetailPojo> lst = piprojectMapper.getPageList(queryParam);
            PageInfo<PiprojectitemdetailPojo> pageInfo = new PageInfo<PiprojectitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PiprojectPojo getBillEntity(String key) {
        try {
            //读取主表
            PiprojectPojo piprojectPojo = this.piprojectMapper.getEntity(key);
            //读取子表
            piprojectPojo.setItem(piprojectitemMapper.getList(piprojectPojo.getId()));
            return piprojectPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PiprojectPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PiprojectPojo> lst = piprojectMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(piprojectitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<PiprojectPojo> pageInfo = new PageInfo<PiprojectPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PiprojectPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PiprojectPojo> lst = piprojectMapper.getPageTh(queryParam);
            PageInfo<PiprojectPojo> pageInfo = new PageInfo<PiprojectPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param piprojectPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public PiprojectPojo insert(PiprojectPojo piprojectPojo) {
//初始化NULL字段
        if (piprojectPojo.getProjectcode() == null) piprojectPojo.setProjectcode("");
        if (piprojectPojo.getProjectname() == null) piprojectPojo.setProjectname("");
        if (piprojectPojo.getDescription() == null) piprojectPojo.setDescription("");
        if (piprojectPojo.getFrontphoto() == null) piprojectPojo.setFrontphoto("");
        if (piprojectPojo.getEnabledmark() == null) piprojectPojo.setEnabledmark(0);
        if (piprojectPojo.getAllowdelete() == null) piprojectPojo.setAllowdelete(0);
        if (piprojectPojo.getRownum() == null) piprojectPojo.setRownum(0);
        if (piprojectPojo.getSummary() == null) piprojectPojo.setSummary("");
        if (piprojectPojo.getCreateby() == null) piprojectPojo.setCreateby("");
        if (piprojectPojo.getCreatebyid() == null) piprojectPojo.setCreatebyid("");
        if (piprojectPojo.getCreatedate() == null) piprojectPojo.setCreatedate(new Date());
        if (piprojectPojo.getLister() == null) piprojectPojo.setLister("");
        if (piprojectPojo.getListerid() == null) piprojectPojo.setListerid("");
        if (piprojectPojo.getModifydate() == null) piprojectPojo.setModifydate(new Date());
        if (piprojectPojo.getRevision() == null) piprojectPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        PiprojectEntity piprojectEntity = new PiprojectEntity();
        BeanUtils.copyProperties(piprojectPojo, piprojectEntity);
        //设置id和新建日期
        piprojectEntity.setId(id);
        piprojectEntity.setRevision(1);  //乐观锁
        //插入主表
        this.piprojectMapper.insert(piprojectEntity);
        //Item子表处理
        List<PiprojectitemPojo> lst = piprojectPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                PiprojectitemPojo itemPojo = this.piprojectitemService.clearNull(lst.get(i));
                PiprojectitemEntity piprojectitemEntity = new PiprojectitemEntity();
                BeanUtils.copyProperties(itemPojo, piprojectitemEntity);
                //设置id和Pid
                piprojectitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                piprojectitemEntity.setPid(id);
                piprojectitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.piprojectitemMapper.insert(piprojectitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(piprojectEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param piprojectPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public PiprojectPojo update(PiprojectPojo piprojectPojo) {
        //主表更改
        PiprojectEntity piprojectEntity = new PiprojectEntity();
        BeanUtils.copyProperties(piprojectPojo, piprojectEntity);
        this.piprojectMapper.update(piprojectEntity);
        //Item子表处理
        List<PiprojectitemPojo> lst = piprojectPojo.getItem();
        //获取被删除的Item
        List<String> lstDelIds = piprojectMapper.getDelItemIds(piprojectPojo);
        if (lstDelIds != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstDelIds.size(); i++) {
                this.piprojectitemMapper.delete(lstDelIds.get(i));
            }
        }
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                PiprojectitemEntity piprojectitemEntity = new PiprojectitemEntity();
                if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                    //初始化item的NULL
                    PiprojectitemPojo itemPojo = this.piprojectitemService.clearNull(lst.get(i));
                    BeanUtils.copyProperties(itemPojo, piprojectitemEntity);
                    //设置id和Pid
                    piprojectitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    piprojectitemEntity.setPid(piprojectEntity.getId());  // 主表 id
                    piprojectitemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.piprojectitemMapper.insert(piprojectitemEntity);
                } else {
                    BeanUtils.copyProperties(lst.get(i), piprojectitemEntity);
                    this.piprojectitemMapper.update(piprojectitemEntity);
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(piprojectEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key) {
        PiprojectPojo piprojectPojo = this.getBillEntity(key);
        //Item子表处理
        List<PiprojectitemPojo> lst = piprojectPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.piprojectitemMapper.delete(lst.get(i).getId());
            }
        }
        return this.piprojectMapper.delete(key);
    }


}
