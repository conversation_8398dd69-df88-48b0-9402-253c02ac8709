package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.system.domain.CifnorderEntity;
import inks.system.domain.PisubscriberEntity;
import inks.system.domain.pojo.*;
import inks.system.mapper.CifnorderMapper;
import inks.system.mapper.PisubscriberMapper;
import inks.system.service.CiconfigService;
import inks.system.service.PisubscriberService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * 订阅信息表(Pisubscriber)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-10 14:17:26
 */
@Service("pisubscriberService")
public class PisubscriberServiceImpl implements PisubscriberService {
    @Resource
    private PisubscriberMapper pisubscriberMapper;


    /**
     * 服务对象
     */
    @Resource
    private CifnorderMapper cifnorderMapper;

    /**
     * 服务对象
     */
    @Resource
    private CiconfigService ciconfigService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PisubscriberPojo getEntity(String key) {
        return this.pisubscriberMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PisubscriberPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PisubscriberPojo> lst = pisubscriberMapper.getPageList(queryParam);
            PageInfo<PisubscriberPojo> pageInfo = new PageInfo<PisubscriberPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param pisubscriberPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PisubscriberPojo insert(PisubscriberPojo pisubscriberPojo) {
        //初始化NULL字段
        if (pisubscriberPojo.getTenantid() == null) pisubscriberPojo.setTenantid("");
        if (pisubscriberPojo.getFunctionid() == null) pisubscriberPojo.setFunctionid("");
        if (pisubscriberPojo.getQuantity() == null) pisubscriberPojo.setQuantity(0D);
        if (pisubscriberPojo.getStartdate() == null) pisubscriberPojo.setStartdate(new Date());
        if (pisubscriberPojo.getEnddate() == null) pisubscriberPojo.setEnddate(new Date());
        if (pisubscriberPojo.getLister() == null) pisubscriberPojo.setLister("");
        if (pisubscriberPojo.getCreatedate() == null) pisubscriberPojo.setCreatedate(new Date());
        if (pisubscriberPojo.getModifydate() == null) pisubscriberPojo.setModifydate(new Date());
        if (pisubscriberPojo.getTenantcode() == null) pisubscriberPojo.setTenantcode("");
        if (pisubscriberPojo.getTenantname() == null) pisubscriberPojo.setTenantname("");
        if (pisubscriberPojo.getCompany() == null) pisubscriberPojo.setCompany("");
        if (pisubscriberPojo.getFunctioncode() == null) pisubscriberPojo.setFunctioncode("");
        if (pisubscriberPojo.getFunctionname() == null) pisubscriberPojo.setFunctionname("");
        if (pisubscriberPojo.getFrontphoto() == null) pisubscriberPojo.setFrontphoto("");
        if (pisubscriberPojo.getCyclecode() == null) pisubscriberPojo.setCyclecode("");
        if (pisubscriberPojo.getContainer() == null) pisubscriberPojo.setContainer(0);
        if (pisubscriberPojo.getTaxprice() == null) pisubscriberPojo.setTaxprice(0D);
        if (pisubscriberPojo.getTaxamount() == null) pisubscriberPojo.setTaxamount(0D);
        if (pisubscriberPojo.getOrderno() == null) pisubscriberPojo.setOrderno("");
        if (pisubscriberPojo.getOrderitemid() == null) pisubscriberPojo.setOrderitemid("");
        if (pisubscriberPojo.getEnabledmark() == null) pisubscriberPojo.setEnabledmark(0);
        PisubscriberEntity pisubscriberEntity = new PisubscriberEntity();
        BeanUtils.copyProperties(pisubscriberPojo, pisubscriberEntity);

        pisubscriberEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.pisubscriberMapper.insert(pisubscriberEntity);
        return this.getEntity(pisubscriberEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param pisubscriberPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PisubscriberPojo update(PisubscriberPojo pisubscriberPojo) {
        PisubscriberEntity pisubscriberEntity = new PisubscriberEntity();
        BeanUtils.copyProperties(pisubscriberPojo, pisubscriberEntity);
        this.pisubscriberMapper.update(pisubscriberEntity);
        return this.getEntity(pisubscriberEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pisubscriberMapper.delete(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PisubscriberPojo> getPageListByTenant(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PisubscriberPojo> lst = pisubscriberMapper.getPageListByTenant(queryParam);
            PageInfo<PisubscriberPojo> pageInfo = new PageInfo<PisubscriberPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PisubscriberPojo getEntityByFunction(String key, String tid) {
        return this.pisubscriberMapper.getEntityByFunction(key, tid, DateUtils.getTime());
    }


    @Override
    public List<PimenuwebPojo> getMenuWebListByTenant(String key,String fncode) {
        if (fncode ==null){
            return this.pisubscriberMapper.getMenuWebListByTenant(key, DateUtils.getTime());
        }else
        {
            return this.pisubscriberMapper.getMenuWebListByTenFnCode(key, DateUtils.getTime(), fncode);
        }
    }



    @Override
    public List<PiweblnkPojo> getWebLnkListByTenant(String key, String fncode) {
        if (fncode ==null||fncode.equals("undefined")){
            return this.pisubscriberMapper.getWebLnkListByTenant(key, DateUtils.getTime());
        }else
        {
            return this.pisubscriberMapper.getWebLnkListByTenFnCode(key, DateUtils.getTime(), fncode);
        }
    }

    @Override
    public List<PimenufrmPojo> getMenuFrmListByTenant(String key, String fncode) {
        if (fncode ==null||fncode.equals("undefined")){
            return this.pisubscriberMapper.getMenuFrmListByTenant(key, DateUtils.getTime());
        }else
        {
            return this.pisubscriberMapper.getMenuFrmListByTenFnCode(key, DateUtils.getTime(), fncode);
        }
    }

    @Override
    public List<PiwebnavPojo> getWebNavListByTenant(String key, String fncode) {
        if (fncode ==null||fncode.equals("undefined")){
            return this.pisubscriberMapper.getWebNavListByTenant(key, DateUtils.getTime());
        }else
        {
            return this.pisubscriberMapper.getWebNavListByTenFnCode(key, DateUtils.getTime(), fncode);
        }
    }

    @Override
    public List<PimenuappPojo> getMenuAppListByTenant(String key) {
        return this.pisubscriberMapper.getMenuAppListByTenant(key, DateUtils.getTime());
    }

    @Override
    public List<PipermcodePojo> getPermAllByTenant(String key) {
        return this.pisubscriberMapper.getPermAllByTenant(key, DateUtils.getTime());
    }

    @Override
    public List<CiconfigPojo> getTenConfigByTenant(String key) {
        List<CiconfigPojo> lst =this.pisubscriberMapper.getDefConfigByTenant(key, DateUtils.getTime());
        List<CiconfigPojo> lstTen = this.ciconfigService.getListByTenant(key);
        if (lst!=null && !lst.isEmpty() && lstTen !=null && !lstTen.isEmpty()) {
            for (CiconfigPojo def :lst){
                for(CiconfigPojo ten : lstTen){
                    if (ten.getCfgkey().equals(def.getCfgkey())){
                        def.setId(ten.getId());
                        def.setCfgvalue(ten.getCfgvalue());
                        def.setTenantid(ten.getTenantid());
                        def.setTenantname(ten.getTenantname());
                        def.setCreatebyid(ten.getCreatebyid());
                        def.setCreateby(ten.getCreateby());
                        def.setCreatedate(ten.getCreatedate());
                        def.setRevision(ten.getRevision());
                       // BeanUtils.copyProperties(ten,def);
                    }
                }
            }
        }
//        System.out.println(lst.size());
        return lst;
    }

    @Override
    public List<PirolePojo> getDefRoleByTenant(String key) {
        return this.pisubscriberMapper.getDefRoleByTenant(key, DateUtils.getTime());
    }

    @Override
    public List<CibigdataPojo> getBigDataByTenant(String key) {
        return this.pisubscriberMapper.getBigDataByTenant(key, DateUtils.getTime());
    }

    @Override
    public List<CiwarningPojo> getWarnByTenant(String key) {
        return this.pisubscriberMapper.getWarnByTenant(key, DateUtils.getTime());
    }

    @Override
    public List<CidashboardPojo> getDashByTenant(String key) {
        return this.pisubscriberMapper.getDashByTenant(key, DateUtils.getTime());
    }

    @Override
    public List<CireportsPojo> getReportsByTenant(String key) {
        return this.pisubscriberMapper.getReportsByTenant(key, DateUtils.getTime());
    }

    @Override
    @Transactional
    public List<PisubscriberPojo> createByOrder(CifnorderPojo cifnorderPojo) {
        try {
            //更新订单状态
            CifnorderEntity cifnorderEntity = new CifnorderEntity();
            cifnorderEntity.setId(cifnorderPojo.getId());
            cifnorderEntity.setPayamount(cifnorderPojo.getBilltaxamount());
            cifnorderEntity.setPaybillcode(cifnorderPojo.getPaybillcode());
            cifnorderEntity.setStatecode("pay");
            cifnorderEntity.setStatedate(new Date());
            this.cifnorderMapper.update(cifnorderEntity);
            //处理订单明细，转订阅表
            List<PisubscriberPojo> lst = new ArrayList<>();
            if (cifnorderPojo.getItem() != null) {
                List<CifnorderitemPojo> item = cifnorderPojo.getItem();
                for (int i = 0; i < item.size(); i++) {
                    //新建订阅表对象
                    PisubscriberPojo pisubscriberPojo = new PisubscriberPojo();
                    pisubscriberPojo.setTenantid(cifnorderPojo.getTenantid());   // 租户信息
                    pisubscriberPojo.setTenantcode(cifnorderPojo.getTenantcode());
                    pisubscriberPojo.setTenantname(cifnorderPojo.getTenantname());
                    pisubscriberPojo.setCompany(cifnorderPojo.getCompany());
                    pisubscriberPojo.setFunctionid(item.get(i).getFunctionid());    // 服务信息
                    pisubscriberPojo.setFunctioncode(item.get(i).getFunctioncode());
                    pisubscriberPojo.setFunctionname(item.get(i).getFunctionname());
                    pisubscriberPojo.setFrontphoto("");
                    pisubscriberPojo.setCyclecode(item.get(i).getCyclecode());   // 订单信息
                    pisubscriberPojo.setQuantity(item.get(i).getQuantity());
                    pisubscriberPojo.setTaxprice(item.get(i).getTaxprice());
                    pisubscriberPojo.setTaxamount(item.get(i).getTaxamount());
                    Date stdate = new Date();
                    //查询为已有服务
                    PisubscriberPojo pisubscriberPojoNow = this.pisubscriberMapper.getEntityByFunction(item.get(i).getFunctionid(), cifnorderPojo.getTenantid(), DateUtils.getTime());
                    if (pisubscriberPojoNow != null) {
                        //开始时间==上一个结束时间
                        stdate = pisubscriberPojoNow.getEnddate();
                    }
                    pisubscriberPojo.setStartdate(stdate);
                    switch (item.get(i).getCyclecode()) {
                        case "W1":
                            pisubscriberPojo.setEnddate(DateUtils.addDays(stdate, 7));
                            break; //可选
                        case "M1":
                            pisubscriberPojo.setEnddate(DateUtils.addMonths(stdate, 1));
                            break; //可选
                        case "M3":
                            pisubscriberPojo.setEnddate(DateUtils.addMonths(stdate, 3));
                            break; //可选
                        case "M6":
                            pisubscriberPojo.setEnddate(DateUtils.addMonths(stdate, 6));
                            break; //可选
                        case "Y1":
                            pisubscriberPojo.setEnddate(DateUtils.addYears(stdate, 1));
                            break; //可选
                        case "Y2":
                            pisubscriberPojo.setEnddate(DateUtils.addYears(stdate, 2));
                            break; //可选
                        case "Y3":
                            pisubscriberPojo.setEnddate(DateUtils.addYears(stdate, 3));
                            break; //可选
                        case "Y5":
                            pisubscriberPojo.setEnddate(DateUtils.addYears(stdate, 5));
                            break; //可选
                        default: //可选
                            pisubscriberPojo.setEnddate(stdate);
                    }

                    pisubscriberPojo.setOrderno(cifnorderPojo.getRefno());
                    pisubscriberPojo.setOrderitemid(item.get(i).getId());
                    pisubscriberPojo.setLister(cifnorderPojo.getLister());   //用户名
                    pisubscriberPojo.setEnabledmark(1);
                    pisubscriberPojo.setModifydate(new Date());   //修改时间
                    lst.add(insert(pisubscriberPojo));
                }
            }
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

}
