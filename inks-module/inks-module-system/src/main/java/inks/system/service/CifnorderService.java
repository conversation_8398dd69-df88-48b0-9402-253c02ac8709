package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CifnorderPojo;
import inks.system.domain.pojo.CifnorderitemdetailPojo;

/**
 * 销售订单(Cifnorder)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-09 20:42:14
 */
public interface CifnorderService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CifnorderPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CifnorderitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CifnorderPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CifnorderPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CifnorderPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param cifnorderPojo 实例对象
     * @return 实例对象
     */
    CifnorderPojo insert(CifnorderPojo cifnorderPojo);

    /**
     * 修改数据
     *
     * @param cifnorderpojo 实例对象
     * @return 实例对象
     */
    CifnorderPojo update(CifnorderPojo cifnorderpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CifnorderPojo getEntityByRefno(String key);



}
