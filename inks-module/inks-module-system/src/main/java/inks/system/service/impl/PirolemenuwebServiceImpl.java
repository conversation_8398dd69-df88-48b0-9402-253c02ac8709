package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PirolemenuwebEntity;
import inks.system.domain.pojo.PirolemenuwebPojo;
import inks.system.mapper.PirolemenuwebMapper;
import inks.system.service.PirolemenuwebService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 角色菜单Web(Pirolemenuweb)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-11 13:55:54
 */
@Service("pirolemenuwebService")
public class PirolemenuwebServiceImpl implements PirolemenuwebService {
    @Resource
    private PirolemenuwebMapper pirolemenuwebMapper;

    @Override
    public PirolemenuwebPojo getEntity(String key, String tid) {
        return this.pirolemenuwebMapper.getEntity(key, tid);
    }


    @Override
    public PageInfo<PirolemenuwebPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PirolemenuwebPojo> lst = pirolemenuwebMapper.getPageList(queryParam);
            PageInfo<PirolemenuwebPojo> pageInfo = new PageInfo<PirolemenuwebPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PirolemenuwebPojo insert(PirolemenuwebPojo pirolemenuwebPojo) {
        //初始化NULL字段
        cleanNull(pirolemenuwebPojo);
        PirolemenuwebEntity pirolemenuwebEntity = new PirolemenuwebEntity();
        BeanUtils.copyProperties(pirolemenuwebPojo, pirolemenuwebEntity);
        //生成雪花id
        pirolemenuwebEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        pirolemenuwebEntity.setRevision(1);  //乐观锁
        this.pirolemenuwebMapper.insert(pirolemenuwebEntity);
        return this.getEntity(pirolemenuwebEntity.getId(), pirolemenuwebEntity.getTenantid());
    }


    @Override
    public PirolemenuwebPojo update(PirolemenuwebPojo pirolemenuwebPojo) {
        PirolemenuwebEntity pirolemenuwebEntity = new PirolemenuwebEntity();
        BeanUtils.copyProperties(pirolemenuwebPojo, pirolemenuwebEntity);
        this.pirolemenuwebMapper.update(pirolemenuwebEntity);
        return this.getEntity(pirolemenuwebEntity.getId(), pirolemenuwebEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.pirolemenuwebMapper.delete(key, tid);
    }

    @Override
    public int deleteByRoleidAndNavid(String roleid, String navid, String tenantid) {
        return this.pirolemenuwebMapper.deleteByRoleidAndNavid(roleid, navid, tenantid);
    }

    @Override
    public Integer batchCreateDelete(String roleid, List<String> deleteNavids, List<String> createNavids, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        String realname = loginUser.getRealname();
        String userid = loginUser.getUserid();
        int affectedRows = 0;
        if (CollectionUtils.isNotEmpty(deleteNavids)) {
            Integer i = this.pirolemenuwebMapper.batchDelete(roleid, deleteNavids, tid);
            affectedRows += i == null ? 0 : i;
        }

        if (CollectionUtils.isNotEmpty(createNavids)) {
            //insert into PiRoleMenuWeb(id, Roleid, Navid, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
            List<PirolemenuwebPojo> pirolemenuwebPojoList = new ArrayList<>();
            for (String navid : createNavids) {
                PirolemenuwebPojo pirolemenuwebPojo = new PirolemenuwebPojo();
                pirolemenuwebPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
                pirolemenuwebPojo.setRoleid(roleid);
                pirolemenuwebPojo.setNavid(navid);
                // pirolemenuwebPojo.setRownum(createNavids.indexOf(navid) + 1);
                pirolemenuwebPojo.setCreateby(realname); // 示例：设置创建者
                pirolemenuwebPojo.setCreatebyid(userid); // 示例：设置创建者ID
                pirolemenuwebPojo.setLister(realname); // 示例：设置列出者
                pirolemenuwebPojo.setListerid(userid); // 示例：设置列出者ID
                pirolemenuwebPojo.setTenantid(tid); // 示例：设置租户ID
                cleanNull(pirolemenuwebPojo);
                pirolemenuwebPojoList.add(pirolemenuwebPojo);
            }

            Integer i = this.pirolemenuwebMapper.batchInsert(pirolemenuwebPojoList);
            affectedRows += i == null ? 0 : i;
        }
        return affectedRows;
    }

    private static void cleanNull(PirolemenuwebPojo pirolemenuwebPojo) {
        if (pirolemenuwebPojo.getRoleid() == null) pirolemenuwebPojo.setRoleid("");
        if (pirolemenuwebPojo.getNavid() == null) pirolemenuwebPojo.setNavid("");
        if (pirolemenuwebPojo.getRownum() == null) pirolemenuwebPojo.setRownum(0);
        if (pirolemenuwebPojo.getCreateby() == null) pirolemenuwebPojo.setCreateby("");
        if (pirolemenuwebPojo.getCreatebyid() == null) pirolemenuwebPojo.setCreatebyid("");
        if (pirolemenuwebPojo.getCreatedate() == null) pirolemenuwebPojo.setCreatedate(new Date());
        if (pirolemenuwebPojo.getLister() == null) pirolemenuwebPojo.setLister("");
        if (pirolemenuwebPojo.getListerid() == null) pirolemenuwebPojo.setListerid("");
        if (pirolemenuwebPojo.getModifydate() == null) pirolemenuwebPojo.setModifydate(new Date());
        if (pirolemenuwebPojo.getTenantid() == null) pirolemenuwebPojo.setTenantid("");
        if (pirolemenuwebPojo.getTenantname() == null) pirolemenuwebPojo.setTenantname("");
        if (pirolemenuwebPojo.getRevision() == null) pirolemenuwebPojo.setRevision(0);
    }

    @Override
    public List<PirolemenuwebPojo> getListByRole(String key, String tenantid) {
        return this.pirolemenuwebMapper.getListByRole(key, tenantid);
    }
}
