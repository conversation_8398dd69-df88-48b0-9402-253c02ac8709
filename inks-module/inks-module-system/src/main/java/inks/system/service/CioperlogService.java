package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CioperlogPojo;
import inks.system.domain.CioperlogEntity;

import com.github.pagehelper.PageInfo;

/**
 * 操作日志(Cioperlog)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-22 13:38:48
 */
public interface CioperlogService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CioperlogPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CioperlogPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param cioperlogPojo 实例对象
     * @return 实例对象
     */
    CioperlogPojo insert(CioperlogPojo cioperlogPojo);

    /**
     * 修改数据
     *
     * @param cioperlogpojo 实例对象
     * @return 实例对象
     */
    CioperlogPojo update(CioperlogPojo cioperlogpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

    int deleteByTime(QueryParam queryParam);
}
