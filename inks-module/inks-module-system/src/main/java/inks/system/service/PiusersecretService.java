package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiusersecretPojo;

/**
 * 用户授权key(Piusersecret)表服务接口
 *
 * <AUTHOR>
 * @since 2022-12-28 09:52:24
 */
public interface PiusersecretService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiusersecretPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PiusersecretPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param piusersecretPojo 实例对象
     * @return 实例对象
     */
    PiusersecretPojo insert(PiusersecretPojo piusersecretPojo);

    /**
     * 修改数据
     *
     * @param piusersecretpojo 实例对象
     * @return 实例对象
     */
    PiusersecretPojo update(PiusersecretPojo piusersecretpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiusersecretPojo getEntityByUserid(String key, String tid);
}
