package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PimenuappPojo;
import inks.system.domain.pojo.PimenuwebPojo;
import inks.system.domain.pojo.PiscmfunctmenuappPojo;
import inks.system.domain.PiscmfunctmenuappEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * SCMAPP关系(Piscmfunctmenuapp)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
public interface PiscmfunctmenuappService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiscmfunctmenuappPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PiscmfunctmenuappPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param piscmfunctmenuappPojo 实例对象
     * @return 实例对象
     */
    PiscmfunctmenuappPojo insert(PiscmfunctmenuappPojo piscmfunctmenuappPojo);

    /**
     * 修改数据
     *
     * @param piscmfunctmenuapppojo 实例对象
     * @return 实例对象
     */
    PiscmfunctmenuappPojo update(PiscmfunctmenuappPojo piscmfunctmenuapppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<PiscmfunctmenuappPojo> getListByFunction(String key);

    List<PimenuappPojo> getListByScmFunctids(String scmfunctids);
}
