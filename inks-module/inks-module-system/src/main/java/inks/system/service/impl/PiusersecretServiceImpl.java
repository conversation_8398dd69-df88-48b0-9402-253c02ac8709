package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PiusersecretEntity;
import inks.system.domain.pojo.PiusersecretPojo;
import inks.system.mapper.PiusersecretMapper;
import inks.system.service.PiusersecretService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 用户授权key(Piusersecret)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-28 09:52:24
 */
@Service("piusersecretService")
public class PiusersecretServiceImpl implements PiusersecretService {
    @Resource
    private PiusersecretMapper piusersecretMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PiusersecretPojo getEntity(String key, String tid) {
        return this.piusersecretMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PiusersecretPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PiusersecretPojo> lst = piusersecretMapper.getPageList(queryParam);
            PageInfo<PiusersecretPojo> pageInfo = new PageInfo<PiusersecretPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param piusersecretPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiusersecretPojo insert(PiusersecretPojo piusersecretPojo) {
    //初始化NULL字段
     if(piusersecretPojo.getSecretcode()==null) piusersecretPojo.setSecretcode("");
     if(piusersecretPojo.getUserid()==null) piusersecretPojo.setUserid("");
     if(piusersecretPojo.getCheckipaddr()==null) piusersecretPojo.setCheckipaddr(0);
     if(piusersecretPojo.getIpaddress()==null) piusersecretPojo.setIpaddress("");
     if(piusersecretPojo.getMacaddress()==null) piusersecretPojo.setMacaddress("");
     if(piusersecretPojo.getFirstvisit()==null) piusersecretPojo.setFirstvisit(new Date());
     if(piusersecretPojo.getPreviouvisit()==null) piusersecretPojo.setPreviouvisit(new Date());
     if(piusersecretPojo.getBrowsername()==null) piusersecretPojo.setBrowsername("");
     if(piusersecretPojo.getHostsystem()==null) piusersecretPojo.setHostsystem("");
     if(piusersecretPojo.getCreateby()==null) piusersecretPojo.setCreateby("");
     if(piusersecretPojo.getCreatebyid()==null) piusersecretPojo.setCreatebyid("");
     if(piusersecretPojo.getCreatedate()==null) piusersecretPojo.setCreatedate(new Date());
     if(piusersecretPojo.getLister()==null) piusersecretPojo.setLister("");
     if(piusersecretPojo.getListerid()==null) piusersecretPojo.setListerid("");
     if(piusersecretPojo.getModifydate()==null) piusersecretPojo.setModifydate(new Date());
     if(piusersecretPojo.getTenantid()==null) piusersecretPojo.setTenantid("");
     if(piusersecretPojo.getTenantname()==null) piusersecretPojo.setTenantname("");
     if(piusersecretPojo.getRevision()==null) piusersecretPojo.setRevision(0);
        PiusersecretEntity piusersecretEntity = new PiusersecretEntity(); 
        BeanUtils.copyProperties(piusersecretPojo,piusersecretEntity);
        
          piusersecretEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          piusersecretEntity.setRevision(1);  //乐观锁
          this.piusersecretMapper.insert(piusersecretEntity);
        return this.getEntity(piusersecretEntity.getId(),piusersecretEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param piusersecretPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiusersecretPojo update(PiusersecretPojo piusersecretPojo) {
        PiusersecretEntity piusersecretEntity = new PiusersecretEntity(); 
        BeanUtils.copyProperties(piusersecretPojo,piusersecretEntity);
        this.piusersecretMapper.update(piusersecretEntity);
        return this.getEntity(piusersecretEntity.getId(),piusersecretEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.piusersecretMapper.delete(key,tid) ;
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PiusersecretPojo getEntityByUserid(String key, String tid) {
        return this.piusersecretMapper.getEntityByUserid(key, tid);
    }
                                                                                                   
}
