package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PidmsfunctmenuwebEntity;
import inks.system.domain.pojo.PimenuwebPojo;
import inks.system.domain.pojo.PidmsfunctmenuwebPojo;
import inks.system.mapper.PidmsfunctmenuwebMapper;
import inks.system.service.PidmsfunctmenuwebService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * DMS菜单关系(Pidmsfunctmenuweb)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@Service("pidmsfunctmenuwebService")
public class PidmsfunctmenuwebServiceImpl implements PidmsfunctmenuwebService {
    @Resource
    private PidmsfunctmenuwebMapper pidmsfunctmenuwebMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PidmsfunctmenuwebPojo getEntity(String key) {
        return this.pidmsfunctmenuwebMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PidmsfunctmenuwebPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PidmsfunctmenuwebPojo> lst = pidmsfunctmenuwebMapper.getPageList(queryParam);
            PageInfo<PidmsfunctmenuwebPojo> pageInfo = new PageInfo<PidmsfunctmenuwebPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param pidmsfunctmenuwebPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PidmsfunctmenuwebPojo insert(PidmsfunctmenuwebPojo pidmsfunctmenuwebPojo) {
    //初始化NULL字段
     if(pidmsfunctmenuwebPojo.getDmsfunctid()==null) pidmsfunctmenuwebPojo.setDmsfunctid("");
     if(pidmsfunctmenuwebPojo.getDmsfunctcode()==null) pidmsfunctmenuwebPojo.setDmsfunctcode("");
     if(pidmsfunctmenuwebPojo.getDmsfunctname()==null) pidmsfunctmenuwebPojo.setDmsfunctname("");
     if(pidmsfunctmenuwebPojo.getNavid()==null) pidmsfunctmenuwebPojo.setNavid("");
     if(pidmsfunctmenuwebPojo.getNavcode()==null) pidmsfunctmenuwebPojo.setNavcode("");
     if(pidmsfunctmenuwebPojo.getNavname()==null) pidmsfunctmenuwebPojo.setNavname("");
     if(pidmsfunctmenuwebPojo.getRemark()==null) pidmsfunctmenuwebPojo.setRemark("");
     if(pidmsfunctmenuwebPojo.getCreateby()==null) pidmsfunctmenuwebPojo.setCreateby("");
     if(pidmsfunctmenuwebPojo.getCreatebyid()==null) pidmsfunctmenuwebPojo.setCreatebyid("");
     if(pidmsfunctmenuwebPojo.getCreatedate()==null) pidmsfunctmenuwebPojo.setCreatedate(new Date());
     if(pidmsfunctmenuwebPojo.getLister()==null) pidmsfunctmenuwebPojo.setLister("");
     if(pidmsfunctmenuwebPojo.getListerid()==null) pidmsfunctmenuwebPojo.setListerid("");
     if(pidmsfunctmenuwebPojo.getModifydate()==null) pidmsfunctmenuwebPojo.setModifydate(new Date());
     if(pidmsfunctmenuwebPojo.getRevision()==null) pidmsfunctmenuwebPojo.setRevision(0);
        PidmsfunctmenuwebEntity pidmsfunctmenuwebEntity = new PidmsfunctmenuwebEntity(); 
        BeanUtils.copyProperties(pidmsfunctmenuwebPojo,pidmsfunctmenuwebEntity);
  //生成雪花id
          pidmsfunctmenuwebEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          pidmsfunctmenuwebEntity.setRevision(1);  //乐观锁
          this.pidmsfunctmenuwebMapper.insert(pidmsfunctmenuwebEntity);
        return this.getEntity(pidmsfunctmenuwebEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param pidmsfunctmenuwebPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PidmsfunctmenuwebPojo update(PidmsfunctmenuwebPojo pidmsfunctmenuwebPojo) {
        PidmsfunctmenuwebEntity pidmsfunctmenuwebEntity = new PidmsfunctmenuwebEntity(); 
        BeanUtils.copyProperties(pidmsfunctmenuwebPojo,pidmsfunctmenuwebEntity);
        this.pidmsfunctmenuwebMapper.update(pidmsfunctmenuwebEntity);
        return this.getEntity(pidmsfunctmenuwebEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pidmsfunctmenuwebMapper.delete(key) ;
    }

    @Override
    public List<PidmsfunctmenuwebPojo> getListByFunction(String key) {
        return this.pidmsfunctmenuwebMapper.getListByFunction(key);
    }

    @Override
    public List<PimenuwebPojo> getListByLoginUser(LoginUser loginUser){
        return this.pidmsfunctmenuwebMapper.getListByLoginUser(loginUser);
    }

    @Override
    public List<PimenuwebPojo> getListByDmsFunctids(String ids) {
        return this.pidmsfunctmenuwebMapper.getListByDmsFunctids(ids);
    }
}
