package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.AESUtil;
import inks.common.core.utils.DateUtils;
import inks.system.domain.CiselfcheckEntity;
import inks.system.domain.pojo.CiselfcheckPojo;
import inks.system.domain.pojo.PiuserPojo;
import inks.system.mapper.CiselfcheckMapper;
import inks.system.mapper.PiuserMapper;
import inks.system.mapper.PiuserloginMapper;
import inks.system.service.CiselfcheckService;
import inks.system.utils.WeakPasswordChecker;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 系统自检(Ciselfcheck)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-02 09:08:53
 */
@Service("ciselfcheckService")
public class CiselfcheckServiceImpl implements CiselfcheckService {
    @Resource
    private CiselfcheckMapper ciselfcheckMapper;
    @Resource
    private PiuserloginMapper piuserloginMapper;
    @Resource
    private PiuserMapper piuserMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CiselfcheckPojo getEntity(String key) {
        return this.ciselfcheckMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CiselfcheckPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiselfcheckPojo> lst = ciselfcheckMapper.getPageList(queryParam);
            PageInfo<CiselfcheckPojo> pageInfo = new PageInfo<CiselfcheckPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param ciselfcheckPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiselfcheckPojo insert(CiselfcheckPojo ciselfcheckPojo) {
        //初始化NULL字段
        if (ciselfcheckPojo.getFunctionid() == null) ciselfcheckPojo.setFunctionid("");
        if (ciselfcheckPojo.getFunctioncode() == null) ciselfcheckPojo.setFunctioncode("");
        if (ciselfcheckPojo.getFunctionname() == null) ciselfcheckPojo.setFunctionname("");
        if (ciselfcheckPojo.getApiurl() == null) ciselfcheckPojo.setApiurl("");
        if (ciselfcheckPojo.getScore() == null) ciselfcheckPojo.setScore(0);
        if (ciselfcheckPojo.getCritical() == null) ciselfcheckPojo.setCritical(0);
        if (ciselfcheckPojo.getRemark() == null) ciselfcheckPojo.setRemark("");
        if (ciselfcheckPojo.getCreateby() == null) ciselfcheckPojo.setCreateby("");
        if (ciselfcheckPojo.getCreatebyid() == null) ciselfcheckPojo.setCreatebyid("");
        if (ciselfcheckPojo.getCreatedate() == null) ciselfcheckPojo.setCreatedate(new Date());
        if (ciselfcheckPojo.getLister() == null) ciselfcheckPojo.setLister("");
        if (ciselfcheckPojo.getListerid() == null) ciselfcheckPojo.setListerid("");
        if (ciselfcheckPojo.getModifydate() == null) ciselfcheckPojo.setModifydate(new Date());
        if (ciselfcheckPojo.getCustom1() == null) ciselfcheckPojo.setCustom1("");
        if (ciselfcheckPojo.getCustom2() == null) ciselfcheckPojo.setCustom2("");
        if (ciselfcheckPojo.getCustom3() == null) ciselfcheckPojo.setCustom3("");
        if (ciselfcheckPojo.getCustom4() == null) ciselfcheckPojo.setCustom4("");
        if (ciselfcheckPojo.getCustom5() == null) ciselfcheckPojo.setCustom5("");
        if (ciselfcheckPojo.getRevision() == null) ciselfcheckPojo.setRevision(0);
        CiselfcheckEntity ciselfcheckEntity = new CiselfcheckEntity();
        BeanUtils.copyProperties(ciselfcheckPojo, ciselfcheckEntity);
        //生成雪花id
        ciselfcheckEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        ciselfcheckEntity.setRevision(1);  //乐观锁
        this.ciselfcheckMapper.insert(ciselfcheckEntity);
        return this.getEntity(ciselfcheckEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param ciselfcheckPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiselfcheckPojo update(CiselfcheckPojo ciselfcheckPojo) {
        CiselfcheckEntity ciselfcheckEntity = new CiselfcheckEntity();
        BeanUtils.copyProperties(ciselfcheckPojo, ciselfcheckEntity);
        this.ciselfcheckMapper.update(ciselfcheckEntity);
        return this.getEntity(ciselfcheckEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.ciselfcheckMapper.delete(key);
    }

    @Override
    public String getFunctionidByTid(String tid) {
        return this.ciselfcheckMapper.getFunctionidByTid(tid, DateUtils.getTime());
    }


    @Override
    public List<String> checkPasswordComplexity(String tid) {
        ArrayList<String> pwdList = new ArrayList<>();
        try {
            // 设定哪些密码过于简单需要检查
            pwdList.add(AESUtil.Encrypt("123456"));
            pwdList.add(AESUtil.Encrypt("123"));
            return this.ciselfcheckMapper.checkPasswordComplexity(pwdList, tid);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public List<String> checkExpiredMach(String tid) {
        return this.ciselfcheckMapper.checkExpiredMach(tid, DateUtils.getTime());
    }


    //校验弱密码 type默认0：校验当前userid; type=1：校验当前租户下所有用户
    @Override
    public List<PiuserPojo> checkWeakPassword(Integer type, LoginUser loginUser) {
        List<PiuserPojo> weakUsers = new ArrayList<>();

        if (type == 0) {
            // 校验当前用户ID
            String userid = loginUser.getUserid();
            String password = piuserloginMapper.getPasswordByUserid(userid);
            if (password != null && WeakPasswordChecker.isWeakPassword(password)) {
                PiuserPojo userPojo = piuserMapper.getEntity(userid);
                if (userPojo != null) {
                    weakUsers.add(userPojo);
                }
            }
        } else if (type == 1) {
            // 校验当前租户下的所有用户
            String tid = loginUser.getTenantid();
            List<String> userids = piuserMapper.getUseridsByTid(tid);
            if (userids != null) {
                for (String userid : userids) {
                    String password = piuserloginMapper.getPasswordByUserid(userid);
                    if (password != null && WeakPasswordChecker.isWeakPassword(password)) {
                        PiuserPojo userPojo = piuserMapper.getEntity(userid);
                        if (userPojo != null) {
                            weakUsers.add(userPojo);
                        }
                    }
                }
            }
        }
        return weakUsers;
    }

}
