package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PipermcodePojo;

/**
 * 权限编码表(Pipermcode)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-27 10:01:54
 */
public interface PipermcodeService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PipermcodePojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PipermcodePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pipermcodePojo 实例对象
     * @return 实例对象
     */
    PipermcodePojo insert(PipermcodePojo pipermcodePojo);

    /**
     * 修改数据
     *
     * @param pipermcodepojo 实例对象
     * @return 实例对象
     */
    PipermcodePojo update(PipermcodePojo pipermcodepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

}
