package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PirolemenuappEntity;
import inks.system.domain.pojo.PirolemenuappPojo;
import inks.system.mapper.PirolemenuappMapper;
import inks.system.service.PirolemenuappService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 角色菜单App(Pirolemenuapp)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-11 13:55:54
 */
@Service("pirolemenuappService")
public class PirolemenuappServiceImpl implements PirolemenuappService {
    @Resource
    private PirolemenuappMapper pirolemenuappMapper;

    @Override
    public PirolemenuappPojo getEntity(String key, String tid) {
        return this.pirolemenuappMapper.getEntity(key, tid);
    }


    @Override
    public PageInfo<PirolemenuappPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PirolemenuappPojo> lst = pirolemenuappMapper.getPageList(queryParam);
            PageInfo<PirolemenuappPojo> pageInfo = new PageInfo<PirolemenuappPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PirolemenuappPojo insert(PirolemenuappPojo pirolemenuappPojo) {
        //初始化NULL字段
        cleanNull(pirolemenuappPojo);
        PirolemenuappEntity pirolemenuappEntity = new PirolemenuappEntity();
        BeanUtils.copyProperties(pirolemenuappPojo, pirolemenuappEntity);
        //生成雪花id
        pirolemenuappEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        pirolemenuappEntity.setRevision(1);  //乐观锁
        this.pirolemenuappMapper.insert(pirolemenuappEntity);
        return this.getEntity(pirolemenuappEntity.getId(), pirolemenuappEntity.getTenantid());
    }


    @Override
    public PirolemenuappPojo update(PirolemenuappPojo pirolemenuappPojo) {
        PirolemenuappEntity pirolemenuappEntity = new PirolemenuappEntity();
        BeanUtils.copyProperties(pirolemenuappPojo, pirolemenuappEntity);
        this.pirolemenuappMapper.update(pirolemenuappEntity);
        return this.getEntity(pirolemenuappEntity.getId(), pirolemenuappEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.pirolemenuappMapper.delete(key, tid);
    }

    @Override
    public int deleteByRoleidAndNavid(String roleid, String navid, String tenantid) {
        return this.pirolemenuappMapper.deleteByRoleidAndNavid(roleid, navid, tenantid);
    }

    @Override
    public Integer batchCreateDelete(String roleid, List<String> deleteNavids, List<String> createNavids, LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        String realname = loginUser.getRealname();
        String userid = loginUser.getUserid();
        int affectedRows = 0;
        if (CollectionUtils.isNotEmpty(deleteNavids)) {
            Integer i = this.pirolemenuappMapper.batchDelete(roleid, deleteNavids, tid);
            affectedRows += i == null ? 0 : i;
        }

        if (CollectionUtils.isNotEmpty(createNavids)) {
            //insert into PiRoleMenuWeb(id, Roleid, Navid, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
            List<PirolemenuappPojo> pirolemenuappPojoList = new ArrayList<>();
            for (String navid : createNavids) {
                PirolemenuappPojo pirolemenuappPojo = new PirolemenuappPojo();
                pirolemenuappPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
                pirolemenuappPojo.setRoleid(roleid);
                pirolemenuappPojo.setNavid(navid);
                // pirolemenuappPojo.setRownum(createNavids.indexOf(navid) + 1);
                pirolemenuappPojo.setCreateby(realname); // 示例：设置创建者
                pirolemenuappPojo.setCreatebyid(userid); // 示例：设置创建者ID
                pirolemenuappPojo.setLister(realname); // 示例：设置列出者
                pirolemenuappPojo.setListerid(userid); // 示例：设置列出者ID
                pirolemenuappPojo.setTenantid(tid); // 示例：设置租户ID
                cleanNull(pirolemenuappPojo);
                pirolemenuappPojoList.add(pirolemenuappPojo);
            }

            Integer i = this.pirolemenuappMapper.batchInsert(pirolemenuappPojoList);
            affectedRows += i == null ? 0 : i;
        }
        return affectedRows;
    }

    private static void cleanNull(PirolemenuappPojo pirolemenuappPojo) {
        if (pirolemenuappPojo.getRoleid() == null) pirolemenuappPojo.setRoleid("");
        if (pirolemenuappPojo.getNavid() == null) pirolemenuappPojo.setNavid("");
        if (pirolemenuappPojo.getRownum() == null) pirolemenuappPojo.setRownum(0);
        if (pirolemenuappPojo.getCreateby() == null) pirolemenuappPojo.setCreateby("");
        if (pirolemenuappPojo.getCreatebyid() == null) pirolemenuappPojo.setCreatebyid("");
        if (pirolemenuappPojo.getCreatedate() == null) pirolemenuappPojo.setCreatedate(new Date());
        if (pirolemenuappPojo.getLister() == null) pirolemenuappPojo.setLister("");
        if (pirolemenuappPojo.getListerid() == null) pirolemenuappPojo.setListerid("");
        if (pirolemenuappPojo.getModifydate() == null) pirolemenuappPojo.setModifydate(new Date());
        if (pirolemenuappPojo.getTenantid() == null) pirolemenuappPojo.setTenantid("");
        if (pirolemenuappPojo.getTenantname() == null) pirolemenuappPojo.setTenantname("");
        if (pirolemenuappPojo.getRevision() == null) pirolemenuappPojo.setRevision(0);
    }

    @Override
    public List<PirolemenuappPojo> getListByRole(String key, String tid) {
        return this.pirolemenuappMapper.getListByRole(key, tid);
    }
}
