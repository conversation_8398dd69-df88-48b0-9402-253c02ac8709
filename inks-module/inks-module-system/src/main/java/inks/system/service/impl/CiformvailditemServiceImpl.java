package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.system.domain.pojo.CiformvailditemPojo;
import inks.system.domain.CiformvailditemEntity;
import inks.system.mapper.CiformvailditemMapper;
import inks.system.service.CiformvailditemService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 窗体验证子表(Ciformvailditem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-31 17:04:05
 */
@Service("ciformvailditemService")
public class CiformvailditemServiceImpl implements CiformvailditemService {
    @Resource
    private CiformvailditemMapper ciformvailditemMapper;

    @Override
    public CiformvailditemPojo getEntity(String key,String tid) {
        return this.ciformvailditemMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<CiformvailditemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiformvailditemPojo> lst = ciformvailditemMapper.getPageList(queryParam);
            PageInfo<CiformvailditemPojo> pageInfo = new PageInfo<CiformvailditemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<CiformvailditemPojo> getList(String Pid,String tid) { 
        try {
            List<CiformvailditemPojo> lst = ciformvailditemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public CiformvailditemPojo insert(CiformvailditemPojo ciformvailditemPojo) {
        //初始化item的NULL
        CiformvailditemPojo itempojo =this.clearNull(ciformvailditemPojo);
        CiformvailditemEntity ciformvailditemEntity = new CiformvailditemEntity(); 
        BeanUtils.copyProperties(itempojo,ciformvailditemEntity);
          //生成雪花id
          ciformvailditemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          ciformvailditemEntity.setRevision(1);  //乐观锁      
          this.ciformvailditemMapper.insert(ciformvailditemEntity);
        return this.getEntity(ciformvailditemEntity.getId(),ciformvailditemEntity.getTenantid());
  
    }

    @Override
    public CiformvailditemPojo update(CiformvailditemPojo ciformvailditemPojo) {
        CiformvailditemEntity ciformvailditemEntity = new CiformvailditemEntity(); 
        BeanUtils.copyProperties(ciformvailditemPojo,ciformvailditemEntity);
        this.ciformvailditemMapper.update(ciformvailditemEntity);
        return this.getEntity(ciformvailditemEntity.getId(),ciformvailditemEntity.getTenantid());
    }

    @Override
    public int delete(String key,String tid) {
        return this.ciformvailditemMapper.delete(key,tid) ;
    }

     @Override
     public CiformvailditemPojo clearNull(CiformvailditemPojo ciformvailditemPojo){
     //初始化NULL字段
     if(ciformvailditemPojo.getPid()==null) ciformvailditemPojo.setPid("");
     if(ciformvailditemPojo.getItemfield()==null) ciformvailditemPojo.setItemfield("");
     if(ciformvailditemPojo.getItemlabel()==null) ciformvailditemPojo.setItemlabel("");
     if(ciformvailditemPojo.getRequired()==null) ciformvailditemPojo.setRequired(0);
     if(ciformvailditemPojo.getMinlimit()==null) ciformvailditemPojo.setMinlimit(0);
     if(ciformvailditemPojo.getMaxlimit()==null) ciformvailditemPojo.setMaxlimit(0);
     if(ciformvailditemPojo.getRegular()==null) ciformvailditemPojo.setRegular("");
     if(ciformvailditemPojo.getTipmsg()==null) ciformvailditemPojo.setTipmsg("");
     if(ciformvailditemPojo.getTipmsgen()==null) ciformvailditemPojo.setTipmsgen("");
     if(ciformvailditemPojo.getEnabledmark()==null) ciformvailditemPojo.setEnabledmark(0);
     if(ciformvailditemPojo.getRownum()==null) ciformvailditemPojo.setRownum(0);
     if(ciformvailditemPojo.getRemark()==null) ciformvailditemPojo.setRemark("");
     if(ciformvailditemPojo.getCustom1()==null) ciformvailditemPojo.setCustom1("");
     if(ciformvailditemPojo.getCustom2()==null) ciformvailditemPojo.setCustom2("");
     if(ciformvailditemPojo.getCustom3()==null) ciformvailditemPojo.setCustom3("");
     if(ciformvailditemPojo.getCustom4()==null) ciformvailditemPojo.setCustom4("");
     if(ciformvailditemPojo.getCustom5()==null) ciformvailditemPojo.setCustom5("");
     if(ciformvailditemPojo.getTenantid()==null) ciformvailditemPojo.setTenantid("");
     if(ciformvailditemPojo.getRevision()==null) ciformvailditemPojo.setRevision(0);
     return ciformvailditemPojo;
     }
}
