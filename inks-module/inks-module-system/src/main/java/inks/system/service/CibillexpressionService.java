package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CibillexpressionPojo;
import inks.system.domain.CibillexpressionEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 单据公式(Cibillexpression)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-29 10:52:48
 */
public interface CibillexpressionService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CibillexpressionPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CibillexpressionPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param cibillexpressionPojo 实例对象
     * @return 实例对象
     */
    CibillexpressionPojo insert(CibillexpressionPojo cibillexpressionPojo);

    /**
     * 修改数据
     *
     * @param cibillexpressionpojo 实例对象
     * @return 实例对象
     */
    CibillexpressionPojo update(CibillexpressionPojo cibillexpressionpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

    List<CibillexpressionPojo> getListByCode(String key, String tid);

}
