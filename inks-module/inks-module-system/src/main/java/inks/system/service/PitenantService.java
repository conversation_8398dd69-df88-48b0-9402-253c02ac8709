package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PitenantPojo;

import java.util.List;
import java.util.Map;

/**
 * 租户表(Pitenant)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-08 14:29:22
 */
public interface PitenantService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PitenantPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PitenantPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pitenantPojo 实例对象
     * @return 实例对象
     */
    PitenantPojo insert(PitenantPojo pitenantPojo);

    /**
     * 修改数据
     *
     * @param pitenantpojo 实例对象
     * @return 实例对象
     */
    PitenantPojo update(PitenantPojo pitenantpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);


    /*
     * 用户新建租户
     *
     * */
    public PitenantPojo createByUser(PitenantPojo piTenantpojo, LoginUser loginUser);

    List<Map<String, Object>> getCountRecord(String fn, String tid);
}
