package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.redis.service.RedisService;
import inks.system.domain.PipermissionEntity;
import inks.system.domain.pojo.PipermcodePojo;
import inks.system.domain.pojo.PipermissionPojo;
import inks.system.mapper.PipermissionMapper;
import inks.system.mapper.PiuseronlineMapper;
import inks.system.mapper.PiuserroleMapper;
import inks.system.service.PipermissionService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 权限关系表(Pipermission)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-17 10:38:00
 */
@Service("pipermissionService")
public class PipermissionServiceImpl implements PipermissionService {
    @Resource
    private PipermissionMapper pipermissionMapper;
    @Resource
    private PiuserroleMapper piuserroleMapper;
    @Resource
    private RedisService redisService;
    @Resource
    private PiuseronlineMapper piuseronlineMapper;

    private final static Logger logger = LoggerFactory.getLogger(PipermissionServiceImpl.class);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PipermissionPojo getEntity(String key, String tid) {
        return this.pipermissionMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PipermissionPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PipermissionPojo> lst = pipermissionMapper.getPageList(queryParam);
            PageInfo<PipermissionPojo> pageInfo = new PageInfo<PipermissionPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param pipermissionPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PipermissionPojo insert(PipermissionPojo pipermissionPojo) {
        //初始化NULL字段
        cleanNull(pipermissionPojo);
        PipermissionEntity pipermissionEntity = new PipermissionEntity();
        BeanUtils.copyProperties(pipermissionPojo, pipermissionEntity);

        pipermissionEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        pipermissionEntity.setRevision(1);  //乐观锁
        this.pipermissionMapper.insert(pipermissionEntity);
        // 更新关联的所有用户的权限缓存 redisKey: user_permset
        updatePermissRedis(pipermissionPojo.getResourceid(), pipermissionPojo.getTenantid());
        return null;
    }

    private static void cleanNull(PipermissionPojo pipermissionPojo) {
        if (pipermissionPojo.getResourcetype() == null) pipermissionPojo.setResourcetype("");
        if (pipermissionPojo.getResourceid() == null) pipermissionPojo.setResourceid("");
        if (pipermissionPojo.getPermid() == null) pipermissionPojo.setPermid("");
        if (pipermissionPojo.getPermcode() == null) pipermissionPojo.setPermcode("");
        if (pipermissionPojo.getPermname() == null) pipermissionPojo.setPermname("");
        if (pipermissionPojo.getCreateby() == null) pipermissionPojo.setCreateby("");
        if (pipermissionPojo.getCreatebyid() == null) pipermissionPojo.setCreatebyid("");
        if (pipermissionPojo.getCreatedate() == null) pipermissionPojo.setCreatedate(new Date());
        if (pipermissionPojo.getLister() == null) pipermissionPojo.setLister("");
        if (pipermissionPojo.getListerid() == null) pipermissionPojo.setListerid("");
        if (pipermissionPojo.getModifydate() == null) pipermissionPojo.setModifydate(new Date());
        if (pipermissionPojo.getTenantid() == null) pipermissionPojo.setTenantid("");
        if (pipermissionPojo.getTenantname() == null) pipermissionPojo.setTenantname("");
        if (pipermissionPojo.getRevision() == null) pipermissionPojo.setRevision(0);
    }


    // 1.更新权限缓存 redisKey: user_permset...   2.更新loginuser权限
    public void updatePermissRedis(String resourceid, String tid) {
        List<String> userids = piuserroleMapper.getUseridsByRoleid(resourceid, tid);
        for (String userid : userids) {
            // 1.获取权限集合
            HashSet<String> permissSet = new HashSet<>();
            List<PipermcodePojo> lst = piuserroleMapper.getPermByUser(userid, tid);
            for (PipermcodePojo pipermcodePojo : lst) {
                if (pipermcodePojo.getPermtype() != null && pipermcodePojo.getPermtype().equals("3")) {
                    permissSet.add(pipermcodePojo.getPermcode());
                }
            }
            // 更新Redis中的权限集合
            String user_permset = "user_permset:" + tid + "-" + userid;
            redisService.setCacheObject(user_permset, permissSet, 30L, TimeUnit.DAYS);

            // 2.更新登录用户的权限 TODO 需保证userid在线token都在Pi_UserOnline
            List<String> tokens = piuseronlineMapper.getAllTokens(userid, tid);
            for (String token : tokens) {
                String tokenRedisKey = "login_tokens:" + token;
                LoginUser loginUser = redisService.getCacheObject(tokenRedisKey);
                if (loginUser != null) {
                    loginUser.setPermissions(permissSet);
                    redisService.setCacheObject(tokenRedisKey, loginUser, 12L, TimeUnit.HOURS);
                }
            }
        }
    }


    /**
     * 修改数据
     *
     * @param pipermissionPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PipermissionPojo update(PipermissionPojo pipermissionPojo) {
        PipermissionEntity pipermissionEntity = new PipermissionEntity();
        BeanUtils.copyProperties(pipermissionPojo, pipermissionEntity);
        this.pipermissionMapper.update(pipermissionEntity);
        // 更新关联的所有用户的权限缓存 redisKey: user_permset
        updatePermissRedis(pipermissionPojo.getResourceid(), pipermissionPojo.getTenantid());
        return this.getEntity(pipermissionEntity.getId(), pipermissionEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        PipermissionPojo pipermissionPojo = getEntity(key, tid);
        int deleteCount = this.pipermissionMapper.delete(key, tid);
        // 更新关联的所有用户的权限缓存 redisKey: user_permset
        updatePermissRedis(pipermissionPojo.getResourceid(), tid);
        return deleteCount;
    }

    /**
     * 按角色查询权限
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public List<PipermissionPojo> getListByRole(String key) {
        try {
            List<PipermissionPojo> list = pipermissionMapper.getListByRole(key);
            return list;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<PipermissionPojo> getUserAllPerm(String key) {
        return pipermissionMapper.getUserAllPerm(key);
    }

    @Override
    public String batchCreateDelete(List<PipermissionPojo> pipermissionList, LoginUser loginUser) {
        if (CollectionUtils.isEmpty(pipermissionList)) {
            return "无数据处理";
        }

        // 获取登录用户信息和角色ID
        String userid = loginUser.getUserid();
        String realName = loginUser.getRealname();
        String tid = loginUser.getTenantid();
        String resourceid = pipermissionList.get(0).getResourceid(); // 角色ID
        pipermissionList.forEach(pipermissionPojo -> {
                    pipermissionPojo.setTenantid(tid);
                }
        );

        // 初始化新建和删除权限的集合（iscreate=1则新建，iscreate=0则删除）
        List<PipermissionPojo> createList = new ArrayList<>();
        List<PipermissionPojo> deleteList = new ArrayList<>();
        for (PipermissionPojo pipermissionPojo : pipermissionList) {
            if (Objects.equals(pipermissionPojo.getIscreate(), 1)) {
                createList.add(pipermissionPojo);
            } else if (Objects.equals(pipermissionPojo.getIscreate(), 0)) {
                deleteList.add(pipermissionPojo);
            }
        }

        int createCount = 0, deleteCount = 0;

        // 分批处理，设置批次大小，例如50条每批
        final int batchSize = 50;
        if (CollectionUtils.isNotEmpty(createList)) {
            createCount = batchInsertByBatch(createList, userid, realName, tid, batchSize);
        }
        if (CollectionUtils.isNotEmpty(deleteList)) {
            deleteCount = batchDeleteByBatch(deleteList, batchSize);
        }

        // 更新关联的所有用户的权限缓存 redisKey: user_permset（可考虑异步处理）
        updatePermissRedis(resourceid, tid);

        return String.format("成功新建权限 %d 条, 删除权限 %d 条", createCount, deleteCount);
    }


    // 分批插入数据
    public int batchInsertByBatch(List<PipermissionPojo> pipermissionPojoList, String userid, String realName, String tid, int batchSize) {
        Date date = new Date();
        int totalInserted = 0;
        int totalRecords = pipermissionPojoList.size();

        for (int i = 0; i < totalRecords; i += batchSize) {
            int end = Math.min(i + batchSize, totalRecords);
            List<PipermissionPojo> batchList = pipermissionPojoList.subList(i, end);

            // 将POJO转换为实体对象
            List<PipermissionEntity> pipermissionEntityList = batchList.stream().map(pipermissionPojo -> {
                pipermissionPojo.setCreateby(realName);
                pipermissionPojo.setCreatebyid(userid);
                pipermissionPojo.setCreatedate(date);
                pipermissionPojo.setLister(realName);
                pipermissionPojo.setListerid(userid);
                pipermissionPojo.setModifydate(date);
                pipermissionPojo.setTenantid(tid);
                cleanNull(pipermissionPojo); // 清除空值（假设该方法已实现）
                PipermissionEntity entity = new PipermissionEntity();
                BeanUtils.copyProperties(pipermissionPojo, entity);
                entity.setId(inksSnowflake.getSnowflake().nextIdStr());
                entity.setRevision(1); // 乐观锁版本号
                return entity;
            }).collect(Collectors.toList());

            // 批量插入当前批次数据
            totalInserted += pipermissionMapper.batchInsert(pipermissionEntityList);

            // 修正后的进度打印（使用 end 替代 i + batchSize）
            logger.info("批量插入进度: {}% ({}/{}条)",
                    (end / (double) totalRecords * 100),
                    end,
                    totalRecords);
        }
        return totalInserted;
    }

    // 分批删除数据
    public int batchDeleteByBatch(List<PipermissionPojo> pipermissionPojoList, int batchSize) {
        int totalDeleted = 0;
        int totalRecords = pipermissionPojoList.size();

        for (int i = 0; i < totalRecords; i += batchSize) {
            int end = Math.min(i + batchSize, totalRecords);
            List<PipermissionPojo> batchList = pipermissionPojoList.subList(i, end);

            totalDeleted += pipermissionMapper.batchDelete(batchList);

            // 修正后的进度打印（使用 end 替代 i + batchSize）
            logger.info("批量删除进度: {}% ({}/{}条)",
                    (end / (double) totalRecords * 100),
                    end,
                    totalRecords);
        }
        return totalDeleted;
    }


}
