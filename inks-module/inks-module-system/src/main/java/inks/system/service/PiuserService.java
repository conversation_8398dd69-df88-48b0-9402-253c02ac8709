package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiuserPojo;

/**
 * 用户表(Piuser)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-08 14:35:50
 */
public interface PiuserService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiuserPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PiuserPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param piuserPojo 实例对象
     * @return 实例对象
     */
    PiuserPojo insert(PiuserPojo piuserPojo);

    /**
     * 修改数据
     *
     * @param piuserpojo 实例对象
     * @return 实例对象
     */
    PiuserPojo update(PiuserPojo piuserpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);


    /**
     * 新增数据,并加入userlogin
     *
     * @param piuserPojo 实例对象
     * @return 实例对象
     */
    PiuserPojo create(PiuserPojo piuserPojo) throws Exception;

    /**
     * 新增数据,并加入userlogin
     *
     * @param piuserPojo 实例对象
     * @return 实例对象
     */
    PiuserPojo createByTenant(PiuserPojo piuserPojo, LoginUser loginUser) throws Exception;


}
