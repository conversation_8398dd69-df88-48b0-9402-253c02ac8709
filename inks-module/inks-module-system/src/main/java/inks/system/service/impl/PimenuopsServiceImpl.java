package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.system.domain.pojo.PimenuopsPojo;
import inks.system.domain.PimenuopsEntity;
import inks.system.mapper.PimenuopsMapper;
import inks.system.service.PimenuopsService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;

import java.util.Collections;
import java.util.Date;
import java.util.List;
/**
 * Ops导航(Pimenuops)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-04-24 17:07:54
 */
@Service("pimenuopsService")
public class PimenuopsServiceImpl implements PimenuopsService {
    @Resource
    private PimenuopsMapper pimenuopsMapper;

    @Override
    public PimenuopsPojo getEntity(String key) {
        return this.pimenuopsMapper.getEntity(key);
    }


    @Override
    public PageInfo<PimenuopsPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PimenuopsPojo> lst = pimenuopsMapper.getPageList(queryParam);
            PageInfo<PimenuopsPojo> pageInfo = new PageInfo<PimenuopsPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public PimenuopsPojo insert(PimenuopsPojo pimenuopsPojo) {
        //初始化NULL字段
        cleanNull(pimenuopsPojo);
        PimenuopsEntity pimenuopsEntity = new PimenuopsEntity(); 
        BeanUtils.copyProperties(pimenuopsPojo,pimenuopsEntity);
        //生成雪花id
        pimenuopsEntity.setNavid(inksSnowflake.getSnowflake().nextIdStr());
        pimenuopsEntity.setRevision(1);  //乐观锁
        this.pimenuopsMapper.insert(pimenuopsEntity);
        return this.getEntity(pimenuopsEntity.getNavid());
    }


    @Override
    public PimenuopsPojo update(PimenuopsPojo pimenuopsPojo) {
        PimenuopsEntity pimenuopsEntity = new PimenuopsEntity(); 
        BeanUtils.copyProperties(pimenuopsPojo,pimenuopsEntity);
        this.pimenuopsMapper.update(pimenuopsEntity);
        return this.getEntity(pimenuopsEntity.getNavid());
    }

    @Override
    public int delete(String key) {
        return this.pimenuopsMapper.delete(key) ;
    }
    

    private static void cleanNull(PimenuopsPojo pimenuopsPojo) {
        if(pimenuopsPojo.getNavid()==null) pimenuopsPojo.setNavid("");
        if(pimenuopsPojo.getNavpid()==null) pimenuopsPojo.setNavpid("");
        if(pimenuopsPojo.getNavtype()==null) pimenuopsPojo.setNavtype("");
        if(pimenuopsPojo.getNavcode()==null) pimenuopsPojo.setNavcode("");
        if(pimenuopsPojo.getNavname()==null) pimenuopsPojo.setNavname("");
        if(pimenuopsPojo.getNavgroup()==null) pimenuopsPojo.setNavgroup("");
        if(pimenuopsPojo.getRownum()==null) pimenuopsPojo.setRownum(0);
        if(pimenuopsPojo.getAssemblyname()==null) pimenuopsPojo.setAssemblyname("");
        if(pimenuopsPojo.getFormname()==null) pimenuopsPojo.setFormname("");
        if(pimenuopsPojo.getImagecss()==null) pimenuopsPojo.setImagecss("");
        if(pimenuopsPojo.getModuletype()==null) pimenuopsPojo.setModuletype("");
        if(pimenuopsPojo.getModulecode()==null) pimenuopsPojo.setModulecode("");
        if(pimenuopsPojo.getRolecode()==null) pimenuopsPojo.setRolecode("");
        if(pimenuopsPojo.getImageindex()==null) pimenuopsPojo.setImageindex("");
        if(pimenuopsPojo.getImagestyle()==null) pimenuopsPojo.setImagestyle("");
        if(pimenuopsPojo.getEnabledmark()==null) pimenuopsPojo.setEnabledmark(0);
        if(pimenuopsPojo.getIconurl()==null) pimenuopsPojo.setIconurl("");
        if(pimenuopsPojo.getNavigateurl()==null) pimenuopsPojo.setNavigateurl("");
        if(pimenuopsPojo.getMvcurl()==null) pimenuopsPojo.setMvcurl("");
        if(pimenuopsPojo.getRemark()==null) pimenuopsPojo.setRemark("");
        if(pimenuopsPojo.getPermissioncode()==null) pimenuopsPojo.setPermissioncode("");
        if(pimenuopsPojo.getCreateby()==null) pimenuopsPojo.setCreateby("");
        if(pimenuopsPojo.getCreatebyid()==null) pimenuopsPojo.setCreatebyid("");
        if(pimenuopsPojo.getCreatedate()==null) pimenuopsPojo.setCreatedate(new Date());
        if(pimenuopsPojo.getLister()==null) pimenuopsPojo.setLister("");
        if(pimenuopsPojo.getListerid()==null) pimenuopsPojo.setListerid("");
        if(pimenuopsPojo.getModifydate()==null) pimenuopsPojo.setModifydate(new Date());
        if(pimenuopsPojo.getRevision()==null) pimenuopsPojo.setRevision(0);
   }

    @Override
    public List<PimenuopsPojo> getListByPid(String key) {
        return this.pimenuopsMapper.getListByPid(key);
    }
}
