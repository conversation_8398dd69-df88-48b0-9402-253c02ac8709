package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DeptinfoPojo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PitenantuserPojo;

import java.util.List;

/**
 * 租户关系表(Pitenantuser)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-08 14:31:54
 */
public interface PitenantuserService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PitenantuserPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PitenantuserPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pitenantuserPojo 实例对象
     * @return 实例对象
     */
    PitenantuserPojo insert(PitenantuserPojo pitenantuserPojo);

    /**
     * 修改数据
     *
     * @param pitenantuserpojo 实例对象
     * @return 实例对象
     */
    PitenantuserPojo update(PitenantuserPojo pitenantuserpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    List<PitenantuserPojo> getListByTenant(String key);


    List<PitenantuserPojo> getListByUser(String key);


    /**
     * 分页查询
     *
     * @param userid 筛选条件
     * @return 查询结果
     */
    List<DeptinfoPojo> getDeptinfoList(String userid, String tid);

}
