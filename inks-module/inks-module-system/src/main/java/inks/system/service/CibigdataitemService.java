package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CibigdataitemPojo;

import java.util.List;
/**
 * 服务大屏关系(Cibigdataitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-25 10:38:41
 */
public interface CibigdataitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CibigdataitemPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CibigdataitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<CibigdataitemPojo> getList(String Pid);
    
    /**
     * 新增数据
     *
     * @param cibigdataitemPojo 实例对象
     * @return 实例对象
     */
    CibigdataitemPojo insert(CibigdataitemPojo cibigdataitemPojo);

    /**
     * 修改数据
     *
     * @param cibigdataitempojo 实例对象
     * @return 实例对象
     */
    CibigdataitemPojo update(CibigdataitemPojo cibigdataitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

     /**
     * 修改数据
     *
     * @param cibigdataitempojo 实例对象
     * @return 实例对象
     */
    CibigdataitemPojo clearNull(CibigdataitemPojo cibigdataitempojo);
}
