package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DeptinfoPojo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PideptuserEntity;
import inks.system.domain.pojo.PideptuserPojo;
import inks.system.mapper.PideptuserMapper;
import inks.system.service.PideptService;
import inks.system.service.PideptuserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 组织用户表(Pideptuser)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-28 11:18:02
 */
@Service("pideptuserService")
public class PideptuserServiceImpl implements PideptuserService {
    @Resource
    private PideptuserMapper pideptuserMapper;

    /**
     * 服务对象
     */
    @Resource
    private PideptService pideptService;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PideptuserPojo getEntity(String key, String tid) {
        return this.pideptuserMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PideptuserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PideptuserPojo> lst = pideptuserMapper.getPageList(queryParam);
            PageInfo<PideptuserPojo> pageInfo = new PageInfo<PideptuserPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param pideptuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PideptuserPojo insert(PideptuserPojo pideptuserPojo) {
        //初始化NULL字段
        if (pideptuserPojo.getDeptid() == null) pideptuserPojo.setDeptid("");
        if (pideptuserPojo.getDeptcode() == null) pideptuserPojo.setDeptcode("");
        if (pideptuserPojo.getDeptname() == null) pideptuserPojo.setDeptname("");
        if (pideptuserPojo.getUserid() == null) pideptuserPojo.setUserid("");
        if (pideptuserPojo.getUsername() == null) pideptuserPojo.setUsername("");
        if (pideptuserPojo.getRealname() == null) pideptuserPojo.setRealname("");
        if (pideptuserPojo.getIsadmin() == null) pideptuserPojo.setIsadmin(0);
        if (pideptuserPojo.getRownum() == null) pideptuserPojo.setRownum(0);
        if (pideptuserPojo.getCreateby() == null) pideptuserPojo.setCreateby("");
        if (pideptuserPojo.getCreatebyid() == null) pideptuserPojo.setCreatebyid("");
        if (pideptuserPojo.getCreatedate() == null) pideptuserPojo.setCreatedate(new Date());
        if (pideptuserPojo.getLister() == null) pideptuserPojo.setLister("");
        if (pideptuserPojo.getListerid() == null) pideptuserPojo.setListerid("");
        if (pideptuserPojo.getModifydate() == null) pideptuserPojo.setModifydate(new Date());
        if (pideptuserPojo.getCustom1() == null) pideptuserPojo.setCustom1("");
        if (pideptuserPojo.getCustom2() == null) pideptuserPojo.setCustom2("");
        if (pideptuserPojo.getCustom3() == null) pideptuserPojo.setCustom3("");
        if (pideptuserPojo.getCustom4() == null) pideptuserPojo.setCustom4("");
        if (pideptuserPojo.getCustom5() == null) pideptuserPojo.setCustom5("");
        if (pideptuserPojo.getTenantid() == null) pideptuserPojo.setTenantid("");
        if (pideptuserPojo.getTenantname() == null) pideptuserPojo.setTenantname("");
        if (pideptuserPojo.getRevision() == null) pideptuserPojo.setRevision(0);
        PideptuserEntity pideptuserEntity = new PideptuserEntity();
        BeanUtils.copyProperties(pideptuserPojo, pideptuserEntity);

        pideptuserEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        pideptuserEntity.setRevision(1);  //乐观锁
        this.pideptuserMapper.insert(pideptuserEntity);
        return this.getEntity(pideptuserEntity.getId(), pideptuserEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param pideptuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PideptuserPojo update(PideptuserPojo pideptuserPojo) {
        PideptuserEntity pideptuserEntity = new PideptuserEntity();
        BeanUtils.copyProperties(pideptuserPojo, pideptuserEntity);
        this.pideptuserMapper.update(pideptuserEntity);
        return this.getEntity(pideptuserEntity.getId(), pideptuserEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.pideptuserMapper.delete(key, tid);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param userid 主键
     * @return 实例对象
     */
    @Override
    public PideptuserPojo getEntityByUser(String userid, String tid) {
        return this.pideptuserMapper.getEntityByUser(userid, tid);
    }

    /**
     * 分页查询
     *
     * @param userid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<DeptinfoPojo> getDeptinfoList(String userid, String tid) {
        List<DeptinfoPojo> lst = new ArrayList<>();
        PideptuserPojo pideptuserPojo = getEntityByUser(userid, tid);
        if (pideptuserPojo == null) {
            return null;
        } else {
            DeptinfoPojo deptinfoPojo = new DeptinfoPojo();
            BeanUtils.copyProperties(pideptuserPojo, deptinfoPojo);
            lst.add(deptinfoPojo);
            pideptService.getSubinfoAll(lst,tid);
            return lst;
        }
    }

}
