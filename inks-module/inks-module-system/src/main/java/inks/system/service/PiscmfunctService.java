package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiscmfunctPojo;
import inks.system.domain.PiscmfunctEntity;

import com.github.pagehelper.PageInfo;

/**
 * SCM功能(Piscmfunct)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
public interface PiscmfunctService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiscmfunctPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PiscmfunctPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param piscmfunctPojo 实例对象
     * @return 实例对象
     */
    PiscmfunctPojo insert(PiscmfunctPojo piscmfunctPojo);

    /**
     * 修改数据
     *
     * @param piscmfunctpojo 实例对象
     * @return 实例对象
     */
    PiscmfunctPojo update(PiscmfunctPojo piscmfunctpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
                                                                                     }
