package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.system.domain.pojo.PimenufrmPojo;
import inks.system.domain.PimenufrmEntity;
import inks.system.mapper.PimenufrmMapper;
import inks.system.service.PimenufrmService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;

import java.util.Collections;
import java.util.Date;
import java.util.List;
/**
 * Frm导航(Pimenufrm)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-05 16:28:27
 */
@Service("pimenufrmService")
public class PimenufrmServiceImpl implements PimenufrmService {
    @Resource
    private PimenufrmMapper pimenufrmMapper;

    @Override
    public PimenufrmPojo getEntity(String key) {
        return this.pimenufrmMapper.getEntity(key);
    }


    @Override
    public PageInfo<PimenufrmPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PimenufrmPojo> lst = pimenufrmMapper.getPageList(queryParam);
            PageInfo<PimenufrmPojo> pageInfo = new PageInfo<PimenufrmPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public PimenufrmPojo insert(PimenufrmPojo pimenufrmPojo) {
        //初始化NULL字段
        cleanNull(pimenufrmPojo);
        PimenufrmEntity pimenufrmEntity = new PimenufrmEntity(); 
        BeanUtils.copyProperties(pimenufrmPojo,pimenufrmEntity);
        //生成雪花id
        pimenufrmEntity.setNavid(inksSnowflake.getSnowflake().nextIdStr());
        pimenufrmEntity.setRevision(1);  //乐观锁
        this.pimenufrmMapper.insert(pimenufrmEntity);
        return this.getEntity(pimenufrmEntity.getNavid());
    }


    @Override
    public PimenufrmPojo update(PimenufrmPojo pimenufrmPojo) {
        PimenufrmEntity pimenufrmEntity = new PimenufrmEntity(); 
        BeanUtils.copyProperties(pimenufrmPojo,pimenufrmEntity);
        this.pimenufrmMapper.update(pimenufrmEntity);
        return this.getEntity(pimenufrmEntity.getNavid());
    }

    @Override
    public int delete(String key) {
        return this.pimenufrmMapper.delete(key) ;
    }
    

    private static void cleanNull(PimenufrmPojo pimenufrmPojo) {
        if(pimenufrmPojo.getNavpid()==null) pimenufrmPojo.setNavpid("");
        if(pimenufrmPojo.getNavtype()==null) pimenufrmPojo.setNavtype("");
        if(pimenufrmPojo.getNavcode()==null) pimenufrmPojo.setNavcode("");
        if(pimenufrmPojo.getNavname()==null) pimenufrmPojo.setNavname("");
        if(pimenufrmPojo.getNavgroup()==null) pimenufrmPojo.setNavgroup("");
        if(pimenufrmPojo.getRownum()==null) pimenufrmPojo.setRownum(0);
        if(pimenufrmPojo.getAssemblyname()==null) pimenufrmPojo.setAssemblyname("");
        if(pimenufrmPojo.getFormname()==null) pimenufrmPojo.setFormname("");
        if(pimenufrmPojo.getImagecss()==null) pimenufrmPojo.setImagecss("");
        if(pimenufrmPojo.getModuletype()==null) pimenufrmPojo.setModuletype("");
        if(pimenufrmPojo.getModulecode()==null) pimenufrmPojo.setModulecode("");
        if(pimenufrmPojo.getRolecode()==null) pimenufrmPojo.setRolecode("");
        if(pimenufrmPojo.getImageindex()==null) pimenufrmPojo.setImageindex("");
        if(pimenufrmPojo.getImagestyle()==null) pimenufrmPojo.setImagestyle("");
        if(pimenufrmPojo.getEnabledmark()==null) pimenufrmPojo.setEnabledmark(0);
        if(pimenufrmPojo.getIconurl()==null) pimenufrmPojo.setIconurl("");
        if(pimenufrmPojo.getNavigateurl()==null) pimenufrmPojo.setNavigateurl("");
        if(pimenufrmPojo.getMvcurl()==null) pimenufrmPojo.setMvcurl("");
        if(pimenufrmPojo.getRemark()==null) pimenufrmPojo.setRemark("");
        if(pimenufrmPojo.getPermissioncode()==null) pimenufrmPojo.setPermissioncode("");
        if(pimenufrmPojo.getPageindex()==null) pimenufrmPojo.setPageindex(0);
        if(pimenufrmPojo.getCreateby()==null) pimenufrmPojo.setCreateby("");
        if(pimenufrmPojo.getCreatebyid()==null) pimenufrmPojo.setCreatebyid("");
        if(pimenufrmPojo.getCreatedate()==null) pimenufrmPojo.setCreatedate(new Date());
        if(pimenufrmPojo.getLister()==null) pimenufrmPojo.setLister("");
        if(pimenufrmPojo.getListerid()==null) pimenufrmPojo.setListerid("");
        if(pimenufrmPojo.getModifydate()==null) pimenufrmPojo.setModifydate(new Date());
        if(pimenufrmPojo.getRevision()==null) pimenufrmPojo.setRevision(0);
   }

    @Override
    public List<PimenufrmPojo> getListByPid(String key) {
        return this.pimenufrmMapper.getListByPid(key);
    }

    @Override
    public List<PimenufrmPojo> getAllMenus() {
        return this.pimenufrmMapper.getAllMenus();
    }
}
