package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CifninvoicePojo;
import inks.system.domain.pojo.CifninvoiceitemdetailPojo;

/**
 * 开票管理(Cifninvoice)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-19 20:01:41
 */
public interface CifninvoiceService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CifninvoicePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CifninvoiceitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CifninvoicePojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CifninvoicePojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CifninvoicePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param cifninvoicePojo 实例对象
     * @return 实例对象
     */
    CifninvoicePojo insert(CifninvoicePojo cifninvoicePojo);

    /**
     * 修改数据
     *
     * @param cifninvoicepojo 实例对象
     * @return 实例对象
     */
    CifninvoicePojo update(CifninvoicePojo cifninvoicepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);




}
