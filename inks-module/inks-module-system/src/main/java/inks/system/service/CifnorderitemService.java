package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CifnorderitemPojo;
import inks.system.domain.CifnorderitemEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 订单明细(Cifnorderitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-09 20:42:37
 */
public interface CifnorderitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CifnorderitemPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CifnorderitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<CifnorderitemPojo> getList(String Pid);
    
    /**
     * 新增数据
     *
     * @param cifnorderitemPojo 实例对象
     * @return 实例对象
     */
    CifnorderitemPojo insert(CifnorderitemPojo cifnorderitemPojo);

    /**
     * 修改数据
     *
     * @param cifnorderitempojo 实例对象
     * @return 实例对象
     */
    CifnorderitemPojo update(CifnorderitemPojo cifnorderitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

     /**
     * 修改数据
     *
     * @param cifnorderitempojo 实例对象
     * @return 实例对象
     */
    CifnorderitemPojo clearNull(CifnorderitemPojo cifnorderitempojo);
}
