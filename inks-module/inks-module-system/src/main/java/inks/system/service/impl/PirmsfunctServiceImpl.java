package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PirmsfunctEntity;
import inks.system.domain.pojo.PirmsfunctPojo;
import inks.system.mapper.PirmsfunctMapper;
import inks.system.service.PirmsfunctService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * RMS功能(Pirmsfunct)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@Service("pirmsfunctService")
public class PirmsfunctServiceImpl implements PirmsfunctService {
    @Resource
    private PirmsfunctMapper pirmsfunctMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PirmsfunctPojo getEntity(String key) {
        return this.pirmsfunctMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PirmsfunctPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PirmsfunctPojo> lst = pirmsfunctMapper.getPageList(queryParam);
            PageInfo<PirmsfunctPojo> pageInfo = new PageInfo<PirmsfunctPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param pirmsfunctPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PirmsfunctPojo insert(PirmsfunctPojo pirmsfunctPojo) {
        //初始化NULL字段
        if (pirmsfunctPojo.getRmsfunctcode() == null) pirmsfunctPojo.setRmsfunctcode("");
        if (pirmsfunctPojo.getRmsfunctname() == null) pirmsfunctPojo.setRmsfunctname("");
        if (pirmsfunctPojo.getDescription() == null) pirmsfunctPojo.setDescription("");
        if (pirmsfunctPojo.getFunctionid() == null) pirmsfunctPojo.setFunctionid("");
        if (pirmsfunctPojo.getFunctioncode() == null) pirmsfunctPojo.setFunctioncode("");
        if (pirmsfunctPojo.getFunctionname() == null) pirmsfunctPojo.setFunctionname("");
        if (pirmsfunctPojo.getEnabledmark() == null) pirmsfunctPojo.setEnabledmark(0);
        if (pirmsfunctPojo.getRownum() == null) pirmsfunctPojo.setRownum(0);
        if (pirmsfunctPojo.getRemark() == null) pirmsfunctPojo.setRemark("");
        if (pirmsfunctPojo.getCreateby() == null) pirmsfunctPojo.setCreateby("");
        if (pirmsfunctPojo.getCreatebyid() == null) pirmsfunctPojo.setCreatebyid("");
        if (pirmsfunctPojo.getCreatedate() == null) pirmsfunctPojo.setCreatedate(new Date());
        if (pirmsfunctPojo.getLister() == null) pirmsfunctPojo.setLister("");
        if (pirmsfunctPojo.getListerid() == null) pirmsfunctPojo.setListerid("");
        if (pirmsfunctPojo.getModifydate() == null) pirmsfunctPojo.setModifydate(new Date());
        if (pirmsfunctPojo.getRevision() == null) pirmsfunctPojo.setRevision(0);
        PirmsfunctEntity pirmsfunctEntity = new PirmsfunctEntity();
        BeanUtils.copyProperties(pirmsfunctPojo, pirmsfunctEntity);
        //生成雪花id
        pirmsfunctEntity.setRmsfunctid(inksSnowflake.getSnowflake().nextIdStr());
        pirmsfunctEntity.setRevision(1);  //乐观锁
        this.pirmsfunctMapper.insert(pirmsfunctEntity);
        return this.getEntity(pirmsfunctEntity.getRmsfunctid());

    }

    /**
     * 修改数据
     *
     * @param pirmsfunctPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PirmsfunctPojo update(PirmsfunctPojo pirmsfunctPojo) {
        PirmsfunctEntity pirmsfunctEntity = new PirmsfunctEntity();
        BeanUtils.copyProperties(pirmsfunctPojo, pirmsfunctEntity);
        this.pirmsfunctMapper.update(pirmsfunctEntity);
        return this.getEntity(pirmsfunctEntity.getRmsfunctid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pirmsfunctMapper.delete(key);
    }


}
