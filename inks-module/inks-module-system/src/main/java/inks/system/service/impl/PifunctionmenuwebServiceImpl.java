package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.pojo.PifunctionmenuappPojo;
import inks.system.domain.pojo.PifunctionmenuwebPojo;
import inks.system.domain.PifunctionmenuwebEntity;
import inks.system.mapper.PifunctionmenuwebMapper;
import inks.system.service.PifunctionmenuwebService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 服务菜单关系(Pifunctionmenuweb)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-11 17:34:49
 */
@Service("pifunctionmenuwebService")
public class PifunctionmenuwebServiceImpl implements PifunctionmenuwebService {
    @Resource
    private PifunctionmenuwebMapper pifunctionmenuwebMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PifunctionmenuwebPojo getEntity(String key, String tid) {
        return this.pifunctionmenuwebMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PifunctionmenuwebPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PifunctionmenuwebPojo> lst = pifunctionmenuwebMapper.getPageList(queryParam);
            PageInfo<PifunctionmenuwebPojo> pageInfo = new PageInfo<PifunctionmenuwebPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param pifunctionmenuwebPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PifunctionmenuwebPojo insert(PifunctionmenuwebPojo pifunctionmenuwebPojo) {
    //初始化NULL字段
     if(pifunctionmenuwebPojo.getFunctionid()==null) pifunctionmenuwebPojo.setFunctionid("");
     if(pifunctionmenuwebPojo.getFunctioncode()==null) pifunctionmenuwebPojo.setFunctioncode("");
     if(pifunctionmenuwebPojo.getFunctionname()==null) pifunctionmenuwebPojo.setFunctionname("");
     if(pifunctionmenuwebPojo.getNavid()==null) pifunctionmenuwebPojo.setNavid("");
     if(pifunctionmenuwebPojo.getNavcode()==null) pifunctionmenuwebPojo.setNavcode("");
     if(pifunctionmenuwebPojo.getNavname()==null) pifunctionmenuwebPojo.setNavname("");
     if(pifunctionmenuwebPojo.getLister()==null) pifunctionmenuwebPojo.setLister("");
     if(pifunctionmenuwebPojo.getCreatedate()==null) pifunctionmenuwebPojo.setCreatedate(new Date());
     if(pifunctionmenuwebPojo.getModifydate()==null) pifunctionmenuwebPojo.setModifydate(new Date());
     if(pifunctionmenuwebPojo.getRemark()==null) pifunctionmenuwebPojo.setRemark("");
     if(pifunctionmenuwebPojo.getCreateby()==null) pifunctionmenuwebPojo.setCreateby("");
     if(pifunctionmenuwebPojo.getTenantid()==null) pifunctionmenuwebPojo.setTenantid("");
     if(pifunctionmenuwebPojo.getRevision()==null) pifunctionmenuwebPojo.setRevision(0);
        PifunctionmenuwebEntity pifunctionmenuwebEntity = new PifunctionmenuwebEntity(); 
        BeanUtils.copyProperties(pifunctionmenuwebPojo,pifunctionmenuwebEntity);
        
          pifunctionmenuwebEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          pifunctionmenuwebEntity.setRevision(1);  //乐观锁
          this.pifunctionmenuwebMapper.insert(pifunctionmenuwebEntity);
        return this.getEntity(pifunctionmenuwebEntity.getId(),pifunctionmenuwebEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param pifunctionmenuwebPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PifunctionmenuwebPojo update(PifunctionmenuwebPojo pifunctionmenuwebPojo) {
        PifunctionmenuwebEntity pifunctionmenuwebEntity = new PifunctionmenuwebEntity(); 
        BeanUtils.copyProperties(pifunctionmenuwebPojo,pifunctionmenuwebEntity);
        this.pifunctionmenuwebMapper.update(pifunctionmenuwebEntity);
        return this.getEntity(pifunctionmenuwebEntity.getId(),pifunctionmenuwebEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.pifunctionmenuwebMapper.delete(key,tid) ;
    }

    @Override
    public List<PifunctionmenuwebPojo> getListByFunction(String key){
        return this.pifunctionmenuwebMapper.getListByFunction(key);
    }
}
