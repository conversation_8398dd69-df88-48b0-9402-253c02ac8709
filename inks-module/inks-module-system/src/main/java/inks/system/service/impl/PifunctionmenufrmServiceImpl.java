package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.system.domain.pojo.PifunctionmenufrmPojo;
import inks.system.domain.PifunctionmenufrmEntity;
import inks.system.domain.pojo.PifunctionmenuwebPojo;
import inks.system.mapper.PifunctionmenufrmMapper;
import inks.system.service.PifunctionmenufrmService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 服务Frm关系(Pifunctionmenufrm)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-05 16:28:10
 */
@Service("pifunctionmenufrmService")
public class PifunctionmenufrmServiceImpl implements PifunctionmenufrmService {
    @Resource
    private PifunctionmenufrmMapper pifunctionmenufrmMapper;

    @Override
    public PifunctionmenufrmPojo getEntity(String key, String tid) {
        return this.pifunctionmenufrmMapper.getEntity(key,tid);
    }


    @Override
    public PageInfo<PifunctionmenufrmPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PifunctionmenufrmPojo> lst = pifunctionmenufrmMapper.getPageList(queryParam);
            PageInfo<PifunctionmenufrmPojo> pageInfo = new PageInfo<PifunctionmenufrmPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public PifunctionmenufrmPojo insert(PifunctionmenufrmPojo pifunctionmenufrmPojo) {
        //初始化NULL字段
        cleanNull(pifunctionmenufrmPojo);
        PifunctionmenufrmEntity pifunctionmenufrmEntity = new PifunctionmenufrmEntity(); 
        BeanUtils.copyProperties(pifunctionmenufrmPojo,pifunctionmenufrmEntity);
          //生成雪花id
          pifunctionmenufrmEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          pifunctionmenufrmEntity.setRevision(1);  //乐观锁
          this.pifunctionmenufrmMapper.insert(pifunctionmenufrmEntity);
        return this.getEntity(pifunctionmenufrmEntity.getId(),pifunctionmenufrmEntity.getTenantid());
    }


    @Override
    public PifunctionmenufrmPojo update(PifunctionmenufrmPojo pifunctionmenufrmPojo) {
        PifunctionmenufrmEntity pifunctionmenufrmEntity = new PifunctionmenufrmEntity(); 
        BeanUtils.copyProperties(pifunctionmenufrmPojo,pifunctionmenufrmEntity);
        this.pifunctionmenufrmMapper.update(pifunctionmenufrmEntity);
        return this.getEntity(pifunctionmenufrmEntity.getId(),pifunctionmenufrmEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.pifunctionmenufrmMapper.delete(key,tid) ;
    }
    

    private static void cleanNull(PifunctionmenufrmPojo pifunctionmenufrmPojo) {
        if(pifunctionmenufrmPojo.getFunctionid()==null) pifunctionmenufrmPojo.setFunctionid("");
        if(pifunctionmenufrmPojo.getFunctioncode()==null) pifunctionmenufrmPojo.setFunctioncode("");
        if(pifunctionmenufrmPojo.getFunctionname()==null) pifunctionmenufrmPojo.setFunctionname("");
        if(pifunctionmenufrmPojo.getNavid()==null) pifunctionmenufrmPojo.setNavid("");
        if(pifunctionmenufrmPojo.getNavcode()==null) pifunctionmenufrmPojo.setNavcode("");
        if(pifunctionmenufrmPojo.getNavname()==null) pifunctionmenufrmPojo.setNavname("");
        if(pifunctionmenufrmPojo.getRemark()==null) pifunctionmenufrmPojo.setRemark("");
        if(pifunctionmenufrmPojo.getCreateby()==null) pifunctionmenufrmPojo.setCreateby("");
        if(pifunctionmenufrmPojo.getCreatebyid()==null) pifunctionmenufrmPojo.setCreatebyid("");
        if(pifunctionmenufrmPojo.getCreatedate()==null) pifunctionmenufrmPojo.setCreatedate(new Date());
        if(pifunctionmenufrmPojo.getLister()==null) pifunctionmenufrmPojo.setLister("");
        if(pifunctionmenufrmPojo.getListerid()==null) pifunctionmenufrmPojo.setListerid("");
        if(pifunctionmenufrmPojo.getModifydate()==null) pifunctionmenufrmPojo.setModifydate(new Date());
        if(pifunctionmenufrmPojo.getTenantid()==null) pifunctionmenufrmPojo.setTenantid("");
        if(pifunctionmenufrmPojo.getRevision()==null) pifunctionmenufrmPojo.setRevision(0);
   }

    @Override
    public List<PifunctionmenuwebPojo> getListByFunction(String key) {
        return this.pifunctionmenufrmMapper.getListByFunction(key);
    }
}
