package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PifunctionconfigPojo;

import java.util.List;

/**
 * 服务参数关系(Pifunctionconfig)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-25 11:20:10
 */
public interface PifunctionconfigService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionconfigPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PifunctionconfigPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pifunctionconfigPojo 实例对象
     * @return 实例对象
     */
    PifunctionconfigPojo insert(PifunctionconfigPojo pifunctionconfigPojo);

    /**
     * 修改数据
     *
     * @param pifunctionconfigpojo 实例对象
     * @return 实例对象
     */
    PifunctionconfigPojo update(PifunctionconfigPojo pifunctionconfigpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);


    List<PifunctionconfigPojo> getListByFunction(String key);
}
