package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiscenePojo;

import java.util.List;

/**
 * 场景管理(Ciscene)表服务接口
 *
 * <AUTHOR>
 * @since 2022-11-24 11:34:52
 */
public interface CisceneService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiscenePojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CiscenePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param ciscenePojo 实例对象
     * @return 实例对象
     */
    CiscenePojo insert(CiscenePojo ciscenePojo);

    /**
     * 修改数据
     *
     * @param ciscenepojo 实例对象
     * @return 实例对象
     */
    CiscenePojo update(CiscenePojo ciscenepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    List<CiscenePojo> getListByCode(String code ,String userid,String tid);
}
