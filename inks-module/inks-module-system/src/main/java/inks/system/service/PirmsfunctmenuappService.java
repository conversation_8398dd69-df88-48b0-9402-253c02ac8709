package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PirmsfunctmenuappPojo;
import inks.system.domain.pojo.PimenuappPojo;

import java.util.List;

/**
 * RMSAPP关系(Pirmsfunctmenuapp)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
public interface PirmsfunctmenuappService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PirmsfunctmenuappPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PirmsfunctmenuappPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pirmsfunctmenuappPojo 实例对象
     * @return 实例对象
     */
    PirmsfunctmenuappPojo insert(PirmsfunctmenuappPojo pirmsfunctmenuappPojo);

    /**
     * 修改数据
     *
     * @param pirmsfunctmenuapppojo 实例对象
     * @return 实例对象
     */
    PirmsfunctmenuappPojo update(PirmsfunctmenuappPojo pirmsfunctmenuapppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<PirmsfunctmenuappPojo> getListByFunction(String key);

    List<PimenuappPojo> getListByRmsFunctids(String rmsfunctids);
}
