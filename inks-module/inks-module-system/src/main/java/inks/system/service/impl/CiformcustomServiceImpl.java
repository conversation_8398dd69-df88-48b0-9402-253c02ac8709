package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.pojo.CiformcustomPojo;
import inks.system.domain.CiformcustomEntity;
import inks.system.mapper.CiformcustomMapper;
import inks.system.service.CiformcustomService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 自定义界面(Ciformcustom)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-06 12:58:00
 */
@Service("ciformcustomService")
public class CiformcustomServiceImpl implements CiformcustomService {
    @Resource
    private CiformcustomMapper ciformcustomMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CiformcustomPojo getEntity(String key, String tid) {
        return this.ciformcustomMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CiformcustomPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiformcustomPojo> lst = ciformcustomMapper.getPageList(queryParam);
            PageInfo<CiformcustomPojo> pageInfo = new PageInfo<CiformcustomPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param ciformcustomPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiformcustomPojo insert(CiformcustomPojo ciformcustomPojo) {
        //初始化NULL字段
        if (ciformcustomPojo.getGengroupid() == null) ciformcustomPojo.setGengroupid("");
        if (ciformcustomPojo.getModulecode() == null) ciformcustomPojo.setModulecode("");
        if (ciformcustomPojo.getFrmcode() == null) ciformcustomPojo.setFrmcode("");
        if (ciformcustomPojo.getFrmname() == null) ciformcustomPojo.setFrmname("");
        if (ciformcustomPojo.getFrmcontent() == null) ciformcustomPojo.setFrmcontent("");
        if (ciformcustomPojo.getRownum() == null) ciformcustomPojo.setRownum(0);
        if (ciformcustomPojo.getEnabledmark() == null) ciformcustomPojo.setEnabledmark(0);
        if (ciformcustomPojo.getRemark() == null) ciformcustomPojo.setRemark("");
        if (ciformcustomPojo.getCreateby() == null) ciformcustomPojo.setCreateby("");
        if (ciformcustomPojo.getCreatebyid() == null) ciformcustomPojo.setCreatebyid("");
        if (ciformcustomPojo.getCreatedate() == null) ciformcustomPojo.setCreatedate(new Date());
        if (ciformcustomPojo.getLister() == null) ciformcustomPojo.setLister("");
        if (ciformcustomPojo.getListerid() == null) ciformcustomPojo.setListerid("");
        if (ciformcustomPojo.getModifydate() == null) ciformcustomPojo.setModifydate(new Date());
        if (ciformcustomPojo.getCustom1() == null) ciformcustomPojo.setCustom1("");
        if (ciformcustomPojo.getCustom2() == null) ciformcustomPojo.setCustom2("");
        if (ciformcustomPojo.getCustom3() == null) ciformcustomPojo.setCustom3("");
        if (ciformcustomPojo.getCustom4() == null) ciformcustomPojo.setCustom4("");
        if (ciformcustomPojo.getCustom5() == null) ciformcustomPojo.setCustom5("");
        if (ciformcustomPojo.getTenantid() == null) ciformcustomPojo.setTenantid("");
        if (ciformcustomPojo.getTenantname() == null) ciformcustomPojo.setTenantname("");
        if (ciformcustomPojo.getRevision() == null) ciformcustomPojo.setRevision(0);
        CiformcustomEntity ciformcustomEntity = new CiformcustomEntity();
        BeanUtils.copyProperties(ciformcustomPojo, ciformcustomEntity);

        ciformcustomEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        ciformcustomEntity.setRevision(1);  //乐观锁
        this.ciformcustomMapper.insert(ciformcustomEntity);
        return this.getEntity(ciformcustomEntity.getId(), ciformcustomEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param ciformcustomPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiformcustomPojo update(CiformcustomPojo ciformcustomPojo) {
        CiformcustomEntity ciformcustomEntity = new CiformcustomEntity();
        BeanUtils.copyProperties(ciformcustomPojo, ciformcustomEntity);
        this.ciformcustomMapper.update(ciformcustomEntity);
        return this.getEntity(ciformcustomEntity.getId(), ciformcustomEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.ciformcustomMapper.delete(key, tid);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CiformcustomPojo getEntityByCode(String key, String tid) {
        return this.ciformcustomMapper.getEntityByCode(key, tid);
    }
}
