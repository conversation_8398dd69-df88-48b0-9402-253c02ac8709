package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.AESUtil;
import inks.system.domain.PiadminloginEntity;
import inks.system.domain.pojo.PiadminPojo;
import inks.system.domain.PiadminEntity;
import inks.system.domain.pojo.PiadminloginPojo;
import inks.system.mapper.PiadminMapper;
import inks.system.mapper.PiadminloginMapper;
import inks.system.service.PiadminService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 平台管理员(Piadmin)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-08 13:55:55
 */
@Service("piadminService")
public class PiadminServiceImpl implements PiadminService {
    @Resource
    private PiadminMapper piadminMapper;

    @Resource
    private PiadminloginMapper piadminloginMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PiadminPojo getEntity(String key) {
        return this.piadminMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PiadminPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PiadminPojo> lst = piadminMapper.getPageList(queryParam);
            PageInfo<PiadminPojo> pageInfo = new PageInfo<PiadminPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param piadminPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiadminPojo insert(PiadminPojo piadminPojo) {
        //初始化NULL字段
        if (piadminPojo.getUsername() == null) piadminPojo.setUsername("");
        if (piadminPojo.getRealname() == null) piadminPojo.setRealname("");
        if (piadminPojo.getMobile() == null) piadminPojo.setMobile("");
        if (piadminPojo.getEmail() == null) piadminPojo.setEmail("");
        if (piadminPojo.getLangcode() == null) piadminPojo.setLangcode("");
        if (piadminPojo.getAvatar() == null) piadminPojo.setAvatar("");
        if (piadminPojo.getUserstatus() == null) piadminPojo.setUserstatus(0);
        if (piadminPojo.getAdmincode() == null) piadminPojo.setAdmincode("");
        if (piadminPojo.getRemark() == null) piadminPojo.setRemark("");
        if (piadminPojo.getWxopenid() == null) piadminPojo.setWxopenid("");
        if (piadminPojo.getRoletype() == null) piadminPojo.setRoletype(0);
        if (piadminPojo.getLister() == null) piadminPojo.setLister("");
        if (piadminPojo.getCreatedate() == null) piadminPojo.setCreatedate(new Date());
        if (piadminPojo.getModifydate() == null) piadminPojo.setModifydate(new Date());
        PiadminEntity piadminEntity = new PiadminEntity();
        BeanUtils.copyProperties(piadminPojo, piadminEntity);

        piadminEntity.setAdminid(inksSnowflake.getSnowflake().nextIdStr());
        this.piadminMapper.insert(piadminEntity);

        PiadminloginEntity piadminloginEntity = new PiadminloginEntity();
        //初始化NULL字段
        piadminloginEntity.setAdminid(piadminEntity.getAdminid());
        try {
            piadminloginEntity.setUserpassword(AESUtil.Encrypt(piadminPojo.getPassword()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        piadminloginEntity.setCheckipaddr(0);
        piadminloginEntity.setIpaddress("");
        piadminloginEntity.setMacaddress("");
        piadminloginEntity.setFirstvisit(new Date());
        piadminloginEntity.setPreviouvisit(new Date());
        piadminloginEntity.setBrowsername("");
        piadminloginEntity.setHostsystem("");
        piadminloginEntity.setLister(piadminPojo.getLister());
        piadminloginEntity.setCreatedate(new Date());
        piadminloginEntity.setModifydate(new Date());
        piadminloginEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.piadminloginMapper.insert(piadminloginEntity);
        return this.getEntity(piadminEntity.getAdminid());

    }

    /**
     * 修改数据
     *
     * @param piadminPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiadminPojo update(PiadminPojo piadminPojo) {
        PiadminEntity piadminEntity = new PiadminEntity();
        BeanUtils.copyProperties(piadminPojo, piadminEntity);
        this.piadminMapper.update(piadminEntity);
        return this.getEntity(piadminEntity.getAdminid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.piadminMapper.delete(key);
    }

    /**
     * 通过用户id初始化密码
     *
     * @param piadminpojo 主键
     * @return 是否成功
     */
    @Override
    public  int initpassword(PiadminPojo piadminpojo) {
        return this.piadminMapper.initpassword(piadminpojo);
    }
}
