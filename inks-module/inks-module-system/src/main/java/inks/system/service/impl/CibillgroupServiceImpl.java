package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.constant.InksConstants;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CibillgroupEntity;
import inks.system.domain.pojo.CibillgroupPojo;
import inks.system.mapper.CibillgroupMapper;
import inks.system.service.CibillgroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 通用分组(Cibillgroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-08 13:28:26
 */
@Service("cibillgroupService")
public class CibillgroupServiceImpl implements CibillgroupService {
    @Resource
    private CibillgroupMapper cibillgroupMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CibillgroupPojo getEntity(String key, String tid) {
        return this.cibillgroupMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CibillgroupPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CibillgroupPojo> lst = cibillgroupMapper.getPageList(queryParam);
            PageInfo<CibillgroupPojo> pageInfo = new PageInfo<CibillgroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param cibillgroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CibillgroupPojo insert(CibillgroupPojo cibillgroupPojo) {
        //初始化NULL字段
        if (cibillgroupPojo.getParentid() == null) cibillgroupPojo.setParentid("");
        if (cibillgroupPojo.getModulecode() == null) cibillgroupPojo.setModulecode("");
        if (cibillgroupPojo.getGroupcode() == null) cibillgroupPojo.setGroupcode("");
        if (cibillgroupPojo.getGroupname() == null) cibillgroupPojo.setGroupname("");
        if (cibillgroupPojo.getEnabledmark() == null) cibillgroupPojo.setEnabledmark(0);
        if (cibillgroupPojo.getRownum() == null) cibillgroupPojo.setRownum(0);
        if (cibillgroupPojo.getRemark() == null) cibillgroupPojo.setRemark("");
        if (cibillgroupPojo.getLister() == null) cibillgroupPojo.setLister("");
        if (cibillgroupPojo.getCreatedate() == null) cibillgroupPojo.setCreatedate(new Date());
        if (cibillgroupPojo.getModifydate() == null) cibillgroupPojo.setModifydate(new Date());
        if (cibillgroupPojo.getTenantid() == null) cibillgroupPojo.setTenantid("");
        CibillgroupEntity cibillgroupEntity = new CibillgroupEntity();
        BeanUtils.copyProperties(cibillgroupPojo, cibillgroupEntity);
        cibillgroupEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.cibillgroupMapper.insert(cibillgroupEntity);
        return this.getEntity(cibillgroupEntity.getId(), cibillgroupEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param cibillgroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CibillgroupPojo update(CibillgroupPojo cibillgroupPojo) {
        CibillgroupEntity cibillgroupEntity = new CibillgroupEntity();
        BeanUtils.copyProperties(cibillgroupPojo, cibillgroupEntity);
        this.cibillgroupMapper.update(cibillgroupEntity);
        return this.getEntity(cibillgroupEntity.getId(), cibillgroupEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.cibillgroupMapper.delete(key, tid);
    }


    /**
     * 通过功能号获得分组
     *
     * @param moduleCode 主键
     * @return 是否成功
     */
    @Override
    public List<CibillgroupPojo> getListByModuleCode(String moduleCode, String tid) {
        try {
            //自定义报表
            List<CibillgroupPojo> lst = cibillgroupMapper.getListByModuleCode(moduleCode, tid);
            if (lst == null) {
                //默认格式
                lst = cibillgroupMapper.getListByModuleCode(moduleCode, InksConstants.DEFAULT_TENANT);
            }
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }
}
