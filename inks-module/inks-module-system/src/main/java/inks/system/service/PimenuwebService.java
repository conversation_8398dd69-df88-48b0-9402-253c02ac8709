package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PimenuappPojo;
import inks.system.domain.pojo.PimenuwebPojo;
import inks.system.domain.PimenuwebEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 后台导航(Pimenuweb)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-08 14:23:19
 */
public interface PimenuwebService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PimenuwebPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PimenuwebPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pimenuwebPojo 实例对象
     * @return 实例对象
     */
    PimenuwebPojo insert(PimenuwebPojo pimenuwebPojo);

    /**
     * 修改数据
     *
     * @param pimenuwebpojo 实例对象
     * @return 实例对象
     */
    PimenuwebPojo update(PimenuwebPojo pimenuwebpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    List<PimenuwebPojo> getListByPid(String key);

    List<PimenuwebPojo> getListByNavids(List<String> navids);
}
