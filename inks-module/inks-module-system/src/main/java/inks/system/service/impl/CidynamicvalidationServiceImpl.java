package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.system.domain.pojo.CidynamicvalidationPojo;
import inks.system.domain.pojo.CidynamicvalidationitemPojo;
import inks.system.domain.pojo.CidynamicvalidationitemdetailPojo;
import inks.system.domain.CidynamicvalidationEntity;
import inks.system.domain.CidynamicvalidationitemEntity;
import inks.system.mapper.CidynamicvalidationMapper;
import inks.system.service.CidynamicvalidationService;
import inks.system.service.CidynamicvalidationitemService;
import inks.system.mapper.CidynamicvalidationitemMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 动态校验规则(Cidynamicvalidation)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-04 15:54:38
 */
@Service("cidynamicvalidationService")
public class CidynamicvalidationServiceImpl implements CidynamicvalidationService {
    @Resource
    private CidynamicvalidationMapper cidynamicvalidationMapper;
    
    @Resource
    private CidynamicvalidationitemMapper cidynamicvalidationitemMapper;
    

    @Resource
    private CidynamicvalidationitemService cidynamicvalidationitemService;

    @Override
    public CidynamicvalidationPojo getEntity(String key, String tid) {
        return this.cidynamicvalidationMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<CidynamicvalidationitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CidynamicvalidationitemdetailPojo> lst = cidynamicvalidationMapper.getPageList(queryParam);
            PageInfo<CidynamicvalidationitemdetailPojo> pageInfo = new PageInfo<CidynamicvalidationitemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public CidynamicvalidationPojo getBillEntity(String key, String tid) {
       try {
           //读取主表
           CidynamicvalidationPojo cidynamicvalidationPojo = this.cidynamicvalidationMapper.getEntity(key,tid);
           //读取子表
           cidynamicvalidationPojo.setItem(cidynamicvalidationitemMapper.getList(cidynamicvalidationPojo.getId(),tid));
           return cidynamicvalidationPojo;
       }catch (Exception e){
          throw new BaseBusinessException(e.getMessage());
       } 
    }


    @Override
    public PageInfo<CidynamicvalidationPojo> getBillList(QueryParam queryParam) {
        String tid = queryParam.getTenantid();
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CidynamicvalidationPojo> lst = cidynamicvalidationMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(CidynamicvalidationPojo item : lst){
                item.setItem(cidynamicvalidationitemMapper.getList(item.getId(), tid));
            }
            PageInfo<CidynamicvalidationPojo> pageInfo = new PageInfo<CidynamicvalidationPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public PageInfo<CidynamicvalidationPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CidynamicvalidationPojo> lst = cidynamicvalidationMapper.getPageTh(queryParam);
            PageInfo<CidynamicvalidationPojo> pageInfo = new PageInfo<CidynamicvalidationPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    @Transactional
    public CidynamicvalidationPojo insert(CidynamicvalidationPojo cidynamicvalidationPojo) {
    String tid = cidynamicvalidationPojo.getTenantid();
        //初始化NULL字段
        cleanNull(cidynamicvalidationPojo);
         //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        CidynamicvalidationEntity cidynamicvalidationEntity = new CidynamicvalidationEntity(); 
        BeanUtils.copyProperties(cidynamicvalidationPojo,cidynamicvalidationEntity);
      
        //设置id和新建日期
        cidynamicvalidationEntity.setId(id);
        cidynamicvalidationEntity.setRevision(1);  //乐观锁
        //插入主表
        this.cidynamicvalidationMapper.insert(cidynamicvalidationEntity);
        //Item子表处理
        List<CidynamicvalidationitemPojo> lst = cidynamicvalidationPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for(CidynamicvalidationitemPojo item : lst){
               //初始化item的NULL
               CidynamicvalidationitemPojo itemPojo =this.cidynamicvalidationitemService.clearNull(item);
               CidynamicvalidationitemEntity cidynamicvalidationitemEntity = new CidynamicvalidationitemEntity(); 
               BeanUtils.copyProperties(itemPojo,cidynamicvalidationitemEntity);
               //设置id和Pid
               cidynamicvalidationitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               cidynamicvalidationitemEntity.setPid(id);
               cidynamicvalidationitemEntity.setTenantid(tid);
               cidynamicvalidationitemEntity.setRevision(1);  //乐观锁
               //插入子表
               this.cidynamicvalidationitemMapper.insert(cidynamicvalidationitemEntity);
            }
            
        } 
        //返回Bill实例
        return this.getBillEntity(cidynamicvalidationEntity.getId(),tid);
    }


    @Override
    @Transactional
    public CidynamicvalidationPojo update(CidynamicvalidationPojo cidynamicvalidationPojo) {
        String tid = cidynamicvalidationPojo.getTenantid();
        //主表更改
        CidynamicvalidationEntity cidynamicvalidationEntity = new CidynamicvalidationEntity(); 
        BeanUtils.copyProperties(cidynamicvalidationPojo,cidynamicvalidationEntity);
        this.cidynamicvalidationMapper.update(cidynamicvalidationEntity);
        if (cidynamicvalidationPojo.getItem() != null) {
        //Item子表处理
        List<CidynamicvalidationitemPojo> lst = cidynamicvalidationPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =cidynamicvalidationMapper.getDelItemIds(cidynamicvalidationPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for (String delId : lstDelIds) {
             this.cidynamicvalidationitemMapper.delete(delId, tid);
            }
        }
        if (lst != null){
            //循环每个item子表
            for(CidynamicvalidationitemPojo item : lst){
               CidynamicvalidationitemEntity cidynamicvalidationitemEntity = new CidynamicvalidationitemEntity(); 
               if ("".equals(item.getId()) || item.getId() == null){
                //初始化item的NULL
               CidynamicvalidationitemPojo itemPojo =this.cidynamicvalidationitemService.clearNull(item);
               BeanUtils.copyProperties(itemPojo,cidynamicvalidationitemEntity);
               //设置id和Pid
               cidynamicvalidationitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               cidynamicvalidationitemEntity.setPid(cidynamicvalidationEntity.getId());  // 主表 id
               cidynamicvalidationitemEntity.setTenantid(tid);   // 租户id
               cidynamicvalidationitemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.cidynamicvalidationitemMapper.insert(cidynamicvalidationitemEntity);
               }
               else
               {
               BeanUtils.copyProperties(item,cidynamicvalidationitemEntity);       
                cidynamicvalidationitemEntity.setTenantid(tid);        
               this.cidynamicvalidationitemMapper.update(cidynamicvalidationitemEntity);
               }
            }
        } 
        }
        //返回Bill实例
        return this.getBillEntity(cidynamicvalidationEntity.getId(),tid);
    }

    @Override
    @Transactional
    public int delete(String key, String tid) {
       CidynamicvalidationPojo cidynamicvalidationPojo =  this.getBillEntity(key,tid);
        //Item子表处理
        List<CidynamicvalidationitemPojo> lst = cidynamicvalidationPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for(CidynamicvalidationitemPojo item : lst){
              this.cidynamicvalidationitemMapper.delete(item.getId(),tid);
            }
        }        
        return this.cidynamicvalidationMapper.delete(key,tid) ;
    }
    

    

    private static void cleanNull(CidynamicvalidationPojo cidynamicvalidationPojo) {
        if(cidynamicvalidationPojo.getValidationcode()==null) cidynamicvalidationPojo.setValidationcode("");
        if(cidynamicvalidationPojo.getValidationtitle()==null) cidynamicvalidationPojo.setValidationtitle("");
        if(cidynamicvalidationPojo.getSummary()==null) cidynamicvalidationPojo.setSummary("");
        if(cidynamicvalidationPojo.getCreateby()==null) cidynamicvalidationPojo.setCreateby("");
        if(cidynamicvalidationPojo.getCreatebyid()==null) cidynamicvalidationPojo.setCreatebyid("");
        if(cidynamicvalidationPojo.getCreatedate()==null) cidynamicvalidationPojo.setCreatedate(new Date());
        if(cidynamicvalidationPojo.getLister()==null) cidynamicvalidationPojo.setLister("");
        if(cidynamicvalidationPojo.getListerid()==null) cidynamicvalidationPojo.setListerid("");
        if(cidynamicvalidationPojo.getModifydate()==null) cidynamicvalidationPojo.setModifydate(new Date());
        if(cidynamicvalidationPojo.getCustom1()==null) cidynamicvalidationPojo.setCustom1("");
        if(cidynamicvalidationPojo.getCustom2()==null) cidynamicvalidationPojo.setCustom2("");
        if(cidynamicvalidationPojo.getCustom3()==null) cidynamicvalidationPojo.setCustom3("");
        if(cidynamicvalidationPojo.getCustom4()==null) cidynamicvalidationPojo.setCustom4("");
        if(cidynamicvalidationPojo.getCustom5()==null) cidynamicvalidationPojo.setCustom5("");
        if(cidynamicvalidationPojo.getCustom6()==null) cidynamicvalidationPojo.setCustom6("");
        if(cidynamicvalidationPojo.getCustom7()==null) cidynamicvalidationPojo.setCustom7("");
        if(cidynamicvalidationPojo.getCustom8()==null) cidynamicvalidationPojo.setCustom8("");
        if(cidynamicvalidationPojo.getCustom9()==null) cidynamicvalidationPojo.setCustom9("");
        if(cidynamicvalidationPojo.getCustom10()==null) cidynamicvalidationPojo.setCustom10("");
        if(cidynamicvalidationPojo.getTenantid()==null) cidynamicvalidationPojo.setTenantid("");
        if(cidynamicvalidationPojo.getRevision()==null) cidynamicvalidationPojo.setRevision(0);
   }

}
