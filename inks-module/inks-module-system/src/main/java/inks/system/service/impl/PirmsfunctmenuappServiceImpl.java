package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PirmsfunctmenuappEntity;
import inks.system.domain.pojo.PirmsfunctmenuappPojo;
import inks.system.domain.pojo.PimenuappPojo;
import inks.system.mapper.PirmsfunctmenuappMapper;
import inks.system.service.PirmsfunctmenuappService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * RMSAPP关系(Pirmsfunctmenuapp)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@Service("pirmsfunctmenuappService")
public class PirmsfunctmenuappServiceImpl implements PirmsfunctmenuappService {
    @Resource
    private PirmsfunctmenuappMapper pirmsfunctmenuappMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PirmsfunctmenuappPojo getEntity(String key) {
        return this.pirmsfunctmenuappMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PirmsfunctmenuappPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PirmsfunctmenuappPojo> lst = pirmsfunctmenuappMapper.getPageList(queryParam);
            PageInfo<PirmsfunctmenuappPojo> pageInfo = new PageInfo<PirmsfunctmenuappPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param pirmsfunctmenuappPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PirmsfunctmenuappPojo insert(PirmsfunctmenuappPojo pirmsfunctmenuappPojo) {
    //初始化NULL字段
     if(pirmsfunctmenuappPojo.getRmsfunctid()==null) pirmsfunctmenuappPojo.setRmsfunctid("");
     if(pirmsfunctmenuappPojo.getRmsfunctcode()==null) pirmsfunctmenuappPojo.setRmsfunctcode("");
     if(pirmsfunctmenuappPojo.getRmsfunctname()==null) pirmsfunctmenuappPojo.setRmsfunctname("");
     if(pirmsfunctmenuappPojo.getNavid()==null) pirmsfunctmenuappPojo.setNavid("");
     if(pirmsfunctmenuappPojo.getNavcode()==null) pirmsfunctmenuappPojo.setNavcode("");
     if(pirmsfunctmenuappPojo.getNavname()==null) pirmsfunctmenuappPojo.setNavname("");
     if(pirmsfunctmenuappPojo.getRemark()==null) pirmsfunctmenuappPojo.setRemark("");
     if(pirmsfunctmenuappPojo.getCreateby()==null) pirmsfunctmenuappPojo.setCreateby("");
     if(pirmsfunctmenuappPojo.getCreatebyid()==null) pirmsfunctmenuappPojo.setCreatebyid("");
     if(pirmsfunctmenuappPojo.getCreatedate()==null) pirmsfunctmenuappPojo.setCreatedate(new Date());
     if(pirmsfunctmenuappPojo.getLister()==null) pirmsfunctmenuappPojo.setLister("");
     if(pirmsfunctmenuappPojo.getListerid()==null) pirmsfunctmenuappPojo.setListerid("");
     if(pirmsfunctmenuappPojo.getModifydate()==null) pirmsfunctmenuappPojo.setModifydate(new Date());
     if(pirmsfunctmenuappPojo.getRevision()==null) pirmsfunctmenuappPojo.setRevision(0);
        PirmsfunctmenuappEntity pirmsfunctmenuappEntity = new PirmsfunctmenuappEntity(); 
        BeanUtils.copyProperties(pirmsfunctmenuappPojo,pirmsfunctmenuappEntity);
  //生成雪花id
          pirmsfunctmenuappEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          pirmsfunctmenuappEntity.setRevision(1);  //乐观锁
          this.pirmsfunctmenuappMapper.insert(pirmsfunctmenuappEntity);
        return this.getEntity(pirmsfunctmenuappEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param pirmsfunctmenuappPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PirmsfunctmenuappPojo update(PirmsfunctmenuappPojo pirmsfunctmenuappPojo) {
        PirmsfunctmenuappEntity pirmsfunctmenuappEntity = new PirmsfunctmenuappEntity(); 
        BeanUtils.copyProperties(pirmsfunctmenuappPojo,pirmsfunctmenuappEntity);
        this.pirmsfunctmenuappMapper.update(pirmsfunctmenuappEntity);
        return this.getEntity(pirmsfunctmenuappEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pirmsfunctmenuappMapper.delete(key) ;
    }

    @Override
    public List<PirmsfunctmenuappPojo> getListByFunction(String key) {
        return this.pirmsfunctmenuappMapper.getListByFunction(key);
    }

    @Override
    public List<PimenuappPojo> getListByRmsFunctids(String rmsfunctids) {
        return this.pirmsfunctmenuappMapper.getListByRmsFunctids(rmsfunctids);
    }
}
