package inks.system.service;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiselfcheckPojo;
import inks.system.domain.CiselfcheckEntity;

import com.github.pagehelper.PageInfo;
import inks.system.domain.pojo.PiuserPojo;

import java.util.List;

/**
 * 系统自检(Ciselfcheck)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-02 09:08:53
 */
public interface CiselfcheckService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiselfcheckPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CiselfcheckPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param ciselfcheckPojo 实例对象
     * @return 实例对象
     */
    CiselfcheckPojo insert(CiselfcheckPojo ciselfcheckPojo);

    /**
     * 修改数据
     *
     * @param ciselfcheckpojo 实例对象
     * @return 实例对象
     */
    CiselfcheckPojo update(CiselfcheckPojo ciselfcheckpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    String getFunctionidByTid(String tid);

    List<String> checkPasswordComplexity(String tid);

    List<String> checkExpiredMach(String tid);

    List<PiuserPojo> checkWeakPassword(Integer type, LoginUser loginUser);
}
