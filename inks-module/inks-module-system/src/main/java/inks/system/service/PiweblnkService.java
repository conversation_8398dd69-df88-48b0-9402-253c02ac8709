package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiweblnkPojo;
import inks.system.domain.PiweblnkEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 快捷方式(Piweblnk)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-08 14:11:24
 */
public interface PiweblnkService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiweblnkPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PiweblnkPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param piweblnkPojo 实例对象
     * @return 实例对象
     */
    PiweblnkPojo insert(PiweblnkPojo piweblnkPojo);

    /**
     * 修改数据
     *
     * @param piweblnkpojo 实例对象
     * @return 实例对象
     */
    PiweblnkPojo update(PiweblnkPojo piweblnkpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<PiweblnkPojo> getListByPid(String key);
}
