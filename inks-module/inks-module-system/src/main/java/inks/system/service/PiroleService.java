package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PirolePojo;
import inks.system.domain.PiroleEntity;

import com.github.pagehelper.PageInfo;

/**
 * 角色表(Pirole)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-17 10:24:53
 */
public interface PiroleService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PirolePojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PirolePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pirolePojo 实例对象
     * @return 实例对象
     */
    PirolePojo insert(PirolePojo pirolePojo);

    /**
     * 修改数据
     *
     * @param pirolepojo 实例对象
     * @return 实例对象
     */
    PirolePojo update(PirolePojo pirolepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);
                                                                                          }
