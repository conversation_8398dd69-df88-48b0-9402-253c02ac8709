package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.pojo.CiscenePojo;
import inks.system.domain.CisceneEntity;
import inks.system.mapper.CisceneMapper;
import inks.system.service.CisceneService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 场景管理(Ciscene)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-24 11:34:52
 */
@Service("cisceneService")
public class CisceneServiceImpl implements CisceneService {
    @Resource
    private CisceneMapper cisceneMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CiscenePojo getEntity(String key, String tid) {
        return this.cisceneMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CiscenePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiscenePojo> lst = cisceneMapper.getPageList(queryParam);
            PageInfo<CiscenePojo> pageInfo = new PageInfo<CiscenePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param ciscenePojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiscenePojo insert(CiscenePojo ciscenePojo) {
        //初始化NULL字段
        if (ciscenePojo.getModulecode() == null) ciscenePojo.setModulecode("");
        if (ciscenePojo.getScenename() == null) ciscenePojo.setScenename("");
        if (ciscenePojo.getScenedata() == null) ciscenePojo.setScenedata("");
        if (ciscenePojo.getRownum() == null) ciscenePojo.setRownum(0);
        if (ciscenePojo.getEnabledmark() == null) ciscenePojo.setEnabledmark(0);
        if (ciscenePojo.getRemark() == null) ciscenePojo.setRemark("");
        if (ciscenePojo.getCreateby() == null) ciscenePojo.setCreateby("");
        if (ciscenePojo.getCreatebyid() == null) ciscenePojo.setCreatebyid("");
        if (ciscenePojo.getCreatedate() == null) ciscenePojo.setCreatedate(new Date());
        if (ciscenePojo.getLister() == null) ciscenePojo.setLister("");
        if (ciscenePojo.getListerid() == null) ciscenePojo.setListerid("");
        if (ciscenePojo.getModifydate() == null) ciscenePojo.setModifydate(new Date());
        if (ciscenePojo.getCustom1() == null) ciscenePojo.setCustom1("");
        if (ciscenePojo.getCustom2() == null) ciscenePojo.setCustom2("");
        if (ciscenePojo.getCustom3() == null) ciscenePojo.setCustom3("");
        if (ciscenePojo.getCustom4() == null) ciscenePojo.setCustom4("");
        if (ciscenePojo.getCustom5() == null) ciscenePojo.setCustom5("");
        if (ciscenePojo.getTenantid() == null) ciscenePojo.setTenantid("");
        if (ciscenePojo.getTenantname() == null) ciscenePojo.setTenantname("");
        if (ciscenePojo.getRevision() == null) ciscenePojo.setRevision(0);
        CisceneEntity cisceneEntity = new CisceneEntity();
        BeanUtils.copyProperties(ciscenePojo, cisceneEntity);

        cisceneEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        cisceneEntity.setRevision(1);  //乐观锁
        this.cisceneMapper.insert(cisceneEntity);
        return this.getEntity(cisceneEntity.getId(), cisceneEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param ciscenePojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiscenePojo update(CiscenePojo ciscenePojo) {
        CisceneEntity cisceneEntity = new CisceneEntity();
        BeanUtils.copyProperties(ciscenePojo, cisceneEntity);
        this.cisceneMapper.update(cisceneEntity);
        return this.getEntity(cisceneEntity.getId(), cisceneEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.cisceneMapper.delete(key, tid);
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @Override
    public List<CiscenePojo> getListByCode(String code, String userid, String tid) {

        return this.cisceneMapper.getListByCode(code, userid, tid);
    }


}
