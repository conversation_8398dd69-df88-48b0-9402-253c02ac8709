package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.constant.InksConstants;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CidictEntity;
import inks.system.domain.CidictitemEntity;
import inks.system.domain.pojo.CidictPojo;
import inks.system.domain.pojo.CidictitemPojo;
import inks.system.domain.pojo.CidictitemdetailPojo;
import inks.system.mapper.CidictMapper;
import inks.system.mapper.CidictitemMapper;
import inks.system.service.CidictService;
import inks.system.service.CidictitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 数据字典(Cidict)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-12 07:38:33
 */
@Service("cidictService")
public class CidictServiceImpl implements CidictService {
    @Resource
    private CidictMapper cidictMapper;

    @Resource
    private CidictitemMapper cidictitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private CidictitemService cidictitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CidictPojo getEntity(String key) {
        return this.cidictMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CidictitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CidictitemdetailPojo> lst = cidictMapper.getPageList(queryParam);
            PageInfo<CidictitemdetailPojo> pageInfo = new PageInfo<CidictitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CidictPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            CidictPojo cidictPojo = this.cidictMapper.getEntity(key);
            //读取子表
            cidictPojo.setItem(cidictitemMapper.getList(cidictPojo.getId(), cidictPojo.getTenantid()));
            return cidictPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CidictPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CidictPojo> lst = cidictMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(cidictitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<CidictPojo> pageInfo = new PageInfo<CidictPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CidictPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CidictPojo> lst = cidictMapper.getPageTh(queryParam);
            PageInfo<CidictPojo> pageInfo = new PageInfo<CidictPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param cidictPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public CidictPojo insert(CidictPojo cidictPojo) {
//初始化NULL字段
        if (cidictPojo.getDictgroupid() == null) cidictPojo.setDictgroupid("");
        if (cidictPojo.getDictcode() == null) cidictPojo.setDictcode("");
        if (cidictPojo.getDictname() == null) cidictPojo.setDictname("");
        if (cidictPojo.getModulecode() == null) cidictPojo.setModulecode("");
        if (cidictPojo.getRownum() == null) cidictPojo.setRownum(0);
        if (cidictPojo.getEnabledmark() == null) cidictPojo.setEnabledmark(0);
        if (cidictPojo.getSummary() == null) cidictPojo.setSummary("");
        if (cidictPojo.getCreateby() == null) cidictPojo.setCreateby("");
        if (cidictPojo.getCreatebyid() == null) cidictPojo.setCreatebyid("");
        if (cidictPojo.getCreatedate() == null) cidictPojo.setCreatedate(new Date());
        if (cidictPojo.getLister() == null) cidictPojo.setLister("");
        if (cidictPojo.getListerid() == null) cidictPojo.setListerid("");
        if (cidictPojo.getModifydate() == null) cidictPojo.setModifydate(new Date());
        if (cidictPojo.getCustom1() == null) cidictPojo.setCustom1("");
        if (cidictPojo.getCustom2() == null) cidictPojo.setCustom2("");
        if (cidictPojo.getCustom3() == null) cidictPojo.setCustom3("");
        if (cidictPojo.getCustom4() == null) cidictPojo.setCustom4("");
        if (cidictPojo.getCustom5() == null) cidictPojo.setCustom5("");
        if (cidictPojo.getTenantid() == null) cidictPojo.setTenantid("");
        if (cidictPojo.getTenantname() == null) cidictPojo.setTenantname("");
        if (cidictPojo.getRevision() == null) cidictPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        CidictEntity cidictEntity = new CidictEntity();
        BeanUtils.copyProperties(cidictPojo, cidictEntity);
        //设置id和新建日期
        cidictEntity.setId(id);
        cidictEntity.setRevision(1);  //乐观锁
        //插入主表
        this.cidictMapper.insert(cidictEntity);
        //Item子表处理
        List<CidictitemPojo> lst = cidictPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (CidictitemPojo cidictitemPojo : lst) {
                //初始化item的NULL
                CidictitemPojo itemPojo = this.cidictitemService.clearNull(cidictitemPojo);
                CidictitemEntity cidictitemEntity = new CidictitemEntity();
                BeanUtils.copyProperties(itemPojo, cidictitemEntity);
                //设置id和Pid
                cidictitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                cidictitemEntity.setPid(id);
                cidictitemEntity.setTenantid(cidictPojo.getTenantid());
                cidictitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.cidictitemMapper.insert(cidictitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(cidictEntity.getId(), cidictEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param cidictPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public CidictPojo update(CidictPojo cidictPojo) {
        //主表更改
        CidictEntity cidictEntity = new CidictEntity();
        BeanUtils.copyProperties(cidictPojo, cidictEntity);
        this.cidictMapper.update(cidictEntity);
        if (cidictPojo.getItem() != null) {
            //Item子表处理
            List<CidictitemPojo> lst = cidictPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = cidictMapper.getDelItemIds(cidictPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.cidictitemMapper.delete(lstDelIds.get(i), cidictEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    CidictitemEntity cidictitemEntity = new CidictitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        CidictitemPojo itemPojo = this.cidictitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, cidictitemEntity);
                        //设置id和Pid
                        cidictitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        cidictitemEntity.setPid(cidictEntity.getId());  // 主表 id
                        cidictitemEntity.setTenantid(cidictPojo.getTenantid());   // 租户id
                        cidictitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.cidictitemMapper.insert(cidictitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), cidictitemEntity);
                        cidictitemEntity.setTenantid(cidictPojo.getTenantid());
                        this.cidictitemMapper.update(cidictitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(cidictEntity.getId(), cidictEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        CidictPojo cidictPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<CidictitemPojo> lst = cidictPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.cidictitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.cidictMapper.delete(key, tid);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CidictPojo getBillEntityByDictCode(String key, String tid) {
        try {
            //读取主表
            CidictPojo cidictPojo = this.cidictMapper.getEntityByDictCode(key, tid);
            //读取子表
            if (cidictPojo == null)  //如果容，读默认的
                cidictPojo = this.cidictMapper.getEntityByDictCode(key, InksConstants.DEFAULT_TENANT);
            if (cidictPojo != null)
                cidictPojo.setItem(cidictitemMapper.getList(cidictPojo.getId(), cidictPojo.getTenantid()));
            return cidictPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


}
