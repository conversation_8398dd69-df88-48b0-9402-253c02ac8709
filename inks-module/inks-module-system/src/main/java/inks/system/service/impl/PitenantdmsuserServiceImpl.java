package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.AESUtil;
import inks.system.domain.PitenantdmsuserEntity;
import inks.system.domain.pojo.PidmsuserPojo;
import inks.system.domain.pojo.PitenantdmsuserPojo;
import inks.system.mapper.PitenantdmsuserMapper;
import inks.system.service.PidmsuserService;
import inks.system.service.PitenantdmsuserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * DMS租户关系表(Pitenantdmsuser)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@Service("pitenantdmsuserService")
public class PitenantdmsuserServiceImpl implements PitenantdmsuserService {
    @Resource
    private PitenantdmsuserMapper pitenantdmsuserMapper;
    @Resource
    private PidmsuserService pidmsuserService;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PitenantdmsuserPojo getEntity(String key) {
        return this.pitenantdmsuserMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PitenantdmsuserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PitenantdmsuserPojo> lst = pitenantdmsuserMapper.getPageList(queryParam);
            PageInfo<PitenantdmsuserPojo> pageInfo = new PageInfo<PitenantdmsuserPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param pitenantdmsuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PitenantdmsuserPojo insert(PitenantdmsuserPojo pitenantdmsuserPojo) {
    //初始化NULL字段
        if(pitenantdmsuserPojo.getTenantid()==null) pitenantdmsuserPojo.setTenantid("");
        if(pitenantdmsuserPojo.getTenantname()==null) pitenantdmsuserPojo.setTenantname("");
        if(pitenantdmsuserPojo.getUserid()==null) pitenantdmsuserPojo.setUserid("");
        if(pitenantdmsuserPojo.getUsername()==null) pitenantdmsuserPojo.setUsername("");
        if(pitenantdmsuserPojo.getRealname()==null) pitenantdmsuserPojo.setRealname("");
        if(pitenantdmsuserPojo.getUsertype()==null) pitenantdmsuserPojo.setUsertype(0);
        if(pitenantdmsuserPojo.getIsadmin()==null) pitenantdmsuserPojo.setIsadmin(0);
        if(pitenantdmsuserPojo.getDeptid()==null) pitenantdmsuserPojo.setDeptid("");
        if(pitenantdmsuserPojo.getDeptcode()==null) pitenantdmsuserPojo.setDeptcode("");
        if(pitenantdmsuserPojo.getDeptname()==null) pitenantdmsuserPojo.setDeptname("");
        if(pitenantdmsuserPojo.getIsdeptadmin()==null) pitenantdmsuserPojo.setIsdeptadmin(0);
        if(pitenantdmsuserPojo.getDeptrownum()==null) pitenantdmsuserPojo.setDeptrownum(0);
        if(pitenantdmsuserPojo.getRownum()==null) pitenantdmsuserPojo.setRownum(0);
        if(pitenantdmsuserPojo.getUserstatus()==null) pitenantdmsuserPojo.setUserstatus(0);
        if(pitenantdmsuserPojo.getUsercode()==null) pitenantdmsuserPojo.setUsercode("");
        if(pitenantdmsuserPojo.getGroupids()==null) pitenantdmsuserPojo.setGroupids("");
        if(pitenantdmsuserPojo.getGroupnames()==null) pitenantdmsuserPojo.setGroupnames("");
        if(pitenantdmsuserPojo.getDmsfunctids()==null) pitenantdmsuserPojo.setDmsfunctids("");
        if(pitenantdmsuserPojo.getDmsfunctnames()==null) pitenantdmsuserPojo.setDmsfunctnames("");
        if(pitenantdmsuserPojo.getCreateby()==null) pitenantdmsuserPojo.setCreateby("");
        if(pitenantdmsuserPojo.getCreatebyid()==null) pitenantdmsuserPojo.setCreatebyid("");
        if(pitenantdmsuserPojo.getCreatedate()==null) pitenantdmsuserPojo.setCreatedate(new Date());
        if(pitenantdmsuserPojo.getLister()==null) pitenantdmsuserPojo.setLister("");
        if(pitenantdmsuserPojo.getListerid()==null) pitenantdmsuserPojo.setListerid("");
        if(pitenantdmsuserPojo.getModifydate()==null) pitenantdmsuserPojo.setModifydate(new Date());
        if(pitenantdmsuserPojo.getCustom1()==null) pitenantdmsuserPojo.setCustom1("");
        if(pitenantdmsuserPojo.getCustom2()==null) pitenantdmsuserPojo.setCustom2("");
        if(pitenantdmsuserPojo.getCustom3()==null) pitenantdmsuserPojo.setCustom3("");
        if(pitenantdmsuserPojo.getCustom4()==null) pitenantdmsuserPojo.setCustom4("");
        if(pitenantdmsuserPojo.getCustom5()==null) pitenantdmsuserPojo.setCustom5("");
        if(pitenantdmsuserPojo.getRevision()==null) pitenantdmsuserPojo.setRevision(0);
        PitenantdmsuserEntity pitenantdmsuserEntity = new PitenantdmsuserEntity(); 
        BeanUtils.copyProperties(pitenantdmsuserPojo,pitenantdmsuserEntity);
  //生成雪花id
          pitenantdmsuserEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          pitenantdmsuserEntity.setRevision(1);  //乐观锁
          this.pitenantdmsuserMapper.insert(pitenantdmsuserEntity);
        return this.getEntity(pitenantdmsuserEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param pitenantdmsuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PitenantdmsuserPojo update(PitenantdmsuserPojo pitenantdmsuserPojo) {
        PitenantdmsuserEntity pitenantdmsuserEntity = new PitenantdmsuserEntity(); 
        BeanUtils.copyProperties(pitenantdmsuserPojo,pitenantdmsuserEntity);
        this.pitenantdmsuserMapper.update(pitenantdmsuserEntity);
        return this.getEntity(pitenantdmsuserEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pitenantdmsuserMapper.delete(key) ;
    }

    /**
     * 分页查询
     * @return 查询结果
     */
    @Override
    public  List<PitenantdmsuserPojo> getListByUser(String Userid) {
        return this.pitenantdmsuserMapper.getListByUser(Userid) ;
    }

    @Override
    public PitenantdmsuserPojo getEntityByUserid(String key, String tid) {
        return this.pitenantdmsuserMapper.getEntityByUserid(key,tid) ;
    }



    @Override
    public PitenantdmsuserPojo createDmsUser(PitenantdmsuserPojo pitenantdmsuserPojo) throws Exception {
        //PitenantdmsuserPojo的用户信息拷贝到PidmsuserPojo
        PidmsuserPojo pidmsuserPojo = new PidmsuserPojo();
        BeanUtils.copyProperties(pitenantdmsuserPojo,pidmsuserPojo);
        //1.判断是否有传入userid;无,新增DmsUser
        if (StringUtils.isBlank(pitenantdmsuserPojo.getUserid())) {
            //新建DmsUser用户,并获取userid(给个默认密码)
            pidmsuserPojo.setUserpassword(AESUtil.Encrypt("123456")); //加密
            PidmsuserPojo insert = pidmsuserService.insert(pidmsuserPojo);
            pitenantdmsuserPojo.setUserid(insert.getUserid());
            //绑定租户,更新groupids,functids
            return this.insert(pitenantdmsuserPojo);
        }
        //1.判断是否有传入userid;有,修改DmsUser
        else {
            pidmsuserService.update(pidmsuserPojo);
            //判断是否绑定当前租户
            PitenantdmsuserPojo entityByUserid = this.getEntityByUserid(pitenantdmsuserPojo.getUserid(), pitenantdmsuserPojo.getTenantid());
            if (entityByUserid == null) {
                //未绑定:绑定租户,更新groupids,functids
                return this.insert(pitenantdmsuserPojo);
            }else {
                //已绑定:更新groupids,functids
                return this.update(pitenantdmsuserPojo);
            }
        }
    }

}
