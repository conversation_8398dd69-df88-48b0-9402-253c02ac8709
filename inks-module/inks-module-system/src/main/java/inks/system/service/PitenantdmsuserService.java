package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PitenantdmsuserPojo;

import java.util.List;

/**
 * DMS租户关系表(Pitenantdmsuser)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
public interface PitenantdmsuserService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PitenantdmsuserPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PitenantdmsuserPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pitenantdmsuserPojo 实例对象
     * @return 实例对象
     */
    PitenantdmsuserPojo insert(PitenantdmsuserPojo pitenantdmsuserPojo);

    /**
     * 修改数据
     *
     * @param pitenantdmsuserpojo 实例对象
     * @return 实例对象
     */
    PitenantdmsuserPojo update(PitenantdmsuserPojo pitenantdmsuserpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 分页查询
     * @return 查询结果
     */
    List<PitenantdmsuserPojo> getListByUser(String Userid);

    PitenantdmsuserPojo createDmsUser(PitenantdmsuserPojo pitenantdmsuserPojo) throws Exception;

    PitenantdmsuserPojo getEntityByUserid(String key, String tid);

}
