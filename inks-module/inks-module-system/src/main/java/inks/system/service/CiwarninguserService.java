package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiwarninguserPojo;

import java.util.List;

/**
 * 用户预警(Ciwarninguser)表服务接口
 *
 * <AUTHOR>
 * @since 2022-07-05 20:11:15
 */
public interface CiwarninguserService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiwarninguserPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CiwarninguserPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param ciwarninguserPojo 实例对象
     * @return 实例对象
     */
    CiwarninguserPojo insert(CiwarninguserPojo ciwarninguserPojo);

    /**
     * 修改数据
     *
     * @param ciwarninguserpojo 实例对象
     * @return 实例对象
     */
    CiwarninguserPojo update(CiwarninguserPojo ciwarninguserpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 用户预警列表
     *
     * @return 查询结果
     */
    List<CiwarninguserPojo> getListByUser(String userid,String tid);

    /**
     * 用户预警列表
     *
     * @return 查询结果
     */
    List<CiwarninguserPojo> getWarnListByUser(String userid,String tid);
}
