package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiformsPojo;

import java.util.List;

/**
 * 窗体中心(CiForms)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-18 15:43:44
 */
public interface CiformsService {

    CiformsPojo getEntity(String key);

    PageInfo<CiformsPojo> getPageList(QueryParam queryParam);

    CiformsPojo insert(CiformsPojo ciformsPojo);

    CiformsPojo update(CiformsPojo ciformspojo);

    int delete(String key);

    List<CiformsPojo> getListByCode(String key, String tenantid);

    CiformsPojo getEntityByCode(String key, int type, String tenantid);

    PageInfo<CiformsPojo> getPageListAll(QueryParam queryParam);
}
