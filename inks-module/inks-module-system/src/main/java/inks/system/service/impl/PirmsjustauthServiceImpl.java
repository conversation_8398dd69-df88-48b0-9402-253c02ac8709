package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PirmsjustauthEntity;
import inks.system.domain.pojo.PirmsjustauthPojo;
import inks.system.mapper.PirmsjustauthMapper;
import inks.system.service.PirmsjustauthService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * RMS第三方登录(Pirmsjustauth)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-21 21:25:20
 */
@Service("pirmsjustauthService")
public class PirmsjustauthServiceImpl implements PirmsjustauthService {
    @Resource
    private PirmsjustauthMapper pirmsjustauthMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PirmsjustauthPojo getEntity(String key, String tid) {
        return this.pirmsjustauthMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PirmsjustauthPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PirmsjustauthPojo> lst = pirmsjustauthMapper.getPageList(queryParam);
            PageInfo<PirmsjustauthPojo> pageInfo = new PageInfo<PirmsjustauthPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param pirmsjustauthPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PirmsjustauthPojo insert(PirmsjustauthPojo pirmsjustauthPojo) {
    //初始化NULL字段
     if(pirmsjustauthPojo.getUserid()==null) pirmsjustauthPojo.setUserid("");
     if(pirmsjustauthPojo.getUsername()==null) pirmsjustauthPojo.setUsername("");
     if(pirmsjustauthPojo.getRealname()==null) pirmsjustauthPojo.setRealname("");
     if(pirmsjustauthPojo.getNickname()==null) pirmsjustauthPojo.setNickname("");
     if(pirmsjustauthPojo.getAuthtype()==null) pirmsjustauthPojo.setAuthtype("");
     if(pirmsjustauthPojo.getAuthuuid()==null) pirmsjustauthPojo.setAuthuuid("");
     if(pirmsjustauthPojo.getUnionid()==null) pirmsjustauthPojo.setUnionid("");
     if(pirmsjustauthPojo.getAuthavatar()==null) pirmsjustauthPojo.setAuthavatar("");
     if(pirmsjustauthPojo.getCreateby()==null) pirmsjustauthPojo.setCreateby("");
     if(pirmsjustauthPojo.getCreatebyid()==null) pirmsjustauthPojo.setCreatebyid("");
     if(pirmsjustauthPojo.getCreatedate()==null) pirmsjustauthPojo.setCreatedate(new Date());
     if(pirmsjustauthPojo.getLister()==null) pirmsjustauthPojo.setLister("");
     if(pirmsjustauthPojo.getListerid()==null) pirmsjustauthPojo.setListerid("");
     if(pirmsjustauthPojo.getModifydate()==null) pirmsjustauthPojo.setModifydate(new Date());
     if(pirmsjustauthPojo.getCustom1()==null) pirmsjustauthPojo.setCustom1("");
     if(pirmsjustauthPojo.getCustom2()==null) pirmsjustauthPojo.setCustom2("");
     if(pirmsjustauthPojo.getCustom3()==null) pirmsjustauthPojo.setCustom3("");
     if(pirmsjustauthPojo.getCustom4()==null) pirmsjustauthPojo.setCustom4("");
     if(pirmsjustauthPojo.getCustom5()==null) pirmsjustauthPojo.setCustom5("");
     if(pirmsjustauthPojo.getTenantid()==null) pirmsjustauthPojo.setTenantid("");
     if(pirmsjustauthPojo.getTenantname()==null) pirmsjustauthPojo.setTenantname("");
     if(pirmsjustauthPojo.getRevision()==null) pirmsjustauthPojo.setRevision(0);
        PirmsjustauthEntity pirmsjustauthEntity = new PirmsjustauthEntity(); 
        BeanUtils.copyProperties(pirmsjustauthPojo,pirmsjustauthEntity);
          //生成雪花id
          pirmsjustauthEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          pirmsjustauthEntity.setRevision(1);  //乐观锁
          this.pirmsjustauthMapper.insert(pirmsjustauthEntity);
        return this.getEntity(pirmsjustauthEntity.getId(),pirmsjustauthEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param pirmsjustauthPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PirmsjustauthPojo update(PirmsjustauthPojo pirmsjustauthPojo) {
        PirmsjustauthEntity pirmsjustauthEntity = new PirmsjustauthEntity(); 
        BeanUtils.copyProperties(pirmsjustauthPojo,pirmsjustauthEntity);
        this.pirmsjustauthMapper.update(pirmsjustauthEntity);
        return this.getEntity(pirmsjustauthEntity.getId(),pirmsjustauthEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.pirmsjustauthMapper.delete(key,tid) ;
    }

    @Override
    public int deleteByOpenid(String openid, String tid) {
        return this.pirmsjustauthMapper.deleteByOpenid(openid,tid) ;
    }
}
