package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PimenuappPojo;
import inks.system.domain.pojo.PimenuwebPojo;
import inks.system.domain.pojo.PidmsfunctmenuappPojo;
import inks.system.domain.PidmsfunctmenuappEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * DMSAPP关系(Pidmsfunctmenuapp)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
public interface PidmsfunctmenuappService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PidmsfunctmenuappPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PidmsfunctmenuappPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pidmsfunctmenuappPojo 实例对象
     * @return 实例对象
     */
    PidmsfunctmenuappPojo insert(PidmsfunctmenuappPojo pidmsfunctmenuappPojo);

    /**
     * 修改数据
     *
     * @param pidmsfunctmenuapppojo 实例对象
     * @return 实例对象
     */
    PidmsfunctmenuappPojo update(PidmsfunctmenuappPojo pidmsfunctmenuapppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<PidmsfunctmenuappPojo> getListByFunction(String key);

    List<PimenuappPojo> getListByDmsFunctids(String dmsfunctids);
}
