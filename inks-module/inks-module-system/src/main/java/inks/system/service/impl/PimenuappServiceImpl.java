package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.pojo.PimenuappPojo;
import inks.system.domain.PimenuappEntity;
import inks.system.domain.pojo.PimenuwebPojo;
import inks.system.mapper.PimenuappMapper;
import inks.system.mapper.PirolemenuappMapper;
import inks.system.service.PimenuappService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.*;

/**
 * APP导航(Pimenuapp)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-08 14:07:54
 */
@Service("pimenuappService")
public class PimenuappServiceImpl implements PimenuappService {
    @Resource
    private PirolemenuappMapper pirolemenuappMapper;

    @Resource
    private PimenuappMapper pimenuappMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PimenuappPojo getEntity(String key) {
        return this.pimenuappMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PimenuappPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PimenuappPojo> lst = pimenuappMapper.getPageList(queryParam);
            PageInfo<PimenuappPojo> pageInfo = new PageInfo<PimenuappPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param pimenuappPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PimenuappPojo insert(PimenuappPojo pimenuappPojo) {
    //初始化NULL字段
     if(pimenuappPojo.getNavpid()==null) pimenuappPojo.setNavpid("");
     if(pimenuappPojo.getNavtype()==null) pimenuappPojo.setNavtype("");
     if(pimenuappPojo.getNavcode()==null) pimenuappPojo.setNavcode("");
     if(pimenuappPojo.getNavname()==null) pimenuappPojo.setNavname("");
     if(pimenuappPojo.getNavgroup()==null) pimenuappPojo.setNavgroup("");
     if(pimenuappPojo.getRownum()==null) pimenuappPojo.setRownum(0);
     if(pimenuappPojo.getImagecss()==null) pimenuappPojo.setImagecss("");
     if(pimenuappPojo.getIconurl()==null) pimenuappPojo.setIconurl("");
     if(pimenuappPojo.getNavigateurl()==null) pimenuappPojo.setNavigateurl("");
     if(pimenuappPojo.getMvcurl()==null) pimenuappPojo.setMvcurl("");
     if(pimenuappPojo.getModuletype()==null) pimenuappPojo.setModuletype("");
     if(pimenuappPojo.getModulecode()==null) pimenuappPojo.setModulecode("");
     if(pimenuappPojo.getRolecode()==null) pimenuappPojo.setRolecode("");
     if(pimenuappPojo.getImageindex()==null) pimenuappPojo.setImageindex("");
     if(pimenuappPojo.getImagestyle()==null) pimenuappPojo.setImagestyle("");
     if(pimenuappPojo.getEnabledmark()==null) pimenuappPojo.setEnabledmark(0);
     if(pimenuappPojo.getRemark()==null) pimenuappPojo.setRemark("");
     if(pimenuappPojo.getPermissioncode()==null) pimenuappPojo.setPermissioncode("");
     if(pimenuappPojo.getFunctionid()==null) pimenuappPojo.setFunctionid("");
     if(pimenuappPojo.getFunctioncode()==null) pimenuappPojo.setFunctioncode("");
     if(pimenuappPojo.getFunctionname()==null) pimenuappPojo.setFunctionname("");
     if(pimenuappPojo.getLister()==null) pimenuappPojo.setLister("");
     if(pimenuappPojo.getCreatedate()==null) pimenuappPojo.setCreatedate(new Date());
     if(pimenuappPojo.getModifydate()==null) pimenuappPojo.setModifydate(new Date());
     if(pimenuappPojo.getDeletemark()==null) pimenuappPojo.setDeletemark(0);
     if(pimenuappPojo.getDeletelister()==null) pimenuappPojo.setDeletelister("");
     if(pimenuappPojo.getDeletedate()==null) pimenuappPojo.setDeletedate(new Date());
        PimenuappEntity pimenuappEntity = new PimenuappEntity(); 
        BeanUtils.copyProperties(pimenuappPojo,pimenuappEntity);
        
          pimenuappEntity.setNavid(inksSnowflake.getSnowflake().nextIdStr());
          this.pimenuappMapper.insert(pimenuappEntity);
        return this.getEntity(pimenuappEntity.getNavid());
  
    }

    /**
     * 修改数据
     *
     * @param pimenuappPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PimenuappPojo update(PimenuappPojo pimenuappPojo) {
        PimenuappEntity pimenuappEntity = new PimenuappEntity(); 
        BeanUtils.copyProperties(pimenuappPojo,pimenuappEntity);
        this.pimenuappMapper.update(pimenuappEntity);
        return this.getEntity(pimenuappEntity.getNavid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pimenuappMapper.delete(key) ;
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public  List<PimenuappPojo> getListByPid(String key) {
        return this.pimenuappMapper.getListByPid(key) ;
    }

    @Override
    public List<PimenuappPojo> getListByNavids(List<String> navids) {
        return pimenuappMapper.getListByNavids(navids);
    }
}
