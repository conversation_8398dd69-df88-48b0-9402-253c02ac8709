package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.AESUtil;
import inks.system.domain.PitenantrmsuserEntity;
import inks.system.domain.pojo.PirmsuserPojo;
import inks.system.domain.pojo.PitenantrmsuserPojo;
import inks.system.mapper.PitenantrmsuserMapper;
import inks.system.service.PirmsuserService;
import inks.system.service.PitenantrmsuserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * RMS租户关系表(Pitenantrmsuser)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@Service("pitenantrmsuserService")
public class PitenantrmsuserServiceImpl implements PitenantrmsuserService {
    @Resource
    private PitenantrmsuserMapper pitenantrmsuserMapper;
    @Resource
    private PirmsuserService pirmsuserService;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PitenantrmsuserPojo getEntity(String key) {
        return this.pitenantrmsuserMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PitenantrmsuserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PitenantrmsuserPojo> lst = pitenantrmsuserMapper.getPageList(queryParam);
            PageInfo<PitenantrmsuserPojo> pageInfo = new PageInfo<PitenantrmsuserPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param pitenantrmsuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PitenantrmsuserPojo insert(PitenantrmsuserPojo pitenantrmsuserPojo) {
    //初始化NULL字段
        if(pitenantrmsuserPojo.getTenantid()==null) pitenantrmsuserPojo.setTenantid("");
        if(pitenantrmsuserPojo.getTenantname()==null) pitenantrmsuserPojo.setTenantname("");
        if(pitenantrmsuserPojo.getUserid()==null) pitenantrmsuserPojo.setUserid("");
        if(pitenantrmsuserPojo.getUsername()==null) pitenantrmsuserPojo.setUsername("");
        if(pitenantrmsuserPojo.getRealname()==null) pitenantrmsuserPojo.setRealname("");
        if(pitenantrmsuserPojo.getUsertype()==null) pitenantrmsuserPojo.setUsertype(0);
        if(pitenantrmsuserPojo.getIsadmin()==null) pitenantrmsuserPojo.setIsadmin(0);
        if(pitenantrmsuserPojo.getDeptid()==null) pitenantrmsuserPojo.setDeptid("");
        if(pitenantrmsuserPojo.getDeptcode()==null) pitenantrmsuserPojo.setDeptcode("");
        if(pitenantrmsuserPojo.getDeptname()==null) pitenantrmsuserPojo.setDeptname("");
        if(pitenantrmsuserPojo.getIsdeptadmin()==null) pitenantrmsuserPojo.setIsdeptadmin(0);
        if(pitenantrmsuserPojo.getDeptrownum()==null) pitenantrmsuserPojo.setDeptrownum(0);
        if(pitenantrmsuserPojo.getRownum()==null) pitenantrmsuserPojo.setRownum(0);
        if(pitenantrmsuserPojo.getUserstatus()==null) pitenantrmsuserPojo.setUserstatus(0);
        if(pitenantrmsuserPojo.getUsercode()==null) pitenantrmsuserPojo.setUsercode("");
        if(pitenantrmsuserPojo.getGroupids()==null) pitenantrmsuserPojo.setGroupids("");
        if(pitenantrmsuserPojo.getGroupnames()==null) pitenantrmsuserPojo.setGroupnames("");
        if(pitenantrmsuserPojo.getRmsfunctids()==null) pitenantrmsuserPojo.setRmsfunctids("");
        if(pitenantrmsuserPojo.getRmsfunctnames()==null) pitenantrmsuserPojo.setRmsfunctnames("");
        if(pitenantrmsuserPojo.getCreateby()==null) pitenantrmsuserPojo.setCreateby("");
        if(pitenantrmsuserPojo.getCreatebyid()==null) pitenantrmsuserPojo.setCreatebyid("");
        if(pitenantrmsuserPojo.getCreatedate()==null) pitenantrmsuserPojo.setCreatedate(new Date());
        if(pitenantrmsuserPojo.getLister()==null) pitenantrmsuserPojo.setLister("");
        if(pitenantrmsuserPojo.getListerid()==null) pitenantrmsuserPojo.setListerid("");
        if(pitenantrmsuserPojo.getModifydate()==null) pitenantrmsuserPojo.setModifydate(new Date());
        if(pitenantrmsuserPojo.getCustom1()==null) pitenantrmsuserPojo.setCustom1("");
        if(pitenantrmsuserPojo.getCustom2()==null) pitenantrmsuserPojo.setCustom2("");
        if(pitenantrmsuserPojo.getCustom3()==null) pitenantrmsuserPojo.setCustom3("");
        if(pitenantrmsuserPojo.getCustom4()==null) pitenantrmsuserPojo.setCustom4("");
        if(pitenantrmsuserPojo.getCustom5()==null) pitenantrmsuserPojo.setCustom5("");
        if(pitenantrmsuserPojo.getRevision()==null) pitenantrmsuserPojo.setRevision(0);
        PitenantrmsuserEntity pitenantrmsuserEntity = new PitenantrmsuserEntity(); 
        BeanUtils.copyProperties(pitenantrmsuserPojo,pitenantrmsuserEntity);
  //生成雪花id
          pitenantrmsuserEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          pitenantrmsuserEntity.setRevision(1);  //乐观锁
          this.pitenantrmsuserMapper.insert(pitenantrmsuserEntity);
        return this.getEntity(pitenantrmsuserEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param pitenantrmsuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PitenantrmsuserPojo update(PitenantrmsuserPojo pitenantrmsuserPojo) {
        PitenantrmsuserEntity pitenantrmsuserEntity = new PitenantrmsuserEntity(); 
        BeanUtils.copyProperties(pitenantrmsuserPojo,pitenantrmsuserEntity);
        this.pitenantrmsuserMapper.update(pitenantrmsuserEntity);
        return this.getEntity(pitenantrmsuserEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pitenantrmsuserMapper.delete(key) ;
    }

    /**
     * 分页查询
     * @return 查询结果
     */
    @Override
    public  List<PitenantrmsuserPojo> getListByUser(String Userid) {
        return this.pitenantrmsuserMapper.getListByUser(Userid) ;
    }

    @Override
    public PitenantrmsuserPojo getEntityByUserid(String key, String tid) {
        return this.pitenantrmsuserMapper.getEntityByUserid(key,tid) ;
    }



    @Override
    public PitenantrmsuserPojo createRmsUser(PitenantrmsuserPojo pitenantrmsuserPojo) throws Exception {
        //PitenantrmsuserPojo的用户信息拷贝到PirmsuserPojo
        PirmsuserPojo pirmsuserPojo = new PirmsuserPojo();
        BeanUtils.copyProperties(pitenantrmsuserPojo,pirmsuserPojo);
        //1.判断是否有传入userid;无,新增RmsUser
        if (StringUtils.isBlank(pitenantrmsuserPojo.getUserid())) {
            //新建RmsUser用户,并获取userid(给个默认密码)
            pirmsuserPojo.setUserpassword(AESUtil.Encrypt("123456")); //加密
            PirmsuserPojo insert = pirmsuserService.insert(pirmsuserPojo);
            pitenantrmsuserPojo.setUserid(insert.getUserid());
            //绑定租户,更新groupids,functids
            return this.insert(pitenantrmsuserPojo);
        }
        //1.判断是否有传入userid;有,修改RmsUser
        else {
            pirmsuserService.update(pirmsuserPojo);
            //判断是否绑定当前租户
            PitenantrmsuserPojo entityByUserid = this.getEntityByUserid(pitenantrmsuserPojo.getUserid(), pitenantrmsuserPojo.getTenantid());
            if (entityByUserid == null) {
                //未绑定:绑定租户,更新groupids,functids
                return this.insert(pitenantrmsuserPojo);
            }else {
                //已绑定:更新groupids,functids
                return this.update(pitenantrmsuserPojo);
            }
        }
    }

}
