package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PipricepolicyitemEntity;
import inks.system.domain.pojo.PipricepolicyitemPojo;
import inks.system.mapper.PipricepolicyitemMapper;
import inks.system.service.PipricepolicyitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

/**
 * 价格项目(Pipricepolicyitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-09 14:05:23
 */
@Service("pipricepolicyitemService")
public class PipricepolicyitemServiceImpl implements PipricepolicyitemService {
    @Resource
    private PipricepolicyitemMapper pipricepolicyitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PipricepolicyitemPojo getEntity(String key) {
        return this.pipricepolicyitemMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PipricepolicyitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PipricepolicyitemPojo> lst = pipricepolicyitemMapper.getPageList(queryParam);
            PageInfo<PipricepolicyitemPojo> pageInfo = new PageInfo<PipricepolicyitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<PipricepolicyitemPojo> getList(String Pid) {
        try {
            List<PipricepolicyitemPojo> lst = pipricepolicyitemMapper.getList(Pid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param pipricepolicyitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PipricepolicyitemPojo insert(PipricepolicyitemPojo pipricepolicyitemPojo) {
        //初始化item的NULL
        PipricepolicyitemPojo itempojo = this.clearNull(pipricepolicyitemPojo);
        PipricepolicyitemEntity pipricepolicyitemEntity = new PipricepolicyitemEntity();
        BeanUtils.copyProperties(itempojo, pipricepolicyitemEntity);

        pipricepolicyitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        pipricepolicyitemEntity.setRevision(1);  //乐观锁
        this.pipricepolicyitemMapper.insert(pipricepolicyitemEntity);
        return this.getEntity(pipricepolicyitemEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param pipricepolicyitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PipricepolicyitemPojo update(PipricepolicyitemPojo pipricepolicyitemPojo) {
        PipricepolicyitemEntity pipricepolicyitemEntity = new PipricepolicyitemEntity();
        BeanUtils.copyProperties(pipricepolicyitemPojo, pipricepolicyitemEntity);
        this.pipricepolicyitemMapper.update(pipricepolicyitemEntity);
        return this.getEntity(pipricepolicyitemEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pipricepolicyitemMapper.delete(key);
    }

    /**
     * 修改数据
     *
     * @param pipricepolicyitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PipricepolicyitemPojo clearNull(PipricepolicyitemPojo pipricepolicyitemPojo) {
        //初始化NULL字段
        if (pipricepolicyitemPojo.getPid() == null) pipricepolicyitemPojo.setPid("");
        if (pipricepolicyitemPojo.getCyclecode() == null) pipricepolicyitemPojo.setCyclecode("");
        if (pipricepolicyitemPojo.getContainer() == null) pipricepolicyitemPojo.setContainer(0);
        if (pipricepolicyitemPojo.getTaxprice() == null) pipricepolicyitemPojo.setTaxprice(0D);
        if (pipricepolicyitemPojo.getEnabledmark() == null) pipricepolicyitemPojo.setEnabledmark(0);
        if (pipricepolicyitemPojo.getRownum() == null) pipricepolicyitemPojo.setRownum(0);
        if (pipricepolicyitemPojo.getRemark() == null) pipricepolicyitemPojo.setRemark("");
        if (pipricepolicyitemPojo.getRevision() == null) pipricepolicyitemPojo.setRevision(0);
        return pipricepolicyitemPojo;
    }
}
