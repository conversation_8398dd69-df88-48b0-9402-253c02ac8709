package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiauthcodePojo;
import inks.system.domain.PiauthcodeEntity;

import com.github.pagehelper.PageInfo;

/**
 * 授权码(Piauthcode)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-22 13:13:41
 */
public interface PiauthcodeService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiauthcodePojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PiauthcodePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param piauthcodePojo 实例对象
     * @return 实例对象
     */
    PiauthcodePojo insert(PiauthcodePojo piauthcodePojo);

    /**
     * 修改数据
     *
     * @param piauthcodepojo 实例对象
     * @return 实例对象
     */
    PiauthcodePojo update(PiauthcodePojo piauthcodepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);
                                                                                                              }
