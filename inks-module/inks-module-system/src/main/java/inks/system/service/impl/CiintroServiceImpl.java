package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CiintroEntity;
import inks.system.domain.pojo.CiintroPojo;
import inks.system.mapper.CiintroMapper;
import inks.system.service.CiintroService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 功能简介(Ciintro)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-07 20:44:33
 */
@Service("ciintroService")
public class CiintroServiceImpl implements CiintroService {
    @Resource
    private CiintroMapper ciintroMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CiintroPojo getEntity(String key) {
        return this.ciintroMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CiintroPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiintroPojo> lst = ciintroMapper.getPageList(queryParam);
            PageInfo<CiintroPojo> pageInfo = new PageInfo<CiintroPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param ciintroPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiintroPojo insert(CiintroPojo ciintroPojo) {
    //初始化NULL字段
     if(ciintroPojo.getGengroupid()==null) ciintroPojo.setGengroupid("");
     if(ciintroPojo.getModulecode()==null) ciintroPojo.setModulecode("");
     if(ciintroPojo.getIntroname()==null) ciintroPojo.setIntroname("");
     if(ciintroPojo.getIntrocontent()==null) ciintroPojo.setIntrocontent("");
     if(ciintroPojo.getRemark()==null) ciintroPojo.setRemark("");
     if(ciintroPojo.getCreateby()==null) ciintroPojo.setCreateby("");
     if(ciintroPojo.getCreatebyid()==null) ciintroPojo.setCreatebyid("");
     if(ciintroPojo.getCreatedate()==null) ciintroPojo.setCreatedate(new Date());
     if(ciintroPojo.getLister()==null) ciintroPojo.setLister("");
     if(ciintroPojo.getListerid()==null) ciintroPojo.setListerid("");
     if(ciintroPojo.getModifydate()==null) ciintroPojo.setModifydate(new Date());
        CiintroEntity ciintroEntity = new CiintroEntity(); 
        BeanUtils.copyProperties(ciintroPojo,ciintroEntity);
        
          ciintroEntity.setId(inksSnowflake.getSnowflake().nextIdStr());

          this.ciintroMapper.insert(ciintroEntity);
        return this.getEntity(ciintroEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param ciintroPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiintroPojo update(CiintroPojo ciintroPojo) {
        CiintroEntity ciintroEntity = new CiintroEntity(); 
        BeanUtils.copyProperties(ciintroPojo,ciintroEntity);
        this.ciintroMapper.update(ciintroEntity);
        return this.getEntity(ciintroEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.ciintroMapper.delete(key) ;
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CiintroPojo getEntityByCode(String key) {
        return this.ciintroMapper.getEntityByCode(key);
    }
}
