package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiprojectPojo;
import inks.system.domain.pojo.PiprojectitemdetailPojo;

/**
 * 项目(Piproject)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-20 12:51:52
 */
public interface PiprojectService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiprojectPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PiprojectitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiprojectPojo getBillEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PiprojectPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PiprojectPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param piprojectPojo 实例对象
     * @return 实例对象
     */
    PiprojectPojo insert(PiprojectPojo piprojectPojo);

    /**
     * 修改数据
     *
     * @param piprojectpojo 实例对象
     * @return 实例对象
     */
    PiprojectPojo update(PiprojectPojo piprojectpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

}
