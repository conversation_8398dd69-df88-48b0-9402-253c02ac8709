package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.system.domain.pojo.CidynamicvalidationitemPojo;
import inks.system.domain.CidynamicvalidationitemEntity;
import inks.system.mapper.CidynamicvalidationitemMapper;
import inks.system.service.CidynamicvalidationitemService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 动态校验规则子表(Cidynamicvalidationitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-06 13:13:11
 */
@Service("cidynamicvalidationitemService")
public class CidynamicvalidationitemServiceImpl implements CidynamicvalidationitemService {
    @Resource
    private CidynamicvalidationitemMapper cidynamicvalidationitemMapper;

    @Override
    public CidynamicvalidationitemPojo getEntity(String key,String tid) {
        return this.cidynamicvalidationitemMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<CidynamicvalidationitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CidynamicvalidationitemPojo> lst = cidynamicvalidationitemMapper.getPageList(queryParam);
            PageInfo<CidynamicvalidationitemPojo> pageInfo = new PageInfo<CidynamicvalidationitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<CidynamicvalidationitemPojo> getList(String Pid,String tid) { 
        try {
            List<CidynamicvalidationitemPojo> lst = cidynamicvalidationitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public CidynamicvalidationitemPojo insert(CidynamicvalidationitemPojo cidynamicvalidationitemPojo) {
        //初始化item的NULL
        CidynamicvalidationitemPojo itempojo =this.clearNull(cidynamicvalidationitemPojo);
        CidynamicvalidationitemEntity cidynamicvalidationitemEntity = new CidynamicvalidationitemEntity(); 
        BeanUtils.copyProperties(itempojo,cidynamicvalidationitemEntity);
          //生成雪花id
          cidynamicvalidationitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          cidynamicvalidationitemEntity.setRevision(1);  //乐观锁      
          this.cidynamicvalidationitemMapper.insert(cidynamicvalidationitemEntity);
        return this.getEntity(cidynamicvalidationitemEntity.getId(),cidynamicvalidationitemEntity.getTenantid());
  
    }

    @Override
    public CidynamicvalidationitemPojo update(CidynamicvalidationitemPojo cidynamicvalidationitemPojo) {
        CidynamicvalidationitemEntity cidynamicvalidationitemEntity = new CidynamicvalidationitemEntity(); 
        BeanUtils.copyProperties(cidynamicvalidationitemPojo,cidynamicvalidationitemEntity);
        this.cidynamicvalidationitemMapper.update(cidynamicvalidationitemEntity);
        return this.getEntity(cidynamicvalidationitemEntity.getId(),cidynamicvalidationitemEntity.getTenantid());
    }

    @Override
    public int delete(String key,String tid) {
        return this.cidynamicvalidationitemMapper.delete(key,tid) ;
    }

     @Override
     public CidynamicvalidationitemPojo clearNull(CidynamicvalidationitemPojo cidynamicvalidationitemPojo){
     //初始化NULL字段
     if(cidynamicvalidationitemPojo.getPid()==null) cidynamicvalidationitemPojo.setPid("");
     if(cidynamicvalidationitemPojo.getFieldname()==null) cidynamicvalidationitemPojo.setFieldname("");
     if(cidynamicvalidationitemPojo.getComment()==null) cidynamicvalidationitemPojo.setComment("");
     if(cidynamicvalidationitemPojo.getRuletype()==null) cidynamicvalidationitemPojo.setRuletype("");
     if(cidynamicvalidationitemPojo.getRulevalue()==null) cidynamicvalidationitemPojo.setRulevalue("");
     if(cidynamicvalidationitemPojo.getErrormessage()==null) cidynamicvalidationitemPojo.setErrormessage("");
     if(cidynamicvalidationitemPojo.getChecktype()==null) cidynamicvalidationitemPojo.setChecktype("");
     if(cidynamicvalidationitemPojo.getRemark()==null) cidynamicvalidationitemPojo.setRemark("");
     if(cidynamicvalidationitemPojo.getRownum()==null) cidynamicvalidationitemPojo.setRownum(0);
     if(cidynamicvalidationitemPojo.getCustom1()==null) cidynamicvalidationitemPojo.setCustom1("");
     if(cidynamicvalidationitemPojo.getCustom2()==null) cidynamicvalidationitemPojo.setCustom2("");
     if(cidynamicvalidationitemPojo.getCustom3()==null) cidynamicvalidationitemPojo.setCustom3("");
     if(cidynamicvalidationitemPojo.getCustom4()==null) cidynamicvalidationitemPojo.setCustom4("");
     if(cidynamicvalidationitemPojo.getCustom5()==null) cidynamicvalidationitemPojo.setCustom5("");
     if(cidynamicvalidationitemPojo.getCustom6()==null) cidynamicvalidationitemPojo.setCustom6("");
     if(cidynamicvalidationitemPojo.getCustom7()==null) cidynamicvalidationitemPojo.setCustom7("");
     if(cidynamicvalidationitemPojo.getCustom8()==null) cidynamicvalidationitemPojo.setCustom8("");
     if(cidynamicvalidationitemPojo.getCustom9()==null) cidynamicvalidationitemPojo.setCustom9("");
     if(cidynamicvalidationitemPojo.getCustom10()==null) cidynamicvalidationitemPojo.setCustom10("");
     if(cidynamicvalidationitemPojo.getTenantid()==null) cidynamicvalidationitemPojo.setTenantid("");
     if(cidynamicvalidationitemPojo.getRevision()==null) cidynamicvalidationitemPojo.setRevision(0);
     return cidynamicvalidationitemPojo;
     }
}
