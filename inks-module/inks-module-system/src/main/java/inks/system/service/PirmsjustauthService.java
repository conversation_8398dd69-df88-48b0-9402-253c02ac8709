package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PirmsjustauthPojo;

/**
 * RMS第三方登录(Pirmsjustauth)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-21 21:25:20
 */
public interface PirmsjustauthService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PirmsjustauthPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PirmsjustauthPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pirmsjustauthPojo 实例对象
     * @return 实例对象
     */
    PirmsjustauthPojo insert(PirmsjustauthPojo pirmsjustauthPojo);

    /**
     * 修改数据
     *
     * @param pirmsjustauthpojo 实例对象
     * @return 实例对象
     */
    PirmsjustauthPojo update(PirmsjustauthPojo pirmsjustauthpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

    int deleteByOpenid(String openid, String tid);
}
