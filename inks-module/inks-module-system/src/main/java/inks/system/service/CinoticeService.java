package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CinoticePojo;
import inks.system.domain.CinoticeEntity;

import com.github.pagehelper.PageInfo;

/**
 * 公告(Cinotice)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-19 19:27:31
 */
public interface CinoticeService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CinoticePojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CinoticePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param cinoticePojo 实例对象
     * @return 实例对象
     */
    CinoticePojo insert(CinoticePojo cinoticePojo);

    /**
     * 修改数据
     *
     * @param cinoticepojo 实例对象
     * @return 实例对象
     */
    CinoticePojo update(CinoticePojo cinoticepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

}
