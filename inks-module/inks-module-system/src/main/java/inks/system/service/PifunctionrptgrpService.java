package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PifunctionrptgrpPojo;
import com.github.pagehelper.PageInfo;
import java.util.List;

/**
 * 服务报表分组数据(PiFunctionRptGrp)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-22 10:31:29
 */
public interface PifunctionrptgrpService {

    PifunctionrptgrpPojo getEntity(String key,String tid);

    PageInfo<PifunctionrptgrpPojo> getPageList(QueryParam queryParam);

    PifunctionrptgrpPojo insert(PifunctionrptgrpPojo pifunctionrptgrpPojo);

    PifunctionrptgrpPojo update(PifunctionrptgrpPojo pifunctionrptgrppojo);

    int delete(String key,String tid);

    PageInfo<PifunctionrptgrpPojo> getPageListByFunctionid(QueryParam queryParam);
    
    List<PifunctionrptgrpPojo> getList(String tid);
}
