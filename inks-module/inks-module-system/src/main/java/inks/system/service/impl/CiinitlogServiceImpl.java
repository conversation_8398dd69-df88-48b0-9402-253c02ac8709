package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CiinitlogEntity;
import inks.system.domain.pojo.CiinitlogPojo;
import inks.system.domain.pojo.CireportsPojo;
import inks.system.mapper.*;
import inks.system.service.CiinitlogService;
import inks.system.service.CireportsService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 初始化日志(Ciinitlog)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-31 11:20:20
 */
@Service("ciinitlogService")
public class CiinitlogServiceImpl implements CiinitlogService {
    @Resource
    private CiinitlogMapper ciinitlogMapper;

    @Resource
    private CiinitsaleMapper ciinitsaleMapper;

    @Resource
    private CiinitbuyMapper ciinitbuyMapper;

    @Resource
    private CiinitstoreMapper ciinitstoreMapper;

    @Resource
    private CiinitmanuMapper ciinitmanuMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CiinitlogPojo getEntity(String key, String tid) {
        return this.ciinitlogMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CiinitlogPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiinitlogPojo> lst = ciinitlogMapper.getPageList(queryParam);
            PageInfo<CiinitlogPojo> pageInfo = new PageInfo<CiinitlogPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param ciinitlogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiinitlogPojo insert(CiinitlogPojo ciinitlogPojo) {
        //初始化NULL字段
        if (ciinitlogPojo.getModulecode() == null) ciinitlogPojo.setModulecode("");
        if (ciinitlogPojo.getInittitle() == null) ciinitlogPojo.setInittitle("");
        if (ciinitlogPojo.getInitstatus() == null) ciinitlogPojo.setInitstatus(0);
        if (ciinitlogPojo.getReccount() == null) ciinitlogPojo.setReccount(0);
        if (ciinitlogPojo.getErrormsg() == null) ciinitlogPojo.setErrormsg("");
        if (ciinitlogPojo.getRemark() == null) ciinitlogPojo.setRemark("");
        if (ciinitlogPojo.getTenantid() == null) ciinitlogPojo.setTenantid("");
        if (ciinitlogPojo.getTenantname() == null) ciinitlogPojo.setTenantname("");
        if (ciinitlogPojo.getCreateby() == null) ciinitlogPojo.setCreateby("");
        if (ciinitlogPojo.getCreatebyid() == null) ciinitlogPojo.setCreatebyid("");
        if (ciinitlogPojo.getCreatedate() == null) ciinitlogPojo.setCreatedate(new Date());
        CiinitlogEntity ciinitlogEntity = new CiinitlogEntity();
        BeanUtils.copyProperties(ciinitlogPojo, ciinitlogEntity);
        ciinitlogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.ciinitlogMapper.insert(ciinitlogEntity);
        return this.getEntity(ciinitlogEntity.getId(), ciinitlogEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param ciinitlogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiinitlogPojo update(CiinitlogPojo ciinitlogPojo) {
        CiinitlogEntity ciinitlogEntity = new CiinitlogEntity();
        BeanUtils.copyProperties(ciinitlogPojo, ciinitlogEntity);
        this.ciinitlogMapper.update(ciinitlogEntity);
        return this.getEntity(ciinitlogEntity.getId(), ciinitlogEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.ciinitlogMapper.delete(key, tid);
    }

    /**
     * 通过主键删除数据
     *
     * @return 是否成功
     */
    @Override
    @Transactional
    public int initSale(QueryParam queryParam) {
        int delNum = 0;
        // D01M03销售订单
        delNum += this.ciinitsaleMapper.deleteMachItem(queryParam);
        delNum += this.ciinitsaleMapper.deleteMach(queryParam);
        // D01M06发出商品
        delNum += this.ciinitsaleMapper.deleteDeliItem(queryParam);
        delNum += this.ciinitsaleMapper.deleteDeli(queryParam);
        // D01M05销售开票
        delNum += this.ciinitsaleMapper.deleteInvoItem(queryParam);
        delNum += this.ciinitsaleMapper.deleteInvo(queryParam);
        // D01M08预收、收款
        delNum += this.ciinitsaleMapper.deleteDepoItem(queryParam);
        delNum += this.ciinitsaleMapper.deleteDepoCash(queryParam);
        delNum += this.ciinitsaleMapper.deleteDepo(queryParam);
        delNum += this.ciinitsaleMapper.deleteReceItem(queryParam);
        delNum += this.ciinitsaleMapper.deleteReceCash(queryParam);
        delNum += this.ciinitsaleMapper.deleteRece(queryParam);
        // D01M09 销售扣款
        delNum += this.ciinitsaleMapper.deleteDeduItem(queryParam);
        delNum += this.ciinitsaleMapper.deleteDedu(queryParam);
        // D01M12 销售账单/结账
        delNum += this.ciinitsaleMapper.deleteAccoItem(queryParam);
        delNum += this.ciinitsaleMapper.deleteAcco(queryParam);
        delNum += this.ciinitsaleMapper.deleteAccoRec(queryParam);
        return delNum;
    }

    /**
     * 通过主键删除数据
     *
     * @return 是否成功
     */
    @Override
    public int initBuy(QueryParam queryParam) {
        int delNum = 0;
        // D03M01 采购计划
        delNum += this.ciinitbuyMapper.deletePlanItem(queryParam);
        delNum += this.ciinitbuyMapper.deletePlan(queryParam);
        // D03M02 采购合同
        delNum += this.ciinitbuyMapper.deleteOrderItem(queryParam);
        delNum += this.ciinitbuyMapper.deleteOrder(queryParam);
        // D03M03 采购收货
        delNum += this.ciinitbuyMapper.deleteFiniItem(queryParam);
        delNum += this.ciinitbuyMapper.deleteFini(queryParam);
        // D03M04 采购扣款
        delNum += this.ciinitbuyMapper.deleteDeduItem(queryParam);
        delNum += this.ciinitbuyMapper.deleteDedu(queryParam);
        // D03M05 采购开票
        delNum += this.ciinitbuyMapper.deleteInvoItem(queryParam);
        delNum += this.ciinitbuyMapper.deleteInvo(queryParam);
        // D03M06预付、付款
        delNum += this.ciinitbuyMapper.deletePrepItem(queryParam);
        delNum += this.ciinitbuyMapper.deletePrepCash(queryParam);
        delNum += this.ciinitbuyMapper.deletePrep(queryParam);
        delNum += this.ciinitbuyMapper.deleteVoucItem(queryParam);
        delNum += this.ciinitbuyMapper.deleteVoucCash(queryParam);
        delNum += this.ciinitbuyMapper.deleteVouc(queryParam);
        // D03M08-10 采购账单/结账
        delNum += this.ciinitbuyMapper.deleteAccoItem(queryParam);
        delNum += this.ciinitbuyMapper.deleteAcco(queryParam);
        delNum += this.ciinitbuyMapper.deleteAccoRec(queryParam);
        return delNum;
    }

    /**
     * 通过主键删除数据
     *
     * @return 是否成功
     */
    @Override
    public int initStore(QueryParam queryParam) {
        int delNum = 0;
        // D04M01 出库单
        delNum += this.ciinitstoreMapper.deleteAcceItem(queryParam);
        delNum += this.ciinitstoreMapper.deleteAcce(queryParam);
        // D04M08 领退料
        delNum += this.ciinitstoreMapper.deleteRequItem(queryParam);
        delNum += this.ciinitstoreMapper.deleteRequ(queryParam);
        delNum += this.ciinitstoreMapper.deleteReqRItem(queryParam);
        delNum += this.ciinitstoreMapper.deleteReqR(queryParam);
        return delNum;
    }

    /**
     * 通过主键删除数据
     *
     * @return 是否成功
     */
    @Override
    public int initManu(QueryParam queryParam) {
        int delNum = 0;

        // D05M01B1 厂制工单
        delNum += this.ciinitmanuMapper.deleteWk_Worksheet(queryParam);
        // D05M02B1 委制单据
        delNum += this.ciinitmanuMapper.deleteWk_Subcontract(queryParam);
        // D05M03B1 生产验收/完工验收
        delNum += this.ciinitmanuMapper.deleteWk_Complete(queryParam);
        // D05M03B1De 委制扣款
        delNum += this.ciinitmanuMapper.deleteWk_ScDeduction(queryParam);
        // D05M03B1Sc 委外加工单验收
        delNum += this.ciinitmanuMapper.deleteWk_ScComplete(queryParam);
        // D05M04B1 生产主计划
        delNum += this.ciinitmanuMapper.deleteWk_MainPlan(queryParam);
        // D05M05B1 WIP记录
        delNum += this.ciinitmanuMapper.deleteWk_WipNote(queryParam);
        // D05M05H1 HiWIP记录
        delNum += this.ciinitmanuMapper.deleteHi_WipNote(queryParam);
        // D05M06B1 生产报工
        delNum += this.ciinitmanuMapper.deleteWk_ManuReport(queryParam);
        // D05M07B1 生产过数
        delNum += this.ciinitmanuMapper.deleteWk_WipQty(queryParam);
        // D05M07H1 Hi生产过数
        delNum += this.ciinitmanuMapper.deleteHi_WipQty(queryParam);
        // D05M09B1 Wip委外
        delNum += this.ciinitmanuMapper.deleteWk_WipEpibole(queryParam);
        // D05M09B1FIN 工序收货
        delNum += this.ciinitmanuMapper.deleteWk_WipEpFinishing(queryParam);
        // D05M11B1 MRP运算
        delNum += this.ciinitmanuMapper.deleteWk_Mrp(queryParam);
        // D05M12B1 SMT上料表
        delNum += this.ciinitmanuMapper.deleteWk_SmtPart(queryParam);
        // D05M13B1 成本预测
        delNum += this.ciinitmanuMapper.deleteWk_CostBudget(queryParam);
        // D05M14B1 生产结转
        delNum += this.ciinitmanuMapper.deleteWk_MpCarryover(queryParam);
        // D05M14B1SC 加工结转
        delNum += this.ciinitmanuMapper.deleteWk_ScCarryover(queryParam);
        // D05M14B1WS 车间结转
        delNum += this.ciinitmanuMapper.deleteWk_WsCarryover(queryParam);
        // D05M15B1 可视化排程
        delNum += this.ciinitmanuMapper.deleteWk_VisualPlan(queryParam);
        // D05M16B1 阶梯工价
        delNum += this.ciinitmanuMapper.deleteWk_StepPrice(queryParam);
        // D05M16B2 阶梯制程
        delNum += this.ciinitmanuMapper.deleteWk_StepProGroup(queryParam);
        // D05M16S1 阶梯工价设置
        delNum += this.ciinitmanuMapper.deleteWk_StepPriceSet(queryParam);
        // D05M17B1 工位状态
        delNum += this.ciinitmanuMapper.deleteWk_StationState(queryParam);
        // D05M21S1 生产工序
        delNum += this.ciinitmanuMapper.deleteWk_Process(queryParam);
        // D05M21S2 生产制程
        delNum += this.ciinitmanuMapper.deleteWk_ProGroup(queryParam);
        // D05M21S3 WIP设定
        delNum += this.ciinitmanuMapper.deleteWk_WipGroup(queryParam);
        // D05M21S4 过数角色
        delNum += this.ciinitmanuMapper.deleteWk_WipQtyRoles(queryParam);
        // D05M21S5 过数小组
        delNum += this.ciinitmanuMapper.deleteWk_WipQtyGroup(queryParam);
        // D05M21S6 工位
        delNum += this.ciinitmanuMapper.deleteWk_Station(queryParam);
        // D05M21S8 工序成本
        delNum += this.ciinitmanuMapper.deleteWk_ProcCost(queryParam);
        // D05M21S9 委外加工类型
        delNum += this.ciinitmanuMapper.deleteWk_ScMachType(queryParam);
        // D05M21S10 委外费用类型
        delNum += this.ciinitmanuMapper.deleteWk_ScCostType(queryParam);
        // D05M21S11 车间预损率
        delNum += this.ciinitmanuMapper.deleteWk_GroupLossRate(queryParam);

        return delNum;
    }


    /**
     * 通过主键删除数据
     *
     * @return 是否成功
     */
    @Override
    public int initFm(QueryParam queryParam) {
        int delNum = 0;
        return delNum;
    }


}
