package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PirmsfunctmenuwebPojo;
import inks.system.domain.pojo.PimenuwebPojo;

import java.util.List;

/**
 * RMS菜单关系(Pirmsfunctmenuweb)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
public interface PirmsfunctmenuwebService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PirmsfunctmenuwebPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PirmsfunctmenuwebPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pirmsfunctmenuwebPojo 实例对象
     * @return 实例对象
     */
    PirmsfunctmenuwebPojo insert(PirmsfunctmenuwebPojo pirmsfunctmenuwebPojo);

    /**
     * 修改数据
     *
     * @param pirmsfunctmenuwebpojo 实例对象
     * @return 实例对象
     */
    PirmsfunctmenuwebPojo update(PirmsfunctmenuwebPojo pirmsfunctmenuwebpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<PirmsfunctmenuwebPojo> getListByFunction(String key);

    List<PimenuwebPojo> getListByLoginUser(LoginUser loginUser);
    List<PimenuwebPojo> getListByRmsFunctids(String ids);

}
