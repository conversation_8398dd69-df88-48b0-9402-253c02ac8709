package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.system.domain.pojo.CiformpartPojo;
import inks.system.domain.CiformpartEntity;
import inks.system.mapper.CiformpartMapper;
import inks.system.service.CiformpartService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 表单组件(Ciformpart)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-19 13:41:52
 */
@Service("ciformpartService")
public class CiformpartServiceImpl implements CiformpartService {
    @Resource
    private CiformpartMapper ciformpartMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CiformpartPojo getEntity(String key, String tid) {
        return this.ciformpartMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CiformpartPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiformpartPojo> lst = ciformpartMapper.getPageList(queryParam);
            PageInfo<CiformpartPojo> pageInfo = new PageInfo<CiformpartPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param ciformpartPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiformpartPojo insert(CiformpartPojo ciformpartPojo) {
    //初始化NULL字段
     if(ciformpartPojo.getGengroupid()==null) ciformpartPojo.setGengroupid("");
     if(ciformpartPojo.getModulecode()==null) ciformpartPojo.setModulecode("");
     if(ciformpartPojo.getParttype()==null) ciformpartPojo.setParttype("");
     if(ciformpartPojo.getPartcode()==null) ciformpartPojo.setPartcode("");
     if(ciformpartPojo.getPartname()==null) ciformpartPojo.setPartname("");
     if(ciformpartPojo.getParamjson()==null) ciformpartPojo.setParamjson("");
     if(ciformpartPojo.getRownum()==null) ciformpartPojo.setRownum(0);
     if(ciformpartPojo.getEnabledmark()==null) ciformpartPojo.setEnabledmark(0);
     if(ciformpartPojo.getRemark()==null) ciformpartPojo.setRemark("");
     if(ciformpartPojo.getCreateby()==null) ciformpartPojo.setCreateby("");
     if(ciformpartPojo.getCreatebyid()==null) ciformpartPojo.setCreatebyid("");
     if(ciformpartPojo.getCreatedate()==null) ciformpartPojo.setCreatedate(new Date());
     if(ciformpartPojo.getLister()==null) ciformpartPojo.setLister("");
     if(ciformpartPojo.getListerid()==null) ciformpartPojo.setListerid("");
     if(ciformpartPojo.getModifydate()==null) ciformpartPojo.setModifydate(new Date());
     if(ciformpartPojo.getCustom1()==null) ciformpartPojo.setCustom1("");
     if(ciformpartPojo.getCustom2()==null) ciformpartPojo.setCustom2("");
     if(ciformpartPojo.getCustom3()==null) ciformpartPojo.setCustom3("");
     if(ciformpartPojo.getCustom4()==null) ciformpartPojo.setCustom4("");
     if(ciformpartPojo.getCustom5()==null) ciformpartPojo.setCustom5("");
     if(ciformpartPojo.getTenantid()==null) ciformpartPojo.setTenantid("");
     if(ciformpartPojo.getTenantname()==null) ciformpartPojo.setTenantname("");
     if(ciformpartPojo.getRevision()==null) ciformpartPojo.setRevision(0);
        CiformpartEntity ciformpartEntity = new CiformpartEntity(); 
        BeanUtils.copyProperties(ciformpartPojo,ciformpartEntity);
          //生成雪花id
          ciformpartEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          ciformpartEntity.setRevision(1);  //乐观锁
          this.ciformpartMapper.insert(ciformpartEntity);
        return this.getEntity(ciformpartEntity.getId(),ciformpartEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param ciformpartPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiformpartPojo update(CiformpartPojo ciformpartPojo) {
        CiformpartEntity ciformpartEntity = new CiformpartEntity(); 
        BeanUtils.copyProperties(ciformpartPojo,ciformpartEntity);
        this.ciformpartMapper.update(ciformpartEntity);
        return this.getEntity(ciformpartEntity.getId(),ciformpartEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.ciformpartMapper.delete(key,tid) ;
    }
    
                                                                                                                            
}
