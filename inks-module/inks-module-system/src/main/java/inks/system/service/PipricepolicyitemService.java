package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PipricepolicyitemPojo;

import java.util.List;
/**
 * 价格项目(Pipricepolicyitem)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-09 14:05:23
 */
public interface PipricepolicyitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PipricepolicyitemPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PipricepolicyitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<PipricepolicyitemPojo> getList(String Pid);
    
    /**
     * 新增数据
     *
     * @param pipricepolicyitemPojo 实例对象
     * @return 实例对象
     */
    PipricepolicyitemPojo insert(PipricepolicyitemPojo pipricepolicyitemPojo);

    /**
     * 修改数据
     *
     * @param pipricepolicyitempojo 实例对象
     * @return 实例对象
     */
    PipricepolicyitemPojo update(PipricepolicyitemPojo pipricepolicyitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

     /**
     * 修改数据
     *
     * @param pipricepolicyitempojo 实例对象
     * @return 实例对象
     */
    PipricepolicyitemPojo clearNull(PipricepolicyitemPojo pipricepolicyitempojo);
}
