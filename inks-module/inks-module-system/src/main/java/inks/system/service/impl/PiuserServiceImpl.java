package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.AESUtil;
import inks.system.domain.PitenantuserEntity;
import inks.system.domain.PiuserEntity;
import inks.system.domain.PiuserloginEntity;
import inks.system.domain.pojo.PiuserPojo;
import inks.system.mapper.PitenantuserMapper;
import inks.system.mapper.PiuserMapper;
import inks.system.mapper.PiuserloginMapper;
import inks.system.service.PiuserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 用户表(<PERSON>er)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-08 14:35:51
 */
@Service("piuserService")
public class PiuserServiceImpl implements PiuserService {
    @Resource
    private PiuserMapper piuserMapper;

    @Resource
    private PiuserloginMapper piuserloginMapper;

    @Resource
    private PitenantuserMapper pitenantuserMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PiuserPojo getEntity(String key) {
        return this.piuserMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PiuserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PiuserPojo> lst = piuserMapper.getPageList(queryParam);
            PageInfo<PiuserPojo> pageInfo = new PageInfo<PiuserPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param piuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiuserPojo insert(PiuserPojo piuserPojo) {
        //初始化NULL字段
        if (piuserPojo.getUsername() == null) piuserPojo.setUsername("");
        if (piuserPojo.getRealname() == null) piuserPojo.setRealname("");
        if (piuserPojo.getNickname() == null) piuserPojo.setNickname("");
        if (piuserPojo.getMobile() == null) piuserPojo.setMobile("");
        if (piuserPojo.getEmail() == null) piuserPojo.setEmail("");
        if (piuserPojo.getSex() == null) piuserPojo.setSex(0);
        if (piuserPojo.getLangcode() == null) piuserPojo.setLangcode("");
        if (piuserPojo.getAvatar() == null) piuserPojo.setAvatar("");
        if (piuserPojo.getUserstatus() == null) piuserPojo.setUserstatus(0);
        if (piuserPojo.getUsercode() == null) piuserPojo.setUsercode("");
        if (piuserPojo.getRemark() == null) piuserPojo.setRemark("");
        if (piuserPojo.getLister() == null) piuserPojo.setLister("");
        if (piuserPojo.getCreateby() == null) piuserPojo.setCreateby("");
        if (piuserPojo.getCreatedate() == null) piuserPojo.setCreatedate(new Date());
        if (piuserPojo.getModifydate() == null) piuserPojo.setModifydate(new Date());
        if (piuserPojo.getRevision() == null) piuserPojo.setRevision(0);
        PiuserEntity piuserEntity = new PiuserEntity();
        BeanUtils.copyProperties(piuserPojo, piuserEntity);

        piuserEntity.setUserid(inksSnowflake.getSnowflake().nextIdStr());
        this.piuserMapper.insert(piuserEntity);
        return this.getEntity(piuserEntity.getUserid());

    }

    /**
     * 修改数据
     *
     * @param piuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiuserPojo update(PiuserPojo piuserPojo) {
        PiuserEntity piuserEntity = new PiuserEntity();
        BeanUtils.copyProperties(piuserPojo, piuserEntity);
        this.piuserMapper.update(piuserEntity);
        return this.getEntity(piuserEntity.getUserid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.piuserMapper.delete(key);
    }


    /**
     * 新增数据
     *
     * @param piuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public PiuserPojo createByTenant(PiuserPojo piuserPojo, LoginUser loginUser) throws Exception {
        PiuserPojo userPojo= this.piuserMapper.getEntityByUserName(piuserPojo.getUsername());
        if (userPojo != null){
            throw new RuntimeException(piuserPojo.getUsername()+" 用户重名");
        }
        //初始化NULL字段
        if (piuserPojo.getUsername() == null) piuserPojo.setUsername("");
        if (piuserPojo.getRealname() == null) piuserPojo.setRealname("");
        if (piuserPojo.getNickname() == null) piuserPojo.setNickname("");
        if (piuserPojo.getMobile() == null) piuserPojo.setMobile("");
        if (piuserPojo.getEmail() == null) piuserPojo.setEmail("");
        if (piuserPojo.getSex() == null) piuserPojo.setSex(0);
        if (piuserPojo.getLangcode() == null) piuserPojo.setLangcode("");
        if (piuserPojo.getAvatar() == null) piuserPojo.setAvatar("");
        if (piuserPojo.getUserstatus() == null) piuserPojo.setUserstatus(0);
        if (piuserPojo.getUsercode() == null) piuserPojo.setUsercode("");
        if (piuserPojo.getRemark() == null) piuserPojo.setRemark("");
        if (piuserPojo.getLister() == null) piuserPojo.setLister("");
        if (piuserPojo.getCreateby() == null) piuserPojo.setCreateby("");
        if (piuserPojo.getCreatedate() == null) piuserPojo.setCreatedate(new Date());
        if (piuserPojo.getModifydate() == null) piuserPojo.setModifydate(new Date());
        if (piuserPojo.getRevision() == null) piuserPojo.setRevision(0);
        PiuserEntity piuserEntity = new PiuserEntity();
        BeanUtils.copyProperties(piuserPojo, piuserEntity);

        piuserEntity.setUserid(inksSnowflake.getSnowflake().nextIdStr());
        this.piuserMapper.insert(piuserEntity);

        String password = AESUtil.Encrypt("123456");
        PiuserloginEntity piuserloginEntity = new PiuserloginEntity();
        BeanUtils.copyProperties(piuserEntity, piuserloginEntity);
        piuserloginEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        piuserloginEntity.setUserpassword(password);
        piuserloginEntity.setCheckipaddr(0);
        this.piuserloginMapper.insert(piuserloginEntity);

        PitenantuserEntity pitenantuserEntity = new PitenantuserEntity();
        BeanUtils.copyProperties(piuserEntity, pitenantuserEntity);
        pitenantuserEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        pitenantuserEntity.setTenantid(loginUser.getTenantid());
        pitenantuserEntity.setTenantname(loginUser.getTenantinfo().getTenantname());
        pitenantuserEntity.setIsadmin(0);
        pitenantuserEntity.setDeptid("");
        pitenantuserEntity.setDeptname("");
        pitenantuserEntity.setDeptcode("");
        pitenantuserEntity.setDeptrownum(0);
        pitenantuserEntity.setIsdeptadmin(0);
        pitenantuserEntity.setRownum(0);
        pitenantuserEntity.setCreatebyid(loginUser.getUserid());
        pitenantuserEntity.setListerid(loginUser.getUserid());
        this.pitenantuserMapper.insert(pitenantuserEntity);
        return this.getEntity(piuserEntity.getUserid());
    }

    /**
     * 新增数据
     *
     * @param piuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public PiuserPojo create(PiuserPojo piuserPojo) throws Exception {
        //初始化NULL字段
        if (piuserPojo.getUsername() == null) piuserPojo.setUsername("");
        if (piuserPojo.getRealname() == null) piuserPojo.setRealname("");
        if (piuserPojo.getNickname() == null) piuserPojo.setNickname("");
        if (piuserPojo.getMobile() == null) piuserPojo.setMobile("");
        if (piuserPojo.getEmail() == null) piuserPojo.setEmail("");
        if (piuserPojo.getSex() == null) piuserPojo.setSex(0);
        if (piuserPojo.getLangcode() == null) piuserPojo.setLangcode("");
        if (piuserPojo.getAvatar() == null) piuserPojo.setAvatar("");
        if (piuserPojo.getUserstatus() == null) piuserPojo.setUserstatus(0);
        if (piuserPojo.getUsercode() == null) piuserPojo.setUsercode("");
        if (piuserPojo.getRemark() == null) piuserPojo.setRemark("");
        if (piuserPojo.getLister() == null) piuserPojo.setLister("");
        if (piuserPojo.getCreateby() == null) piuserPojo.setCreateby("");
        if (piuserPojo.getCreatedate() == null) piuserPojo.setCreatedate(new Date());
        if (piuserPojo.getModifydate() == null) piuserPojo.setModifydate(new Date());
        if (piuserPojo.getRevision() == null) piuserPojo.setRevision(0);
        PiuserEntity piuserEntity = new PiuserEntity();
        BeanUtils.copyProperties(piuserPojo, piuserEntity);

        piuserEntity.setUserid(inksSnowflake.getSnowflake().nextIdStr());
        this.piuserMapper.insert(piuserEntity);

        String password = AESUtil.Encrypt(piuserPojo.getPassword());
        PiuserloginEntity piuserloginEntity = new PiuserloginEntity();
        piuserloginEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        piuserloginEntity.setUserid(piuserEntity.getUserid());
        piuserloginEntity.setUserpassword(password);
        piuserloginEntity.setLister(piuserEntity.getLister());
        piuserloginEntity.setCreatedate(new Date());
        piuserloginEntity.setModifydate(new Date());
        piuserloginEntity.setCheckipaddr(0);
        this.piuserloginMapper.insert(piuserloginEntity);

        return this.getEntity(piuserEntity.getUserid());

    }
}
