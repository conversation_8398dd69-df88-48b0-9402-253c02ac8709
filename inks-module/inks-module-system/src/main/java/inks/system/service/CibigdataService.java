package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CibigdataPojo;
import inks.system.domain.pojo.CibigdataitemdetailPojo;

import java.util.List;

/**
 * 数据大屏(Cibigdata)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-25 10:32:02
 */
public interface CibigdataService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CibigdataPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CibigdataitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CibigdataPojo getBillEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CibigdataPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CibigdataPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param cibigdataPojo 实例对象
     * @return 实例对象
     */
    CibigdataPojo insert(CibigdataPojo cibigdataPojo);

    /**
     * 修改数据
     *
     * @param cibigdatapojo 实例对象
     * @return 实例对象
     */
    CibigdataPojo update(CibigdataPojo cibigdatapojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

   // 查询租户下的大屏
    List<CibigdataPojo> getListByTenant(String key);


}
