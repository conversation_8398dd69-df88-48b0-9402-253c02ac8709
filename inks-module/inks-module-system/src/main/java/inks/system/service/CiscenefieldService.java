package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiscenefieldPojo;

import java.util.List;

/**
 * 场景字段(Ciscenefield)表服务接口
 *
 * <AUTHOR>
 * @since 2022-11-24 11:35:06
 */
public interface CiscenefieldService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiscenefieldPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CiscenefieldPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param ciscenefieldPojo 实例对象
     * @return 实例对象
     */
    CiscenefieldPojo insert(CiscenefieldPojo ciscenefieldPojo);

    /**
     * 修改数据
     *
     * @param ciscenefieldpojo 实例对象
     * @return 实例对象
     */
    CiscenefieldPojo update(CiscenefieldPojo ciscenefieldpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);


    /**
     * 分页查询
     *
     * @param code 筛选条件
     * @return 查询结果
     */
    List<CiscenefieldPojo> getListByCode(String code);
}
