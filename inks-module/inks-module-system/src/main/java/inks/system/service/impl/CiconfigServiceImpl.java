package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.constant.CacheConstants;
import inks.common.core.constant.InksConstants;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.redis.service.RedisService;
import inks.system.domain.CiconfigEntity;
import inks.system.domain.pojo.CiconfigPojo;
import inks.system.domain.pojo.PitenantPojo;
import inks.system.domain.pojo.PitenantuserPojo;
import inks.system.mapper.CiconfigMapper;
import inks.system.mapper.PitenantMapper;
import inks.system.mapper.PitenantuserMapper;
import inks.system.service.CiconfigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 系统参数(Ciconfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-27 18:30:51
 */
@Service("ciconfigService")
public class CiconfigServiceImpl implements CiconfigService {
    @Resource
    private CiconfigMapper ciconfigMapper;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    @Autowired
    private PitenantMapper pitenantMapper;
    @Autowired
    private PitenantuserMapper pitenantuserMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CiconfigPojo getEntity(String key, String tid) {
        return this.ciconfigMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CiconfigPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiconfigPojo> lst = ciconfigMapper.getPageList(queryParam);
            PageInfo<CiconfigPojo> pageInfo = new PageInfo<CiconfigPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param ciconfigPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiconfigPojo insert(CiconfigPojo ciconfigPojo) {
        //初始化NULL字段
        if (ciconfigPojo.getParentid() == null) ciconfigPojo.setParentid("");
        if (ciconfigPojo.getCfgname() == null) ciconfigPojo.setCfgname("");
        if (ciconfigPojo.getCfgkey() == null) ciconfigPojo.setCfgkey("");
        if (ciconfigPojo.getCfgvalue() == null) ciconfigPojo.setCfgvalue("");
        if (ciconfigPojo.getCfgtype() == null) ciconfigPojo.setCfgtype(0);
        if (ciconfigPojo.getCfglevel() == null) ciconfigPojo.setCfglevel(0);
        if (ciconfigPojo.getCtrltype() == null) ciconfigPojo.setCtrltype(0);
        if (ciconfigPojo.getCfgicon() == null) ciconfigPojo.setCfgicon("");
        if (ciconfigPojo.getCfgoption() == null) ciconfigPojo.setCfgoption("");
        if (ciconfigPojo.getAllowui() == null) ciconfigPojo.setAllowui(0);
        if (ciconfigPojo.getRownum() == null) ciconfigPojo.setRownum(0);
        if (ciconfigPojo.getEnabledmark() == null) ciconfigPojo.setEnabledmark(0);
        if (ciconfigPojo.getAllowdelete() == null) ciconfigPojo.setAllowdelete(0);
        if (ciconfigPojo.getSpecialmark() == null) ciconfigPojo.setSpecialmark(0);
        if (ciconfigPojo.getRemark() == null) ciconfigPojo.setRemark("");
        if (ciconfigPojo.getAppsetting() == null) ciconfigPojo.setAppsetting(0);
        if (ciconfigPojo.getCreateby() == null) ciconfigPojo.setCreateby("");
        if (ciconfigPojo.getCreatebyid() == null) ciconfigPojo.setCreatebyid("");
        if (ciconfigPojo.getCreatedate() == null) ciconfigPojo.setCreatedate(new Date());
        if (ciconfigPojo.getLister() == null) ciconfigPojo.setLister("");
        if (ciconfigPojo.getListerid() == null) ciconfigPojo.setListerid("");
        if (ciconfigPojo.getModifydate() == null) ciconfigPojo.setModifydate(new Date());
        if (ciconfigPojo.getCustom1() == null) ciconfigPojo.setCustom1("");
        if (ciconfigPojo.getCustom2() == null) ciconfigPojo.setCustom2("");
        if (ciconfigPojo.getCustom3() == null) ciconfigPojo.setCustom3("");
        if (ciconfigPojo.getCustom4() == null) ciconfigPojo.setCustom4("");
        if (ciconfigPojo.getCustom5() == null) ciconfigPojo.setCustom5("");
        if (ciconfigPojo.getUserid() == null) ciconfigPojo.setUserid("");
        if (ciconfigPojo.getTenantid() == null) ciconfigPojo.setTenantid("");
        if (ciconfigPojo.getRevision() == null) ciconfigPojo.setRevision(0);
        CiconfigEntity ciconfigEntity = new CiconfigEntity();
        BeanUtils.copyProperties(ciconfigPojo, ciconfigEntity);

        ciconfigEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        ciconfigEntity.setRevision(1);  //乐观锁
        this.ciconfigMapper.insert(ciconfigEntity);
        return this.getEntity(ciconfigEntity.getId(), ciconfigEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param ciconfigPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiconfigPojo update(CiconfigPojo ciconfigPojo) {
        CiconfigEntity ciconfigEntity = new CiconfigEntity();
        BeanUtils.copyProperties(ciconfigPojo, ciconfigEntity);
        this.ciconfigMapper.update(ciconfigEntity);
        return this.getEntity(ciconfigEntity.getId(), ciconfigEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.ciconfigMapper.delete(key, tid);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CiconfigPojo getEntityByKey(String key, String tid) {
        return this.ciconfigMapper.getEntityByKey(key, tid);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CiconfigPojo getEntityByKeyUser(String key, String userid, String tid) {
        return this.ciconfigMapper.getEntityByKeyUser(key, userid, tid);
    }

    /**
     * 通过key获得值
     *
     * @param key 主键
     * @return 值
     */
    @Override
    public String getConfigValue(String key, String tid, String userid) {
        //用户级参数
        CiconfigPojo ciconfigPojo = this.ciconfigMapper.getEntityByKeyUser(key, userid, tid);
        if (ciconfigPojo == null) {
            //租户级参数
            ciconfigPojo = this.ciconfigMapper.getEntityByKey(key, tid);
        }
        if (ciconfigPojo == null) {
            //默认级参数
            ciconfigPojo = this.ciconfigMapper.getEntityByKey(key, InksConstants.DEFAULT_TENANT);
        }
        if (ciconfigPojo == null) {
            return null;
        } else {
            return ciconfigPojo.getCfgvalue();
        }

    }

    /**
     * 修改数据
     *
     * @param lst 例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public int updateList(List<CiconfigPojo> lst, LoginUser loginUser) {
        int num = 0;
        for (CiconfigPojo pojo : lst) {
            // 非一级参数
            if (!pojo.getParentid().equals("") && !pojo.getParentid().equals("root")) {
                // 传来是默认租户，新建
                if (pojo.getTenantid().equals("default")) {
                    // 避免重复提交，根据key查询数据库
                    CiconfigPojo ciconfigPojo = this.ciconfigMapper.getEntityByKey(pojo.getCfgkey(), loginUser.getTenantid());
                    if (ciconfigPojo == null) {
                        pojo.setId(inksSnowflake.getSnowflake().nextIdStr());
                        pojo.setTenantid(loginUser.getTenantid());
                        pojo.setTenantname(loginUser.getTenantinfo().getTenantname());
                        pojo.setLister(loginUser.getRealName());
                        pojo.setListerid(loginUser.getUserid());
                        pojo.setModifydate(new Date());
                        pojo.setCreatebyid(loginUser.getUserid());
                        pojo.setCreateby(loginUser.getRealName());
                        pojo.setCreatedate(new Date());
                        pojo.setRevision(1);
                        CiconfigEntity entity = new CiconfigEntity();
                        BeanUtils.copyProperties(pojo, entity);
                        this.ciconfigMapper.insert(entity);
                    } else {
                        ciconfigPojo.setCfgvalue(pojo.getCfgvalue());
                        // 原参数
                        ciconfigPojo.setTenantid(loginUser.getTenantid());
                        ciconfigPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
                        ciconfigPojo.setLister(loginUser.getRealName());
                        ciconfigPojo.setListerid(loginUser.getUserid());
                        ciconfigPojo.setModifydate(new Date());
                        CiconfigEntity entity = new CiconfigEntity();
                        BeanUtils.copyProperties(ciconfigPojo, entity);
                        this.ciconfigMapper.update(entity);
                        num++;
                    }
                    num++;
                } else {
                    // 原参数
                    pojo.setTenantid(loginUser.getTenantid());
                    pojo.setTenantname(loginUser.getTenantinfo().getTenantname());
                    pojo.setLister(loginUser.getRealName());
                    pojo.setListerid(loginUser.getUserid());
                    pojo.setModifydate(new Date());
                    CiconfigEntity entity = new CiconfigEntity();
                    BeanUtils.copyProperties(pojo, entity);
                    this.ciconfigMapper.update(entity);
                    num++;
                }
            }
        }
        return num;
    }

    /**
     * 通过key获得值
     *
     * @param tid 主键
     * @return 值
     */
    @Override
    public List<CiconfigPojo> getListByTenant(String tid) {
        return this.ciconfigMapper.getListByTenant(tid);
    }

    /**
     * 通过key获得值
     *
     * @param tid 主键
     * @return 值
     */
    @Override
    public Map<String, String> getMapByTenant(Integer db, String tid) {
        //从redis中获取Reprot内容
        Map<String, String> mapConfig = this.redisService.getCacheObject(CacheConstants.TENANT_CONFIG_KEY + tid);
        if (mapConfig == null || db == 1) {
            mapConfig = new HashMap<>();
            List<CiconfigPojo> lst = this.ciconfigMapper.getListByTenant(tid);
            for (CiconfigPojo pojo : lst) {
                mapConfig.put(pojo.getCfgkey(), pojo.getCfgvalue());
            }
            this.redisService.setCacheObject(CacheConstants.TENANT_CONFIG_KEY + tid, mapConfig, (long) (30), TimeUnit.DAYS);
        }

        return mapConfig;
    }

    /**
     * 通过key获得值
     *
     * @param tid 主键
     * @return 值
     */
    @Override
    public Map<String, String> getMapByTenUi(Integer db, String tid) {
        //从redis中获取Reprot内容
        Map<String, String> mapConfig = this.redisService.getCacheObject(CacheConstants.TENANT_CONFIG_KEY + "_UI_" + tid);
        if (mapConfig == null || db == 1) {
            mapConfig = new HashMap<>();
            List<CiconfigPojo> lst = this.ciconfigMapper.getListByTenUi(tid);
            for (CiconfigPojo pojo : lst) {
                mapConfig.put(pojo.getCfgkey(), pojo.getCfgvalue());
            }
            this.redisService.setCacheObject(CacheConstants.TENANT_CONFIG_KEY + "_UI_" + tid, mapConfig, (long) (30), TimeUnit.DAYS);
        }

        return mapConfig;
    }

    /**
     * 通过key获得值
     *
     * @param tid 主键
     * @return 值
     */
    @Override
    public Map<String, String> getMapByUserUi(String userid, String tid) {
        //从redis中获取Reprot内容
        Map<String, String> mapConfig = this.redisService.getCacheObject(CacheConstants.USER_CONFIG_KEY + "_UI_" + tid + "_" + userid);
        if (mapConfig == null) {
            mapConfig = new HashMap<String, String>();
            List<CiconfigPojo> lst = this.ciconfigMapper.getListByUserUi(userid, tid);
            for (CiconfigPojo pojo : lst) {
                mapConfig.put(pojo.getCfgkey(), pojo.getCfgvalue());
            }
            this.redisService.setCacheObject(CacheConstants.USER_CONFIG_KEY + "_UI_" + tid + "_" + userid, mapConfig, (long) (30), TimeUnit.DAYS);
        }
        return mapConfig;
    }

    /**
     * 通过key获得值
     *
     * @return 值
     */
    @Override
    public List<CiconfigPojo> getListByDefault() {
        return this.ciconfigMapper.getListByDefault();
    }


    /**
     * 通过key获得值
     *
     * @return 值
     */
    @Override
    public List<CiconfigPojo> getListByPrefix(String key, String tid) {
        return this.ciconfigMapper.getListByPrefix(key, tid);
    }

    private static final String CHECK_ONLINE_KEY = "system.user.checkonline";
    private static final String CHECK_ONLINE_NAME = "禁止同时登录";

    @Override
    public String unCheckOnline(String username, boolean ischeck, String tid) {
        String cfgvalue_checkonline = ischeck ? "true" : "false";
        int insertCount = 0; // 插入的条数
        int updateCount = 0; // 修改的条数
        if (StringUtils.isNotBlank(tid)) {
            if (insertOrUpdateConfig(tid, cfgvalue_checkonline, "通过unCheckOnline方法ByTid插入")) {
                insertCount++;
            } else {
                updateCount++;
            }
        }
        if (StringUtils.isNotBlank(username)) {
            List<PitenantuserPojo> list = pitenantuserMapper.getListByUserName(username);
            for (PitenantuserPojo pojo : list) {
                if (insertOrUpdateConfig(pojo.getTenantid(), cfgvalue_checkonline, "通过unCheckOnline方法ByUserName插入,UserName: " + username)) {
                    insertCount++;
                } else {
                    updateCount++;
                }
            }
        }
        return (ischeck ? "加入" : "取消") + "登录检查 " + CHECK_ONLINE_KEY + ": 成功插入" + insertCount + "条数据，修改" + updateCount + "条数据";
    }

    private boolean insertOrUpdateConfig(String tid, String cfgvalue, String remark) {
        CiconfigPojo entityByKey = ciconfigMapper.getEntityByKey(CHECK_ONLINE_KEY, tid);
        if (entityByKey == null) {
            PitenantPojo tenantDB = pitenantMapper.getEntity(tid);
            entityByKey = new CiconfigPojo();
            entityByKey.setCfgname(CHECK_ONLINE_NAME);
            entityByKey.setCfgkey(CHECK_ONLINE_KEY);
            entityByKey.setCfgvalue(cfgvalue);
            entityByKey.setTenantid(tid);
            entityByKey.setTenantname(tenantDB.getTenantname());
            entityByKey.setRemark(remark);
            insert(entityByKey);
            return true; // 插入了一条新的记录
        } else {
            entityByKey.setCfgvalue(cfgvalue);
            entityByKey.setRemark(remark);
            update(entityByKey);
            return false; // 更新了一条已存在的记录
        }
    }

    @Override
    public List<Map<String, String>> getMicroAppMapList() {
        List<Map<String, String>> result = new ArrayList<>();
        try {
            // 直接查询平台级微应用配置 (CfgLevel=0, CfgKey like 'platform.microapp.%')
            List<CiconfigPojo> microAppList = ciconfigMapper.getMicroAppList();
            
            // 转换为List<Map>格式，每个Map包含key和value
            for (CiconfigPojo config : microAppList) {
                String cfgKey = config.getCfgkey();
                String cfgValue = config.getCfgvalue();
                
                if (cfgKey != null && cfgValue != null) {
                    // 提取最后一个点后的部分并转大写
                    int lastDotIndex = cfgKey.lastIndexOf('.');
                    if (lastDotIndex != -1 && lastDotIndex < cfgKey.length() - 1) {
                        String keyPart = cfgKey.substring(lastDotIndex + 1).toUpperCase();
                        
                        Map<String, String> item = new HashMap<>();
                        item.put("key", keyPart);
                        item.put("value", cfgValue);
                        result.add(item);
                    }
                }
            }
        } catch (Exception e) {
            // 记录异常但不抛出，返回空List
            e.printStackTrace();
        }
        return result;
    }
}
