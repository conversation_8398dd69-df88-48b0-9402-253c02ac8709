package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.pojo.CinoticePojo;
import inks.system.domain.CinoticeEntity;
import inks.system.mapper.CinoticeMapper;
import inks.system.service.CinoticeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 公告(Cinotice)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-19 19:27:31
 */
@Service("cinoticeService")
public class CinoticeServiceImpl implements CinoticeService {
    @Resource
    private CinoticeMapper cinoticeMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CinoticePojo getEntity(String key, String tid) {
        return this.cinoticeMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CinoticePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CinoticePojo> lst = cinoticeMapper.getPageList(queryParam);
            PageInfo<CinoticePojo> pageInfo = new PageInfo<CinoticePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param cinoticePojo 实例对象
     * @return 实例对象
     */
    @Override
    public CinoticePojo insert(CinoticePojo cinoticePojo) {
    //初始化NULL字段
     if(cinoticePojo.getNoticecode()==null) cinoticePojo.setNoticecode("");
     if(cinoticePojo.getNoticetype()==null) cinoticePojo.setNoticetype("");
     if(cinoticePojo.getNoticetitle()==null) cinoticePojo.setNoticetitle("");
     if(cinoticePojo.getNoticecontent()==null) cinoticePojo.setNoticecontent("");
     if(cinoticePojo.getNoticedate()==null) cinoticePojo.setNoticedate(new Date());
     if(cinoticePojo.getRownum()==null) cinoticePojo.setRownum(0);
     if(cinoticePojo.getEnabledmark()==null) cinoticePojo.setEnabledmark(0);
     if(cinoticePojo.getTenantid()==null) cinoticePojo.setTenantid("");
     if(cinoticePojo.getFunctionid()==null) cinoticePojo.setFunctionid("");
     if(cinoticePojo.getFunctioncode()==null) cinoticePojo.setFunctioncode("");
     if(cinoticePojo.getFunctionname()==null) cinoticePojo.setFunctionname("");
     if(cinoticePojo.getRemark()==null) cinoticePojo.setRemark("");
     if(cinoticePojo.getCreateby()==null) cinoticePojo.setCreateby("");
     if(cinoticePojo.getCreatebyid()==null) cinoticePojo.setCreatebyid("");
     if(cinoticePojo.getCreatedate()==null) cinoticePojo.setCreatedate(new Date());
     if(cinoticePojo.getLister()==null) cinoticePojo.setLister("");
     if(cinoticePojo.getListerid()==null) cinoticePojo.setListerid("");
     if(cinoticePojo.getModifydate()==null) cinoticePojo.setModifydate(new Date());
     if(cinoticePojo.getRevision()==null) cinoticePojo.setRevision(0);
        CinoticeEntity cinoticeEntity = new CinoticeEntity(); 
        BeanUtils.copyProperties(cinoticePojo,cinoticeEntity);
        
          cinoticeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          cinoticeEntity.setRevision(1);  //乐观锁
          this.cinoticeMapper.insert(cinoticeEntity);
        return this.getEntity(cinoticeEntity.getId(),cinoticeEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param cinoticePojo 实例对象
     * @return 实例对象
     */
    @Override
    public CinoticePojo update(CinoticePojo cinoticePojo) {
        CinoticeEntity cinoticeEntity = new CinoticeEntity(); 
        BeanUtils.copyProperties(cinoticePojo,cinoticeEntity);
        this.cinoticeMapper.update(cinoticeEntity);
        return this.getEntity(cinoticeEntity.getId(),cinoticeEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.cinoticeMapper.delete(key,tid) ;
    }
}
