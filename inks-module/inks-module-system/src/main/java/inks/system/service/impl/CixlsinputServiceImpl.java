package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.constant.InksConstants;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CixlsinputEntity;
import inks.system.domain.pojo.CixlsinputPojo;
import inks.system.mapper.CixlsinputMapper;
import inks.system.service.CixlsinputService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * xls导入格式(Cixlsinput)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-24 16:29:32
 */
@Service("cixlsinputService")
public class CixlsinputServiceImpl implements CixlsinputService {
    @Resource
    private CixlsinputMapper cixlsinputMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CixlsinputPojo getEntity(String key, String tid) {
        return this.cixlsinputMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CixlsinputPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CixlsinputPojo> lst = cixlsinputMapper.getPageList(queryParam);
            PageInfo<CixlsinputPojo> pageInfo = new PageInfo<CixlsinputPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param cixlsinputPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CixlsinputPojo insert(CixlsinputPojo cixlsinputPojo) {
    //初始化NULL字段
     if(cixlsinputPojo.getModulecode()==null) cixlsinputPojo.setModulecode("");
     if(cixlsinputPojo.getFiletype()==null) cixlsinputPojo.setFiletype("");
     if(cixlsinputPojo.getFilename()==null) cixlsinputPojo.setFilename("");
     if(cixlsinputPojo.getTitlejson()==null) cixlsinputPojo.setTitlejson("");
     if(cixlsinputPojo.getRownum()==null) cixlsinputPojo.setRownum(0);
     if(cixlsinputPojo.getEnabledmark()==null) cixlsinputPojo.setEnabledmark(0);
     if(cixlsinputPojo.getRemark()==null) cixlsinputPojo.setRemark("");
     if(cixlsinputPojo.getCreateby()==null) cixlsinputPojo.setCreateby("");
     if(cixlsinputPojo.getCreatebyid()==null) cixlsinputPojo.setCreatebyid("");
     if(cixlsinputPojo.getCreatedate()==null) cixlsinputPojo.setCreatedate(new Date());
     if(cixlsinputPojo.getLister()==null) cixlsinputPojo.setLister("");
     if(cixlsinputPojo.getListerid()==null) cixlsinputPojo.setListerid("");
     if(cixlsinputPojo.getModifydate()==null) cixlsinputPojo.setModifydate(new Date());
     if(cixlsinputPojo.getCustom1()==null) cixlsinputPojo.setCustom1("");
     if(cixlsinputPojo.getCustom2()==null) cixlsinputPojo.setCustom2("");
     if(cixlsinputPojo.getCustom3()==null) cixlsinputPojo.setCustom3("");
     if(cixlsinputPojo.getCustom4()==null) cixlsinputPojo.setCustom4("");
     if(cixlsinputPojo.getCustom5()==null) cixlsinputPojo.setCustom5("");
     if(cixlsinputPojo.getTenantid()==null) cixlsinputPojo.setTenantid("");
     if(cixlsinputPojo.getTenantname()==null) cixlsinputPojo.setTenantname("");
     if(cixlsinputPojo.getRevision()==null) cixlsinputPojo.setRevision(0);
        CixlsinputEntity cixlsinputEntity = new CixlsinputEntity(); 
        BeanUtils.copyProperties(cixlsinputPojo,cixlsinputEntity);
          //生成雪花id
          cixlsinputEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          cixlsinputEntity.setRevision(1);  //乐观锁
          this.cixlsinputMapper.insert(cixlsinputEntity);
        return this.getEntity(cixlsinputEntity.getId(),cixlsinputEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param cixlsinputPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CixlsinputPojo update(CixlsinputPojo cixlsinputPojo) {
        CixlsinputEntity cixlsinputEntity = new CixlsinputEntity(); 
        BeanUtils.copyProperties(cixlsinputPojo,cixlsinputEntity);
        this.cixlsinputMapper.update(cixlsinputEntity);
        return this.getEntity(cixlsinputEntity.getId(),cixlsinputEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.cixlsinputMapper.delete(key,tid) ;
    }

    /**
     * 通过功能号获得报表样式
     *
     * @param moduleCode 主键
     * @return 是否成功
     */
    @Override
    public List<CixlsinputPojo> getListByModuleCode(String moduleCode, String tid) {
        try {
            //自定义报表
            List<CixlsinputPojo> lst = cixlsinputMapper.getListByModuleCode(moduleCode, tid);
            //默认格式
            List<CixlsinputPojo> lstdef = cixlsinputMapper.getListByModuleCode(moduleCode, InksConstants.DEFAULT_TENANT);
            lst.addAll(lstdef);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }
}
