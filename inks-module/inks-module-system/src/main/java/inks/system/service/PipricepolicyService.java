package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PipricepolicyPojo;
import inks.system.domain.pojo.PipricepolicyitemdetailPojo;

/**
 * 功能价格(Pipricepolicy)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-08 14:32:50
 */
public interface PipricepolicyService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PipricepolicyPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PipricepolicyitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PipricepolicyPojo getBillEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PipricepolicyPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PipricepolicyPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pipricepolicyPojo 实例对象
     * @return 实例对象
     */
    PipricepolicyPojo insert(PipricepolicyPojo pipricepolicyPojo);

    /**
     * 修改数据
     *
     * @param pipricepolicypojo 实例对象
     * @return 实例对象
     */
    PipricepolicyPojo update(PipricepolicyPojo pipricepolicypojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 审核数据
     *
     * @param pipricepolicyPojo 实例对象
     * @return 实例对象
     */
    PipricepolicyPojo approval(PipricepolicyPojo pipricepolicyPojo);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PipricepolicyPojo getBillEntityByFunction(String key);

}
