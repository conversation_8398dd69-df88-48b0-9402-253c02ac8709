package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PifunctionEntity;
import inks.system.domain.pojo.PifunctionPojo;
import inks.system.mapper.PifunctionMapper;
import inks.system.service.PifunctionService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 服务总表(Pifunction)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-29 20:02:56
 */
@Service("pifunctionService")
public class PifunctionServiceImpl implements PifunctionService {
    @Resource
    private PifunctionMapper pifunctionMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PifunctionPojo getEntity(String key) {
        return this.pifunctionMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PifunctionPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PifunctionPojo> lst = pifunctionMapper.getPageList(queryParam);
            PageInfo<PifunctionPojo> pageInfo = new PageInfo<PifunctionPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param pifunctionPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PifunctionPojo insert(PifunctionPojo pifunctionPojo) {
    //初始化NULL字段
     if(pifunctionPojo.getFunctioncode()==null) pifunctionPojo.setFunctioncode("");
     if(pifunctionPojo.getFunctionname()==null) pifunctionPojo.setFunctionname("");
     if(pifunctionPojo.getDescription()==null) pifunctionPojo.setDescription("");
     if(pifunctionPojo.getFrontphoto()==null) pifunctionPojo.setFrontphoto("");
     if(pifunctionPojo.getPublicmark()==null) pifunctionPojo.setPublicmark(0);
     if(pifunctionPojo.getEnabledmark()==null) pifunctionPojo.setEnabledmark(0);
     if(pifunctionPojo.getReleasemark()==null) pifunctionPojo.setReleasemark(0);
     if(pifunctionPojo.getRownum()==null) pifunctionPojo.setRownum(0);
     if(pifunctionPojo.getWeight()==null) pifunctionPojo.setWeight(0);
     if(pifunctionPojo.getRemark()==null) pifunctionPojo.setRemark("");
     if(pifunctionPojo.getFunctionurl()==null) pifunctionPojo.setFunctionurl("");
     if(pifunctionPojo.getCreateby()==null) pifunctionPojo.setCreateby("");
     if(pifunctionPojo.getCreatebyid()==null) pifunctionPojo.setCreatebyid("");
     if(pifunctionPojo.getCreatedate()==null) pifunctionPojo.setCreatedate(new Date());
     if(pifunctionPojo.getLister()==null) pifunctionPojo.setLister("");
     if(pifunctionPojo.getListerid()==null) pifunctionPojo.setListerid("");
     if(pifunctionPojo.getModifydate()==null) pifunctionPojo.setModifydate(new Date());
     if(pifunctionPojo.getDeletemark()==null) pifunctionPojo.setDeletemark(0);
     if(pifunctionPojo.getDeletelister()==null) pifunctionPojo.setDeletelister("");
     if(pifunctionPojo.getDeletelisterid()==null) pifunctionPojo.setDeletelisterid("");
     if(pifunctionPojo.getDeletedate()==null) pifunctionPojo.setDeletedate(new Date());
     if(pifunctionPojo.getRevision()==null) pifunctionPojo.setRevision(0);
        PifunctionEntity pifunctionEntity = new PifunctionEntity(); 
        BeanUtils.copyProperties(pifunctionPojo,pifunctionEntity);
        
          pifunctionEntity.setFunctionid(inksSnowflake.getSnowflake().nextIdStr());
          pifunctionEntity.setRevision(1);  //乐观锁
          this.pifunctionMapper.insert(pifunctionEntity);
        return this.getEntity(pifunctionEntity.getFunctionid());
  
    }

    /**
     * 修改数据
     *
     * @param pifunctionPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PifunctionPojo update(PifunctionPojo pifunctionPojo) {
        PifunctionEntity pifunctionEntity = new PifunctionEntity(); 
        BeanUtils.copyProperties(pifunctionPojo,pifunctionEntity);
        this.pifunctionMapper.update(pifunctionEntity);
        return this.getEntity(pifunctionEntity.getFunctionid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pifunctionMapper.delete(key) ;
    }

    @Override
    public List<PifunctionPojo> getFunctionListBySelf(String tenantid) {
        return this.pifunctionMapper.getFunctionListBySelf(tenantid);
    }
}
