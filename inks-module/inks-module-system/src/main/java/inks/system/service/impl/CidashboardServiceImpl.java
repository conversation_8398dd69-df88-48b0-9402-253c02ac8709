package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.pojo.CidashboardPojo;
import inks.system.domain.CidashboardEntity;
import inks.system.mapper.CidashboardMapper;
import inks.system.service.CidashboardService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 工作台(Cidashboard)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-16 16:20:57
 */
@Service("cidashboardService")
public class CidashboardServiceImpl implements CidashboardService {
    @Resource
    private CidashboardMapper cidashboardMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CidashboardPojo getEntity(String key, String tid) {
        return this.cidashboardMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CidashboardPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CidashboardPojo> lst = cidashboardMapper.getPageList(queryParam);
            PageInfo<CidashboardPojo> pageInfo = new PageInfo<CidashboardPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param cidashboardPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CidashboardPojo insert(CidashboardPojo cidashboardPojo) {
        //初始化NULL字段
        if (cidashboardPojo.getGengroupid() == null) cidashboardPojo.setGengroupid("");
        if (cidashboardPojo.getDashcode() == null) cidashboardPojo.setDashcode("");
        if (cidashboardPojo.getDashname() == null) cidashboardPojo.setDashname("");
        if (cidashboardPojo.getDashdesc() == null) cidashboardPojo.setDashdesc("");
        if (cidashboardPojo.getFrontphoto() == null) cidashboardPojo.setFrontphoto("");
        if (cidashboardPojo.getMvcurl() == null) cidashboardPojo.setMvcurl("");
        if (cidashboardPojo.getPermcode() == null) cidashboardPojo.setPermcode("");
        if (cidashboardPojo.getEnabledmark() == null) cidashboardPojo.setEnabledmark(0);
        if (cidashboardPojo.getRemark() == null) cidashboardPojo.setRemark("");
        if (cidashboardPojo.getCreateby() == null) cidashboardPojo.setCreateby("");
        if (cidashboardPojo.getCreatebyid() == null) cidashboardPojo.setCreatebyid("");
        if (cidashboardPojo.getCreatedate() == null) cidashboardPojo.setCreatedate(new Date());
        if (cidashboardPojo.getLister() == null) cidashboardPojo.setLister("");
        if (cidashboardPojo.getListerid() == null) cidashboardPojo.setListerid("");
        if (cidashboardPojo.getModifydate() == null) cidashboardPojo.setModifydate(new Date());
        if (cidashboardPojo.getTenantid() == null) cidashboardPojo.setTenantid("");
        if (cidashboardPojo.getTenantname() == null) cidashboardPojo.setTenantname("");
        if (cidashboardPojo.getRevision() == null) cidashboardPojo.setRevision(0);
        CidashboardEntity cidashboardEntity = new CidashboardEntity();
        BeanUtils.copyProperties(cidashboardPojo, cidashboardEntity);

        cidashboardEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        cidashboardEntity.setRevision(1);  //乐观锁
        this.cidashboardMapper.insert(cidashboardEntity);
        return this.getEntity(cidashboardEntity.getId(), cidashboardEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param cidashboardPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CidashboardPojo update(CidashboardPojo cidashboardPojo) {
        CidashboardEntity cidashboardEntity = new CidashboardEntity();
        BeanUtils.copyProperties(cidashboardPojo, cidashboardEntity);
        this.cidashboardMapper.update(cidashboardEntity);
        return this.getEntity(cidashboardEntity.getId(), cidashboardEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.cidashboardMapper.delete(key, tid);
    }


}
