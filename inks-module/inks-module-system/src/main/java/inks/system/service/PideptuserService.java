package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DeptinfoPojo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PideptuserPojo;

import java.util.List;

/**
 * 组织用户表(Pideptuser)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-28 11:18:02
 */
public interface PideptuserService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PideptuserPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PideptuserPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pideptuserPojo 实例对象
     * @return 实例对象
     */
    PideptuserPojo insert(PideptuserPojo pideptuserPojo);

    /**
     * 修改数据
     *
     * @param pideptuserpojo 实例对象
     * @return 实例对象
     */
    PideptuserPojo update(PideptuserPojo pideptuserpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param userid 主键
     * @return 实例对象
     */
    PideptuserPojo getEntityByUser(String userid, String tid) ;


    /**
     * 分页查询
     *
     * @param userid 筛选条件
     * @return 查询结果
     */
    List<DeptinfoPojo> getDeptinfoList(String userid, String tid);

}
