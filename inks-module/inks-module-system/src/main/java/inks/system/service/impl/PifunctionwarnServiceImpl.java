package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.pojo.PifunctionconfigPojo;
import inks.system.domain.pojo.PifunctionwarnPojo;
import inks.system.domain.PifunctionwarnEntity;
import inks.system.mapper.PifunctionwarnMapper;
import inks.system.service.PifunctionwarnService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 服务预警关系(Pifunctionwarn)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-05 20:11:43
 */
@Service("pifunctionwarnService")
public class PifunctionwarnServiceImpl implements PifunctionwarnService {
    @Resource
    private PifunctionwarnMapper pifunctionwarnMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PifunctionwarnPojo getEntity(String key) {
        return this.pifunctionwarnMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PifunctionwarnPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PifunctionwarnPojo> lst = pifunctionwarnMapper.getPageList(queryParam);
            PageInfo<PifunctionwarnPojo> pageInfo = new PageInfo<PifunctionwarnPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param pifunctionwarnPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PifunctionwarnPojo insert(PifunctionwarnPojo pifunctionwarnPojo) {
        //初始化NULL字段
        if (pifunctionwarnPojo.getFunctionid() == null) pifunctionwarnPojo.setFunctionid("");
        if (pifunctionwarnPojo.getFunctioncode() == null) pifunctionwarnPojo.setFunctioncode("");
        if (pifunctionwarnPojo.getFunctionname() == null) pifunctionwarnPojo.setFunctionname("");
        if (pifunctionwarnPojo.getWarnid() == null) pifunctionwarnPojo.setWarnid("");
        if (pifunctionwarnPojo.getWarncode() == null) pifunctionwarnPojo.setWarncode("");
        if (pifunctionwarnPojo.getWarnname() == null) pifunctionwarnPojo.setWarnname("");
        if (pifunctionwarnPojo.getRemark() == null) pifunctionwarnPojo.setRemark("");
        if (pifunctionwarnPojo.getCreateby() == null) pifunctionwarnPojo.setCreateby("");
        if (pifunctionwarnPojo.getCreatebyid() == null) pifunctionwarnPojo.setCreatebyid("");
        if (pifunctionwarnPojo.getCreatedate() == null) pifunctionwarnPojo.setCreatedate(new Date());
        if (pifunctionwarnPojo.getLister() == null) pifunctionwarnPojo.setLister("");
        if (pifunctionwarnPojo.getListerid() == null) pifunctionwarnPojo.setListerid("");
        if (pifunctionwarnPojo.getModifydate() == null) pifunctionwarnPojo.setModifydate(new Date());
        if (pifunctionwarnPojo.getRevision() == null) pifunctionwarnPojo.setRevision(0);
        PifunctionwarnEntity pifunctionwarnEntity = new PifunctionwarnEntity();
        BeanUtils.copyProperties(pifunctionwarnPojo, pifunctionwarnEntity);

        pifunctionwarnEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        pifunctionwarnEntity.setRevision(1);  //乐观锁
        this.pifunctionwarnMapper.insert(pifunctionwarnEntity);
        return this.getEntity(pifunctionwarnEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param pifunctionwarnPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PifunctionwarnPojo update(PifunctionwarnPojo pifunctionwarnPojo) {
        PifunctionwarnEntity pifunctionwarnEntity = new PifunctionwarnEntity();
        BeanUtils.copyProperties(pifunctionwarnPojo, pifunctionwarnEntity);
        this.pifunctionwarnMapper.update(pifunctionwarnEntity);
        return this.getEntity(pifunctionwarnEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pifunctionwarnMapper.delete(key);
    }

    @Override
    public List<PifunctionwarnPojo> getListByFunction(String key) {
        return this.pifunctionwarnMapper.getListByFunction(key);
    }


}
