package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CiwarningEntity;
import inks.system.domain.pojo.CiwarningPojo;
import inks.system.mapper.CiwarningMapper;
import inks.system.service.CiwarningService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 预警项目(Ciwarning)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-05 20:11:00
 */
@Service("ciwarningService")
public class CiwarningServiceImpl implements CiwarningService {
    @Resource
    private CiwarningMapper ciwarningMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CiwarningPojo getEntity(String key) {
        return this.ciwarningMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CiwarningPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiwarningPojo> lst = ciwarningMapper.getPageList(queryParam);
            PageInfo<CiwarningPojo> pageInfo = new PageInfo<CiwarningPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param ciwarningPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiwarningPojo insert(CiwarningPojo ciwarningPojo) {
        //初始化NULL字段
        if (ciwarningPojo.getGengroupid() == null) ciwarningPojo.setGengroupid("");
        if (ciwarningPojo.getModulecode() == null) ciwarningPojo.setModulecode("");
        if (ciwarningPojo.getWarncode() == null) ciwarningPojo.setWarncode("");
        if (ciwarningPojo.getWarnname() == null) ciwarningPojo.setWarnname("");
        if (ciwarningPojo.getWarnfield() == null) ciwarningPojo.setWarnfield("");
        if (ciwarningPojo.getSvccode() == null) ciwarningPojo.setSvccode("");
        if (ciwarningPojo.getWarnapi() == null) ciwarningPojo.setWarnapi("");
        if (ciwarningPojo.getWebpath() == null) ciwarningPojo.setWebpath("");
        if (ciwarningPojo.getImagecss() == null) ciwarningPojo.setImagecss("");
        if (ciwarningPojo.getTagtitle() == null) ciwarningPojo.setTagtitle("");
        if (ciwarningPojo.getPermcode() == null) ciwarningPojo.setPermcode("");
        if (ciwarningPojo.getRownum() == null) ciwarningPojo.setRownum(0);
        if (ciwarningPojo.getEnabledmark() == null) ciwarningPojo.setEnabledmark(0);
        if (ciwarningPojo.getRemark() == null) ciwarningPojo.setRemark("");
        if (ciwarningPojo.getCreateby() == null) ciwarningPojo.setCreateby("");
        if (ciwarningPojo.getCreatebyid() == null) ciwarningPojo.setCreatebyid("");
        if (ciwarningPojo.getCreatedate() == null) ciwarningPojo.setCreatedate(new Date());
        if (ciwarningPojo.getLister() == null) ciwarningPojo.setLister("");
        if (ciwarningPojo.getListerid() == null) ciwarningPojo.setListerid("");
        if (ciwarningPojo.getModifydate() == null) ciwarningPojo.setModifydate(new Date());
        if (ciwarningPojo.getRevision() == null) ciwarningPojo.setRevision(0);
        CiwarningEntity ciwarningEntity = new CiwarningEntity();
        BeanUtils.copyProperties(ciwarningPojo, ciwarningEntity);

        ciwarningEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        ciwarningEntity.setRevision(1);  //乐观锁
        this.ciwarningMapper.insert(ciwarningEntity);
        return this.getEntity(ciwarningEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param ciwarningPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiwarningPojo update(CiwarningPojo ciwarningPojo) {
        CiwarningEntity ciwarningEntity = new CiwarningEntity();
        BeanUtils.copyProperties(ciwarningPojo, ciwarningEntity);
        this.ciwarningMapper.update(ciwarningEntity);
        return this.getEntity(ciwarningEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.ciwarningMapper.delete(key);
    }


}
