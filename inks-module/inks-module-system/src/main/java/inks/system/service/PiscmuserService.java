package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiscmuserPojo;
import inks.system.domain.PiscmuserEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * SCM用户(Piscmuser)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-21 19:08:54
 */
public interface PiscmuserService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiscmuserPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PiscmuserPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param piscmuserPojo 实例对象
     * @return 实例对象
     */
    PiscmuserPojo insert(PiscmuserPojo piscmuserPojo);

    /**
     * 修改数据
     *
     * @param piscmuserpojo 实例对象
     * @return 实例对象
     */
    PiscmuserPojo update(PiscmuserPojo piscmuserpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    PiscmuserPojo getEntityByUserName(String username);

    PageInfo<PiscmuserPojo> getPageListByTen(QueryParam queryParam);

    PiscmuserPojo getEntityByOpenid(String openid, String tid);

    List<PiscmuserPojo> getListByOpenid(String openid);

    PiscmuserPojo getEntityByUserid(String userid, String tenantid);
}
