package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiformvailditemPojo;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 窗体验证子表(Ciformvailditem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-31 17:04:05
 */
public interface CiformvailditemService {

    CiformvailditemPojo getEntity(String key,String tid);

    PageInfo<CiformvailditemPojo> getPageList(QueryParam queryParam);

    List<CiformvailditemPojo> getList(String Pid,String tid);  

    CiformvailditemPojo insert(CiformvailditemPojo ciformvailditemPojo);

    CiformvailditemPojo update(CiformvailditemPojo ciformvailditempojo);

    int delete(String key,String tid);

    CiformvailditemPojo clearNull(CiformvailditemPojo ciformvailditempojo);
}
