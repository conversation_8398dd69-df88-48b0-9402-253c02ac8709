package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PiscmfunctmenuwebEntity;
import inks.system.domain.pojo.PimenuwebPojo;
import inks.system.domain.pojo.PiscmfunctmenuwebPojo;
import inks.system.mapper.PiscmfunctmenuwebMapper;
import inks.system.service.PiscmfunctmenuwebService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * SCM菜单关系(Piscmfunctmenuweb)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@Service("piscmfunctmenuwebService")
public class PiscmfunctmenuwebServiceImpl implements PiscmfunctmenuwebService {
    @Resource
    private PiscmfunctmenuwebMapper piscmfunctmenuwebMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PiscmfunctmenuwebPojo getEntity(String key) {
        return this.piscmfunctmenuwebMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PiscmfunctmenuwebPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PiscmfunctmenuwebPojo> lst = piscmfunctmenuwebMapper.getPageList(queryParam);
            PageInfo<PiscmfunctmenuwebPojo> pageInfo = new PageInfo<PiscmfunctmenuwebPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param piscmfunctmenuwebPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiscmfunctmenuwebPojo insert(PiscmfunctmenuwebPojo piscmfunctmenuwebPojo) {
    //初始化NULL字段
     if(piscmfunctmenuwebPojo.getScmfunctid()==null) piscmfunctmenuwebPojo.setScmfunctid("");
     if(piscmfunctmenuwebPojo.getScmfunctcode()==null) piscmfunctmenuwebPojo.setScmfunctcode("");
     if(piscmfunctmenuwebPojo.getScmfunctname()==null) piscmfunctmenuwebPojo.setScmfunctname("");
     if(piscmfunctmenuwebPojo.getNavid()==null) piscmfunctmenuwebPojo.setNavid("");
     if(piscmfunctmenuwebPojo.getNavcode()==null) piscmfunctmenuwebPojo.setNavcode("");
     if(piscmfunctmenuwebPojo.getNavname()==null) piscmfunctmenuwebPojo.setNavname("");
     if(piscmfunctmenuwebPojo.getRemark()==null) piscmfunctmenuwebPojo.setRemark("");
     if(piscmfunctmenuwebPojo.getCreateby()==null) piscmfunctmenuwebPojo.setCreateby("");
     if(piscmfunctmenuwebPojo.getCreatebyid()==null) piscmfunctmenuwebPojo.setCreatebyid("");
     if(piscmfunctmenuwebPojo.getCreatedate()==null) piscmfunctmenuwebPojo.setCreatedate(new Date());
     if(piscmfunctmenuwebPojo.getLister()==null) piscmfunctmenuwebPojo.setLister("");
     if(piscmfunctmenuwebPojo.getListerid()==null) piscmfunctmenuwebPojo.setListerid("");
     if(piscmfunctmenuwebPojo.getModifydate()==null) piscmfunctmenuwebPojo.setModifydate(new Date());
     if(piscmfunctmenuwebPojo.getRevision()==null) piscmfunctmenuwebPojo.setRevision(0);
        PiscmfunctmenuwebEntity piscmfunctmenuwebEntity = new PiscmfunctmenuwebEntity(); 
        BeanUtils.copyProperties(piscmfunctmenuwebPojo,piscmfunctmenuwebEntity);
  //生成雪花id
          piscmfunctmenuwebEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          piscmfunctmenuwebEntity.setRevision(1);  //乐观锁
          this.piscmfunctmenuwebMapper.insert(piscmfunctmenuwebEntity);
        return this.getEntity(piscmfunctmenuwebEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param piscmfunctmenuwebPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiscmfunctmenuwebPojo update(PiscmfunctmenuwebPojo piscmfunctmenuwebPojo) {
        PiscmfunctmenuwebEntity piscmfunctmenuwebEntity = new PiscmfunctmenuwebEntity(); 
        BeanUtils.copyProperties(piscmfunctmenuwebPojo,piscmfunctmenuwebEntity);
        this.piscmfunctmenuwebMapper.update(piscmfunctmenuwebEntity);
        return this.getEntity(piscmfunctmenuwebEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.piscmfunctmenuwebMapper.delete(key) ;
    }

    @Override
    public List<PiscmfunctmenuwebPojo> getListByFunction(String key) {
        return this.piscmfunctmenuwebMapper.getListByFunction(key);
    }

    @Override
    public List<PimenuwebPojo> getListByLoginUser(LoginUser loginUser){
        return this.piscmfunctmenuwebMapper.getListByLoginUser(loginUser);
    }

    @Override
    public List<PimenuwebPojo> getListByScmFunctids(String ids) {
        return this.piscmfunctmenuwebMapper.getListByScmFunctids(ids);
    }
}
