//package inks.system.service;
//
//import com.seeta.proxy.GenderPredictorProxy;
//import com.seeta.proxy.MaskDetectorProxy;
//import com.seeta.sdk.FaceAntiSpoofing;
//import inks.common.core.domain.LoginUser;
//import inks.common.core.domain.QueryParam;
//import inks.system.config.face.entity.FaceInfo;
//import inks.system.config.face.entity.FaceInfoBo;
//import inks.system.config.face.exception.Seetaface6Exception;
//import inks.system.domain.pojo.PifaceinfoPojo;
//import com.github.pagehelper.PageInfo;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.servlet.http.HttpServletRequest;
//import java.io.File;
//import java.util.List;
//
///**
// * 人脸信息表(PiFaceInfo)表服务接口
// *
// * <AUTHOR>
// * @since 2024-11-19 17:02:11
// */
//public interface PifaceinfoService {
//
//    PifaceinfoPojo getEntity(String key,String tid);
//
//    PageInfo<PifaceinfoPojo> getPageList(QueryParam queryParam);
//
//    PifaceinfoPojo insert(PifaceinfoPojo pifaceinfoPojo);
//
//    PifaceinfoPojo update(PifaceinfoPojo pifaceinfopojo);
//
//    int delete(String key,String tid);
//
//    int save(MultipartFile face, HttpServletRequest request, LoginUser loginUser) throws Exception;
//
//    // 人脸识别API---------------------------------------
//    /**
//     * 攻击人脸检测
//     *
//     * @param faceImage
//     * @return List<FaceAntiSpoofing.Status>
//     */
//    List<FaceAntiSpoofing.Status> faceAntiSpoofing(MultipartFile faceImage) throws Seetaface6Exception;
//
//    /**
//     * 带口罩人脸检测
//     *
//     * @param faceImage
//     * @return 人是否带口罩
//     */
//    List<MaskDetectorProxy.MaskItem> maskDetector(MultipartFile faceImage) throws Seetaface6Exception;
//
//    /**
//     * 人脸性别判断
//     *
//     * @param faceImage
//     * @return List<GenderPredictorProxy.GenderItem> 多个人脸的性别
//     */
//    List<GenderPredictorProxy.GenderItem> genderPredictor(MultipartFile faceImage) throws Seetaface6Exception;
//
//    /**
//     * 人脸年龄判断
//     *
//     * @param faceImage
//     * @return List<Integer> 多个人脸的年龄
//     */
//    List<Integer> agePredictor(MultipartFile faceImage) throws Seetaface6Exception;
//
//    /**
//     * 人脸对比，1：1
//     *
//     * @param face1
//     * @param face2
//     * @return Float 分数 0~1
//     */
//    Float faceRecognizer(MultipartFile face1, MultipartFile face2) throws Seetaface6Exception;
//
//    float[] faceRecognizer(MultipartFile faceImage) throws Seetaface6Exception;
//
//    List<FaceInfo> getFaceInfo(MultipartFile face, String path) throws Exception;
//
//    List<FaceInfo> getFaceInfo(File face) throws Exception;
//
//    List<PifaceinfoPojo> getPiFaceInfo(MultipartFile face, String path, LoginUser loginUser) throws Exception;
//
//    List<FaceInfoBo> queryTopN(MultipartFile face, int topN) throws Seetaface6Exception;
//}
