package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiinitlogPojo;

/**
 * 初始化日志(Ciinitlog)表服务接口
 *
 * <AUTHOR>
 * @since 2022-07-31 11:20:20
 */
public interface CiinitlogService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiinitlogPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CiinitlogPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param ciinitlogPojo 实例对象
     * @return 实例对象
     */
    CiinitlogPojo insert(CiinitlogPojo ciinitlogPojo);

    /**
     * 修改数据
     *
     * @param ciinitlogpojo 实例对象
     * @return 实例对象
     */
    CiinitlogPojo update(CiinitlogPojo ciinitlogpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 通过主键删除数据
     *
     * @return 是否成功
     */
    int initSale(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 是否成功
     */
    int initBuy(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 是否成功
     */
    int initStore(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 是否成功
     */
    int initManu(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 是否成功
     */
    int initFm(QueryParam queryParam);
}
