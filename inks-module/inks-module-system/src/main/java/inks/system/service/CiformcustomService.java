package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiformcustomPojo;

/**
 * 自定义界面(Ciformcustom)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-06 12:58:00
 */
public interface CiformcustomService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiformcustomPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CiformcustomPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param ciformcustomPojo 实例对象
     * @return 实例对象
     */
    CiformcustomPojo insert(CiformcustomPojo ciformcustomPojo);

    /**
     * 修改数据
     *
     * @param ciformcustompojo 实例对象
     * @return 实例对象
     */
    CiformcustomPojo update(CiformcustomPojo ciformcustompojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiformcustomPojo getEntityByCode(String key,String tid);
}
