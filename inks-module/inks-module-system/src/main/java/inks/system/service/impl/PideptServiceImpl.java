package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DeptinfoPojo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PideptEntity;
import inks.system.domain.pojo.PideptPojo;
import inks.system.mapper.PideptMapper;
import inks.system.service.PideptService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 用户组织架构(Pidept)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-28 11:10:59
 */
@Service("pideptService")
public class PideptServiceImpl implements PideptService {
    @Resource
    private PideptMapper pideptMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PideptPojo getEntity(String key, String tid) {
        return this.pideptMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PideptPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PideptPojo> lst = pideptMapper.getPageList(queryParam);
            PageInfo<PideptPojo> pageInfo = new PageInfo<PideptPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param pideptPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PideptPojo insert(PideptPojo pideptPojo) {
        //初始化NULL字段
        if (pideptPojo.getParentid() == null) pideptPojo.setParentid("");
        if (pideptPojo.getAncestors() == null) pideptPojo.setAncestors("");
        if (pideptPojo.getDeptcode() == null) pideptPojo.setDeptcode("");
        if (pideptPojo.getDeptname() == null) pideptPojo.setDeptname("");
        if (pideptPojo.getEnabledmark() == null) pideptPojo.setEnabledmark(0);
        if (pideptPojo.getLeader() == null) pideptPojo.setLeader("");
        if (pideptPojo.getPhone() == null) pideptPojo.setPhone("");
        if (pideptPojo.getEmail() == null) pideptPojo.setEmail("");
        if (pideptPojo.getRownum() == null) pideptPojo.setRownum(0);
        if (pideptPojo.getRemark() == null) pideptPojo.setRemark("");
        if (pideptPojo.getCreateby() == null) pideptPojo.setCreateby("");
        if (pideptPojo.getCreatebyid() == null) pideptPojo.setCreatebyid("");
        if (pideptPojo.getCreatedate() == null) pideptPojo.setCreatedate(new Date());
        if (pideptPojo.getLister() == null) pideptPojo.setLister("");
        if (pideptPojo.getListerid() == null) pideptPojo.setListerid("");
        if (pideptPojo.getModifydate() == null) pideptPojo.setModifydate(new Date());
        if (pideptPojo.getCustom1() == null) pideptPojo.setCustom1("");
        if (pideptPojo.getCustom2() == null) pideptPojo.setCustom2("");
        if (pideptPojo.getCustom3() == null) pideptPojo.setCustom3("");
        if (pideptPojo.getCustom4() == null) pideptPojo.setCustom4("");
        if (pideptPojo.getCustom5() == null) pideptPojo.setCustom5("");
        if (pideptPojo.getTenantid() == null) pideptPojo.setTenantid("");
        if (pideptPojo.getRevision() == null) pideptPojo.setRevision(0);
        PideptEntity pideptEntity = new PideptEntity();
        BeanUtils.copyProperties(pideptPojo, pideptEntity);

        pideptEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        pideptEntity.setRevision(1);  //乐观锁
        this.pideptMapper.insert(pideptEntity);
        return this.getEntity(pideptEntity.getId(), pideptEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param pideptPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PideptPojo update(PideptPojo pideptPojo) {
        PideptEntity pideptEntity = new PideptEntity();
        BeanUtils.copyProperties(pideptPojo, pideptEntity);
        this.pideptMapper.update(pideptEntity);
        return this.getEntity(pideptEntity.getId(), pideptEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.pideptMapper.delete(key, tid);
    }

    /**
     * 分页查询
     *
     * @param key 筛选条件
     * @return 查询结果
     */
    @Override
    public List<PideptPojo> getListByParentid(String key, String tid) {
        return this.pideptMapper.getListByParentid(key, tid);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param lst 主键
     * @return 实例对象
     */
    @Override
    public void getSubinfoAll(List<DeptinfoPojo> lst, String tid) {
        getDetailitem(lst, lst.get(0).getDeptid(), 1, tid);
    }

    private void getDetailitem(List<DeptinfoPojo> lstinfo, String key, Integer level, String tid) {
        DeptinfoPojo deptinfoPojo;
        List<PideptPojo> lst = getListByParentid(key, tid);
        for (PideptPojo pojo : lst) {
            deptinfoPojo = new DeptinfoPojo();
            BeanUtils.copyProperties(pojo, deptinfoPojo);
            deptinfoPojo.setDeptid(pojo.getId());
            deptinfoPojo.setIsdeptadmin(0);
            lstinfo.add(deptinfoPojo);
            if (level < 10) {
                getDetailitem(lstinfo, pojo.getId(), level + 1, tid);
            }
        }
    }

}
