package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PitenantrmsuserPojo;

import java.util.List;

/**
 * RMS租户关系表(Pitenantrmsuser)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
public interface PitenantrmsuserService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PitenantrmsuserPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PitenantrmsuserPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pitenantrmsuserPojo 实例对象
     * @return 实例对象
     */
    PitenantrmsuserPojo insert(PitenantrmsuserPojo pitenantrmsuserPojo);

    /**
     * 修改数据
     *
     * @param pitenantrmsuserpojo 实例对象
     * @return 实例对象
     */
    PitenantrmsuserPojo update(PitenantrmsuserPojo pitenantrmsuserpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 分页查询
     * @return 查询结果
     */
    List<PitenantrmsuserPojo> getListByUser(String Userid);

    PitenantrmsuserPojo createRmsUser(PitenantrmsuserPojo pitenantrmsuserPojo) throws Exception;

    PitenantrmsuserPojo getEntityByUserid(String key, String tid);

}
