package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CitablecustomEntity;
import inks.system.domain.pojo.CitablecustomPojo;
import inks.system.mapper.CitablecustomMapper;
import inks.system.service.CitablecustomService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 自定义字段(Citablecustom)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-10 14:39:53
 */
@Service("citablecustomService")
public class CitablecustomServiceImpl implements CitablecustomService {
    @Resource
    private CitablecustomMapper citablecustomMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CitablecustomPojo getEntity(String key, String tid) {
        return this.citablecustomMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CitablecustomPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CitablecustomPojo> lst = citablecustomMapper.getPageList(queryParam);
            PageInfo<CitablecustomPojo> pageInfo = new PageInfo<CitablecustomPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param citablecustomPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CitablecustomPojo insert(CitablecustomPojo citablecustomPojo) {
        //初始化NULL字段
        if (citablecustomPojo.getGengroupid() == null) citablecustomPojo.setGengroupid("");
        if (citablecustomPojo.getModulecode() == null) citablecustomPojo.setModulecode("");
        if (citablecustomPojo.getTablename() == null) citablecustomPojo.setTablename("");
        if (citablecustomPojo.getTablecode() == null) citablecustomPojo.setTablecode("");
        if (citablecustomPojo.getColumncode() == null) citablecustomPojo.setColumncode("");
        if (citablecustomPojo.getItemcode() == null) citablecustomPojo.setItemcode("");
        if (citablecustomPojo.getItemname() == null) citablecustomPojo.setItemname("");
        if (citablecustomPojo.getItemlabel() == null) citablecustomPojo.setItemlabel("");
        if (citablecustomPojo.getRownum() == null) citablecustomPojo.setRownum(0);
        if (citablecustomPojo.getEnabledmark() == null) citablecustomPojo.setEnabledmark(1);
        if (citablecustomPojo.getRemark() == null) citablecustomPojo.setRemark("");
        if (citablecustomPojo.getCreateby() == null) citablecustomPojo.setCreateby("");
        if (citablecustomPojo.getCreatebyid() == null) citablecustomPojo.setCreatebyid("");
        if (citablecustomPojo.getCreatedate() == null) citablecustomPojo.setCreatedate(new Date());
        if (citablecustomPojo.getLister() == null) citablecustomPojo.setLister("");
        if (citablecustomPojo.getListerid() == null) citablecustomPojo.setListerid("");
        if (citablecustomPojo.getModifydate() == null) citablecustomPojo.setModifydate(new Date());
        if (citablecustomPojo.getCustom1() == null) citablecustomPojo.setCustom1("");
        if (citablecustomPojo.getCustom2() == null) citablecustomPojo.setCustom2("");
        if (citablecustomPojo.getCustom3() == null) citablecustomPojo.setCustom3("");
        if (citablecustomPojo.getCustom4() == null) citablecustomPojo.setCustom4("");
        if (citablecustomPojo.getCustom5() == null) citablecustomPojo.setCustom5("");
        if (citablecustomPojo.getTenantid() == null) citablecustomPojo.setTenantid("");
        if (citablecustomPojo.getTenantname() == null) citablecustomPojo.setTenantname("");
        if (citablecustomPojo.getRevision() == null) citablecustomPojo.setRevision(0);
        CitablecustomEntity citablecustomEntity = new CitablecustomEntity();
        BeanUtils.copyProperties(citablecustomPojo, citablecustomEntity);

        citablecustomEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        citablecustomEntity.setRevision(1);  //乐观锁
        this.citablecustomMapper.insert(citablecustomEntity);
        return this.getEntity(citablecustomEntity.getId(), citablecustomEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param citablecustomPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CitablecustomPojo update(CitablecustomPojo citablecustomPojo) {
        CitablecustomEntity citablecustomEntity = new CitablecustomEntity();
        BeanUtils.copyProperties(citablecustomPojo, citablecustomEntity);
        this.citablecustomMapper.update(citablecustomEntity);
        return this.getEntity(citablecustomEntity.getId(), citablecustomEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.citablecustomMapper.delete(key, tid);
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @Override
    public List<CitablecustomPojo> getListByCode(String key, String tid) {
        return this.citablecustomMapper.getListByCode(key, tid);
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @Override
    public List<CitablecustomPojo> getListByGroupid(String key, String tid) {
        return this.citablecustomMapper.getListByGroupid(key, tid);
    }
    /**
     * 修改数据
     *
     * @return 实例对象
     */
    @Override
    @Transactional
    public List<CitablecustomPojo> updateList(List<CitablecustomPojo> lst) {
        List<CitablecustomPojo> newlst = new ArrayList<>();
        for (CitablecustomPojo citablecustomPojo : lst) {
            if (citablecustomPojo.getId() != null && !"".equals(citablecustomPojo.getId())) {
                newlst.add(update(citablecustomPojo));
            } else {
                newlst.add(insert(citablecustomPojo));
            }
        }
        return newlst;
    }
}
