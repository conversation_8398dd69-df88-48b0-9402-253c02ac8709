package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PifunctionweblnkPojo;
import inks.system.domain.PifunctionweblnkEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * Web快捷方式(Pifunctionweblnk)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-08 14:48:39
 */
public interface PifunctionweblnkService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionweblnkPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PifunctionweblnkPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pifunctionweblnkPojo 实例对象
     * @return 实例对象
     */
    PifunctionweblnkPojo insert(PifunctionweblnkPojo pifunctionweblnkPojo);

    /**
     * 修改数据
     *
     * @param pifunctionweblnkpojo 实例对象
     * @return 实例对象
     */
    PifunctionweblnkPojo update(PifunctionweblnkPojo pifunctionweblnkpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

    List<PifunctionweblnkPojo> getListByFunction(String key);
}
