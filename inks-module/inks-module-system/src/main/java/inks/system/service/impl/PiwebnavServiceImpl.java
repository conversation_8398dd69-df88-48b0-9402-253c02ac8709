package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.system.domain.pojo.PiwebnavPojo;
import inks.system.domain.PiwebnavEntity;
import inks.system.mapper.PiwebnavMapper;
import inks.system.service.PiwebnavService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * Pc导航(Piwebnav)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-01 15:39:11
 */
@Service("piwebnavService")
public class PiwebnavServiceImpl implements PiwebnavService {
    @Resource
    private PiwebnavMapper piwebnavMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PiwebnavPojo getEntity(String key) {
        return this.piwebnavMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PiwebnavPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PiwebnavPojo> lst = piwebnavMapper.getPageList(queryParam);
            PageInfo<PiwebnavPojo> pageInfo = new PageInfo<PiwebnavPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param piwebnavPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiwebnavPojo insert(PiwebnavPojo piwebnavPojo) {
    //初始化NULL字段
     if(piwebnavPojo.getNavcode()==null) piwebnavPojo.setNavcode("");
     if(piwebnavPojo.getNavname()==null) piwebnavPojo.setNavname("");
     if(piwebnavPojo.getNavcontent()==null) piwebnavPojo.setNavcontent("");
     if(piwebnavPojo.getRownum()==null) piwebnavPojo.setRownum(0);
     if(piwebnavPojo.getEnabledmark()==null) piwebnavPojo.setEnabledmark(0);
     if(piwebnavPojo.getPermissioncode()==null) piwebnavPojo.setPermissioncode("");
     if(piwebnavPojo.getRemark()==null) piwebnavPojo.setRemark("");
     if(piwebnavPojo.getCreateby()==null) piwebnavPojo.setCreateby("");
     if(piwebnavPojo.getCreatebyid()==null) piwebnavPojo.setCreatebyid("");
     if(piwebnavPojo.getCreatedate()==null) piwebnavPojo.setCreatedate(new Date());
     if(piwebnavPojo.getLister()==null) piwebnavPojo.setLister("");
     if(piwebnavPojo.getListerid()==null) piwebnavPojo.setListerid("");
     if(piwebnavPojo.getModifydate()==null) piwebnavPojo.setModifydate(new Date());
     if(piwebnavPojo.getRevision()==null) piwebnavPojo.setRevision(0);
        PiwebnavEntity piwebnavEntity = new PiwebnavEntity(); 
        BeanUtils.copyProperties(piwebnavPojo,piwebnavEntity);
        //生成雪花id
          piwebnavEntity.setNavid(inksSnowflake.getSnowflake().nextIdStr());
          piwebnavEntity.setRevision(1);  //乐观锁
          this.piwebnavMapper.insert(piwebnavEntity);
        return this.getEntity(piwebnavEntity.getNavid());
  
    }

    /**
     * 修改数据
     *
     * @param piwebnavPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiwebnavPojo update(PiwebnavPojo piwebnavPojo) {
        PiwebnavEntity piwebnavEntity = new PiwebnavEntity(); 
        BeanUtils.copyProperties(piwebnavPojo,piwebnavEntity);
        this.piwebnavMapper.update(piwebnavEntity);
        return this.getEntity(piwebnavEntity.getNavid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.piwebnavMapper.delete(key) ;
    }
    

}
