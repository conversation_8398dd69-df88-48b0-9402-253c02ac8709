package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CidgformatitemEntity;
import inks.system.domain.pojo.CidgformatitemPojo;
import inks.system.mapper.CidgformatitemMapper;
import inks.system.service.CidgformatitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;
/**
 * 列表项目(Cidgformatitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-24 09:55:09
 */
@Service("cidgformatitemService")
public class CidgformatitemServiceImpl implements CidgformatitemService {
    @Resource
    private CidgformatitemMapper cidgformatitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CidgformatitemPojo getEntity(String key,String tid) {
        return this.cidgformatitemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CidgformatitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CidgformatitemPojo> lst = cidgformatitemMapper.getPageList(queryParam);
            PageInfo<CidgformatitemPojo> pageInfo = new PageInfo<CidgformatitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<CidgformatitemPojo> getList(String Pid,String tid) { 
        try {
            List<CidgformatitemPojo> lst = cidgformatitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param cidgformatitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CidgformatitemPojo insert(CidgformatitemPojo cidgformatitemPojo) {
        //初始化item的NULL
        CidgformatitemPojo itempojo =this.clearNull(cidgformatitemPojo);
        CidgformatitemEntity cidgformatitemEntity = new CidgformatitemEntity(); 
        BeanUtils.copyProperties(itempojo,cidgformatitemEntity);
        
          cidgformatitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          cidgformatitemEntity.setRevision(1);  //乐观锁      
          this.cidgformatitemMapper.insert(cidgformatitemEntity);
        return this.getEntity(cidgformatitemEntity.getId(),cidgformatitemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param cidgformatitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CidgformatitemPojo update(CidgformatitemPojo cidgformatitemPojo) {
        CidgformatitemEntity cidgformatitemEntity = new CidgformatitemEntity(); 
        BeanUtils.copyProperties(cidgformatitemPojo,cidgformatitemEntity);
        this.cidgformatitemMapper.update(cidgformatitemEntity);
        return this.getEntity(cidgformatitemEntity.getId(),cidgformatitemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.cidgformatitemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param cidgformatitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public CidgformatitemPojo clearNull(CidgformatitemPojo cidgformatitemPojo){
     //初始化NULL字段
     if(cidgformatitemPojo.getPid()==null) cidgformatitemPojo.setPid("");
     if(cidgformatitemPojo.getItemcode()==null) cidgformatitemPojo.setItemcode("");
     if(cidgformatitemPojo.getItemname()==null) cidgformatitemPojo.setItemname("");
     if(cidgformatitemPojo.getDefwidth()==null) cidgformatitemPojo.setDefwidth("");
     if(cidgformatitemPojo.getMinwidth()==null) cidgformatitemPojo.setMinwidth("");
     if(cidgformatitemPojo.getDisplaymark()==null) cidgformatitemPojo.setDisplaymark(0);
     if(cidgformatitemPojo.getFixed()==null) cidgformatitemPojo.setFixed(0);
     if(cidgformatitemPojo.getSortable()==null) cidgformatitemPojo.setSortable(0);
     if(cidgformatitemPojo.getOrderfield()==null) cidgformatitemPojo.setOrderfield("");
     if(cidgformatitemPojo.getOverflow()==null) cidgformatitemPojo.setOverflow(0);
     if(cidgformatitemPojo.getFormatter()==null) cidgformatitemPojo.setFormatter("");
     if(cidgformatitemPojo.getClassname()==null) cidgformatitemPojo.setClassname("");
     if(cidgformatitemPojo.getAligntype()==null) cidgformatitemPojo.setAligntype("");
     if(cidgformatitemPojo.getEventname()==null) cidgformatitemPojo.setEventname("");
     if(cidgformatitemPojo.getEditmark()==null) cidgformatitemPojo.setEditmark(0);
     if(cidgformatitemPojo.getOperationmark()==null) cidgformatitemPojo.setOperationmark(0);
     if(cidgformatitemPojo.getDisplayindex()==null) cidgformatitemPojo.setDisplayindex(0);
     if(cidgformatitemPojo.getRownum()==null) cidgformatitemPojo.setRownum(0);
     if(cidgformatitemPojo.getRemark()==null) cidgformatitemPojo.setRemark("");
     if(cidgformatitemPojo.getTenantid()==null) cidgformatitemPojo.setTenantid("");
     if(cidgformatitemPojo.getRevision()==null) cidgformatitemPojo.setRevision(0);
     return cidgformatitemPojo;
     }
}
