package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PiauthcodeEntity;
import inks.system.domain.pojo.PiauthcodePojo;
import inks.system.mapper.PiauthcodeMapper;
import inks.system.service.PiauthcodeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 授权码(Piauthcode)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-22 13:13:41
 */
@Service("piauthcodeService")
public class PiauthcodeServiceImpl implements PiauthcodeService {
    @Resource
    private PiauthcodeMapper piauthcodeMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PiauthcodePojo getEntity(String key, String tid) {
        return this.piauthcodeMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PiauthcodePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PiauthcodePojo> lst = piauthcodeMapper.getPageList(queryParam);
            PageInfo<PiauthcodePojo> pageInfo = new PageInfo<PiauthcodePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param piauthcodePojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiauthcodePojo insert(PiauthcodePojo piauthcodePojo) {
    //初始化NULL字段
     if(piauthcodePojo.getAuthcode()==null) piauthcodePojo.setAuthcode("");
     if(piauthcodePojo.getAuthdesc()==null) piauthcodePojo.setAuthdesc("");
     if(piauthcodePojo.getUsername()==null) piauthcodePojo.setUsername("");
     if(piauthcodePojo.getUserpassword()==null) piauthcodePojo.setUserpassword("");
     if(piauthcodePojo.getRownum()==null) piauthcodePojo.setRownum(0);
     if(piauthcodePojo.getEnabledmark()==null) piauthcodePojo.setEnabledmark(0);
     if(piauthcodePojo.getRemark()==null) piauthcodePojo.setRemark("");
     if(piauthcodePojo.getCreateby()==null) piauthcodePojo.setCreateby("");
     if(piauthcodePojo.getCreatebyid()==null) piauthcodePojo.setCreatebyid("");
     if(piauthcodePojo.getCreatedate()==null) piauthcodePojo.setCreatedate(new Date());
     if(piauthcodePojo.getLister()==null) piauthcodePojo.setLister("");
     if(piauthcodePojo.getListerid()==null) piauthcodePojo.setListerid("");
     if(piauthcodePojo.getModifydate()==null) piauthcodePojo.setModifydate(new Date());
     if(piauthcodePojo.getCustom1()==null) piauthcodePojo.setCustom1("");
     if(piauthcodePojo.getCustom2()==null) piauthcodePojo.setCustom2("");
     if(piauthcodePojo.getCustom3()==null) piauthcodePojo.setCustom3("");
     if(piauthcodePojo.getCustom4()==null) piauthcodePojo.setCustom4("");
     if(piauthcodePojo.getCustom5()==null) piauthcodePojo.setCustom5("");
     if(piauthcodePojo.getTenantid()==null) piauthcodePojo.setTenantid("");
     if(piauthcodePojo.getTenantname()==null) piauthcodePojo.setTenantname("");
     if(piauthcodePojo.getRevision()==null) piauthcodePojo.setRevision(0);
        PiauthcodeEntity piauthcodeEntity = new PiauthcodeEntity();
        BeanUtils.copyProperties(piauthcodePojo,piauthcodeEntity);

          piauthcodeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          piauthcodeEntity.setRevision(1);  //乐观锁
          this.piauthcodeMapper.insert(piauthcodeEntity);
        return this.getEntity(piauthcodeEntity.getId(),piauthcodeEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param piauthcodePojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiauthcodePojo update(PiauthcodePojo piauthcodePojo) {
        PiauthcodeEntity piauthcodeEntity = new PiauthcodeEntity();
        BeanUtils.copyProperties(piauthcodePojo,piauthcodeEntity);
        this.piauthcodeMapper.update(piauthcodeEntity);
        return this.getEntity(piauthcodeEntity.getId(),piauthcodeEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.piauthcodeMapper.delete(key,tid) ;
    }


}
