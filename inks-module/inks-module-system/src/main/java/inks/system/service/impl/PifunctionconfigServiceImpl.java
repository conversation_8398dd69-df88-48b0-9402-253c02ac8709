package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.pojo.PifunctionconfigPojo;
import inks.system.domain.PifunctionconfigEntity;
import inks.system.domain.pojo.PifunctionmenuappPojo;
import inks.system.mapper.PifunctionconfigMapper;
import inks.system.service.PifunctionconfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 服务参数关系(Pifunctionconfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-25 11:20:10
 */
@Service("pifunctionconfigService")
public class PifunctionconfigServiceImpl implements PifunctionconfigService {
    @Resource
    private PifunctionconfigMapper pifunctionconfigMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PifunctionconfigPojo getEntity(String key) {
        return this.pifunctionconfigMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PifunctionconfigPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PifunctionconfigPojo> lst = pifunctionconfigMapper.getPageList(queryParam);
            PageInfo<PifunctionconfigPojo> pageInfo = new PageInfo<PifunctionconfigPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param pifunctionconfigPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PifunctionconfigPojo insert(PifunctionconfigPojo pifunctionconfigPojo) {
    //初始化NULL字段
     if(pifunctionconfigPojo.getRevision()==null) pifunctionconfigPojo.setRevision(0);
     if(pifunctionconfigPojo.getModifydate()==null) pifunctionconfigPojo.setModifydate(new Date());
     if(pifunctionconfigPojo.getListerid()==null) pifunctionconfigPojo.setListerid("");
     if(pifunctionconfigPojo.getLister()==null) pifunctionconfigPojo.setLister("");
     if(pifunctionconfigPojo.getCreatedate()==null) pifunctionconfigPojo.setCreatedate(new Date());
     if(pifunctionconfigPojo.getCreatebyid()==null) pifunctionconfigPojo.setCreatebyid("");
     if(pifunctionconfigPojo.getCreateby()==null) pifunctionconfigPojo.setCreateby("");
     if(pifunctionconfigPojo.getRemark()==null) pifunctionconfigPojo.setRemark("");
     if(pifunctionconfigPojo.getCfglevel()==null) pifunctionconfigPojo.setCfglevel(0);
     if(pifunctionconfigPojo.getCfgtype()==null) pifunctionconfigPojo.setCfgtype(0);
     if(pifunctionconfigPojo.getCfgvalue()==null) pifunctionconfigPojo.setCfgvalue("");
     if(pifunctionconfigPojo.getCfgkey()==null) pifunctionconfigPojo.setCfgkey("");
     if(pifunctionconfigPojo.getCfgname()==null) pifunctionconfigPojo.setCfgname("");
     if(pifunctionconfigPojo.getCfgid()==null) pifunctionconfigPojo.setCfgid("");
     if(pifunctionconfigPojo.getFunctionname()==null) pifunctionconfigPojo.setFunctionname("");
     if(pifunctionconfigPojo.getFunctioncode()==null) pifunctionconfigPojo.setFunctioncode("");
     if(pifunctionconfigPojo.getFunctionid()==null) pifunctionconfigPojo.setFunctionid("");
        PifunctionconfigEntity pifunctionconfigEntity = new PifunctionconfigEntity(); 
        BeanUtils.copyProperties(pifunctionconfigPojo,pifunctionconfigEntity);
        
          pifunctionconfigEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          pifunctionconfigEntity.setRevision(1);  //乐观锁
          this.pifunctionconfigMapper.insert(pifunctionconfigEntity);
        return this.getEntity(pifunctionconfigEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param pifunctionconfigPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PifunctionconfigPojo update(PifunctionconfigPojo pifunctionconfigPojo) {
        PifunctionconfigEntity pifunctionconfigEntity = new PifunctionconfigEntity(); 
        BeanUtils.copyProperties(pifunctionconfigPojo,pifunctionconfigEntity);
        this.pifunctionconfigMapper.update(pifunctionconfigEntity);
        return this.getEntity(pifunctionconfigEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pifunctionconfigMapper.delete(key) ;
    }

    @Override
    public List<PifunctionconfigPojo> getListByFunction(String key){
        return this.pifunctionconfigMapper.getListByFunction(key);
    }
                                                                                              
}
