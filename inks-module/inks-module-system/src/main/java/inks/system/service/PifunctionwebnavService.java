package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PifunctionweblnkPojo;
import inks.system.domain.pojo.PifunctionwebnavPojo;
import inks.system.domain.PifunctionwebnavEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * web导航(Pifunctionwebnav)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-01 15:36:24
 */
public interface PifunctionwebnavService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionwebnavPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PifunctionwebnavPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pifunctionwebnavPojo 实例对象
     * @return 实例对象
     */
    PifunctionwebnavPojo insert(PifunctionwebnavPojo pifunctionwebnavPojo);

    /**
     * 修改数据
     *
     * @param pifunctionwebnavpojo 实例对象
     * @return 实例对象
     */
    PifunctionwebnavPojo update(PifunctionwebnavPojo pifunctionwebnavpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

    List<PifunctionwebnavPojo> getListByFunction(String key);
}
