package inks.system.service;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CibillcodePojo;
import inks.system.domain.CibillcodeEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 单据编码(Cibillcode)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-08 13:27:45
 */
public interface CibillcodeService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CibillcodePojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CibillcodePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param cibillcodePojo 实例对象
     * @return 实例对象
     */
    CibillcodePojo insert(CibillcodePojo cibillcodePojo);

    /**
     * 修改数据
     *
     * @param cibillcodepojo 实例对象
     * @return 实例对象
     */
    CibillcodePojo update(CibillcodePojo cibillcodepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

    /**
     * 通过功能code生成序号
     *
     * @param ModuleCode 主键
     * @return 是否成功
     */

     String getSerialNo(String ModuleCode, String tid);
     String getSerialNo(String ModuleCode,String tablename, String prefix, String tid);

    List<String> getSerialNoList(String ModuleCode, Integer num, String tid);

    int copyBillCode(String tid,LoginUser loginUser);

    String initSerialNo(String moduleCode, String refno, String tid);

    CibillcodeEntity getEntityByCode(String modulecode, String tid);
}
