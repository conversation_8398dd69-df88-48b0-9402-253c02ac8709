package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiwebnavPojo;
import inks.system.domain.PiwebnavEntity;

import com.github.pagehelper.PageInfo;

/**
 * Pc导航(Piwebnav)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-01 15:39:11
 */
public interface PiwebnavService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiwebnavPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PiwebnavPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param piwebnavPojo 实例对象
     * @return 实例对象
     */
    PiwebnavPojo insert(PiwebnavPojo piwebnavPojo);

    /**
     * 修改数据
     *
     * @param piwebnavpojo 实例对象
     * @return 实例对象
     */
    PiwebnavPojo update(PiwebnavPojo piwebnavpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
