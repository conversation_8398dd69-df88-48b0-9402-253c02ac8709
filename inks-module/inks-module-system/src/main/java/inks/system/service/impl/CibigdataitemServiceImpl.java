package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CibigdataitemEntity;
import inks.system.domain.pojo.CibigdataitemPojo;
import inks.system.mapper.CibigdataitemMapper;
import inks.system.service.CibigdataitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;
/**
 * 服务大屏关系(Cibigdataitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-25 10:38:41
 */
@Service("cibigdataitemService")
public class CibigdataitemServiceImpl implements CibigdataitemService {
    @Resource
    private CibigdataitemMapper cibigdataitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CibigdataitemPojo getEntity(String key) {
        return this.cibigdataitemMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CibigdataitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CibigdataitemPojo> lst = cibigdataitemMapper.getPageList(queryParam);
            PageInfo<CibigdataitemPojo> pageInfo = new PageInfo<CibigdataitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<CibigdataitemPojo> getList(String Pid) {
        try {
            List<CibigdataitemPojo> lst = cibigdataitemMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param cibigdataitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CibigdataitemPojo insert(CibigdataitemPojo cibigdataitemPojo) {
        //初始化item的NULL
        CibigdataitemPojo itempojo =this.clearNull(cibigdataitemPojo);
        CibigdataitemEntity cibigdataitemEntity = new CibigdataitemEntity(); 
        BeanUtils.copyProperties(itempojo,cibigdataitemEntity);
        
          cibigdataitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          cibigdataitemEntity.setRevision(1);  //乐观锁      
          this.cibigdataitemMapper.insert(cibigdataitemEntity);
        return this.getEntity(cibigdataitemEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param cibigdataitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CibigdataitemPojo update(CibigdataitemPojo cibigdataitemPojo) {
        CibigdataitemEntity cibigdataitemEntity = new CibigdataitemEntity(); 
        BeanUtils.copyProperties(cibigdataitemPojo,cibigdataitemEntity);
        this.cibigdataitemMapper.update(cibigdataitemEntity);
        return this.getEntity(cibigdataitemEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.cibigdataitemMapper.delete(key) ;
    }
    
     /**
     * 修改数据
     *
     * @param cibigdataitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public CibigdataitemPojo clearNull(CibigdataitemPojo cibigdataitemPojo){
     //初始化NULL字段
     if(cibigdataitemPojo.getPid()==null) cibigdataitemPojo.setPid("");
     if(cibigdataitemPojo.getFunctionid()==null) cibigdataitemPojo.setFunctionid("");
     if(cibigdataitemPojo.getFunctioncode()==null) cibigdataitemPojo.setFunctioncode("");
     if(cibigdataitemPojo.getFunctionname()==null) cibigdataitemPojo.setFunctionname("");
     if(cibigdataitemPojo.getRownum()==null) cibigdataitemPojo.setRownum(0);
     if(cibigdataitemPojo.getRemark()==null) cibigdataitemPojo.setRemark("");
     if(cibigdataitemPojo.getRevision()==null) cibigdataitemPojo.setRevision(0);
     return cibigdataitemPojo;
     }
}
