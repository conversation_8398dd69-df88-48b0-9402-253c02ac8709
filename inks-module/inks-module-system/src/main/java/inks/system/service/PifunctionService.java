package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PifunctionPojo;

import java.util.List;

/**
 * 服务总表(Pifunction)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-29 20:02:56
 */
public interface PifunctionService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PifunctionPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pifunctionPojo 实例对象
     * @return 实例对象
     */
    PifunctionPojo insert(PifunctionPojo pifunctionPojo);

    /**
     * 修改数据
     *
     * @param pifunctionpojo 实例对象
     * @return 实例对象
     */
    PifunctionPojo update(PifunctionPojo pifunctionpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<PifunctionPojo> getFunctionListBySelf(String tenantid);
}
