package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PifunctionreportsPojo;

import java.util.List;

/**
 * 服务报表关系(Pifunctionreports)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-17 07:54:31
 */
public interface PifunctionreportsService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionreportsPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PifunctionreportsPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pifunctionreportsPojo 实例对象
     * @return 实例对象
     */
    PifunctionreportsPojo insert(PifunctionreportsPojo pifunctionreportsPojo);

    /**
     * 修改数据
     *
     * @param pifunctionreportspojo 实例对象
     * @return 实例对象
     */
    PifunctionreportsPojo update(PifunctionreportsPojo pifunctionreportspojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<PifunctionreportsPojo> getListByFunction(String key);
}
