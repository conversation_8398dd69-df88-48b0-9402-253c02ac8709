package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.system.domain.pojo.CiformvaildPojo;
import inks.system.domain.pojo.CiformvailditemPojo;
import inks.system.domain.pojo.CiformvailditemdetailPojo;
import inks.system.domain.CiformvaildEntity;
import inks.system.domain.CiformvailditemEntity;
import inks.system.mapper.CiformvaildMapper;
import inks.system.service.CiformvaildService;
import inks.system.service.CiformvailditemService;
import inks.system.mapper.CiformvailditemMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 窗体验证(Ciformvaild)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-31 17:03:53
 */
@Service("ciformvaildService")
public class CiformvaildServiceImpl implements CiformvaildService {
    @Resource
    private CiformvaildMapper ciformvaildMapper;
    
    @Resource
    private CiformvailditemMapper ciformvailditemMapper;
    

    @Resource
    private CiformvailditemService ciformvailditemService;

    @Override
    public CiformvaildPojo getEntity(String key, String tid) {
        return this.ciformvaildMapper.getEntity(key,tid);
    }

    @Override
    public PageInfo<CiformvailditemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiformvailditemdetailPojo> lst = ciformvaildMapper.getPageList(queryParam);
            PageInfo<CiformvailditemdetailPojo> pageInfo = new PageInfo<CiformvailditemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public CiformvaildPojo getBillEntity(String key, String tid) {
       try {
           //读取主表
           CiformvaildPojo ciformvaildPojo = this.ciformvaildMapper.getEntity(key,tid);
           //读取子表
           ciformvaildPojo.setItem(ciformvailditemMapper.getList(ciformvaildPojo.getId(),tid));
           return ciformvaildPojo;
       }catch (Exception e){
          throw new BaseBusinessException(e.getMessage());
       } 
    }

    @Override
    public CiformvaildPojo getBillEntityByFormCode(String key, String tenantid) {
        try {
            //读取主表
            CiformvaildPojo ciformvaildPojo = this.ciformvaildMapper.getEntityByFormCode(key,tenantid);
            //读取子表
            ciformvaildPojo.setItem(ciformvailditemMapper.getList(ciformvaildPojo.getId(),tenantid));
            return ciformvaildPojo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<CiformvaildPojo> getBillList(QueryParam queryParam) {
        String tid = queryParam.getTenantid();
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiformvaildPojo> lst = ciformvaildMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(CiformvaildPojo item : lst){
                item.setItem(ciformvailditemMapper.getList(item.getId(), tid));
            }
            PageInfo<CiformvaildPojo> pageInfo = new PageInfo<CiformvaildPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public PageInfo<CiformvaildPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiformvaildPojo> lst = ciformvaildMapper.getPageTh(queryParam);
            PageInfo<CiformvaildPojo> pageInfo = new PageInfo<CiformvaildPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    @Transactional
    public CiformvaildPojo insert(CiformvaildPojo ciformvaildPojo) {
    String tid = ciformvaildPojo.getTenantid();
        //初始化NULL字段
        cleanNull(ciformvaildPojo);
         //生成雪花id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        CiformvaildEntity ciformvaildEntity = new CiformvaildEntity(); 
        BeanUtils.copyProperties(ciformvaildPojo,ciformvaildEntity);
      
        //设置id和新建日期
        ciformvaildEntity.setId(id);
        ciformvaildEntity.setRevision(1);  //乐观锁
        //插入主表
        this.ciformvaildMapper.insert(ciformvaildEntity);
        //Item子表处理
        List<CiformvailditemPojo> lst = ciformvaildPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for(CiformvailditemPojo item : lst){
               //初始化item的NULL
               CiformvailditemPojo itemPojo =this.ciformvailditemService.clearNull(item);
               CiformvailditemEntity ciformvailditemEntity = new CiformvailditemEntity(); 
               BeanUtils.copyProperties(itemPojo,ciformvailditemEntity);
               //设置id和Pid
               ciformvailditemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               ciformvailditemEntity.setPid(id);
               ciformvailditemEntity.setTenantid(tid);
               ciformvailditemEntity.setRevision(1);  //乐观锁
               //插入子表
               this.ciformvailditemMapper.insert(ciformvailditemEntity);
            }
            
        } 
        //返回Bill实例
        return this.getBillEntity(ciformvaildEntity.getId(),tid);
    }


    @Override
    @Transactional
    public CiformvaildPojo update(CiformvaildPojo ciformvaildPojo) {
        String tid = ciformvaildPojo.getTenantid();
        //主表更改
        CiformvaildEntity ciformvaildEntity = new CiformvaildEntity(); 
        BeanUtils.copyProperties(ciformvaildPojo,ciformvaildEntity);
        this.ciformvaildMapper.update(ciformvaildEntity);
        if (ciformvaildPojo.getItem() != null) {
        //Item子表处理
        List<CiformvailditemPojo> lst = ciformvaildPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =ciformvaildMapper.getDelItemIds(ciformvaildPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for (String delId : lstDelIds) {
             this.ciformvailditemMapper.delete(delId, tid);
            }
        }
        if (lst != null){
            //循环每个item子表
            for(CiformvailditemPojo item : lst){
               CiformvailditemEntity ciformvailditemEntity = new CiformvailditemEntity(); 
               if ("".equals(item.getId()) || item.getId() == null){
                //初始化item的NULL
               CiformvailditemPojo itemPojo =this.ciformvailditemService.clearNull(item);
               BeanUtils.copyProperties(itemPojo,ciformvailditemEntity);
               //设置id和Pid
               ciformvailditemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               ciformvailditemEntity.setPid(ciformvaildEntity.getId());  // 主表 id
               ciformvailditemEntity.setTenantid(tid);   // 租户id
               ciformvailditemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.ciformvailditemMapper.insert(ciformvailditemEntity);
               }
               else
               {
               BeanUtils.copyProperties(item,ciformvailditemEntity);       
                ciformvailditemEntity.setTenantid(tid);        
               this.ciformvailditemMapper.update(ciformvailditemEntity);
               }
            }
        } 
        }
        //返回Bill实例
        return this.getBillEntity(ciformvaildEntity.getId(),tid);
    }

    @Override
    @Transactional
    public int delete(String key, String tid) {
       CiformvaildPojo ciformvaildPojo =  this.getBillEntity(key,tid);
        //Item子表处理
        List<CiformvailditemPojo> lst = ciformvaildPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for(CiformvailditemPojo item : lst){
              this.ciformvailditemMapper.delete(item.getId(),tid);
            }
        }        
        return this.ciformvaildMapper.delete(key,tid) ;
    }
    

    

    private static void cleanNull(CiformvaildPojo ciformvaildPojo) {
        if(ciformvaildPojo.getFormcode()==null) ciformvaildPojo.setFormcode("");
        if(ciformvaildPojo.getFormname()==null) ciformvaildPojo.setFormname("");
        if(ciformvaildPojo.getRownum()==null) ciformvaildPojo.setRownum(0);
        if(ciformvaildPojo.getRemark()==null) ciformvaildPojo.setRemark("");
        if(ciformvaildPojo.getCreateby()==null) ciformvaildPojo.setCreateby("");
        if(ciformvaildPojo.getCreatebyid()==null) ciformvaildPojo.setCreatebyid("");
        if(ciformvaildPojo.getCreatedate()==null) ciformvaildPojo.setCreatedate(new Date());
        if(ciformvaildPojo.getLister()==null) ciformvaildPojo.setLister("");
        if(ciformvaildPojo.getListerid()==null) ciformvaildPojo.setListerid("");
        if(ciformvaildPojo.getModifydate()==null) ciformvaildPojo.setModifydate(new Date());
        if(ciformvaildPojo.getCustom1()==null) ciformvaildPojo.setCustom1("");
        if(ciformvaildPojo.getCustom2()==null) ciformvaildPojo.setCustom2("");
        if(ciformvaildPojo.getCustom3()==null) ciformvaildPojo.setCustom3("");
        if(ciformvaildPojo.getCustom4()==null) ciformvaildPojo.setCustom4("");
        if(ciformvaildPojo.getCustom5()==null) ciformvaildPojo.setCustom5("");
        if(ciformvaildPojo.getTenantid()==null) ciformvaildPojo.setTenantid("");
        if(ciformvaildPojo.getTenantname()==null) ciformvaildPojo.setTenantname("");
        if(ciformvaildPojo.getRevision()==null) ciformvaildPojo.setRevision(0);
   }

}
