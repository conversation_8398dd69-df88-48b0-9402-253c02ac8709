package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PidmsuserPojo;
import inks.system.domain.PidmsuserEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * DMS用户(Pidmsuser)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-21 19:08:54
 */
public interface PidmsuserService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PidmsuserPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PidmsuserPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pidmsuserPojo 实例对象
     * @return 实例对象
     */
    PidmsuserPojo insert(PidmsuserPojo pidmsuserPojo);

    /**
     * 修改数据
     *
     * @param pidmsuserpojo 实例对象
     * @return 实例对象
     */
    PidmsuserPojo update(PidmsuserPojo pidmsuserpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    PidmsuserPojo getEntityByUserName(String username);

    PageInfo<PidmsuserPojo> getPageListByTen(QueryParam queryParam);

    PidmsuserPojo getEntityByOpenid(String openid, String tid);

    List<PidmsuserPojo> getListByOpenid(String openid);

    PidmsuserPojo getEntityByUserid(String userid, String tenantid);
}
