package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CivalidatorEntity;
import inks.system.domain.pojo.CivalidatorPojo;
import inks.system.domain.vo.ValidationResponse;
import inks.system.domain.vo.ValidationResultCollector;
import inks.system.mapper.CivalidatorMapper;
import inks.system.service.CivalidatorService;
import inks.system.utils.ExpressionUtils;
import inks.system.utils.PrintColor;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.StringWriter;
import java.util.*;

/**
 * 数据验证(Civalidator)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-15 10:02:13
 */
@Service("civalidatorService")
public class CivalidatorServiceImpl implements CivalidatorService {
    @Resource
    private CivalidatorMapper civalidatorMapper;

    @Override
    public CivalidatorPojo getEntity(String key, String tid) {
        return this.civalidatorMapper.getEntity(key, tid);
    }


    @Override
    public PageInfo<CivalidatorPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CivalidatorPojo> lst = civalidatorMapper.getPageList(queryParam);
            PageInfo<CivalidatorPojo> pageInfo = new PageInfo<CivalidatorPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public CivalidatorPojo insert(CivalidatorPojo civalidatorPojo) {
        //初始化NULL字段
        cleanNull(civalidatorPojo);
        CivalidatorEntity civalidatorEntity = new CivalidatorEntity();
        BeanUtils.copyProperties(civalidatorPojo, civalidatorEntity);
        //生成雪花id
        civalidatorEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        civalidatorEntity.setRevision(1);  //乐观锁
        this.civalidatorMapper.insert(civalidatorEntity);
        return this.getEntity(civalidatorEntity.getId(), civalidatorEntity.getTenantid());
    }


    @Override
    public CivalidatorPojo update(CivalidatorPojo civalidatorPojo) {
        CivalidatorEntity civalidatorEntity = new CivalidatorEntity();
        BeanUtils.copyProperties(civalidatorPojo, civalidatorEntity);
        this.civalidatorMapper.update(civalidatorEntity);
        return this.getEntity(civalidatorEntity.getId(), civalidatorEntity.getTenantid());
    }


    @Override
    public int delete(String key, String tid) {
        return this.civalidatorMapper.delete(key, tid);
    }


    private static void cleanNull(CivalidatorPojo civalidatorPojo) {
        if (civalidatorPojo.getValicode() == null) civalidatorPojo.setValicode("");
        if(civalidatorPojo.getValititle()==null) civalidatorPojo.setValititle("");
        if(civalidatorPojo.getSqlmark()==null) civalidatorPojo.setSqlmark(0);
        if (civalidatorPojo.getSqlstr() == null) civalidatorPojo.setSqlstr("");
        if (civalidatorPojo.getExpression() == null) civalidatorPojo.setExpression("");
        if (civalidatorPojo.getTipmsg() == null) civalidatorPojo.setTipmsg("");
        if (civalidatorPojo.getTipmsgen() == null) civalidatorPojo.setTipmsgen("");
        if (civalidatorPojo.getRequiredmark() == null) civalidatorPojo.setRequiredmark(0);
        if (civalidatorPojo.getItemloopmark() == null) civalidatorPojo.setItemloopmark(0);
        if(civalidatorPojo.getEnabledmark()==null) civalidatorPojo.setEnabledmark(0);
        if(civalidatorPojo.getRownum()==null) civalidatorPojo.setRownum(0);
        if (civalidatorPojo.getRemark() == null) civalidatorPojo.setRemark("");
        if (civalidatorPojo.getCreateby() == null) civalidatorPojo.setCreateby("");
        if (civalidatorPojo.getCreatebyid() == null) civalidatorPojo.setCreatebyid("");
        if (civalidatorPojo.getCreatedate() == null) civalidatorPojo.setCreatedate(new Date());
        if (civalidatorPojo.getLister() == null) civalidatorPojo.setLister("");
        if (civalidatorPojo.getListerid() == null) civalidatorPojo.setListerid("");
        if (civalidatorPojo.getModifydate() == null) civalidatorPojo.setModifydate(new Date());
        if (civalidatorPojo.getCustom1() == null) civalidatorPojo.setCustom1("");
        if (civalidatorPojo.getCustom2() == null) civalidatorPojo.setCustom2("");
        if (civalidatorPojo.getCustom3() == null) civalidatorPojo.setCustom3("");
        if (civalidatorPojo.getCustom4() == null) civalidatorPojo.setCustom4("");
        if (civalidatorPojo.getCustom5() == null) civalidatorPojo.setCustom5("");
        if (civalidatorPojo.getTenantid() == null) civalidatorPojo.setTenantid("");
        if (civalidatorPojo.getTenantname() == null) civalidatorPojo.setTenantname("");
        if (civalidatorPojo.getRevision() == null) civalidatorPojo.setRevision(0);
    }

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 执行数据验证
     *
     * @param valicode  验证编码
     * @param dataObj   待验证数据对象
     * @param loginUser 登录用户信息
     * @return ValidationResponse 验证结果
     */
    public ValidationResponse validate(String valicode, Map<String, Object> dataObj, LoginUser loginUser) {
        try {
            String tid = loginUser.getTenantid();
            // 1. 获取所有启用的验证规则
            List<CivalidatorPojo> validators = civalidatorMapper.getListByValicodeEnabled(valicode, tid);
            if (validators == null || validators.isEmpty()) {
                return null; //没有设置规则也返回200，null
            }

            // 2. 创建一个总的结果对象
            int totalCount = 0;
            int totalRequCount = 0;
            List<Map<String, Object>> allMessages = new ArrayList<>();

            // 3. 遍历每个验证规则并执行验证
            for (CivalidatorPojo validator : validators) {
                ValidationResponse singleResponse = validateSingle(validator, dataObj);
                if (singleResponse != null) {
                    totalCount += singleResponse.getTotal();
                    totalRequCount += singleResponse.getRequcount();
                    if (singleResponse.getList() != null) {
                        allMessages.addAll(singleResponse.getList());
                    }
                }
            }

            // 4. 构建合并后的返回结果
            return new ValidationResponse.Builder()
                    .total(totalCount)
                    .requcount(totalRequCount)
                    .list(allMessages)
                    .build();

        } catch (Exception e) {
            throw new BaseBusinessException("验证过程发生错误: " + e.getMessage());
        }
    }

    /**
     * 执行单个验证规则
     */
    private ValidationResponse validateSingle(CivalidatorPojo validator, Map<String, Object> dataObj) {
        // 初始化结果收集器
        ValidationResultCollector collector = new ValidationResultCollector(validator.getRequiredmark());

        // 根据规则类型执行验证: 是否进行item循环验证
        if (validator.getItemloopmark() == 1) {
            processItemLoop(validator, dataObj, collector);
        } else {//普通单次验证
            processSingle(validator, dataObj, collector);
        }

        // 返回单个规则的验证结果
        return new ValidationResponse.Builder()
                .total(collector.getTotalCount())
                .requcount(collector.getTrueCount())
                .list(collector.getMessages())
                .build();
    }

    /**
     * 处理循环验证 必须有item:[]属性
     */
    private void processItemLoop(CivalidatorPojo validator, Map<String, Object> dataObj,
                                 ValidationResultCollector collector) {
        if (dataObj.get("item") == null) {
            throw new BaseBusinessException("循环验证必须有item:[]属性");
        }
        List<Map<String, Object>> items = (List<Map<String, Object>>) dataObj.get("item");
        collector.setTotalCount(items.size());

        for (Map<String, Object> item : items) {
            Map<String, Object> fullData = new HashMap<>(dataObj);
            fullData.putAll(item);

            validateSingleItem(validator, fullData, collector);
        }
    }

    /**
     * 处理单项验证
     */
    private void processSingle(CivalidatorPojo validator, Map<String, Object> dataObj,
                               ValidationResultCollector collector) {
        collector.setTotalCount(1);
        validateSingleItem(validator, dataObj, collector);
    }

    /**
     * 验证单个数据项
     */
    private void validateSingleItem(CivalidatorPojo validator, Map<String, Object> data,
                                    ValidationResultCollector collector) {
        // 执行SQL查询（如果需要）查询结果放到data的sqlobj属性中
        if (validator.getSqlmark() == 1) {
            String sql = processTemplate(validator.getSqlstr(), data);
            Map<String, Object> queryResult = jdbcTemplate.queryForMap(sql);
            data.put("sqlobj", queryResult);
        }

        // 计算表达式
        String resolvedExpression = processTemplate(validator.getExpression(), data);
        boolean result = ExpressionUtils.evaluateExpression(resolvedExpression);

        if (result) {
            collector.incrementTrueCount();
            // 生成提示消息
            String messageTemplate = StringUtils.isNotEmpty(validator.getTipmsg()) ?
                    validator.getTipmsg() : validator.getTipmsgen();
            String message = processTemplate(messageTemplate, data);
            collector.addMessage(message);
        }
    }

    /**
     * 使用Velocity处理模板
     */
    private String processTemplate(String template, Map<String, Object> data) {
        VelocityContext context = new VelocityContext();
        context.put("math", Math.class);  // 添加 Math 工具
        // 将数据放入上下文中 最终context格式为： {id:1,name:"张三",sqlobj:{price:12,quantity:10,amount:120}}
        data.forEach(context::put);
        StringWriter writer = new StringWriter();
        try {
            PrintColor.lan("Velocity引擎替换前内容:" + template);
            Velocity.evaluate(context, writer, "log", template);
            PrintColor.red("Velocity引擎替换后内容:" + writer);
            return writer.toString();
        } catch (Exception e) {
            throw new RuntimeException("模板处理失败", e);
        }
    }

}

/// **
// * 验证结果收集器
// */
//@Data
//class ValidationResultCollector {
//    private int totalCount;
//    private int trueCount;
//    private final List<Map<String, Object>> messages;
//    private final Integer requiredMark;
//
//    public ValidationResultCollector(Integer requiredMark) {
//        this.requiredMark = requiredMark;
//        this.messages = new ArrayList<>();
//        this.trueCount = 0;
//    }
//
//    public void incrementTrueCount() {
//        this.trueCount++;
//    }
//
//    public void addMessage(String message) {
//        Map<String, Object> messageMap = new HashMap<>();
//        messageMap.put("msg", message);
//        messageMap.put("requ", requiredMark);
//        messages.add(messageMap);
//    }
//}


