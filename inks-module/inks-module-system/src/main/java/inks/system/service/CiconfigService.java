package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiconfigPojo;

import java.util.List;
import java.util.Map;

/**
 * 系统参数(Ciconfig)表服务接口
 *
 * <AUTHOR>
 * @since 2021-12-27 18:30:51
 */
public interface CiconfigService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiconfigPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CiconfigPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param ciconfigPojo 实例对象
     * @return 实例对象
     */
    CiconfigPojo insert(CiconfigPojo ciconfigPojo);

    /**
     * 修改数据
     *
     * @param ciconfigpojo 实例对象
     * @return 实例对象
     */
    CiconfigPojo update(CiconfigPojo ciconfigpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiconfigPojo getEntityByKey(String key, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiconfigPojo getEntityByKeyUser(String key, String userid, String tid);

    /**
     * 通过key获得值
     *
     * @param key 主键
     * @return 值
     */
    String getConfigValue(String key, String tid, String userid);

    /**
     * 通过key获得值
     *
     * @param tid 主键
     * @return 值
     */
    List<CiconfigPojo> getListByTenant(String tid);

    /**
     * 通过key获得值
     *
     * @param tid 主键
     * @return 值
     */
    Map<String, String> getMapByTenant(Integer db, String tid);

    /**
     * 通过key获得值
     *
     * @param tid 主键
     * @return 值
     */
    Map<String, String> getMapByTenUi(Integer db, String tid);


    /**
     * 通过key获得值
     *
     * @param tid 主键
     * @return 值
     */
    Map<String, String> getMapByUserUi(String Userid, String tid);

    /**
     * 修改数据
     *
     * @param lst 例对象
     * @return 实例对象
     */
    int updateList(List<CiconfigPojo> lst, LoginUser loginUser);

    /**
     * 通过key获得值
     *
     * @return 值
     */
    List<CiconfigPojo> getListByDefault();

    /**
     * 通过key获得值
     *
     * @return 值
     */
    List<CiconfigPojo> getListByPrefix(String key, String tid);

    String unCheckOnline(String username, boolean ischeck, String tid);



    List<Map<String, String>> getMicroAppMapList();
}
