package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiwebprinterPojo;

import java.util.List;

/**
 * 网络打印机(Ciwebprinter)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-08 15:45:15
 */
public interface CiwebprinterService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiwebprinterPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CiwebprinterPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param ciwebprinterPojo 实例对象
     * @return 实例对象
     */
    CiwebprinterPojo insert(CiwebprinterPojo ciwebprinterPojo);

    /**
     * 修改数据
     *
     * @param ciwebprinterpojo 实例对象
     * @return 实例对象
     */
    CiwebprinterPojo update(CiwebprinterPojo ciwebprinterpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);


    /**
     * 通过功能号获得报表样式
     *
     * @param moduleCode 主键
     * @return 是否成功
     */
    List<CiwebprinterPojo> getListByModuleCode(String moduleCode, String tid);

}
