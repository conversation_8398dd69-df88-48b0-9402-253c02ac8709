package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CidgformatEntity;
import inks.system.domain.CidgformatitemEntity;
import inks.system.domain.pojo.CidgformatPojo;
import inks.system.domain.pojo.CidgformatitemPojo;
import inks.system.domain.pojo.CidgformatitemdetailPojo;
import inks.system.mapper.CidgformatMapper;
import inks.system.mapper.CidgformatitemMapper;
import inks.system.service.CidgformatService;
import inks.system.service.CidgformatitemService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 列表格式(Cidgformat)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-21 15:14:27
 */
@Service("cidgformatService")
public class CidgformatServiceImpl implements CidgformatService {
    @Resource
    private CidgformatMapper cidgformatMapper;

    @Resource
    private CidgformatitemMapper cidgformatitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private CidgformatitemService cidgformatitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CidgformatPojo getEntity(String key, String tid) {
        return this.cidgformatMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CidgformatitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CidgformatitemdetailPojo> lst = cidgformatMapper.getPageList(queryParam);
            PageInfo<CidgformatitemdetailPojo> pageInfo = new PageInfo<CidgformatitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CidgformatPojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            CidgformatPojo cidgformatPojo = this.cidgformatMapper.getEntity(key, tid);
            //读取子表
            cidgformatPojo.setItem(cidgformatitemMapper.getList(cidgformatPojo.getId(), cidgformatPojo.getTenantid()));
            return cidgformatPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CidgformatPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CidgformatPojo> lst = cidgformatMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(cidgformatitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<CidgformatPojo> pageInfo = new PageInfo<CidgformatPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CidgformatPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CidgformatPojo> lst = cidgformatMapper.getPageTh(queryParam);
            PageInfo<CidgformatPojo> pageInfo = new PageInfo<CidgformatPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param cidgformatPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public CidgformatPojo insert(CidgformatPojo cidgformatPojo) {

        // 删除原有内容 EricRen 2022-04-21
//        CidgformatPojo DbPojo = this.getBillEntityByCode(cidgformatPojo.getFormcode(), cidgformatPojo.getListerid(), cidgformatPojo.getTenantid());
//        if (DbPojo != null) {
//            this.delete(DbPojo.getId(), DbPojo.getTenantid());
//        }
//初始化NULL字段
        if (cidgformatPojo.getFormgroupid() == null) cidgformatPojo.setFormgroupid("");
        if (cidgformatPojo.getFormcode() == null) cidgformatPojo.setFormcode("");
        if (cidgformatPojo.getFormname() == null) cidgformatPojo.setFormname("");
        if (cidgformatPojo.getRownum() == null) cidgformatPojo.setRownum(0);
        if (cidgformatPojo.getEnabledmark() == null) cidgformatPojo.setEnabledmark(0);
        if (cidgformatPojo.getDefmark() == null) cidgformatPojo.setDefmark(0);
        if (cidgformatPojo.getSummary() == null) cidgformatPojo.setSummary("");
        if (cidgformatPojo.getCreateby() == null) cidgformatPojo.setCreateby("");
        if (cidgformatPojo.getCreatebyid() == null) cidgformatPojo.setCreatebyid("");
        if (cidgformatPojo.getCreatedate() == null) cidgformatPojo.setCreatedate(new Date());
        if (cidgformatPojo.getLister() == null) cidgformatPojo.setLister("");
        if (cidgformatPojo.getListerid() == null) cidgformatPojo.setListerid("");
        if (cidgformatPojo.getModifydate() == null) cidgformatPojo.setModifydate(new Date());
        if (cidgformatPojo.getCustom1() == null) cidgformatPojo.setCustom1("");
        if (cidgformatPojo.getCustom2() == null) cidgformatPojo.setCustom2("");
        if (cidgformatPojo.getCustom3() == null) cidgformatPojo.setCustom3("");
        if (cidgformatPojo.getCustom4() == null) cidgformatPojo.setCustom4("");
        if (cidgformatPojo.getCustom5() == null) cidgformatPojo.setCustom5("");
        if (cidgformatPojo.getTenantid() == null) cidgformatPojo.setTenantid("");
        if (cidgformatPojo.getTenantname() == null) cidgformatPojo.setTenantname("");
        if (cidgformatPojo.getRevision() == null) cidgformatPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        CidgformatEntity cidgformatEntity = new CidgformatEntity();
        BeanUtils.copyProperties(cidgformatPojo, cidgformatEntity);
        //设置id和新建日期
        cidgformatEntity.setId(id);
        cidgformatEntity.setRevision(1);  //乐观锁
        //插入主表
        this.cidgformatMapper.insert(cidgformatEntity);
        //Item子表处理
        List<CidgformatitemPojo> lst = cidgformatPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (CidgformatitemPojo cidgformatitemPojo : lst) {
                //初始化item的NULL
                CidgformatitemPojo itemPojo = this.cidgformatitemService.clearNull(cidgformatitemPojo);
                CidgformatitemEntity cidgformatitemEntity = new CidgformatitemEntity();
                BeanUtils.copyProperties(itemPojo, cidgformatitemEntity);
                //设置id和Pid
                cidgformatitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                cidgformatitemEntity.setPid(id);
                cidgformatitemEntity.setTenantid(cidgformatPojo.getTenantid());
                cidgformatitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.cidgformatitemMapper.insert(cidgformatitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(cidgformatEntity.getId(), cidgformatEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param cidgformatPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public CidgformatPojo update(CidgformatPojo cidgformatPojo) {
        //主表更改
        CidgformatEntity cidgformatEntity = new CidgformatEntity();
        BeanUtils.copyProperties(cidgformatPojo, cidgformatEntity);
        this.cidgformatMapper.update(cidgformatEntity);
        if (cidgformatPojo.getItem() != null) {
            //Item子表处理
            List<CidgformatitemPojo> lst = cidgformatPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = cidgformatMapper.getDelItemIds(cidgformatPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.cidgformatitemMapper.delete(lstDelIds.get(i), cidgformatEntity.getTenantid());
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    CidgformatitemEntity cidgformatitemEntity = new CidgformatitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        CidgformatitemPojo itemPojo = this.cidgformatitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, cidgformatitemEntity);
                        //设置id和Pid
                        cidgformatitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        cidgformatitemEntity.setPid(cidgformatEntity.getId());  // 主表 id
                        cidgformatitemEntity.setTenantid(cidgformatPojo.getTenantid());   // 租户id
                        cidgformatitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.cidgformatitemMapper.insert(cidgformatitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), cidgformatitemEntity);
                        cidgformatitemEntity.setTenantid(cidgformatPojo.getTenantid());
                        this.cidgformatitemMapper.update(cidgformatitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(cidgformatEntity.getId(), cidgformatEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        CidgformatPojo cidgformatPojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<CidgformatitemPojo> lst = cidgformatPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (CidgformatitemPojo cidgformatitemPojo : lst) {
                this.cidgformatitemMapper.delete(cidgformatitemPojo.getId(), tid);
            }
        }
        return this.cidgformatMapper.delete(key, tid);
    }

    @Override
    public CidgformatPojo getEntityByCodeUser(String formCode, String userid, String tenantid) {
        return this.cidgformatMapper.getEntityByCodeUser(formCode, userid, tenantid);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param code 主键
     * @return 实例对象
     */
    @Override
    public CidgformatPojo getBillEntityByCode(String code, String userid, String tid) {
        try {
            //1.加载个人列设置；
            CidgformatPojo cidgformatPojo = this.cidgformatMapper.getEntityByCodeUser(code, userid, tid);
            if (cidgformatPojo != null) {
                //赋值子表
                cidgformatPojo.setItem(cidgformatitemMapper.getList(cidgformatPojo.getId(), cidgformatPojo.getTenantid()));
            }
            //2.无：加载租户下唯一的默认列设置；
            if (cidgformatPojo == null) {
                cidgformatPojo = this.cidgformatMapper.getTenBillEntityByCode(code, tid);
                if (cidgformatPojo != null) {
                    //赋值子表
                    cidgformatPojo.setItem(cidgformatitemMapper.getList(cidgformatPojo.getId(), cidgformatPojo.getTenantid()));
                    cidgformatPojo.setId("");
                }
            }
            //3.还无：加载随机同事的列设置(ORDER BY CreateDate)；
            if (cidgformatPojo == null) {
                cidgformatPojo = this.cidgformatMapper.getEntityByCode(code, tid);
                if (cidgformatPojo != null) {
                    //赋值子表
                    cidgformatPojo.setItem(cidgformatitemMapper.getList(cidgformatPojo.getId(), cidgformatPojo.getTenantid()));
                    cidgformatPojo.setId("");
                }
            }
            return cidgformatPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public CidgformatPojo getTenBillEntityByCode(String code, String tid) {
        // 读取租户指定code下唯一一条默认数据 主表defmark=1 ;
        CidgformatPojo cidgformatPojo = this.cidgformatMapper.getTenBillEntityByCode(code, tid);
        if (cidgformatPojo != null) {
//            throw new BaseBusinessException("管理员需先设置一个默认的列表格式！");
            //读取子表
            cidgformatPojo.setItem(cidgformatitemMapper.getList(cidgformatPojo.getId(), cidgformatPojo.getTenantid()));
        }
        return cidgformatPojo;
    }


    @Override
    @Transactional
    public int deleteByCode(String code, String userid, String tid) {
        // 当前用户的列设置
        List<String> ids = cidgformatMapper.getIdByCode(code, userid, tid);
        if (CollectionUtils.isNotEmpty(ids)) {
            for (String id : ids) {
                 this.delete(id, tid);
            }
        }
        return ids.size();
    }
}
