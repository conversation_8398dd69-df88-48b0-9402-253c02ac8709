package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PidmsfunctEntity;
import inks.system.domain.pojo.PidmsfunctPojo;
import inks.system.mapper.PidmsfunctMapper;
import inks.system.service.PidmsfunctService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * DMS功能(Pidmsfunct)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@Service("pidmsfunctService")
public class PidmsfunctServiceImpl implements PidmsfunctService {
    @Resource
    private PidmsfunctMapper pidmsfunctMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PidmsfunctPojo getEntity(String key) {
        return this.pidmsfunctMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PidmsfunctPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PidmsfunctPojo> lst = pidmsfunctMapper.getPageList(queryParam);
            PageInfo<PidmsfunctPojo> pageInfo = new PageInfo<PidmsfunctPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param pidmsfunctPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PidmsfunctPojo insert(PidmsfunctPojo pidmsfunctPojo) {
        //初始化NULL字段
        if (pidmsfunctPojo.getDmsfunctcode() == null) pidmsfunctPojo.setDmsfunctcode("");
        if (pidmsfunctPojo.getDmsfunctname() == null) pidmsfunctPojo.setDmsfunctname("");
        if (pidmsfunctPojo.getDescription() == null) pidmsfunctPojo.setDescription("");
        if (pidmsfunctPojo.getFunctionid() == null) pidmsfunctPojo.setFunctionid("");
        if (pidmsfunctPojo.getFunctioncode() == null) pidmsfunctPojo.setFunctioncode("");
        if (pidmsfunctPojo.getFunctionname() == null) pidmsfunctPojo.setFunctionname("");
        if (pidmsfunctPojo.getEnabledmark() == null) pidmsfunctPojo.setEnabledmark(0);
        if (pidmsfunctPojo.getRownum() == null) pidmsfunctPojo.setRownum(0);
        if (pidmsfunctPojo.getRemark() == null) pidmsfunctPojo.setRemark("");
        if (pidmsfunctPojo.getCreateby() == null) pidmsfunctPojo.setCreateby("");
        if (pidmsfunctPojo.getCreatebyid() == null) pidmsfunctPojo.setCreatebyid("");
        if (pidmsfunctPojo.getCreatedate() == null) pidmsfunctPojo.setCreatedate(new Date());
        if (pidmsfunctPojo.getLister() == null) pidmsfunctPojo.setLister("");
        if (pidmsfunctPojo.getListerid() == null) pidmsfunctPojo.setListerid("");
        if (pidmsfunctPojo.getModifydate() == null) pidmsfunctPojo.setModifydate(new Date());
        if (pidmsfunctPojo.getRevision() == null) pidmsfunctPojo.setRevision(0);
        PidmsfunctEntity pidmsfunctEntity = new PidmsfunctEntity();
        BeanUtils.copyProperties(pidmsfunctPojo, pidmsfunctEntity);
        //生成雪花id
        pidmsfunctEntity.setDmsfunctid(inksSnowflake.getSnowflake().nextIdStr());
        pidmsfunctEntity.setRevision(1);  //乐观锁
        this.pidmsfunctMapper.insert(pidmsfunctEntity);
        return this.getEntity(pidmsfunctEntity.getDmsfunctid());

    }

    /**
     * 修改数据
     *
     * @param pidmsfunctPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PidmsfunctPojo update(PidmsfunctPojo pidmsfunctPojo) {
        PidmsfunctEntity pidmsfunctEntity = new PidmsfunctEntity();
        BeanUtils.copyProperties(pidmsfunctPojo, pidmsfunctEntity);
        this.pidmsfunctMapper.update(pidmsfunctEntity);
        return this.getEntity(pidmsfunctEntity.getDmsfunctid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pidmsfunctMapper.delete(key);
    }


}
