package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiwarningPojo;

/**
 * 预警项目(Ciwarning)表服务接口
 *
 * <AUTHOR>
 * @since 2022-07-05 20:11:00
 */
public interface CiwarningService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiwarningPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CiwarningPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param ciwarningPojo 实例对象
     * @return 实例对象
     */
    CiwarningPojo insert(CiwarningPojo ciwarningPojo);

    /**
     * 修改数据
     *
     * @param ciwarningpojo 实例对象
     * @return 实例对象
     */
    CiwarningPojo update(CiwarningPojo ciwarningpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
                                                                                                              }
