package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PirmsfunctPojo;

/**
 * RMS功能(Pirmsfunct)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
public interface PirmsfunctService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PirmsfunctPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PirmsfunctPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pirmsfunctPojo 实例对象
     * @return 实例对象
     */
    PirmsfunctPojo insert(PirmsfunctPojo pirmsfunctPojo);

    /**
     * 修改数据
     *
     * @param pirmsfunctpojo 实例对象
     * @return 实例对象
     */
    PirmsfunctPojo update(PirmsfunctPojo pirmsfunctpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
                                                                                     }
