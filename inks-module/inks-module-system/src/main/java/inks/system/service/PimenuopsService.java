package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PimenuopsPojo;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * Ops导航(Pimenuops)表服务接口
 *
 * <AUTHOR>
 * @since 2024-04-24 17:07:53
 */
public interface PimenuopsService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PimenuopsPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PimenuopsPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pimenuopsPojo 实例对象
     * @return 实例对象
     */
    PimenuopsPojo insert(PimenuopsPojo pimenuopsPojo);

    /**
     * 修改数据
     *
     * @param pimenuopspojo 实例对象
     * @return 实例对象
     */
    PimenuopsPojo update(PimenuopsPojo pimenuopspojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<PimenuopsPojo> getListByPid(String key);
}
