package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.constant.InksConstants;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.SerialNoPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.BillCodeUtil;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.StringUtils;
import inks.common.redis.service.RedisService;
import inks.system.constant.MyConstant;
import inks.system.domain.CibillcodeEntity;
import inks.system.domain.pojo.CibillcodePojo;
import inks.system.mapper.CibillcodeMapper;
import inks.system.service.CibillcodeService;
import inks.system.utils.BillCodePrefixValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 单据编码(Cibillcode)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-08 13:27:46
 */
@Service("cibillcodeService")
public class CibillcodeServiceImpl implements CibillcodeService {
    public static final String REDIS_SERIALNO_REFNO = "refno:";
    @Resource
    private CibillcodeMapper cibillcodeMapper;

    @Resource
    private RedisService redisService;
    private static final Logger log = LoggerFactory.getLogger(CibillcodeServiceImpl.class);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CibillcodePojo getEntity(String key, String tid) {
        return this.cibillcodeMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CibillcodePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CibillcodePojo> lst = cibillcodeMapper.getPageList(queryParam);
            PageInfo<CibillcodePojo> pageInfo = new PageInfo<CibillcodePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param cibillcodePojo 实例对象
     * @return 实例对象
     */
    @Override
    public CibillcodePojo insert(CibillcodePojo cibillcodePojo) {
        /**
         * 单据编码格式校验方法
         *
         * 功能说明：
         * 1. 校验前缀格式必须为指定格式之一：YYYY(年)、YY(年)、MM(月)、DD(日)、[00]、[000]、[0000]、[00000]
         * 2. 校验后缀格式必须为"-"或"/"（最后一个前缀不允许有后缀）
         * 3. 校验前缀必须连续填写，不能有跳开的空值
         * 4. 最后一个有值的前缀必须是[00]、[0000]、[00000]或[000000]格式
         * 5. 根据counttype校验必须包含对应的前缀：
         *    - year类型：必须包含YY或YYYY
         *    - month类型：必须包含MM
         *    - day类型：必须包含DD
         * 6. 非最后一个有值的前缀必须设置对应的后缀
         */
        BillCodePrefixValidator.validatePrefix(cibillcodePojo);
        //初始化NULL字段
        cleanNull(cibillcodePojo);
        CibillcodeEntity cibillcodeEntity = new CibillcodeEntity();
        BeanUtils.copyProperties(cibillcodePojo, cibillcodeEntity);

        cibillcodeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.cibillcodeMapper.insert(cibillcodeEntity);
        return this.getEntity(cibillcodeEntity.getId(), cibillcodeEntity.getTenantid());

    }




    /**
     * 修改数据
     *
     * @param cibillcodePojo 实例对象
     * @return 实例对象
     */
    @Override
    public CibillcodePojo update(CibillcodePojo cibillcodePojo) {
        //单据编码格式校验方法
        BillCodePrefixValidator.validatePrefix(cibillcodePojo);
        String tid = cibillcodePojo.getTenantid();
        // 原单需要从Redis中删除： 防止生成编码时察觉不到Cibillcode的改变  参考RefNoUtils.generateRefNo方法
        CibillcodePojo cibillcodePojoDB = this.cibillcodeMapper.getEntity(cibillcodePojo.getId(), tid);
        String redisKey = RefNoUtils.REDIS_SERIALNO_TEMP + cibillcodePojoDB.getModulecode() + tid;
        redisService.deleteObject(redisKey);
        CibillcodeEntity cibillcodeEntity = new CibillcodeEntity();
        BeanUtils.copyProperties(cibillcodePojo, cibillcodeEntity);
        this.cibillcodeMapper.update(cibillcodeEntity);
        return this.getEntity(cibillcodeEntity.getId(), tid);
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        // 原单需要从Redis中删除： 防止生成编码时察觉不到Cibillcode的改变  参考RefNoUtils.generateRefNo方法
        CibillcodePojo cibillcodePojoDB = this.cibillcodeMapper.getEntity(key, tid);
        String redisKey = RefNoUtils.REDIS_SERIALNO_TEMP + cibillcodePojoDB.getModulecode() + tid;
        redisService.deleteObject(redisKey);
        return this.cibillcodeMapper.delete(key, tid);
    }




    private static void cleanNull(CibillcodePojo cibillcodePojo) {
        if (cibillcodePojo.getModulecode() == null) cibillcodePojo.setModulecode("");
        if (cibillcodePojo.getBillname() == null) cibillcodePojo.setBillname("");
        if (cibillcodePojo.getPrefix1() == null) cibillcodePojo.setPrefix1("");
        if (cibillcodePojo.getSuffix1() == null) cibillcodePojo.setSuffix1("");
        if (cibillcodePojo.getPrefix2() == null) cibillcodePojo.setPrefix2("");
        if (cibillcodePojo.getSuffix2() == null) cibillcodePojo.setSuffix2("");
        if (cibillcodePojo.getPrefix3() == null) cibillcodePojo.setPrefix3("");
        if (cibillcodePojo.getSuffix3() == null) cibillcodePojo.setSuffix3("");
        if (cibillcodePojo.getPrefix4() == null) cibillcodePojo.setPrefix4("");
        if (cibillcodePojo.getSuffix4() == null) cibillcodePojo.setSuffix4("");
        if (cibillcodePojo.getPrefix5() == null) cibillcodePojo.setPrefix5("");
        if (cibillcodePojo.getSuffix5() == null) cibillcodePojo.setSuffix5("");
        if (cibillcodePojo.getCounttype() == null) cibillcodePojo.setCounttype("");
        if (cibillcodePojo.getStep() == null) cibillcodePojo.setStep(0);
        if (cibillcodePojo.getCurrentnum() == null) cibillcodePojo.setCurrentnum(0);
        if (cibillcodePojo.getTablename() == null) cibillcodePojo.setTablename("");
        if (cibillcodePojo.getDatecolumn() == null) cibillcodePojo.setDatecolumn("");
        if (cibillcodePojo.getColumnname() == null) cibillcodePojo.setColumnname("");
        if (cibillcodePojo.getDbfilter() == null) cibillcodePojo.setDbfilter("");
        if (cibillcodePojo.getAllowedit() == null) cibillcodePojo.setAllowedit(0);
        if (cibillcodePojo.getAllowdelete() == null) cibillcodePojo.setAllowdelete(0);
        if (cibillcodePojo.getParam1() == null) cibillcodePojo.setParam1("");
        if (cibillcodePojo.getParam2() == null) cibillcodePojo.setParam2("");
        if (cibillcodePojo.getParam3() == null) cibillcodePojo.setParam3("");
        if (cibillcodePojo.getParam4() == null) cibillcodePojo.setParam4("");
        if (cibillcodePojo.getParam5() == null) cibillcodePojo.setParam5("");
        if (cibillcodePojo.getRemark() == null) cibillcodePojo.setRemark("");
        if (cibillcodePojo.getLister() == null) cibillcodePojo.setLister("");
        if (cibillcodePojo.getCreatedate() == null) cibillcodePojo.setCreatedate(new Date());
        if (cibillcodePojo.getModifydate() == null) cibillcodePojo.setModifydate(new Date());
        if (cibillcodePojo.getTenantid() == null) cibillcodePojo.setTenantid("");
    }

    /**
     * 通过功能code生成序号
     *
     * @param moduleCode 主键
     * @return 是否成功
     */
    @Override
    public String getSerialNo(String moduleCode, String tid) {
        return generateSerialNo(moduleCode, null, null, tid);
    }

    @Override
    public String getSerialNo(String moduleCode, String tablename_req, String prefix_req, String tid) {
        return generateSerialNo(moduleCode, tablename_req, prefix_req, tid);
    }

    private String generateSerialNo(String moduleCode, String tablename_req, String prefix_req, String tid) {
        // 记录方法开始时间
        long startTime = System.currentTimeMillis();
        String redisLock_Key = MyConstant.REFNO_LOCK + moduleCode + tid;
        // 查询Redis锁
        Object redisLock_Value = redisService.getCacheObject(redisLock_Key);
        if (redisLock_Value != null) {
            log.error("--------------------moduleCode: " + moduleCode + "： 编码生成方法：禁止重复提交，请5秒后重试  redisService.getCacheObject(redisLock_Key)有值");
            throw new BaseBusinessException(": 禁止重复提交，请5秒后重试");

        }
        String serialNo = DateUtils.dateTimeNow();  // YYYYMMDDHHMMSS
        String renfosn;

        // 查询单据编码生成规则
        CibillcodeEntity cibillcodeEntity = cibillcodeMapper.getEntityByModuleCode(moduleCode, tid);
        if (cibillcodeEntity == null) {
            cibillcodeEntity = cibillcodeMapper.getEntityByModuleCode(moduleCode, InksConstants.DEFAULT_TENANT);
        }

        // 如果数据库没有此编码规则，则手动创建一个CibillcodeEntity对象
        // 要求格式： “BD-yyyyMM-[0000] ,以月计数； 排序字段 createdate
        // Bus_Deliery 若prefix_req没有传，则取2个大写BD
        if (cibillcodeEntity == null && StringUtils.isNotBlank(tablename_req)) {
            cibillcodeEntity = new CibillcodeEntity();
            cibillcodeEntity.setTablename(tablename_req);
            cibillcodeEntity.setColumnname("RefNo");
            cibillcodeEntity.setDatecolumn("CreateDate");
            // 若未传入前缀，则默认取表名前俩大写作为前缀
            if (StringUtils.isBlank(prefix_req)) {
                prefix_req = this.getPrefixByTableName(tablename_req);
            }
            cibillcodeEntity.setPrefix1(prefix_req);
            cibillcodeEntity.setSuffix1("-");
            cibillcodeEntity.setPrefix2("YYYY");
            cibillcodeEntity.setSuffix2("-");
            cibillcodeEntity.setPrefix3("MM");
            cibillcodeEntity.setSuffix3("-");
            cibillcodeEntity.setPrefix4("[0000]");
            cibillcodeEntity.setSuffix4("");
            cibillcodeEntity.setPrefix5("");
            cibillcodeEntity.setSuffix5("");
            cibillcodeEntity.setCounttype("month");
            cibillcodeEntity.setStep(1);
        }

        if (cibillcodeEntity != null) {
            SerialNoPojo serialNoPojo = new SerialNoPojo();
            BeanUtils.copyProperties(cibillcodeEntity, serialNoPojo);
            String prefix = BillCodeUtil.getPrefix(serialNoPojo);

            try {
                // 从DB中获取当前序号
                Date currentDate = new Date();
                Map<String, Object> serialNoMap = cibillcodeMapper.getSerialNo(cibillcodeEntity, prefix.length() + 1, tid, currentDate);
                renfosn = serialNoMap.get("RefNo").toString();

                // 如果不是数字时，改为1
                if (!StringUtils.isNumeric(renfosn)) {
                    renfosn = "1";
                }

                // 生成单据序列号
                serialNo = BillCodeUtil.getSerialNo(serialNoPojo, renfosn);

                // 加Redis锁 防止5秒内并发同一RefNo
                redisService.setCacheObject(redisLock_Key, 1, 5L, TimeUnit.SECONDS);
            } catch (Exception e) {
                // 出现异常时删除Redis中的缓存记录
                redisService.deleteObject(redisLock_Key);
                throw new RuntimeException("生成序号失败，RedisLock已删除，请稍后重试。", e);
            }
        }
        // 记录方法结束时间
        long endTime = System.currentTimeMillis();
        // 计算并打印耗时
        long duration = endTime - startTime;
        log.info("----------------------moduleCode: " + moduleCode + "： 编码生成方法 执行时间: " + duration + " 毫秒, 最终生成编码：" + serialNo);
        //log.info("-----------------耗时：" + (System.currentTimeMillis() - DateUtils.dateTimeNow().getTime()) + "毫秒");
        return serialNo;
    }

    // 默认拿表名前2个大写字母作为前缀：假设tablename_req是Bus_Deliery，前缀是BD,
    //                            假设tablename_req是Bus_deliery，前缀是BU
    public String getPrefixByTableName(String tablename_req) {
        // 只要2个大写字母，就取前2个
        StringBuilder prefix = new StringBuilder(2);
        for (char ch : tablename_req.toCharArray()) {
            if (Character.isUpperCase(ch)) {
                prefix.append(ch);
                if (prefix.length() == 2) {
                    return prefix.toString();
                }
            }
        }
        // 如果没有找到两个大写字母，提取前两个字符
        return tablename_req.substring(0, Math.min(2, tablename_req.length())).toUpperCase();
    }


//    @Override 原版:
//    public String getSerialNo(String ModuleCode, String tid) {
//        String redisKey = "refno:" + ModuleCode + tid;
//        String serialNo = DateUtils.dateTimeNow();  //YYYYMMDDHHMMSS
//        String renfosn = "";
//        //查询单据编码生成规则
//        CibillcodeEntity cibillcodeEntity = cibillcodeMapper.getEntityByModuleCode(ModuleCode, tid);
//        if (cibillcodeEntity == null) {
//            cibillcodeEntity = cibillcodeMapper.getEntityByModuleCode(ModuleCode, InksConstants.DEFAULT_TENANT);
//        }
//
//        if (cibillcodeEntity != null) {
//            SerialNoPojo serialNoPojo = new SerialNoPojo();
//            BeanUtils.copyProperties(cibillcodeEntity, serialNoPojo);
//            String getPrefix = BillCodeUtil.getPrefix(serialNoPojo);
//            // 从redis中获取当前序号
//            try {
//                // 从Redis中获取当前序号
//                String redisSerialNo = redisService.getCacheObject(redisKey);
//                if (StringUtils.isNotEmpty(redisSerialNo)) {
//                    if (redisSerialNo.length() > getPrefix.length()) {
//                        renfosn = redisSerialNo.substring(getPrefix.length()); // 去掉前缀(包括-)
//                    } else {
//                        renfosn = "1";
//                    }
//                } else {
//                    // 获取当前时间
//                    Date currentDate = new Date();
//                    Map<String, Object> map = cibillcodeMapper.getSerialNo(cibillcodeEntity, getPrefix.length() + 1, tid, currentDate);
//                    renfosn = map.get("RefNo").toString();
//                }
//
//                // 如果不是数字时，改为1
//                if (!StringUtils.isNumeric(renfosn)) renfosn = "1";
//                // 单据生成
//                serialNo = BillCodeUtil.getSerialNo(serialNoPojo, renfosn);
//                // 更新Redis中的序号
//                redisService.setCacheObject(redisKey, serialNo, 10L, TimeUnit.MINUTES);
//
//            } catch (Exception e) {
//                // 出现异常时删除Redis中的缓存记录
//                redisService.deleteObject(redisKey);
//                throw new RuntimeException("生成序号失败，Redis缓存已删除，请稍后重试。", e);
//            }
//        }
//        return serialNo;
//    }


    @Override
    public List<String> getSerialNoList(String ModuleCode, Integer num, String tid) {
        List<String> serialNoList = new ArrayList<>();
        String redisKey = REDIS_SERIALNO_REFNO + ModuleCode + tid;
        String serialNo = DateUtils.dateTimeNow();  //YYYYMMDDHHMMSS
        String renfosn = "";
        //查询单据编码生成规则
        CibillcodeEntity cibillcodeEntity = cibillcodeMapper.getEntityByModuleCode(ModuleCode, tid);
        if (cibillcodeEntity == null) {
            cibillcodeEntity = cibillcodeMapper.getEntityByModuleCode(ModuleCode, InksConstants.DEFAULT_TENANT);
        }
        if (cibillcodeEntity != null) {
            SerialNoPojo serialNoPojo = new SerialNoPojo();
            BeanUtils.copyProperties(cibillcodeEntity, serialNoPojo);
            String getPrefix = BillCodeUtil.getPrefix(serialNoPojo);
            for (int i = 0; i < num; i++) {
                // 从redis中获取当前序号
                String redisSerialNo = redisService.getCacheObject(redisKey);
                if (StringUtils.isNotEmpty(redisSerialNo)) {
                    if (redisSerialNo.length() > getPrefix.length()) {
                        renfosn = redisSerialNo.substring(getPrefix.length()); // 去掉前缀(包括-)
                    } else {
                        renfosn = "1";
                    }
                } else {
                    // 获取当前时间
                    Date currentDate = new Date();
                    Map<String, Object> map = cibillcodeMapper.getSerialNo(cibillcodeEntity, getPrefix.length() + 1, tid, currentDate);
                    renfosn = map.get("RefNo").toString();
                }
                // 如果不是数时时，改为1
                if (!StringUtils.isNumeric(renfosn)) renfosn = "1";
                //单据生成
                serialNo = BillCodeUtil.getSerialNo(serialNoPojo, renfosn);
                redisService.setCacheObject(redisKey, serialNo, 10L, TimeUnit.MINUTES);
                serialNoList.add(serialNo);
            }
        } else {
            for (int i = 0; i < num; i++) {
                serialNoList.add(serialNo);
            }
        }
        return serialNoList;
    }

    @Override
    public int copyBillCode(String tid, LoginUser loginUser) {
        Date nowDate = new Date();
        List<CibillcodePojo> defaultBillCodes = cibillcodeMapper.getDefaultBillCodes(InksConstants.DEFAULT_TENANT);
        int count = 0;
        for (CibillcodePojo cibillcodePojo : defaultBillCodes) {
            cibillcodePojo.setTenantid(tid);
            cibillcodePojo.setId(inksSnowflake.getSnowflake().nextIdStr());
            cibillcodePojo.setCreatedate(nowDate);
            cibillcodePojo.setModifydate(nowDate);
            cibillcodePojo.setLister(loginUser.getRealname());
            CibillcodeEntity newBillCode = new CibillcodeEntity();
            BeanUtils.copyProperties(cibillcodePojo, newBillCode);
            cibillcodeMapper.insert(newBillCode);
            count++;
        }
        return count;
    }

    @Override
    public String initSerialNo(String moduleCode, String refno, String tid) {
        // 返回字符串中的数字部分  原始字符串：DM-2024-01-0101，提取到的字符串(去0)：101
        String refnoNumber = extractLastDigits(refno);
        // 覆盖redis
        String redisKey = REDIS_SERIALNO_REFNO + moduleCode + tid;
        Long timeout = 30L;
        redisService.setCacheObject(redisKey, refnoNumber, timeout, TimeUnit.MINUTES);
        return timeout + "分钟内有效! redisKey:" + redisKey;
    }


    // 返回字符串中的数字部分  原始字符串：DM-2024-01-0101，提取到的字符串(去0)：101
    private static String extractLastDigits(String refno) {
        // 使用正则表达式匹配数字部分
        Pattern pattern = Pattern.compile("\\d+$");
        Matcher matcher = pattern.matcher(refno);
        if (matcher.find()) {
            // 提取匹配到的数字并转换为整数
            int number = Integer.parseInt(matcher.group());
            // 去除前导零，得到非零数字的字符串
            return String.valueOf(number);
        } else {
            // 如果没有匹配到数字，可以根据需求返回默认值或抛出异常
            throw new IllegalArgumentException("RefNo格式不正确，未在末尾找到数字部分");
        }
    }


    //拿到Entity用于修改；如果没有时先用于Def的复制一份到tid，再读出
    @Override
    public CibillcodeEntity getEntityByCode(String modulecode, String tid) {
        CibillcodeEntity billcodeEntity = cibillcodeMapper.getEntityByModuleCode(modulecode, tid);
        if (billcodeEntity == null) {
            billcodeEntity = cibillcodeMapper.getEntityByModuleCode(modulecode, InksConstants.DEFAULT_TENANT);
            if (billcodeEntity != null) {
                billcodeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                billcodeEntity.setTenantid(tid);
                billcodeEntity.setCreatedate(new Date());
                billcodeEntity.setModifydate(new Date());
                cibillcodeMapper.insert(billcodeEntity);
            } else {
                throw new BaseBusinessException("默认编码方式不存在，请联系管理员");
            }
        }
        return billcodeEntity;
    }
}
