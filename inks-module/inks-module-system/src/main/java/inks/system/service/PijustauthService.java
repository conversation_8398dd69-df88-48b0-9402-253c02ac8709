package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.JustauthPojo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PijustauthPojo;

import java.util.List;

/**
 * 第三方登录(Pijustauth)表服务接口
 *
 * <AUTHOR>
 * @since 2022-01-16 14:45:58
 */
public interface PijustauthService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PijustauthPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PijustauthPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pijustauthPojo 实例对象
     * @return 实例对象
     */
    PijustauthPojo insert(PijustauthPojo pijustauthPojo);

    /**
     * 修改数据
     *
     * @param pijustauthpojo 实例对象
     * @return 实例对象
     */
    PijustauthPojo update(PijustauthPojo pijustauthpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PijustauthPojo getJustauthByUuid(String key, String type, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PijustauthPojo getJustauthByUserid(String key, String type, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    List<JustauthPojo> getAdminListByDeptid(String key, String type, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    List<JustauthPojo> getListByUnionid( String key);
}
