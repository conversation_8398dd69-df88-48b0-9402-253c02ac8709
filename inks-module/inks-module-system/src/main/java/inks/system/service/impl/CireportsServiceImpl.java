package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.constant.CacheConstants;
import inks.common.core.constant.InksConstants;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.DateUtils;
import inks.common.redis.service.RedisService;
import inks.system.domain.CireportsEntity;
import inks.system.domain.pojo.CireportsPojo;
import inks.system.mapper.CireportsMapper;
import inks.system.mapper.PisubscriberMapper;
import inks.system.service.CireportsService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 报表中心(Cireports)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-08 13:35:26
 */
@Service("cireportsService")
public class CireportsServiceImpl implements CireportsService {
    @Resource
    private CireportsMapper cireportsMapper;
    @Resource
    private PisubscriberMapper pisubscriberMapper;

    @Resource
    private RedisService redisService;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CireportsPojo getEntity(String key, String tid) {
        return this.cireportsMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CireportsPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CireportsPojo> lst = cireportsMapper.getPageList(queryParam);
            PageInfo<CireportsPojo> pageInfo = new PageInfo<CireportsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param cireportsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CireportsPojo insert(CireportsPojo cireportsPojo) {
        //初始化NULL字段
        if (cireportsPojo.getGengroupid() == null) cireportsPojo.setGengroupid("");
        if (cireportsPojo.getModulecode() == null) cireportsPojo.setModulecode("");
        if (cireportsPojo.getRpttype() == null) cireportsPojo.setRpttype("");
        if (cireportsPojo.getRptname() == null) cireportsPojo.setRptname("");
        if (cireportsPojo.getRptdata() == null) cireportsPojo.setRptdata("");
        if (cireportsPojo.getPagerow() == null) cireportsPojo.setPagerow(0);
        if (cireportsPojo.getRownum() == null) cireportsPojo.setRownum(0);
        if (cireportsPojo.getTempurl() == null) cireportsPojo.setTempurl("");
        if (cireportsPojo.getFilename() == null) cireportsPojo.setFilename("");
        if (cireportsPojo.getPrintersn() == null) cireportsPojo.setPrintersn("");
        if (cireportsPojo.getEnabledmark() == null) cireportsPojo.setEnabledmark(0);
        if (cireportsPojo.getGrfdata() == null) cireportsPojo.setGrfdata("");
        if (cireportsPojo.getPaperlength() == null) cireportsPojo.setPaperlength(0D);
        if (cireportsPojo.getPaperwidth() == null) cireportsPojo.setPaperwidth(0D);
        if (cireportsPojo.getRemark() == null) cireportsPojo.setRemark("");
        if (cireportsPojo.getCreateby() == null) cireportsPojo.setCreateby("");
        if (cireportsPojo.getCreatebyid() == null) cireportsPojo.setCreatebyid("");
        if (cireportsPojo.getListerid() == null) cireportsPojo.setListerid("");
        if (cireportsPojo.getLister() == null) cireportsPojo.setLister("");
        if (cireportsPojo.getCreatedate() == null) cireportsPojo.setCreatedate(new Date());
        if (cireportsPojo.getModifydate() == null) cireportsPojo.setModifydate(new Date());
        if (cireportsPojo.getRevision() == null) cireportsPojo.setRevision(0);
        CireportsEntity cireportsEntity = new CireportsEntity();
        BeanUtils.copyProperties(cireportsPojo, cireportsEntity);
        if (cireportsEntity.getRptdata() != null) {
            try {
                //将前端传来的BASE64转换成XML
                cireportsEntity.setRptdata(new String(inks.common.core.utils.Base64.decode(cireportsEntity.getRptdata()), "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        if (cireportsEntity.getGrfdata() != null) {
            try {
                //将前端传来的BASE64转换成XML
                cireportsEntity.setGrfdata(new String(inks.common.core.utils.Base64.decode(cireportsEntity.getGrfdata()), "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        cireportsEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.cireportsMapper.insert(cireportsEntity);
        return this.getEntity(cireportsEntity.getId(), cireportsEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param cireportsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CireportsPojo update(CireportsPojo cireportsPojo) {
        CireportsEntity cireportsEntity = new CireportsEntity();
        BeanUtils.copyProperties(cireportsPojo, cireportsEntity);
        if (cireportsEntity.getRptdata() != null) {
            try {
                //将前端传来的BASE64转换成XML
                cireportsEntity.setRptdata(new String(inks.common.core.utils.Base64.decode(cireportsEntity.getRptdata()), "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        if (cireportsEntity.getGrfdata() != null) {
            try {
                //将前端传来的BASE64转换成XML
                cireportsEntity.setGrfdata(new String(inks.common.core.utils.Base64.decode(cireportsEntity.getGrfdata()), "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        this.cireportsMapper.update(cireportsEntity);
        CireportsPojo cireportsPojoDB = this.getEntity(cireportsEntity.getId(), cireportsEntity.getTenantid());
        // 将修改的报表保存到Redis：代码参考SYSM07B3/getListByModuleCode
        String verifyKey = CacheConstants.REPORT_CODES_KEY + cireportsPojoDB.getId();
        ReportsPojo reportsPojo = new ReportsPojo();
        reportsPojo.setRptdata(cireportsPojoDB.getRptdata());
        reportsPojo.setPagerow(cireportsPojoDB.getPagerow());
        reportsPojo.setTempurl(cireportsPojoDB.getTempurl());
        reportsPojo.setPrintersn(cireportsPojoDB.getPrintersn());
        reportsPojo.setPaperlength(cireportsPojoDB.getPaperlength());
        reportsPojo.setPaperwidth(cireportsPojoDB.getPaperwidth());
        reportsPojo.setGrfdata(cireportsPojoDB.getGrfdata());
        redisService.setCacheObject(verifyKey, reportsPojo, (long) (60 * 12), TimeUnit.MINUTES);
        return cireportsPojoDB;
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.cireportsMapper.delete(key, tid);
    }

    /**
     * 通过功能号获得报表样式
     *
     * @param moduleCode 主键
     * @return 是否成功
     */
    @Override
    public List<CireportsPojo> getListByModuleCode(String moduleCode, String tid) {
        try {
            //自定义报表
            List<CireportsPojo> lst = cireportsMapper.getListByModuleCode(moduleCode, tid);
            //默认格式
            // List<CireportsPojo> lstdef = cireportsMapper.getListByModuleCode(moduleCode, InksConstants.DEFAULT_TENANT);
            //lst.addAll(lstdef);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<CireportsPojo> pullDefault(String moduleCode, LoginUser loginUser) {
        try {
            //搜索默认科目模板
            List<CireportsPojo> lstnew = new ArrayList<>();
            List<CireportsPojo> lstdef = this.pisubscriberMapper.getReportsByTenant(loginUser.getTenantid(), DateUtils.getTime()); // this.cireportsMapper.getListByDef(moduleCode);
            for (CireportsPojo item : lstdef) {
                CireportsPojo dbpojo = cireportsMapper.getEntityByNameCode(item.getRptname(), item.getModulecode(), loginUser.getTenantid());
                if (dbpojo == null) {
                    CireportsPojo cireportsPojo = new CireportsPojo();
                    BeanUtils.copyProperties(item, cireportsPojo);
                    cireportsPojo.setGengroupid("");
                    cireportsPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
                    cireportsPojo.setTenantid(loginUser.getTenantid());
                    cireportsPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
                    cireportsPojo.setCreatebyid(loginUser.getUserid());
                    cireportsPojo.setCreateby(loginUser.getUsername());
                    cireportsPojo.setCreatedate(new Date());
                    cireportsPojo.setListerid(loginUser.getUserid());
                    cireportsPojo.setLister(loginUser.getUsername());
                    cireportsPojo.setModifydate(new Date());
                    CireportsEntity cireportsEntity = new CireportsEntity();
                    BeanUtils.copyProperties(cireportsPojo, cireportsEntity);
                    this.cireportsMapper.insert(cireportsEntity);
                    lstnew.add(cireportsPojo);
                }
            }
            return lstnew;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CireportsPojo> getPageListAll(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CireportsPojo> lst = cireportsMapper.getPageListAll(queryParam);
            PageInfo<CireportsPojo> pageInfo = new PageInfo<CireportsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public int copyReports(String tid, LoginUser loginUser) {
        Date nowDate = new Date();
        List<CireportsPojo> defaultReports = cireportsMapper.getDefaultReports(InksConstants.DEFAULT_TENANT);

        int count = 0;
        for (CireportsPojo cireportsPojo : defaultReports) {
            cireportsPojo.setTenantid(tid);
            cireportsPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
            cireportsPojo.setCreatedate(nowDate);
            cireportsPojo.setCreateby(loginUser.getRealname());
            cireportsPojo.setCreatebyid(loginUser.getUserid());
            cireportsPojo.setModifydate(nowDate);
            cireportsPojo.setListerid(loginUser.getUserid());
            cireportsPojo.setLister(loginUser.getRealname());
            CireportsEntity cireportsEntity = new CireportsEntity();
            BeanUtils.copyProperties(cireportsPojo, cireportsEntity);
            cireportsMapper.insert(cireportsEntity);
            count++;
        }
        return count;
    }

    //初始化创建报表 拷贝默认报表到tid
    @Override
    public String initReports(LoginUser loginUser) {
        String tid = loginUser.getTenantid();
        String userid = loginUser.getUserid();
        String realname = loginUser.getRealname();
        // 获取默认报表列表（默认租户 id 为 "default"）
        List<CireportsPojo> reports = cireportsMapper.getListByTid("default");
        Date date = new Date();
        int total = reports.size();
        int batchSize = 50;
        // 预处理报表数据：设置租户及创建信息
        for (CireportsPojo report : reports) {
            report.setTenantid(tid);
            report.setId(inksSnowflake.getSnowflake().nextIdStr());
            report.setRevision(0);
            report.setRemark("报表初始化/initReports");
            report.setCreatedate(date);
            report.setCreateby(realname);
            report.setCreatebyid(userid);
            report.setLister(realname);
            report.setListerid(userid);
            report.setModifydate(date);
        }
        // 分批批量插入，每批50条
        for (int i = 0; i < total; i += batchSize) {
            int end = Math.min(i + batchSize, total);
            List<CireportsPojo> batch = reports.subList(i, end);
            cireportsMapper.batchInsert(batch);
            System.out.println("已完成 " + end + " / " + total + " 条报表数据插入");
        }
        return "初始化拷贝默认报表：" + total + "条";
    }

    // 初始化权限
    @Override
    public String initPermissions(LoginUser loginUser) {

        return "";
    }
}
