package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.system.domain.pojo.PiuseronlinePojo;
import inks.system.domain.PiuseronlineEntity;
import inks.system.mapper.PiuseronlineMapper;
import inks.system.service.PiuseronlineService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 用户在线(Piuseronline)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-13 10:37:29
 */
@Service("piuseronlineService")
public class PiuseronlineServiceImpl implements PiuseronlineService {
    @Resource
    private PiuseronlineMapper piuseronlineMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PiuseronlinePojo getEntity(String key, String tid) {
        return this.piuseronlineMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PiuseronlinePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PiuseronlinePojo> lst = piuseronlineMapper.getPageList(queryParam);
            PageInfo<PiuseronlinePojo> pageInfo = new PageInfo<PiuseronlinePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param piuseronlinePojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiuseronlinePojo insert(PiuseronlinePojo piuseronlinePojo) {
    //初始化NULL字段
     if(piuseronlinePojo.getUserid()==null) piuseronlinePojo.setUserid("");
     if(piuseronlinePojo.getUsername()==null) piuseronlinePojo.setUsername("");
     if(piuseronlinePojo.getRealname()==null) piuseronlinePojo.setRealname("");
     if(piuseronlinePojo.getNickname()==null) piuseronlinePojo.setNickname("");
     if(piuseronlinePojo.getTenantid()==null) piuseronlinePojo.setTenantid("");
     if(piuseronlinePojo.getTenantname()==null) piuseronlinePojo.setTenantname("");
     if(piuseronlinePojo.getIpaddress()==null) piuseronlinePojo.setIpaddress("");
     if(piuseronlinePojo.getIplocation()==null) piuseronlinePojo.setIplocation("");
     if(piuseronlinePojo.getMacaddress()==null) piuseronlinePojo.setMacaddress("");
     if(piuseronlinePojo.getBrowsername()==null) piuseronlinePojo.setBrowsername("");
     if(piuseronlinePojo.getHostsystem()==null) piuseronlinePojo.setHostsystem("");
     if(piuseronlinePojo.getTerminaltype()==null) piuseronlinePojo.setTerminaltype(0);
     if(piuseronlinePojo.getToken()==null) piuseronlinePojo.setToken("");
     if(piuseronlinePojo.getLogindate()==null) piuseronlinePojo.setLogindate(new Date());
     if(piuseronlinePojo.getLastactivitydate()==null) piuseronlinePojo.setLastactivitydate(new Date());
     if(piuseronlinePojo.getTokenexpirydate()==null) piuseronlinePojo.setTokenexpirydate(new Date());
        PiuseronlineEntity piuseronlineEntity = new PiuseronlineEntity(); 
        BeanUtils.copyProperties(piuseronlinePojo,piuseronlineEntity);
          //生成雪花id
          piuseronlineEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          this.piuseronlineMapper.insert(piuseronlineEntity);
        return this.getEntity(piuseronlineEntity.getId(),piuseronlineEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param piuseronlinePojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiuseronlinePojo update(PiuseronlinePojo piuseronlinePojo) {
        PiuseronlineEntity piuseronlineEntity = new PiuseronlineEntity(); 
        BeanUtils.copyProperties(piuseronlinePojo,piuseronlineEntity);
        this.piuseronlineMapper.update(piuseronlineEntity);
        return this.getEntity(piuseronlineEntity.getId(),piuseronlineEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.piuseronlineMapper.delete(key,tid) ;
    }
    

}
