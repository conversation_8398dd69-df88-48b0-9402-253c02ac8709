package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.JustauthPojo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PijustauthEntity;
import inks.system.domain.pojo.PijustauthPojo;
import inks.system.mapper.PijustauthMapper;
import inks.system.service.PijustauthService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 第三方登录(Pijustauth)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-16 14:45:59
 */
@Service("pijustauthService")
public class PijustauthServiceImpl implements PijustauthService {
    @Resource
    private PijustauthMapper pijustauthMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PijustauthPojo getEntity(String key) {
        return this.pijustauthMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PijustauthPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PijustauthPojo> lst = pijustauthMapper.getPageList(queryParam);
            PageInfo<PijustauthPojo> pageInfo = new PageInfo<PijustauthPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param pijustauthPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PijustauthPojo insert(PijustauthPojo pijustauthPojo) {
        //初始化NULL字段
        if (pijustauthPojo.getUserid() == null) pijustauthPojo.setUserid("");
        if (pijustauthPojo.getUsername() == null) pijustauthPojo.setUsername("");
        if (pijustauthPojo.getRealname() == null) pijustauthPojo.setRealname("");
        if (pijustauthPojo.getNickname() == null) pijustauthPojo.setNickname("");
        if (pijustauthPojo.getAuthtype() == null) pijustauthPojo.setAuthtype("");
        if (pijustauthPojo.getAuthuuid() == null) pijustauthPojo.setAuthuuid("");
        if (pijustauthPojo.getUnionid()==null) pijustauthPojo.setUnionid("");
        if (pijustauthPojo.getAuthavatar() == null) pijustauthPojo.setAuthavatar("");
        if (pijustauthPojo.getCreateby() == null) pijustauthPojo.setCreateby("");
        if (pijustauthPojo.getCreatebyid() == null) pijustauthPojo.setCreatebyid("");
        if (pijustauthPojo.getCreatedate() == null) pijustauthPojo.setCreatedate(new Date());
        if (pijustauthPojo.getLister() == null) pijustauthPojo.setLister("");
        if (pijustauthPojo.getListerid() == null) pijustauthPojo.setListerid("");
        if (pijustauthPojo.getModifydate() == null) pijustauthPojo.setModifydate(new Date());
        if (pijustauthPojo.getCustom1() == null) pijustauthPojo.setCustom1("");
        if (pijustauthPojo.getCustom2() == null) pijustauthPojo.setCustom2("");
        if (pijustauthPojo.getCustom3() == null) pijustauthPojo.setCustom3("");
        if (pijustauthPojo.getCustom4() == null) pijustauthPojo.setCustom4("");
        if (pijustauthPojo.getCustom5() == null) pijustauthPojo.setCustom5("");
        if (pijustauthPojo.getTenantid() == null) pijustauthPojo.setTenantid("");
        if (pijustauthPojo.getTenantname() == null) pijustauthPojo.setTenantname("");
        if (pijustauthPojo.getRevision() == null) pijustauthPojo.setRevision(0);
        PijustauthEntity pijustauthEntity = new PijustauthEntity();
        BeanUtils.copyProperties(pijustauthPojo, pijustauthEntity);

        pijustauthEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        pijustauthEntity.setRevision(1);  //乐观锁
        this.pijustauthMapper.insert(pijustauthEntity);
        return this.getEntity(pijustauthEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param pijustauthPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PijustauthPojo update(PijustauthPojo pijustauthPojo) {
        PijustauthEntity pijustauthEntity = new PijustauthEntity();
        BeanUtils.copyProperties(pijustauthPojo, pijustauthEntity);
        this.pijustauthMapper.update(pijustauthEntity);
        return this.getEntity(pijustauthEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pijustauthMapper.delete(key);
    }


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PijustauthPojo getJustauthByUuid(String key, String type, String tid) {

        return this.pijustauthMapper.getJustauthByUuid(key, type, tid);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PijustauthPojo getJustauthByUserid(String key, String type, String tid) {
        return this.pijustauthMapper.getJustauthByUserid(key, type, tid);
    }


     /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
     @Override
    public   List<JustauthPojo> getAdminListByDeptid(String key, String type, String tid) {
        return this.pijustauthMapper.getAdminListByDeptid(key, type, tid);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public  List<JustauthPojo> getListByUnionid( String key){
        return this.pijustauthMapper.getListByUnionid(key);
    }
}
