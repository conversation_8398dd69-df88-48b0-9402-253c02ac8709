package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PimenuwebPojo;
import inks.system.domain.pojo.PiscmfunctmenuwebPojo;

import java.util.List;

/**
 * SCM菜单关系(Piscmfunctmenuweb)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
public interface PiscmfunctmenuwebService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiscmfunctmenuwebPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PiscmfunctmenuwebPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param piscmfunctmenuwebPojo 实例对象
     * @return 实例对象
     */
    PiscmfunctmenuwebPojo insert(PiscmfunctmenuwebPojo piscmfunctmenuwebPojo);

    /**
     * 修改数据
     *
     * @param piscmfunctmenuwebpojo 实例对象
     * @return 实例对象
     */
    PiscmfunctmenuwebPojo update(PiscmfunctmenuwebPojo piscmfunctmenuwebpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<PiscmfunctmenuwebPojo> getListByFunction(String key);

    List<PimenuwebPojo> getListByLoginUser(LoginUser loginUser);
    List<PimenuwebPojo> getListByScmFunctids(String ids);

}
