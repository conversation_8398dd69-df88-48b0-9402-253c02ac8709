package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CidictitemPojo;
import inks.system.domain.CidictitemEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 字典项目(Cidictitem)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-12 07:38:59
 */
public interface CidictitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CidictitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CidictitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<CidictitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param cidictitemPojo 实例对象
     * @return 实例对象
     */
    CidictitemPojo insert(CidictitemPojo cidictitemPojo);

    /**
     * 修改数据
     *
     * @param cidictitempojo 实例对象
     * @return 实例对象
     */
    CidictitemPojo update(CidictitemPojo cidictitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param cidictitempojo 实例对象
     * @return 实例对象
     */
    CidictitemPojo clearNull(CidictitemPojo cidictitempojo);
}
