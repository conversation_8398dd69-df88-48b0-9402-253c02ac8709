package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.BillCodeUtil;
import inks.system.constant.MyConstant;
import inks.system.domain.PitenantEntity;
import inks.system.domain.PitenantuserEntity;
import inks.system.domain.pojo.PitenantPojo;
import inks.system.mapper.PitenantMapper;
import inks.system.mapper.PitenantuserMapper;
import inks.system.service.PitenantService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 租户表(Pitenant)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-08 14:29:22
 */
@Service("pitenantService")
public class PitenantServiceImpl implements PitenantService {
    @Resource
    private PitenantMapper pitenantMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PitenantPojo getEntity(String key) {
        return this.pitenantMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PitenantPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PitenantPojo> lst = pitenantMapper.getPageList(queryParam);
            PageInfo<PitenantPojo> pageInfo = new PageInfo<PitenantPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param pitenantPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PitenantPojo insert(PitenantPojo pitenantPojo) {
        //初始化NULL字段
        if (pitenantPojo.getTenantcode() == null) pitenantPojo.setTenantcode("");
        if (pitenantPojo.getTenantname() == null) pitenantPojo.setTenantname("");
        if (pitenantPojo.getCompany() == null) pitenantPojo.setCompany("");
        if (pitenantPojo.getCompanyadd() == null) pitenantPojo.setCompanyadd("");
        if (pitenantPojo.getCompanytel() == null) pitenantPojo.setCompanytel("");
        if (pitenantPojo.getContactor() == null) pitenantPojo.setContactor("");
        if (pitenantPojo.getTenantstate() == null) pitenantPojo.setTenantstate(0);
        if (pitenantPojo.getSellerid() == null) pitenantPojo.setSellerid("");
        if (pitenantPojo.getSellercode() == null) pitenantPojo.setSellercode("");
        if (pitenantPojo.getCreateby()== null) pitenantPojo.setCreateby("");
        if (pitenantPojo.getLister() == null) pitenantPojo.setLister("");
        if (pitenantPojo.getCreatedate() == null) pitenantPojo.setCreatedate(new Date());
        if (pitenantPojo.getModifydate() == null) pitenantPojo.setModifydate(new Date());
        if (pitenantPojo.getPreviouvisit() == null) pitenantPojo.setPreviouvisit(new Date());
        if(pitenantPojo.getRevision()==null) pitenantPojo.setRevision(0);
        PitenantEntity pitenantEntity = new PitenantEntity();
        BeanUtils.copyProperties(pitenantPojo, pitenantEntity);

        pitenantEntity.setTenantid(inksSnowflake.getSnowflake().nextIdStr());
        this.pitenantMapper.insert(pitenantEntity);
        return this.getEntity(pitenantEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param pitenantPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PitenantPojo update(PitenantPojo pitenantPojo) {
        PitenantEntity pitenantEntity = new PitenantEntity();
        BeanUtils.copyProperties(pitenantPojo, pitenantEntity);
        this.pitenantMapper.update(pitenantEntity);
        return this.getEntity(pitenantEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pitenantMapper.delete(key);
    }


    @Resource
    private PitenantuserMapper pitenantuserMapper;

    @Override
    @Transactional
    public PitenantPojo createByUser(PitenantPojo piTenantpojo, LoginUser loginUser) {
        int i = 0;
        String tid = inks.common.core.text.inksSnowflake.getSnowflake().nextIdStr();

        //创建租户entity
        PitenantEntity piTenant = new PitenantEntity();
        try {
            //将pojo内容拷贝到entity用于新增
            BeanUtils.copyProperties(piTenantpojo, piTenant);
            piTenant.setCreatedate(BillCodeUtil.newDate());
            piTenant.setModifydate(BillCodeUtil.newDate());
            piTenant.setLister(loginUser.getRealname());
            piTenant.setCreateby(loginUser.getRealname());
            piTenant.setPreviouvisit(new Date());
            piTenant.setRevision(0);
            piTenant.setTenantid(tid);
            //新增租户
            i += pitenantMapper.insert(piTenant);
            //租户于用户关系对象赋值




            //创建租户于用户关系对象
            PitenantuserEntity pitenantuserEntity = new PitenantuserEntity();
            pitenantuserEntity.setIsadmin(1);
            pitenantuserEntity.setRownum(1);
            pitenantuserEntity.setId(inks.common.core.text.inksSnowflake.getSnowflake().nextIdStr());
            pitenantuserEntity.setTenantid(tid);
            pitenantuserEntity.setUserid(loginUser.getUserid());
            pitenantuserEntity.setListerid(loginUser.getUserid());
            pitenantuserEntity.setLister(loginUser.getRealname());
            pitenantuserEntity.setCreateby(loginUser.getRealName());
            pitenantuserEntity.setCreatebyid(loginUser.getUserid());
            pitenantuserEntity.setCreatedate(new Date());
            pitenantuserEntity.setModifydate(new Date());
            pitenantuserEntity.setDeptrownum(0);
            pitenantuserEntity.setRevision(0);

            //初始化NULL字段
            if(pitenantuserEntity.getTenantname()==null) pitenantuserEntity.setTenantname(piTenant.getTenantname());
            if(pitenantuserEntity.getUsername()==null) pitenantuserEntity.setUsername(loginUser.getRealName());
            if(pitenantuserEntity.getRealname()==null) pitenantuserEntity.setRealname(loginUser.getRealName());
            if(pitenantuserEntity.getDeptid()==null) pitenantuserEntity.setDeptid("");
            if(pitenantuserEntity.getDeptcode()==null) pitenantuserEntity.setDeptcode("");
            if(pitenantuserEntity.getDeptname()==null) pitenantuserEntity.setDeptname("");
            if(pitenantuserEntity.getIsdeptadmin()==null) pitenantuserEntity.setIsdeptadmin(0);


            i += pitenantuserMapper.insert(pitenantuserEntity);

            PitenantPojo pitenantPojo = this.getEntity(tid);
            return pitenantPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    //接口：getCountRecord?fn=oms&tid=xxx
    //如果tid空时，比logininfo中取；
    //fn定义要查哪几个表格；
    //比如：oms
    //
    //itemname,   count,  firstdate,   lastdate;
    //销售订单  ，1200， 2023-1-1， 2024-11-25
    @Override
    public List<Map<String, Object>> getCountRecord(String fn, String tid) {
        // 根据 fn 值获取对应的表格列表    FN_TABLE_MAP.put("oms", FN_OMS_TABLES);
        List<String> tables = MyConstant.FN_TABLES_MAP.get(fn.toLowerCase());
        if (tables == null) {
            throw new RuntimeException("fn没有对应的表格列表");
        }
        List<Map<String, Object>> countRecord = pitenantuserMapper.getCountRecord(tables, tid);

        // 遍历返回结果，加入表名的注释
        for (Map<String, Object> record : countRecord) {
            String tableName = (String) record.get("tablename");
            String comment = MyConstant.TABLE_NAME_COMMENTS.get(tableName);
            if (comment != null) {
                record.put("tablename_comment", comment); // 添加注释字段
            }
        }

        return countRecord;
    }



}
