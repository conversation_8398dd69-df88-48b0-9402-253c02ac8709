package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.constant.InksConstants;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CiemailEntity;
import inks.system.domain.pojo.CiemailPojo;
import inks.system.mapper.CiemailMapper;
import inks.system.service.CiemailService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 邮件模板(Ciemail)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-29 15:00:43
 */
@Service("ciemailService")
public class CiemailServiceImpl implements CiemailService {
    @Resource
    private CiemailMapper ciemailMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CiemailPojo getEntity(String key, String tid) {
        return this.ciemailMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CiemailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiemailPojo> lst = ciemailMapper.getPageList(queryParam);
            PageInfo<CiemailPojo> pageInfo = new PageInfo<CiemailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param ciemailPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiemailPojo insert(CiemailPojo ciemailPojo) {
    //初始化NULL字段
     if(ciemailPojo.getModulecode()==null) ciemailPojo.setModulecode("");
     if(ciemailPojo.getEmailname()==null) ciemailPojo.setEmailname("");
     if(ciemailPojo.getEmailcode()==null) ciemailPojo.setEmailcode("");
     if(ciemailPojo.getEmailtype()==null) ciemailPojo.setEmailtype("");
     if(ciemailPojo.getEmailtemplate()==null) ciemailPojo.setEmailtemplate("");
     if(ciemailPojo.getDeftojson()==null) ciemailPojo.setDeftojson("");
     if(ciemailPojo.getPagerow()==null) ciemailPojo.setPagerow(0);
     if(ciemailPojo.getRownum()==null) ciemailPojo.setRownum(0);
     if(ciemailPojo.getEnabledmark()==null) ciemailPojo.setEnabledmark(0);
     if(ciemailPojo.getRemark()==null) ciemailPojo.setRemark("");
     if(ciemailPojo.getCreateby()==null) ciemailPojo.setCreateby("");
     if(ciemailPojo.getCreatebyid()==null) ciemailPojo.setCreatebyid("");
     if(ciemailPojo.getCreatedate()==null) ciemailPojo.setCreatedate(new Date());
     if(ciemailPojo.getListerid()==null) ciemailPojo.setListerid("");
     if(ciemailPojo.getLister()==null) ciemailPojo.setLister("");
     if(ciemailPojo.getModifydate()==null) ciemailPojo.setModifydate(new Date());
     if(ciemailPojo.getCustom1()==null) ciemailPojo.setCustom1("");
     if(ciemailPojo.getCustom2()==null) ciemailPojo.setCustom2("");
     if(ciemailPojo.getCustom3()==null) ciemailPojo.setCustom3("");
     if(ciemailPojo.getCustom4()==null) ciemailPojo.setCustom4("");
     if(ciemailPojo.getCustom5()==null) ciemailPojo.setCustom5("");
     if(ciemailPojo.getTenantid()==null) ciemailPojo.setTenantid("");
     if(ciemailPojo.getTenantname()==null) ciemailPojo.setTenantname("");
     if(ciemailPojo.getRevision()==null) ciemailPojo.setRevision(0);
        CiemailEntity ciemailEntity = new CiemailEntity(); 
        BeanUtils.copyProperties(ciemailPojo,ciemailEntity);
        
          ciemailEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          ciemailEntity.setRevision(1);  //乐观锁
          this.ciemailMapper.insert(ciemailEntity);
        return this.getEntity(ciemailEntity.getId(),ciemailEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param ciemailPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiemailPojo update(CiemailPojo ciemailPojo) {
        CiemailEntity ciemailEntity = new CiemailEntity(); 
        BeanUtils.copyProperties(ciemailPojo,ciemailEntity);
        this.ciemailMapper.update(ciemailEntity);
        return this.getEntity(ciemailEntity.getId(),ciemailEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.ciemailMapper.delete(key,tid) ;
    }


    /**
     * 通过功能号获得报表样式
     *
     * @param moduleCode 主键
     * @return 是否成功
     */
    @Override
    public List<CiemailPojo> getListByModuleCode(String moduleCode, String tid) {
        try {
            //自定义报表
            List<CiemailPojo> lst = ciemailMapper.getListByModuleCode(moduleCode, tid);
            //默认格式
            List<CiemailPojo> lstdef = ciemailMapper.getListByModuleCode(moduleCode, InksConstants.DEFAULT_TENANT);
            lst.addAll(lstdef);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }
                                                                                                                                 
}
