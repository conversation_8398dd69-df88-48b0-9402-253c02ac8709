package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PiroleEntity;
import inks.system.domain.pojo.PirolePojo;
import inks.system.domain.pojo.PiuserrolePojo;
import inks.system.mapper.PiroleMapper;
import inks.system.service.PiroleService;
import inks.system.service.PiuserroleService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 角色表(Pirole)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-17 10:24:53
 */
@Service("piroleService")
public class PiroleServiceImpl implements PiroleService {
    @Resource
    private PiroleMapper piroleMapper;
    @Resource
    private PiuserroleService piuserroleService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PirolePojo getEntity(String key, String tid) {
        return this.piroleMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PirolePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PirolePojo> lst = piroleMapper.getPageList(queryParam);
            PageInfo<PirolePojo> pageInfo = new PageInfo<PirolePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param pirolePojo 实例对象
     * @return 实例对象
     */
    @Override
    public PirolePojo insert(PirolePojo pirolePojo) {
        //初始化NULL字段
        if (pirolePojo.getRolecode() == null) pirolePojo.setRolecode("");
        if (pirolePojo.getRolename() == null) pirolePojo.setRolename("");
        if (pirolePojo.getFunctionid() == null) pirolePojo.setFunctionid("");
        if (pirolePojo.getFunctioncode() == null) pirolePojo.setFunctioncode("");
        if (pirolePojo.getFunctionname() == null) pirolePojo.setFunctionname("");
        if (pirolePojo.getEnabledmark() == null) pirolePojo.setEnabledmark(0);
        if (pirolePojo.getRownum() == null) pirolePojo.setRownum(0);
        if (pirolePojo.getRemark() == null) pirolePojo.setRemark("");
        if (pirolePojo.getCreateby() == null) pirolePojo.setCreateby("");
        if (pirolePojo.getCreatebyid() == null) pirolePojo.setCreatebyid("");
        if (pirolePojo.getCreatedate() == null) pirolePojo.setCreatedate(new Date());
        if (pirolePojo.getLister() == null) pirolePojo.setLister("");
        if (pirolePojo.getListerid() == null) pirolePojo.setListerid("");
        if (pirolePojo.getModifydate() == null) pirolePojo.setModifydate(new Date());
        if (pirolePojo.getTenantid() == null) pirolePojo.setTenantid("");
        if (pirolePojo.getTenantname() == null) pirolePojo.setTenantname("");
        if (pirolePojo.getRevision() == null) pirolePojo.setRevision(0);
        PiroleEntity piroleEntity = new PiroleEntity();
        BeanUtils.copyProperties(pirolePojo, piroleEntity);

        piroleEntity.setRoleid(inksSnowflake.getSnowflake().nextIdStr());
        piroleEntity.setRevision(1);  //乐观锁
        this.piroleMapper.insert(piroleEntity);
        return this.getEntity(piroleEntity.getRoleid(), piroleEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param pirolePojo 实例对象
     * @return 实例对象
     */
    @Override
    public PirolePojo update(PirolePojo pirolePojo) {
        PiroleEntity piroleEntity = new PiroleEntity();
        BeanUtils.copyProperties(pirolePojo, piroleEntity);
        this.piroleMapper.update(piroleEntity);
        return this.getEntity(piroleEntity.getRoleid(), piroleEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        // 删除角色需检查是否被用户关联
        List<PiuserrolePojo> listUserByRole = this.piuserroleService.getListByRole(key);//根据角色id获取关系List
        if (CollectionUtils.isNotEmpty(listUserByRole)) {
            throw new BaseBusinessException("该角色下存在关联用户,禁止删除");
        }
        return this.piroleMapper.delete(key, tid);
    }


}
