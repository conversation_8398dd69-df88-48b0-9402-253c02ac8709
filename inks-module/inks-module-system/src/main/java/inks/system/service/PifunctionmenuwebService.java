package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PifunctionmenuwebPojo;
import inks.system.domain.PifunctionmenuwebEntity;

import com.github.pagehelper.PageInfo;
import inks.system.domain.pojo.PifunctionpermPojo;

import java.util.List;

/**
 * 服务菜单关系(Pifunctionmenuweb)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-11 17:34:49
 */
public interface PifunctionmenuwebService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionmenuwebPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PifunctionmenuwebPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pifunctionmenuwebPojo 实例对象
     * @return 实例对象
     */
    PifunctionmenuwebPojo insert(PifunctionmenuwebPojo pifunctionmenuwebPojo);

    /**
     * 修改数据
     *
     * @param pifunctionmenuwebpojo 实例对象
     * @return 实例对象
     */
    PifunctionmenuwebPojo update(PifunctionmenuwebPojo pifunctionmenuwebpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);


    List<PifunctionmenuwebPojo> getListByFunction(String key);

}
