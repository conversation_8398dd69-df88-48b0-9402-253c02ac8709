package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PirmsfunctmenuwebEntity;
import inks.system.domain.pojo.PirmsfunctmenuwebPojo;
import inks.system.domain.pojo.PimenuwebPojo;
import inks.system.mapper.PirmsfunctmenuwebMapper;
import inks.system.service.PirmsfunctmenuwebService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * RMS菜单关系(Pirmsfunctmenuweb)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@Service("pirmsfunctmenuwebService")
public class PirmsfunctmenuwebServiceImpl implements PirmsfunctmenuwebService {
    @Resource
    private PirmsfunctmenuwebMapper pirmsfunctmenuwebMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PirmsfunctmenuwebPojo getEntity(String key) {
        return this.pirmsfunctmenuwebMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PirmsfunctmenuwebPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PirmsfunctmenuwebPojo> lst = pirmsfunctmenuwebMapper.getPageList(queryParam);
            PageInfo<PirmsfunctmenuwebPojo> pageInfo = new PageInfo<PirmsfunctmenuwebPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param pirmsfunctmenuwebPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PirmsfunctmenuwebPojo insert(PirmsfunctmenuwebPojo pirmsfunctmenuwebPojo) {
    //初始化NULL字段
     if(pirmsfunctmenuwebPojo.getRmsfunctid()==null) pirmsfunctmenuwebPojo.setRmsfunctid("");
     if(pirmsfunctmenuwebPojo.getRmsfunctcode()==null) pirmsfunctmenuwebPojo.setRmsfunctcode("");
     if(pirmsfunctmenuwebPojo.getRmsfunctname()==null) pirmsfunctmenuwebPojo.setRmsfunctname("");
     if(pirmsfunctmenuwebPojo.getNavid()==null) pirmsfunctmenuwebPojo.setNavid("");
     if(pirmsfunctmenuwebPojo.getNavcode()==null) pirmsfunctmenuwebPojo.setNavcode("");
     if(pirmsfunctmenuwebPojo.getNavname()==null) pirmsfunctmenuwebPojo.setNavname("");
     if(pirmsfunctmenuwebPojo.getRemark()==null) pirmsfunctmenuwebPojo.setRemark("");
     if(pirmsfunctmenuwebPojo.getCreateby()==null) pirmsfunctmenuwebPojo.setCreateby("");
     if(pirmsfunctmenuwebPojo.getCreatebyid()==null) pirmsfunctmenuwebPojo.setCreatebyid("");
     if(pirmsfunctmenuwebPojo.getCreatedate()==null) pirmsfunctmenuwebPojo.setCreatedate(new Date());
     if(pirmsfunctmenuwebPojo.getLister()==null) pirmsfunctmenuwebPojo.setLister("");
     if(pirmsfunctmenuwebPojo.getListerid()==null) pirmsfunctmenuwebPojo.setListerid("");
     if(pirmsfunctmenuwebPojo.getModifydate()==null) pirmsfunctmenuwebPojo.setModifydate(new Date());
     if(pirmsfunctmenuwebPojo.getRevision()==null) pirmsfunctmenuwebPojo.setRevision(0);
        PirmsfunctmenuwebEntity pirmsfunctmenuwebEntity = new PirmsfunctmenuwebEntity(); 
        BeanUtils.copyProperties(pirmsfunctmenuwebPojo,pirmsfunctmenuwebEntity);
  //生成雪花id
          pirmsfunctmenuwebEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          pirmsfunctmenuwebEntity.setRevision(1);  //乐观锁
          this.pirmsfunctmenuwebMapper.insert(pirmsfunctmenuwebEntity);
        return this.getEntity(pirmsfunctmenuwebEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param pirmsfunctmenuwebPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PirmsfunctmenuwebPojo update(PirmsfunctmenuwebPojo pirmsfunctmenuwebPojo) {
        PirmsfunctmenuwebEntity pirmsfunctmenuwebEntity = new PirmsfunctmenuwebEntity(); 
        BeanUtils.copyProperties(pirmsfunctmenuwebPojo,pirmsfunctmenuwebEntity);
        this.pirmsfunctmenuwebMapper.update(pirmsfunctmenuwebEntity);
        return this.getEntity(pirmsfunctmenuwebEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pirmsfunctmenuwebMapper.delete(key) ;
    }

    @Override
    public List<PirmsfunctmenuwebPojo> getListByFunction(String key) {
        return this.pirmsfunctmenuwebMapper.getListByFunction(key);
    }

    @Override
    public List<PimenuwebPojo> getListByLoginUser(LoginUser loginUser){
        return this.pirmsfunctmenuwebMapper.getListByLoginUser(loginUser);
    }

    @Override
    public List<PimenuwebPojo> getListByRmsFunctids(String ids) {
        return this.pirmsfunctmenuwebMapper.getListByRmsFunctids(ids);
    }
}
