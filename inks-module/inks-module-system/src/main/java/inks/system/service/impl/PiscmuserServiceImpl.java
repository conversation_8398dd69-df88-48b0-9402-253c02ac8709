package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.system.domain.pojo.PiscmuserPojo;
import inks.system.domain.PiscmuserEntity;
import inks.system.mapper.PiscmuserMapper;
import inks.system.service.PiscmuserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * SCM用户(Piscmuser)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-21 19:05:49
 */
@Service("piscmuserService")
public class PiscmuserServiceImpl implements PiscmuserService {
    @Resource
    private PiscmuserMapper piscmuserMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PiscmuserPojo getEntity(String key, String tid) {
        return this.piscmuserMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PiscmuserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PiscmuserPojo> lst = piscmuserMapper.getPageList(queryParam);
            PageInfo<PiscmuserPojo> pageInfo = new PageInfo<PiscmuserPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public PageInfo<PiscmuserPojo> getPageListByTen(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PiscmuserPojo> lst = piscmuserMapper.getPageListByTen(queryParam);
            PageInfo<PiscmuserPojo> pageInfo = new PageInfo<PiscmuserPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param piscmuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiscmuserPojo insert(PiscmuserPojo piscmuserPojo) {
    //初始化NULL字段
     if(piscmuserPojo.getUsername()==null) piscmuserPojo.setUsername("");
     if(piscmuserPojo.getRealname()==null) piscmuserPojo.setRealname("");
     if(piscmuserPojo.getNickname()==null) piscmuserPojo.setNickname("");
     if(piscmuserPojo.getUserpassword()==null) piscmuserPojo.setUserpassword("");
     if(piscmuserPojo.getMobile()==null) piscmuserPojo.setMobile("");
     if(piscmuserPojo.getEmail()==null) piscmuserPojo.setEmail("");
     if(piscmuserPojo.getSex()==null) piscmuserPojo.setSex(0);
     if(piscmuserPojo.getLangcode()==null) piscmuserPojo.setLangcode("");
     if(piscmuserPojo.getAvatar()==null) piscmuserPojo.setAvatar("");
     if(piscmuserPojo.getUsertype()==null) piscmuserPojo.setUsertype(0);
     if(piscmuserPojo.getIsadmin()==null) piscmuserPojo.setIsadmin(0);
     if(piscmuserPojo.getDeptid()==null) piscmuserPojo.setDeptid("");
     if(piscmuserPojo.getDeptcode()==null) piscmuserPojo.setDeptcode("");
     if(piscmuserPojo.getDeptname()==null) piscmuserPojo.setDeptname("");
     if(piscmuserPojo.getIsdeptadmin()==null) piscmuserPojo.setIsdeptadmin(0);
     if(piscmuserPojo.getDeptrownum()==null) piscmuserPojo.setDeptrownum(0);
     if(piscmuserPojo.getRownum()==null) piscmuserPojo.setRownum(0);
     if(piscmuserPojo.getUserstatus()==null) piscmuserPojo.setUserstatus(0);
     if(piscmuserPojo.getUsercode()==null) piscmuserPojo.setUsercode("");
     if(piscmuserPojo.getGroupids()==null) piscmuserPojo.setGroupids("");
     if(piscmuserPojo.getGroupnames()==null) piscmuserPojo.setGroupnames("");
     if(piscmuserPojo.getScmfunctids()==null) piscmuserPojo.setScmfunctids("");
     if(piscmuserPojo.getScmfunctnames()==null) piscmuserPojo.setScmfunctnames("");
     if(piscmuserPojo.getRemark()==null) piscmuserPojo.setRemark("");
        if(piscmuserPojo.getDatalabel()==null) piscmuserPojo.setDatalabel("");
     if(piscmuserPojo.getCreateby()==null) piscmuserPojo.setCreateby("");
     if(piscmuserPojo.getCreatebyid()==null) piscmuserPojo.setCreatebyid("");
     if(piscmuserPojo.getCreatedate()==null) piscmuserPojo.setCreatedate(new Date());
     if(piscmuserPojo.getLister()==null) piscmuserPojo.setLister("");
     if(piscmuserPojo.getListerid()==null) piscmuserPojo.setListerid("");
     if(piscmuserPojo.getModifydate()==null) piscmuserPojo.setModifydate(new Date());
     if(piscmuserPojo.getTenantid()==null) piscmuserPojo.setTenantid("");
     if(piscmuserPojo.getTenantname()==null) piscmuserPojo.setTenantname("");
     if(piscmuserPojo.getRevision()==null) piscmuserPojo.setRevision(0);
        PiscmuserEntity piscmuserEntity = new PiscmuserEntity(); 
        BeanUtils.copyProperties(piscmuserPojo,piscmuserEntity);
          //生成雪花id
          piscmuserEntity.setUserid(inksSnowflake.getSnowflake().nextIdStr());
          piscmuserEntity.setRevision(1);  //乐观锁
          this.piscmuserMapper.insert(piscmuserEntity);
        return this.getEntity(piscmuserEntity.getUserid(),piscmuserEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param piscmuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiscmuserPojo update(PiscmuserPojo piscmuserPojo) {
        PiscmuserEntity piscmuserEntity = new PiscmuserEntity(); 
        BeanUtils.copyProperties(piscmuserPojo,piscmuserEntity);
        this.piscmuserMapper.update(piscmuserEntity);
        return this.getEntity(piscmuserEntity.getUserid(),piscmuserEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.piscmuserMapper.delete(key,tid) ;
    }

    @Override
    public PiscmuserPojo getEntityByUserName(String username) {
        return this.piscmuserMapper.getEntityByUserName(username);
    }
    @Override
    public PiscmuserPojo getEntityByOpenid(String openid, String tenantid) {
        return this.piscmuserMapper.getEntityByOpenid(openid, tenantid);
    }

    @Override
    public List<PiscmuserPojo> getListByOpenid(String openid) {
        return this.piscmuserMapper.getListByOpenid(openid);
    }

    @Override
    public PiscmuserPojo getEntityByUserid(String userid, String tid) {
        return this.piscmuserMapper.getEntityByUserid(userid, tid);
    }
}
