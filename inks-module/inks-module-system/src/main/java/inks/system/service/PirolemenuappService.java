package inks.system.service;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PirolemenuappPojo;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 角色菜单App(PiRoleMenuApp)表服务接口
 *
 * <AUTHOR>
 * @since 2024-06-11 13:55:54
 */
public interface PirolemenuappService {

    PirolemenuappPojo getEntity(String key,String tid);

    PageInfo<PirolemenuappPojo> getPageList(QueryParam queryParam);

    PirolemenuappPojo insert(PirolemenuappPojo pirolemenuappPojo);

    PirolemenuappPojo update(PirolemenuappPojo pirolemenuapppojo);

    int delete(String key,String tid);

    int deleteByRoleidAndNavid(String roleid, String navid, String tenantid);

    Integer batchCreateDelete(String roleid, List<String> deleteNavids, List<String> createNavids, LoginUser loginUser);

    List<PirolemenuappPojo> getListByRole(String key, String tid);
}
