package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.system.domain.pojo.PiscmjustauthPojo;
import inks.system.domain.PiscmjustauthEntity;
import inks.system.mapper.PiscmjustauthMapper;
import inks.system.service.PiscmjustauthService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * SCM第三方登录(Piscmjustauth)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-21 21:25:20
 */
@Service("piscmjustauthService")
public class PiscmjustauthServiceImpl implements PiscmjustauthService {
    @Resource
    private PiscmjustauthMapper piscmjustauthMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PiscmjustauthPojo getEntity(String key, String tid) {
        return this.piscmjustauthMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PiscmjustauthPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PiscmjustauthPojo> lst = piscmjustauthMapper.getPageList(queryParam);
            PageInfo<PiscmjustauthPojo> pageInfo = new PageInfo<PiscmjustauthPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param piscmjustauthPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiscmjustauthPojo insert(PiscmjustauthPojo piscmjustauthPojo) {
    //初始化NULL字段
     if(piscmjustauthPojo.getUserid()==null) piscmjustauthPojo.setUserid("");
     if(piscmjustauthPojo.getUsername()==null) piscmjustauthPojo.setUsername("");
     if(piscmjustauthPojo.getRealname()==null) piscmjustauthPojo.setRealname("");
     if(piscmjustauthPojo.getNickname()==null) piscmjustauthPojo.setNickname("");
     if(piscmjustauthPojo.getAuthtype()==null) piscmjustauthPojo.setAuthtype("");
     if(piscmjustauthPojo.getAuthuuid()==null) piscmjustauthPojo.setAuthuuid("");
     if(piscmjustauthPojo.getUnionid()==null) piscmjustauthPojo.setUnionid("");
     if(piscmjustauthPojo.getAuthavatar()==null) piscmjustauthPojo.setAuthavatar("");
     if(piscmjustauthPojo.getCreateby()==null) piscmjustauthPojo.setCreateby("");
     if(piscmjustauthPojo.getCreatebyid()==null) piscmjustauthPojo.setCreatebyid("");
     if(piscmjustauthPojo.getCreatedate()==null) piscmjustauthPojo.setCreatedate(new Date());
     if(piscmjustauthPojo.getLister()==null) piscmjustauthPojo.setLister("");
     if(piscmjustauthPojo.getListerid()==null) piscmjustauthPojo.setListerid("");
     if(piscmjustauthPojo.getModifydate()==null) piscmjustauthPojo.setModifydate(new Date());
     if(piscmjustauthPojo.getCustom1()==null) piscmjustauthPojo.setCustom1("");
     if(piscmjustauthPojo.getCustom2()==null) piscmjustauthPojo.setCustom2("");
     if(piscmjustauthPojo.getCustom3()==null) piscmjustauthPojo.setCustom3("");
     if(piscmjustauthPojo.getCustom4()==null) piscmjustauthPojo.setCustom4("");
     if(piscmjustauthPojo.getCustom5()==null) piscmjustauthPojo.setCustom5("");
     if(piscmjustauthPojo.getTenantid()==null) piscmjustauthPojo.setTenantid("");
     if(piscmjustauthPojo.getTenantname()==null) piscmjustauthPojo.setTenantname("");
     if(piscmjustauthPojo.getRevision()==null) piscmjustauthPojo.setRevision(0);
        PiscmjustauthEntity piscmjustauthEntity = new PiscmjustauthEntity(); 
        BeanUtils.copyProperties(piscmjustauthPojo,piscmjustauthEntity);
          //生成雪花id
          piscmjustauthEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          piscmjustauthEntity.setRevision(1);  //乐观锁
          this.piscmjustauthMapper.insert(piscmjustauthEntity);
        return this.getEntity(piscmjustauthEntity.getId(),piscmjustauthEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param piscmjustauthPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiscmjustauthPojo update(PiscmjustauthPojo piscmjustauthPojo) {
        PiscmjustauthEntity piscmjustauthEntity = new PiscmjustauthEntity(); 
        BeanUtils.copyProperties(piscmjustauthPojo,piscmjustauthEntity);
        this.piscmjustauthMapper.update(piscmjustauthEntity);
        return this.getEntity(piscmjustauthEntity.getId(),piscmjustauthEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.piscmjustauthMapper.delete(key,tid) ;
    }

    @Override
    public int deleteByOpenid(String openid, String tid) {
        return this.piscmjustauthMapper.deleteByOpenid(openid,tid) ;
    }
}
