package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PimenuwebPojo;
import inks.system.domain.pojo.PidmsfunctmenuwebPojo;

import java.util.List;

/**
 * DMS菜单关系(Pidmsfunctmenuweb)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
public interface PidmsfunctmenuwebService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PidmsfunctmenuwebPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PidmsfunctmenuwebPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pidmsfunctmenuwebPojo 实例对象
     * @return 实例对象
     */
    PidmsfunctmenuwebPojo insert(PidmsfunctmenuwebPojo pidmsfunctmenuwebPojo);

    /**
     * 修改数据
     *
     * @param pidmsfunctmenuwebpojo 实例对象
     * @return 实例对象
     */
    PidmsfunctmenuwebPojo update(PidmsfunctmenuwebPojo pidmsfunctmenuwebpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<PidmsfunctmenuwebPojo> getListByFunction(String key);

    List<PimenuwebPojo> getListByLoginUser(LoginUser loginUser);
    List<PimenuwebPojo> getListByDmsFunctids(String ids);

}
