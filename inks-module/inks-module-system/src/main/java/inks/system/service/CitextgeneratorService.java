package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CitextgeneratorPojo;
import inks.system.domain.CitextgeneratorEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 文本生成器(Citextgenerator)表服务接口
 *
 * <AUTHOR>
 * @since 2023-12-18 15:00:23
 */
public interface CitextgeneratorService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CitextgeneratorPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CitextgeneratorPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param citextgeneratorPojo 实例对象
     * @return 实例对象
     */
    CitextgeneratorPojo insert(CitextgeneratorPojo citextgeneratorPojo);

    /**
     * 修改数据
     *
     * @param citextgeneratorpojo 实例对象
     * @return 实例对象
     */
    CitextgeneratorPojo update(CitextgeneratorPojo citextgeneratorpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    List<CitextgeneratorPojo> orderByRowNum(List<String> ids, String tid);

    List<CitextgeneratorPojo> getTree(String tenantid);
}
