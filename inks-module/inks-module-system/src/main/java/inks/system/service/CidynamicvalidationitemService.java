package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CidynamicvalidationitemPojo;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 动态校验规则子表(Cidynamicvalidationitem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-04 15:55:01
 */
public interface CidynamicvalidationitemService {

    CidynamicvalidationitemPojo getEntity(String key,String tid);

    PageInfo<CidynamicvalidationitemPojo> getPageList(QueryParam queryParam);

    List<CidynamicvalidationitemPojo> getList(String Pid,String tid);  

    CidynamicvalidationitemPojo insert(CidynamicvalidationitemPojo cidynamicvalidationitemPojo);

    CidynamicvalidationitemPojo update(CidynamicvalidationitemPojo cidynamicvalidationitempojo);

    int delete(String key,String tid);

    CidynamicvalidationitemPojo clearNull(CidynamicvalidationitemPojo cidynamicvalidationitempojo);
}
