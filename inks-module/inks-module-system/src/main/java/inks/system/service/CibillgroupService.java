package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CibillgroupPojo;
import inks.system.domain.CibillgroupEntity;

import com.github.pagehelper.PageInfo;
import inks.system.domain.pojo.CireportsPojo;

import java.util.List;

/**
 * 通用分组(Cibillgroup)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-08 13:28:26
 */
public interface CibillgroupService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CibillgroupPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CibillgroupPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param cibillgroupPojo 实例对象
     * @return 实例对象
     */
    CibillgroupPojo insert(CibillgroupPojo cibillgroupPojo);

    /**
     * 修改数据
     *
     * @param cibillgrouppojo 实例对象
     * @return 实例对象
     */
    CibillgroupPojo update(CibillgroupPojo cibillgrouppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);


    /**
     * 通过功能号获得分组
     *
     * @param moduleCode 主键
     * @return 是否成功
     */
    List<CibillgroupPojo> getListByModuleCode(String moduleCode, String tid);
}
