package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.pojo.CidictitemPojo;
import inks.system.domain.CidictitemEntity;
import inks.system.mapper.CidictitemMapper;
import inks.system.service.CidictitemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 字典项目(Cidictitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-12 07:38:59
 */
@Service("cidictitemService")
public class CidictitemServiceImpl implements CidictitemService {
    @Resource
    private CidictitemMapper cidictitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CidictitemPojo getEntity(String key, String tid) {
        return this.cidictitemMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CidictitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CidictitemPojo> lst = cidictitemMapper.getPageList(queryParam);
            PageInfo<CidictitemPojo> pageInfo = new PageInfo<CidictitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<CidictitemPojo> getList(String Pid, String tid) {
        try {
            List<CidictitemPojo> lst = cidictitemMapper.getList(Pid, tid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param cidictitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CidictitemPojo insert(CidictitemPojo cidictitemPojo) {
        //初始化item的NULL
        CidictitemPojo itempojo = this.clearNull(cidictitemPojo);
        CidictitemEntity cidictitemEntity = new CidictitemEntity();
        BeanUtils.copyProperties(itempojo, cidictitemEntity);

        cidictitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        cidictitemEntity.setRevision(1);  //乐观锁
        this.cidictitemMapper.insert(cidictitemEntity);
        return this.getEntity(cidictitemEntity.getId(), cidictitemEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param cidictitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CidictitemPojo update(CidictitemPojo cidictitemPojo) {
        CidictitemEntity cidictitemEntity = new CidictitemEntity();
        BeanUtils.copyProperties(cidictitemPojo, cidictitemEntity);
        this.cidictitemMapper.update(cidictitemEntity);
        return this.getEntity(cidictitemEntity.getId(), cidictitemEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.cidictitemMapper.delete(key, tid);
    }

    /**
     * 修改数据
     *
     * @param cidictitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CidictitemPojo clearNull(CidictitemPojo cidictitemPojo) {
        //初始化NULL字段
        if (cidictitemPojo.getPid() == null) cidictitemPojo.setPid("");
        if (cidictitemPojo.getDictcode() == null) cidictitemPojo.setDictcode("");
        if (cidictitemPojo.getDictvalue() == null) cidictitemPojo.setDictvalue("");
        if (cidictitemPojo.getEssential() == null) cidictitemPojo.setEssential(0);
        if (cidictitemPojo.getCssclass() == null) cidictitemPojo.setCssclass("");
        if (cidictitemPojo.getRownum() == null) cidictitemPojo.setRownum(0);
        if (cidictitemPojo.getDefaultmark() == null) cidictitemPojo.setDefaultmark(0);
        if (cidictitemPojo.getEnabledmark() == null) cidictitemPojo.setEnabledmark(0);
        if (cidictitemPojo.getRemark() == null) cidictitemPojo.setRemark("");
        if (cidictitemPojo.getBackgroundcolor() == null) cidictitemPojo.setBackgroundcolor("");
        if (cidictitemPojo.getFontcolor() == null) cidictitemPojo.setFontcolor("");
        if (cidictitemPojo.getIcon() == null) cidictitemPojo.setIcon("");
        if (cidictitemPojo.getCustom1() == null) cidictitemPojo.setCustom1("");
        if (cidictitemPojo.getCustom2() == null) cidictitemPojo.setCustom2("");
        if (cidictitemPojo.getCustom3() == null) cidictitemPojo.setCustom3("");
        if (cidictitemPojo.getCustom4() == null) cidictitemPojo.setCustom4("");
        if (cidictitemPojo.getCustom5() == null) cidictitemPojo.setCustom5("");
        if (cidictitemPojo.getTenantid() == null) cidictitemPojo.setTenantid("");
        if (cidictitemPojo.getRevision() == null) cidictitemPojo.setRevision(0);
        return cidictitemPojo;
    }
}
