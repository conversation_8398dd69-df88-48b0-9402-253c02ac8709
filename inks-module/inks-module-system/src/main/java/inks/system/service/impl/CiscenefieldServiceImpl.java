package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CiscenefieldEntity;
import inks.system.domain.pojo.CiscenefieldPojo;
import inks.system.mapper.CiscenefieldMapper;
import inks.system.service.CiscenefieldService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 场景字段(Ciscenefield)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-24 11:35:06
 */
@Service("ciscenefieldService")
public class CiscenefieldServiceImpl implements CiscenefieldService {
    @Resource
    private CiscenefieldMapper ciscenefieldMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CiscenefieldPojo getEntity(String key) {
        return this.ciscenefieldMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CiscenefieldPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiscenefieldPojo> lst = ciscenefieldMapper.getPageList(queryParam);
            PageInfo<CiscenefieldPojo> pageInfo = new PageInfo<CiscenefieldPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param ciscenefieldPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiscenefieldPojo insert(CiscenefieldPojo ciscenefieldPojo) {
        //初始化NULL字段
        if (ciscenefieldPojo.getGengroupid() == null) ciscenefieldPojo.setGengroupid("");
        if (ciscenefieldPojo.getModulecode() == null) ciscenefieldPojo.setModulecode("");
        if (ciscenefieldPojo.getFieldcode() == null) ciscenefieldPojo.setFieldcode("");
        if (ciscenefieldPojo.getFieldname() == null) ciscenefieldPojo.setFieldname("");
        if (ciscenefieldPojo.getFieldtype() == null) ciscenefieldPojo.setFieldtype(0);
        if (ciscenefieldPojo.getSearchmark() == null) ciscenefieldPojo.setSearchmark(0);
        if (ciscenefieldPojo.getRownum() == null) ciscenefieldPojo.setRownum(0);
        if (ciscenefieldPojo.getRemark() == null) ciscenefieldPojo.setRemark("");
        if (ciscenefieldPojo.getCreateby() == null) ciscenefieldPojo.setCreateby("");
        if (ciscenefieldPojo.getCreatebyid() == null) ciscenefieldPojo.setCreatebyid("");
        if (ciscenefieldPojo.getCreatedate() == null) ciscenefieldPojo.setCreatedate(new Date());
        if (ciscenefieldPojo.getLister() == null) ciscenefieldPojo.setLister("");
        if (ciscenefieldPojo.getListerid() == null) ciscenefieldPojo.setListerid("");
        if (ciscenefieldPojo.getModifydate() == null) ciscenefieldPojo.setModifydate(new Date());
        if (ciscenefieldPojo.getCustom1() == null) ciscenefieldPojo.setCustom1("");
        if (ciscenefieldPojo.getCustom2() == null) ciscenefieldPojo.setCustom2("");
        if (ciscenefieldPojo.getCustom3() == null) ciscenefieldPojo.setCustom3("");
        if (ciscenefieldPojo.getCustom4() == null) ciscenefieldPojo.setCustom4("");
        if (ciscenefieldPojo.getCustom5() == null) ciscenefieldPojo.setCustom5("");
        if (ciscenefieldPojo.getRevision() == null) ciscenefieldPojo.setRevision(0);
        CiscenefieldEntity ciscenefieldEntity = new CiscenefieldEntity();
        BeanUtils.copyProperties(ciscenefieldPojo, ciscenefieldEntity);

        ciscenefieldEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        ciscenefieldEntity.setRevision(1);  //乐观锁
        this.ciscenefieldMapper.insert(ciscenefieldEntity);
        return this.getEntity(ciscenefieldEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param ciscenefieldPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiscenefieldPojo update(CiscenefieldPojo ciscenefieldPojo) {
        CiscenefieldEntity ciscenefieldEntity = new CiscenefieldEntity();
        BeanUtils.copyProperties(ciscenefieldPojo, ciscenefieldEntity);
        this.ciscenefieldMapper.update(ciscenefieldEntity);
        return this.getEntity(ciscenefieldEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.ciscenefieldMapper.delete(key);
    }

    /**
     * 分页查询
     *
     * @param code 筛选条件
     * @return 查询结果
     */
    @Override
    public List<CiscenefieldPojo> getListByCode(String code) {
        return this.ciscenefieldMapper.getListByCode(code);
    }

}
