package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PifunctiondashEntity;
import inks.system.domain.pojo.PifunctiondashPojo;
import inks.system.mapper.PifunctiondashMapper;
import inks.system.service.PifunctiondashService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 服务工作台(Pifunctiondash)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-15 21:13:36
 */
@Service("pifunctiondashService")
public class PifunctiondashServiceImpl implements PifunctiondashService {
    @Resource
    private PifunctiondashMapper pifunctiondashMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PifunctiondashPojo getEntity(String key) {
        return this.pifunctiondashMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PifunctiondashPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PifunctiondashPojo> lst = pifunctiondashMapper.getPageList(queryParam);
            PageInfo<PifunctiondashPojo> pageInfo = new PageInfo<PifunctiondashPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param pifunctiondashPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PifunctiondashPojo insert(PifunctiondashPojo pifunctiondashPojo) {
        //初始化NULL字段
        if (pifunctiondashPojo.getFunctionid() == null) pifunctiondashPojo.setFunctionid("");
        if (pifunctiondashPojo.getFunctioncode() == null) pifunctiondashPojo.setFunctioncode("");
        if (pifunctiondashPojo.getFunctionname() == null) pifunctiondashPojo.setFunctionname("");
        if (pifunctiondashPojo.getDashid() == null) pifunctiondashPojo.setDashid("");
        if (pifunctiondashPojo.getDashname() == null) pifunctiondashPojo.setDashname("");
        if (pifunctiondashPojo.getRemark() == null) pifunctiondashPojo.setRemark("");
        if (pifunctiondashPojo.getCreateby() == null) pifunctiondashPojo.setCreateby("");
        if (pifunctiondashPojo.getCreatebyid() == null) pifunctiondashPojo.setCreatebyid("");
        if (pifunctiondashPojo.getCreatedate() == null) pifunctiondashPojo.setCreatedate(new Date());
        if (pifunctiondashPojo.getLister() == null) pifunctiondashPojo.setLister("");
        if (pifunctiondashPojo.getListerid() == null) pifunctiondashPojo.setListerid("");
        if (pifunctiondashPojo.getModifydate() == null) pifunctiondashPojo.setModifydate(new Date());
        if (pifunctiondashPojo.getRevision() == null) pifunctiondashPojo.setRevision(0);
        PifunctiondashEntity pifunctiondashEntity = new PifunctiondashEntity();
        BeanUtils.copyProperties(pifunctiondashPojo, pifunctiondashEntity);

        pifunctiondashEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        pifunctiondashEntity.setRevision(1);  //乐观锁
        this.pifunctiondashMapper.insert(pifunctiondashEntity);
        return this.getEntity(pifunctiondashEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param pifunctiondashPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PifunctiondashPojo update(PifunctiondashPojo pifunctiondashPojo) {
        PifunctiondashEntity pifunctiondashEntity = new PifunctiondashEntity();
        BeanUtils.copyProperties(pifunctiondashPojo, pifunctiondashEntity);
        this.pifunctiondashMapper.update(pifunctiondashEntity);
        return this.getEntity(pifunctiondashEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pifunctiondashMapper.delete(key);
    }

    @Override
    public List<PifunctiondashPojo> getListByFunction(String key) {
        return this.pifunctiondashMapper.getListByFunction(key);
    }
}
