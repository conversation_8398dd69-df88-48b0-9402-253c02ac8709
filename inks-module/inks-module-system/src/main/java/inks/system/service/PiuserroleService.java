package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PipermcodePojo;
import inks.system.domain.pojo.PiuserrolePojo;

import java.util.HashSet;
import java.util.List;

/**
 * 角色关系表(Piuserrole)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-08 15:26:54
 */
public interface PiuserroleService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiuserrolePojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PiuserrolePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param piuserrolePojo 实例对象
     * @return 实例对象
     */
    PiuserrolePojo insert(PiuserrolePojo piuserrolePojo);

    /**
     * 修改数据
     *
     * @param piuserrolepojo 实例对象
     * @return 实例对象
     */
    PiuserrolePojo update(PiuserrolePojo piuserrolepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);


    List<PiuserrolePojo> getListByRole(String key);


    List<PiuserrolePojo> getListByUser(String key, String tid);


    List<PipermcodePojo> getPermByUser(String key, String tid);

    HashSet<String> getPermSetByUser(String key, String tid);
}
