package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PiuserloginEntity;
import inks.system.domain.pojo.PiuserloginPojo;
import inks.system.mapper.PiuserloginMapper;
import inks.system.service.PiuserloginService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 用户登录表(Piuserlogin)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-08 14:36:11
 */
@Service("piuserloginService")
public class PiuserloginServiceImpl implements PiuserloginService {
    @Resource
    private PiuserloginMapper piuserloginMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PiuserloginPojo getEntity(String key) {
        return this.piuserloginMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PiuserloginPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PiuserloginPojo> lst = piuserloginMapper.getPageList(queryParam);
            PageInfo<PiuserloginPojo> pageInfo = new PageInfo<PiuserloginPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param piuserloginPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiuserloginPojo insert(PiuserloginPojo piuserloginPojo) {
        //初始化NULL字段
        if (piuserloginPojo.getUserid() == null) piuserloginPojo.setUserid("");
        if (piuserloginPojo.getUserpassword() == null) piuserloginPojo.setUserpassword("");
        if (piuserloginPojo.getCheckipaddr() == null) piuserloginPojo.setCheckipaddr(0);
        if (piuserloginPojo.getIpaddress() == null) piuserloginPojo.setIpaddress("");
        if (piuserloginPojo.getMacaddress() == null) piuserloginPojo.setMacaddress("");
        if (piuserloginPojo.getFirstvisit() == null) piuserloginPojo.setFirstvisit(new Date());
        if (piuserloginPojo.getPreviouvisit() == null) piuserloginPojo.setPreviouvisit(new Date());
        if (piuserloginPojo.getBrowsername() == null) piuserloginPojo.setBrowsername("");
        if (piuserloginPojo.getHostsystem() == null) piuserloginPojo.setHostsystem("");
        if (piuserloginPojo.getLister() == null) piuserloginPojo.setLister("");
        if (piuserloginPojo.getCreatedate() == null) piuserloginPojo.setCreatedate(new Date());
        if (piuserloginPojo.getModifydate() == null) piuserloginPojo.setModifydate(new Date());
        PiuserloginEntity piuserloginEntity = new PiuserloginEntity();
        BeanUtils.copyProperties(piuserloginPojo, piuserloginEntity);

        piuserloginEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.piuserloginMapper.insert(piuserloginEntity);
        return this.getEntity(piuserloginEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param piuserloginPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiuserloginPojo update(PiuserloginPojo piuserloginPojo) {
        PiuserloginEntity piuserloginEntity = new PiuserloginEntity();
        BeanUtils.copyProperties(piuserloginPojo, piuserloginEntity);
        this.piuserloginMapper.update(piuserloginEntity);
        return this.getEntity(piuserloginEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.piuserloginMapper.delete(key);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PiuserloginPojo getEntityByUserid(String key) {
        return this.piuserloginMapper.getEntityByUserid(key);
    }

    @Override
    public List<PiuserloginPojo> getListByUserid(String key, String tenantid) {
        return this.piuserloginMapper.getListByUserid(key, tenantid);
    }
}
