//package inks.system.service.impl;
//
//import cn.hutool.core.date.DateTime;
//import cn.hutool.core.date.DateUtil;
//import cn.hutool.core.io.FileUtil;
//import com.github.pagehelper.PageHelper;
//import com.github.pagehelper.PageInfo;
//import com.seeta.proxy.*;
//import com.seeta.sdk.FaceAntiSpoofing;
//import com.seeta.sdk.SeetaImageData;
//import com.seeta.sdk.SeetaPointF;
//import com.seeta.sdk.SeetaRect;
//import com.seeta.sdk.util.SeetafaceUtil;
//import inks.api.feign.UtilsFeignService;
//import inks.common.core.domain.FileInfo;
//import inks.common.core.domain.LoginUser;
//import inks.common.core.domain.QueryParam;
//import inks.common.core.domain.R;
//import inks.common.core.exception.BaseBusinessException;
//import inks.common.core.text.inksSnowflake;
//import inks.system.config.face.entity.FaceInfo;
//import inks.system.config.face.entity.FaceInfoBo;
//import inks.system.config.face.exception.Seetaface6Exception;
//import inks.system.domain.PifaceinfoEntity;
//import inks.system.domain.pojo.PifaceinfoPojo;
//import inks.system.mapper.PifaceinfoMapper;
//import inks.system.service.PifaceinfoService;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.BeanUtils;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.annotation.Resource;
//import javax.imageio.ImageIO;
//import javax.servlet.http.HttpServletRequest;
//import java.awt.image.BufferedImage;
//import java.io.File;
//import java.io.InputStream;
//import java.util.ArrayList;
//import java.util.Comparator;
//import java.util.Date;
//import java.util.List;
//import java.util.stream.Collectors;
//
///**
// * 人脸信息表(Pifaceinfo)表服务实现类
// *
// * <AUTHOR>
// * @since 2024-11-19 17:02:11
// */
//@Service("pifaceinfoService")
//public class PifaceinfoServiceImpl implements PifaceinfoService {
//    @Resource
//    private PifaceinfoMapper pifaceinfoMapper;
//    Logger logger = LoggerFactory.getLogger(PifaceinfoServiceImpl.class);
//
//    @Override
//    public PifaceinfoPojo getEntity(String key, String tid) {
//        return this.pifaceinfoMapper.getEntity(key, tid);
//    }
//
//
//    @Override
//    public PageInfo<PifaceinfoPojo> getPageList(QueryParam queryParam) {
//        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
//        try {
//            List<PifaceinfoPojo> lst = pifaceinfoMapper.getPageList(queryParam);
//            PageInfo<PifaceinfoPojo> pageInfo = new PageInfo<PifaceinfoPojo>(lst);
//            return pageInfo;
//        } catch (Exception e) {
//            throw new BaseBusinessException(e.getMessage());
//        }
//    }
//
//
//    @Override
//    public PifaceinfoPojo insert(PifaceinfoPojo pifaceinfoPojo) {
//        //初始化NULL字段
//        cleanNull(pifaceinfoPojo);
//        PifaceinfoEntity pifaceinfoEntity = new PifaceinfoEntity();
//        BeanUtils.copyProperties(pifaceinfoPojo, pifaceinfoEntity);
//        //生成雪花id
//        pifaceinfoEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
//        pifaceinfoEntity.setRevision(1);  //乐观锁
//        this.pifaceinfoMapper.insert(pifaceinfoEntity);
//        return this.getEntity(pifaceinfoEntity.getId(), pifaceinfoEntity.getTenantid());
//    }
//
//
//    @Override
//    public PifaceinfoPojo update(PifaceinfoPojo pifaceinfoPojo) {
//        PifaceinfoEntity pifaceinfoEntity = new PifaceinfoEntity();
//        BeanUtils.copyProperties(pifaceinfoPojo, pifaceinfoEntity);
//        this.pifaceinfoMapper.update(pifaceinfoEntity);
//        return this.getEntity(pifaceinfoEntity.getId(), pifaceinfoEntity.getTenantid());
//    }
//
//
//    @Override
//    public int delete(String key, String tid) {
//        return this.pifaceinfoMapper.delete(key, tid);
//    }
//
//
//    public static void cleanNull(PifaceinfoPojo pifaceinfoPojo) {
//        if (pifaceinfoPojo.getFilename() == null) pifaceinfoPojo.setFilename("");
//        if (pifaceinfoPojo.getFilepath() == null) pifaceinfoPojo.setFilepath("");
//        if (pifaceinfoPojo.getX() == null) pifaceinfoPojo.setX(0);
//        if (pifaceinfoPojo.getY() == null) pifaceinfoPojo.setY(0);
//        if (pifaceinfoPojo.getWidth() == null) pifaceinfoPojo.setWidth(0);
//        if (pifaceinfoPojo.getHeight() == null) pifaceinfoPojo.setHeight(0);
//        if (pifaceinfoPojo.getPoints() == null) {
//            pifaceinfoPojo.setPoints(new double[5][0]);  // 人脸关键5点，取决于你的业务需求
//        }
//        if (pifaceinfoPojo.getCreateby() == null) pifaceinfoPojo.setCreateby("");
//        if (pifaceinfoPojo.getCreatebyid() == null) pifaceinfoPojo.setCreatebyid("");
//        if (pifaceinfoPojo.getCreatedate() == null) pifaceinfoPojo.setCreatedate(new Date());
//        if (pifaceinfoPojo.getLister() == null) pifaceinfoPojo.setLister("");
//        if (pifaceinfoPojo.getListerid() == null) pifaceinfoPojo.setListerid("");
//        if (pifaceinfoPojo.getModifydate() == null) pifaceinfoPojo.setModifydate(new Date());
//        if (pifaceinfoPojo.getTenantid() == null) pifaceinfoPojo.setTenantid("");
//        if (pifaceinfoPojo.getTenantname() == null) pifaceinfoPojo.setTenantname("");
//        if (pifaceinfoPojo.getRevision() == null) pifaceinfoPojo.setRevision(0);
//    }
//
//
//    //        ==================================================人脸识别API==================================================================
//    @Resource
//    private UtilsFeignService utilsFeignService;
//
//    /// /@Override
//    /// /@Transactional
//    //public int save原始(MultipartFile face, HttpServletRequest servletRequest, LoginUser loginUser) throws Exception {
//    //    // 上传到本地文件服务
//    //    List<UploadFileResult> uploadFileResults = this.uploadFile(servletRequest);
//    //    if (uploadFileResults != null && !uploadFileResults.isEmpty()) {
//    //        UploadFileResult uploadFileResult = uploadFileResults.get(0);
//    //        // getPiFaceInfo()方法会执行获取人脸数据(人脸向量float[] features、人脸五点关键点double[][] points)
//    //        // 并将数据存到List<PifaceinfoPojo>返回
//    //        List<PifaceinfoPojo> faceInfo = this.getPiFaceInfo(face, uploadFileResult.getFileUrl(),loginUser);
//    //        if (faceInfo != null && !faceInfo.isEmpty()) {
//    //            // 批量保存
//    //            int insertCount = pifaceinfoMapper.insertBatch(faceInfo);
//    //            return insertCount;
//    //        }
//    //    }
//    //    return 0;
//    //}
//    @Override
//    @Transactional
//    public int save(MultipartFile face, HttpServletRequest servletRequest, LoginUser loginUser) throws Exception {
//        // 上传到本地文件服务
//        //List<UploadFileResult> uploadFileResults = this.uploadFile(servletRequest);
//        R<FileInfo> fileInfoR = utilsFeignService.uploadFile(face, "file", "face", "minio", loginUser.getToken());
//        //R<FileInfo> fileInfoR = utilsFeignService.uploadByPath("D:\\face\\image\\me\\10.jpg", "file", "face", "minio", loginUser.getToken());
//        String fileUrl = "";
//        if (fileInfoR.getCode() == 200) {
//            FileInfo fileInfo = fileInfoR.getData();
//            fileUrl = fileInfo.getFileurl();
//            // getPiFaceInfo()方法会执行获取人脸数据(人脸向量float[] features、人脸五点关键点double[][] points)
//            // 并将数据存到List<PifaceinfoPojo>返回
//            List<PifaceinfoPojo> faceInfo = this.getPiFaceInfo(face, fileUrl, loginUser);
//            if (faceInfo != null && !faceInfo.isEmpty()) {
//                // 批量保存
//                int insertCount = pifaceinfoMapper.insertBatch(faceInfo);
//                return insertCount;
//            }
//        }
//        return 0;
//    }
//
//
//    ///**
//    // * 需要上传文件的话可以用这个
//    // */
//    //@Resource
//    //UFOPFactory ufopFactory;
//    //
//    //
//    //public List<UploadFileResult> uploadFile(HttpServletRequest request) {
//    //    Uploader uploader = ufopFactory.getUploader();
//    //    // UploadFile uploadFile = new UploadFile();
//    //
//    //    List<UploadFileResult> upload = uploader.upload(request);
//    //    for (UploadFileResult uploadFileResult : upload) {
//    //
//    //        logger.info(uploadFileResult.toString());
//    //    }
//    //
//    //    return upload;
//    //}
//
//    @Resource
//    FaceDetectorProxy faceDetector;
//
//    @Resource
//    FaceLandmarkerProxy faceLandmarker5;
//
//    @Resource
//    FaceRecognizerProxy faceRecognizer;
//
//    @Resource
//    AgePredictorProxy agePredictor;
//
//    @Resource
//    GenderPredictorProxy genderPredictor;
//
//    @Resource
//    MaskDetectorProxy maskDetector;
//
//    @Resource
//    FaceAntiSpoofingProxy faceAntiSpoofing;
//
//    /**
//     * 攻击人脸检测
//     *
//     * @param faceImage 人脸图片
//     * @return List<FaceAntiSpoofing.Status>
//     */
//    @Override
//    public List<FaceAntiSpoofing.Status> faceAntiSpoofing(MultipartFile faceImage) throws Seetaface6Exception {
//        List<FaceAntiSpoofing.Status> list = new ArrayList<>();
//        try {
//            SeetaImageData image = SeetafaceUtil.toSeetaImageData(ImageIO.read(faceImage.getInputStream()));
//            SeetaRect[] detects = faceDetector.detect(image);
//            for (SeetaRect seetaRect : detects) {
//                SeetaPointF[] pointFS = faceLandmarker5.mark(image, seetaRect);
//                FaceAntiSpoofing.Status predict = faceAntiSpoofing.predict(image, seetaRect, pointFS);
//                list.add(predict);
//            }
//        } catch (Exception e) {
//            logger.error("攻击人脸检测报错", e);
//            throw new Seetaface6Exception(e);
//        }
//        return list;
//    }
//
//    /**
//     * 带口罩人脸检测
//     *
//     * @param faceImage 人脸图片
//     * @return 人是否带口罩
//     */
//    @Override
//    public List<MaskDetectorProxy.MaskItem> maskDetector(MultipartFile faceImage) throws Seetaface6Exception {
//        List<MaskDetectorProxy.MaskItem> list = new ArrayList<>();
//        try {
//            SeetaImageData image = SeetafaceUtil.toSeetaImageData(ImageIO.read(faceImage.getInputStream()));
//            SeetaRect[] detects = faceDetector.detect(image);
//            for (SeetaRect seetaRect : detects) {
//                MaskDetectorProxy.MaskItem detect = maskDetector.detect(image, seetaRect);
//                list.add(detect);
//            }
//        } catch (Exception e) {
//            logger.error("带口罩人脸检测报错", e);
//            throw new Seetaface6Exception(e);
//        }
//        return list;
//    }
//
//    /**
//     * 人脸性别判断
//     *
//     * @param faceImage 人脸图片
//     * @return List<GenderPredictorProxy.GenderItem> 多个人脸的性别
//     */
//    @Override
//    public List<GenderPredictorProxy.GenderItem> genderPredictor(MultipartFile faceImage) throws Seetaface6Exception {
//        List<GenderPredictorProxy.GenderItem> list = new ArrayList<>();
//        try {
//            SeetaImageData image = SeetafaceUtil.toSeetaImageData(ImageIO.read(faceImage.getInputStream()));
//            SeetaRect[] detects = faceDetector.detect(image);
//            for (SeetaRect seetaRect : detects) {
//                //face_landmarker_pts5 根据这个来的
//                SeetaPointF[] pointFS = faceLandmarker5.mark(image, seetaRect);
//                list.add(genderPredictor.predictGenderWithCrop(image, pointFS));
//            }
//        } catch (Exception e) {
//            logger.error("人脸性别判断检测报错", e);
//            throw new Seetaface6Exception(e);
//        }
//        return list;
//    }
//
//    /**
//     * 人脸年龄判断
//     *
//     * @param faceImage 人脸图片
//     * @return List<Integer> 多个人脸的年龄
//     */
//    @Override
//    public List<Integer> agePredictor(MultipartFile faceImage) throws Seetaface6Exception {
//        List<Integer> ageArray = new ArrayList<>();
//        try {
//            BufferedImage srcImage = ImageIO.read(faceImage.getInputStream());
//            SeetaImageData image = SeetafaceUtil.toSeetaImageData(srcImage);
//            //检测人脸，识别到人脸
//            SeetaRect[] detects = faceDetector.detect(image);
//            for (SeetaRect seetaRect : detects) {
//                //人脸关键点定位
//                SeetaPointF[] pointFS = faceLandmarker5.mark(image, seetaRect);
//                //识别年龄
//                int age = agePredictor.predictAgeWithCrop(image, pointFS);
//                ageArray.add(age);
//
//            }
//        } catch (Exception e) {
//            logger.error("人脸年龄判断报错", e);
//            throw new Seetaface6Exception(e);
//        }
//        return ageArray;
//    }
//
//    /**
//     * 人脸对比，1：1
//     *
//     * @param face1 人脸1
//     * @param face2 人脸2
//     * @return Float 分数 0~1
//     */
//    @Override
//    public Float faceRecognizer(MultipartFile face1, MultipartFile face2) throws Seetaface6Exception {
//        float[] features1 = null;
//        float[] features2 = null;
//        try {
//            SeetaImageData image1 = SeetafaceUtil.toSeetaImageData(ImageIO.read(face1.getInputStream()));
//            SeetaImageData image2 = SeetafaceUtil.toSeetaImageData(ImageIO.read(face2.getInputStream()));
//            SeetaRect[] detects1 = faceDetector.detect(image1);
//            for (SeetaRect seetaRect : detects1) {
//                SeetaPointF[] pointFS = faceLandmarker5.mark(image1, seetaRect);
//                features1 = faceRecognizer.extract(image1, pointFS);
//            }
//            SeetaRect[] detects2 = faceDetector.detect(image2);
//            for (SeetaRect seetaRect : detects2) {
//                SeetaPointF[] pointFS = faceLandmarker5.mark(image2, seetaRect);
//                features2 = faceRecognizer.extract(image2, pointFS);
//            }
//        } catch (Exception e) {
//            logger.error("人脸对比，1：1报错", e);
//            throw new Seetaface6Exception(e);
//        }
//        float calculateSimilarity = 0.00F;
//        if (features1 != null && features2 != null) {
//            // calculateSimilarity = VectorUtils.cosineSimilarity(features1, features2);
//            calculateSimilarity = faceRecognizer.cosineSimilarity(features1, features2);
//        }
//        return calculateSimilarity;
//    }
//
//    @Override
//    public float[] faceRecognizer(MultipartFile face) throws Seetaface6Exception {
//        float[] features = null;
//        try {
//            SeetaImageData image = SeetafaceUtil.toSeetaImageData(ImageIO.read(face.getInputStream()));
//            SeetaRect[] detects = faceDetector.detect(image);
//            if (detects != null && detects.length > 0) {
//                SeetaPointF[] pointFS = faceLandmarker5.mark(image, detects[0]);
//                features = faceRecognizer.extract(image, pointFS);
//            }
//        } catch (Exception e) {
//            throw new Seetaface6Exception(e);
//        }
//        return features;
//    }
//
//    @Override
//    public List<FaceInfo> getFaceInfo(MultipartFile face, String path) throws Exception {
//
//        return getFaceInfo(face.getInputStream(), face.getOriginalFilename(), path);
//    }
//
//    @Override
//    public List<FaceInfo> getFaceInfo(File face) throws Exception {
//
//        return getFaceInfo(FileUtil.getInputStream(face), face.getName(), face.getAbsolutePath());
//    }
//
//    public List<FaceInfo> getFaceInfo(InputStream face, String name, String path) throws Exception {
//
//        List<FaceInfo> list = null;
//        SeetaImageData image = SeetafaceUtil.toSeetaImageData(ImageIO.read(face));
//        SeetaRect[] detects = faceDetector.detect(image);
//        if (detects != null && detects.length > 0) {
//            list = new ArrayList<>();
//
//            for (SeetaRect seetaRect : detects) {
//                FaceInfo faceInfo = new FaceInfo();
//
//                SeetaPointF[] pointFs = faceLandmarker5.mark(image, seetaRect);
//                float[] features = faceRecognizer.extract(image, pointFs);
//
//                // 创建时间
//                DateTime date = DateUtil.date();
//                faceInfo.setCreateTime(date);
//                faceInfo.setUpdateTime(date);
//
//                // 人脸向量
//                faceInfo.setFeatures(features);
//
//                // 文件名
//                faceInfo.setFileName(name);
//
//                // 文件路径
//                faceInfo.setFilePath(path);
//
//                // 人脸在原图片中的坐标和高宽
//                faceInfo.setX(seetaRect.x);
//                faceInfo.setY(seetaRect.y);
//                faceInfo.setHeight(seetaRect.height);
//                faceInfo.setWidth(seetaRect.width);
//
//                // 人脸关键点 5点
//                double[][] ponitArr = new double[5][2];
//                for (int i = 0; i < pointFs.length; i++) {
//                    SeetaPointF pointF = pointFs[i];
//                    double[] ponit = new double[2];
//                    ponit[0] = pointF.x;
//                    ponit[1] = pointF.y;
//                    ponitArr[i] = ponit;
//                }
//                faceInfo.setPoints(ponitArr);
//                list.add(faceInfo);
//            }
//
//        }
//        return list;
//    }
//
//    @Override
//    public List<PifaceinfoPojo> getPiFaceInfo(MultipartFile face, String path, LoginUser loginUser) throws Exception {
//        return getPiFaceInfo(face.getInputStream(), face.getOriginalFilename(), path, loginUser);
//    }
//
//    public List<PifaceinfoPojo> getPiFaceInfo(InputStream face, String name, String path, LoginUser loginUser) throws Exception {
//
//        List<PifaceinfoPojo> list = null;
//        SeetaImageData image = SeetafaceUtil.toSeetaImageData(ImageIO.read(face));
//        SeetaRect[] detects = faceDetector.detect(image);
//        if (detects != null && detects.length > 0) {
//            list = new ArrayList<>();
//
//            for (SeetaRect seetaRect : detects) {
//                PifaceinfoPojo faceInfo = new PifaceinfoPojo();
//
//                SeetaPointF[] pointFs = faceLandmarker5.mark(image, seetaRect);
//                float[] features = faceRecognizer.extract(image, pointFs);
//
//                faceInfo.setId(inksSnowflake.getSnowflake().nextIdStr());
//                faceInfo.setTenantid(loginUser.getTenantid());
//                // 创建时间
//                Date date = new Date();
//                faceInfo.setCreatedate(date);
//                faceInfo.setCreateby(loginUser.getRealName());
//                faceInfo.setCreatebyid(loginUser.getUserid());
//                faceInfo.setLister(loginUser.getRealName());
//                faceInfo.setListerid(loginUser.getUserid());
//                faceInfo.setModifydate(date);
//
//                // 人脸向量
//                faceInfo.setFeatures(features);
//
//                // 文件名
//                faceInfo.setFilename(name);
//
//                // 文件路径
//                faceInfo.setFilepath(path);
//
//                // 人脸在原图片中的坐标和高宽
//                faceInfo.setX(seetaRect.x);
//                faceInfo.setY(seetaRect.y);
//                faceInfo.setHeight(seetaRect.height);
//                faceInfo.setWidth(seetaRect.width);
//
//                // 人脸关键点 5点
//                double[][] ponitArr = new double[5][2];
//                for (int i = 0; i < pointFs.length; i++) {
//                    SeetaPointF pointF = pointFs[i];
//                    double[] ponit = new double[2];
//                    ponit[0] = pointF.x;
//                    ponit[1] = pointF.y;
//                    ponitArr[i] = ponit;
//                }
//                faceInfo.setPoints(ponitArr);
//
//                PifaceinfoServiceImpl.cleanNull(faceInfo);
//
//                list.add(faceInfo);
//            }
//
//        }
//        return list;
//    }
//
//    //@Override
//    public List<FaceInfoBo> queryTopN原始(MultipartFile face, int topN) throws Seetaface6Exception {
//
//        float[] oneFaceFeature = this.faceRecognizer(face);
//
//        //if (oneFaceFeature != null && oneFaceFeature.length > 0) {
//        //    // 设置参数，session范围内有效
//        //    pifaceinfoMapper.setEfSearch();
//        //    // 执行向量查询
//        //    List<FaceInfoBo> list = pifaceinfoMapper.queryByFeatures(oneFaceFeature, topN);
//        //
//        //    // 这里可以做一下其他业务操作
//        //    // .....
//        //
//        //    return list;
//        //}
//
//        return null;
//    }
//
//    @Override
//    public List<FaceInfoBo> queryTopN(MultipartFile face, int topN) throws Seetaface6Exception {
//        // 获取输入的图片特征向量
//        float[] oneFaceFeature = this.faceRecognizer(face);
//
//        if (oneFaceFeature != null && oneFaceFeature.length > 0) {
//            // 获取所有面部特征向量
//            List<FaceInfoBo> allFeatures = pifaceinfoMapper.queryAllList();
//            // 计算余弦相似度
//            List<FaceInfoBo> sortedList = allFeatures.stream()
//                    .map(faceInfo -> {
//                        // 计算余弦相似度
//                        float similarity = calculateCosineSimilarity(oneFaceFeature, faceInfo.getFeatures());
//                        faceInfo.setScore(similarity);
//                        return faceInfo;
//                    })
//                    .sorted(Comparator.comparing(FaceInfoBo::getScore).reversed())  // 按相似度排序
//                    .limit(topN)  // 取前N个
//                    .collect(Collectors.toList());
//
//            return sortedList;
//        }
//
//        return null;
//    }
//
//    // 计算余弦相似度
//    private float calculateCosineSimilarity(float[] vector1, float[] vector2) {
//        float dotProduct = 0.0f;
//        float normA = 0.0f;
//        float normB = 0.0f;
//
//        for (int i = 0; i < vector1.length; i++) {
//            dotProduct += vector1[i] * vector2[i];
//            normA += (float) Math.pow(vector1[i], 2);
//            normB += (float) Math.pow(vector2[i], 2);
//        }
//
//        return dotProduct / (float) (Math.sqrt(normA) * Math.sqrt(normB));
//    }
//
//
//}
