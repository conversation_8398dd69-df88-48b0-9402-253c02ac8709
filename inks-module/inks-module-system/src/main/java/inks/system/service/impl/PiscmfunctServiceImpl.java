package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PiscmfunctEntity;
import inks.system.domain.pojo.PiscmfunctPojo;
import inks.system.mapper.PiscmfunctMapper;
import inks.system.service.PiscmfunctService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * SCM功能(Piscmfunct)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@Service("piscmfunctService")
public class PiscmfunctServiceImpl implements PiscmfunctService {
    @Resource
    private PiscmfunctMapper piscmfunctMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PiscmfunctPojo getEntity(String key) {
        return this.piscmfunctMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PiscmfunctPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PiscmfunctPojo> lst = piscmfunctMapper.getPageList(queryParam);
            PageInfo<PiscmfunctPojo> pageInfo = new PageInfo<PiscmfunctPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param piscmfunctPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiscmfunctPojo insert(PiscmfunctPojo piscmfunctPojo) {
        //初始化NULL字段
        if (piscmfunctPojo.getScmfunctcode() == null) piscmfunctPojo.setScmfunctcode("");
        if (piscmfunctPojo.getScmfunctname() == null) piscmfunctPojo.setScmfunctname("");
        if (piscmfunctPojo.getDescription() == null) piscmfunctPojo.setDescription("");
        if (piscmfunctPojo.getFunctionid() == null) piscmfunctPojo.setFunctionid("");
        if (piscmfunctPojo.getFunctioncode() == null) piscmfunctPojo.setFunctioncode("");
        if (piscmfunctPojo.getFunctionname() == null) piscmfunctPojo.setFunctionname("");
        if (piscmfunctPojo.getEnabledmark() == null) piscmfunctPojo.setEnabledmark(0);
        if (piscmfunctPojo.getRownum() == null) piscmfunctPojo.setRownum(0);
        if (piscmfunctPojo.getRemark() == null) piscmfunctPojo.setRemark("");
        if (piscmfunctPojo.getCreateby() == null) piscmfunctPojo.setCreateby("");
        if (piscmfunctPojo.getCreatebyid() == null) piscmfunctPojo.setCreatebyid("");
        if (piscmfunctPojo.getCreatedate() == null) piscmfunctPojo.setCreatedate(new Date());
        if (piscmfunctPojo.getLister() == null) piscmfunctPojo.setLister("");
        if (piscmfunctPojo.getListerid() == null) piscmfunctPojo.setListerid("");
        if (piscmfunctPojo.getModifydate() == null) piscmfunctPojo.setModifydate(new Date());
        if (piscmfunctPojo.getRevision() == null) piscmfunctPojo.setRevision(0);
        PiscmfunctEntity piscmfunctEntity = new PiscmfunctEntity();
        BeanUtils.copyProperties(piscmfunctPojo, piscmfunctEntity);
        //生成雪花id
        piscmfunctEntity.setScmfunctid(inksSnowflake.getSnowflake().nextIdStr());
        piscmfunctEntity.setRevision(1);  //乐观锁
        this.piscmfunctMapper.insert(piscmfunctEntity);
        return this.getEntity(piscmfunctEntity.getScmfunctid());

    }

    /**
     * 修改数据
     *
     * @param piscmfunctPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PiscmfunctPojo update(PiscmfunctPojo piscmfunctPojo) {
        PiscmfunctEntity piscmfunctEntity = new PiscmfunctEntity();
        BeanUtils.copyProperties(piscmfunctPojo, piscmfunctEntity);
        this.piscmfunctMapper.update(piscmfunctEntity);
        return this.getEntity(piscmfunctEntity.getScmfunctid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.piscmfunctMapper.delete(key);
    }


}
