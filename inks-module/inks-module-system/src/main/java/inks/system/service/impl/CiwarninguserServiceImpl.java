package inks.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CiwarninguserEntity;
import inks.system.domain.pojo.CiwarninguserPojo;
import inks.system.mapper.CiwarninguserMapper;
import inks.system.service.CiwarninguserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 用户预警(Ciwarninguser)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-05 20:11:15
 */
@Service("ciwarninguserService")
public class CiwarninguserServiceImpl implements CiwarninguserService {
    @Resource
    private CiwarninguserMapper ciwarninguserMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CiwarninguserPojo getEntity(String key, String tid) {
        return this.ciwarninguserMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CiwarninguserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CiwarninguserPojo> lst = ciwarninguserMapper.getPageList(queryParam);
            PageInfo<CiwarninguserPojo> pageInfo = new PageInfo<CiwarninguserPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param ciwarninguserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiwarninguserPojo insert(CiwarninguserPojo ciwarninguserPojo) {
        //初始化NULL字段
        if (ciwarninguserPojo.getWarnid() == null) ciwarninguserPojo.setWarnid("");
        if (ciwarninguserPojo.getDiffnum() == null) ciwarninguserPojo.setDiffnum(0);
        if (ciwarninguserPojo.getRownum() == null) ciwarninguserPojo.setRownum(0);
        if (ciwarninguserPojo.getRemark() == null) ciwarninguserPojo.setRemark("");
        if (ciwarninguserPojo.getCreateby() == null) ciwarninguserPojo.setCreateby("");
        if (ciwarninguserPojo.getCreatebyid() == null) ciwarninguserPojo.setCreatebyid("");
        if (ciwarninguserPojo.getCreatedate() == null) ciwarninguserPojo.setCreatedate(new Date());
        if (ciwarninguserPojo.getLister() == null) ciwarninguserPojo.setLister("");
        if (ciwarninguserPojo.getListerid() == null) ciwarninguserPojo.setListerid("");
        if (ciwarninguserPojo.getModifydate() == null) ciwarninguserPojo.setModifydate(new Date());
        if (ciwarninguserPojo.getUserid() == null) ciwarninguserPojo.setUserid("");
        if (ciwarninguserPojo.getRealname() == null) ciwarninguserPojo.setRealname("");
        if (ciwarninguserPojo.getTenantid() == null) ciwarninguserPojo.setTenantid("");
        if (ciwarninguserPojo.getTenantname() == null) ciwarninguserPojo.setTenantname("");
        if (ciwarninguserPojo.getRevision() == null) ciwarninguserPojo.setRevision(0);
        CiwarninguserEntity ciwarninguserEntity = new CiwarninguserEntity();
        BeanUtils.copyProperties(ciwarninguserPojo, ciwarninguserEntity);

        ciwarninguserEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        ciwarninguserEntity.setRevision(1);  //乐观锁
        this.ciwarninguserMapper.insert(ciwarninguserEntity);
        return this.getEntity(ciwarninguserEntity.getId(), ciwarninguserEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param ciwarninguserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CiwarninguserPojo update(CiwarninguserPojo ciwarninguserPojo) {
        CiwarninguserEntity ciwarninguserEntity = new CiwarninguserEntity();
        BeanUtils.copyProperties(ciwarninguserPojo, ciwarninguserEntity);
        this.ciwarninguserMapper.update(ciwarninguserEntity);
        return this.getEntity(ciwarninguserEntity.getId(), ciwarninguserEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.ciwarninguserMapper.delete(key, tid);
    }

    /**
     * 用户预警列表
     *
     * @return 查询结果
     */
    @Override
    public List<CiwarninguserPojo> getListByUser(String userid, String tid) {
        return this.ciwarninguserMapper.getListByUser(userid, tid);
    }

    /**
     * 用户预警列表
     *
     * @return 查询结果
     */
    @Override
    public List<CiwarninguserPojo> getWarnListByUser(String userid, String tid) {
        try {
            List<CiwarninguserPojo> lst = this.ciwarninguserMapper.getListByUser(userid, tid);
            for (CiwarninguserPojo pojo : lst) {
                // 销售订单
                if (pojo.getSvccode().equals("D01M03")) {
                    if (pojo.getWarnfield() ==null || "".equals(pojo.getWarnfield())){
                        pojo.setWarnfield("Bus_MachiningItem.ItemPlanDate");
                    }
                    List<Map<String, Object>> lstdata = this.ciwarninguserMapper.getBusMachList(pojo.getWarnfield(), pojo.getDiffnum(), tid);
                    if (lstdata != null) {
                        pojo.setTagdata(JSONObject.toJSONString(lstdata));
                    }
                }
                // 销售订单 预警生产完工但未发货完成的记录
                if (pojo.getSvccode().equals("D01M03CT")) {
                    if (pojo.getWarnfield() ==null || "".equals(pojo.getWarnfield())){
                        pojo.setWarnfield("Bus_MachiningItem.ItemPlanDate");
                    }
                    List<Map<String, Object>> lstdata = this.ciwarninguserMapper.getBusMachCanOutList(pojo.getWarnfield(), pojo.getDiffnum(), tid);
                    if (lstdata != null) {
                        pojo.setTagdata(JSONObject.toJSONString(lstdata));
                    }
                }
                // 销售开票
                if (pojo.getSvccode().equals("D01M05")) {
                    if (pojo.getWarnfield() ==null || "".equals(pojo.getWarnfield())){
                        pojo.setWarnfield("Bus_Invoice.AimDate");
                    }
                    List<Map<String, Object>> lstdata = this.ciwarninguserMapper.getBusInvoList(pojo.getWarnfield(), pojo.getDiffnum(), tid);
                    if (lstdata != null) {
                        pojo.setTagdata(JSONObject.toJSONString(lstdata));
                    }
                }
                // 采购订单
                if (pojo.getSvccode().equals("D03M02")) {
                    if (pojo.getWarnfield() ==null || "".equals(pojo.getWarnfield())){
                        pojo.setWarnfield("Buy_OrderItem.PlanDate");
                    }
                    List<Map<String, Object>> lstdata = this.ciwarninguserMapper.getBuyOrderList(pojo.getWarnfield(), pojo.getDiffnum(), tid);
                    if (lstdata != null) {
                        pojo.setTagdata(JSONObject.toJSONString(lstdata));
                    }
                }
                // 销售开票
                if (pojo.getSvccode().equals("D03M05")) {
                    if (pojo.getWarnfield() ==null || "".equals(pojo.getWarnfield())){
                        pojo.setWarnfield("Buy_Invoice.AimDate");
                    }
                    List<Map<String, Object>> lstdata = this.ciwarninguserMapper.getBuyInvoList(pojo.getWarnfield(), pojo.getDiffnum(), tid);
                    if (lstdata != null) {
                        pojo.setTagdata(JSONObject.toJSONString(lstdata));
                    }
                }
            }
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }
}
