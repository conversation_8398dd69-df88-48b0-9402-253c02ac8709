package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiscmjustauthPojo;
import inks.system.domain.PiscmjustauthEntity;

import com.github.pagehelper.PageInfo;

/**
 * SCM第三方登录(Piscmjustauth)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-21 21:25:20
 */
public interface PiscmjustauthService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiscmjustauthPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PiscmjustauthPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param piscmjustauthPojo 实例对象
     * @return 实例对象
     */
    PiscmjustauthPojo insert(PiscmjustauthPojo piscmjustauthPojo);

    /**
     * 修改数据
     *
     * @param piscmjustauthpojo 实例对象
     * @return 实例对象
     */
    PiscmjustauthPojo update(PiscmjustauthPojo piscmjustauthpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

    int deleteByOpenid(String openid, String tid);
}
