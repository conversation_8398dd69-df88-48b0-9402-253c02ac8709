package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PifunctionreportsEntity;
import inks.system.domain.pojo.PifunctionreportsPojo;
import inks.system.mapper.PifunctionreportsMapper;
import inks.system.service.PifunctionreportsService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;
/**
 * 服务报表关系(Pifunctionreports)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-17 07:54:32
 */
@Service("pifunctionreportsService")
public class PifunctionreportsServiceImpl implements PifunctionreportsService {
    @Resource
    private PifunctionreportsMapper pifunctionreportsMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PifunctionreportsPojo getEntity(String key) {
        return this.pifunctionreportsMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PifunctionreportsPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PifunctionreportsPojo> lst = pifunctionreportsMapper.getPageList(queryParam);
            PageInfo<PifunctionreportsPojo> pageInfo = new PageInfo<PifunctionreportsPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param pifunctionreportsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PifunctionreportsPojo insert(PifunctionreportsPojo pifunctionreportsPojo) {
    //初始化NULL字段
     if(pifunctionreportsPojo.getFunctionid()==null) pifunctionreportsPojo.setFunctionid("");
     if(pifunctionreportsPojo.getFunctioncode()==null) pifunctionreportsPojo.setFunctioncode("");
     if(pifunctionreportsPojo.getFunctionname()==null) pifunctionreportsPojo.setFunctionname("");
     if(pifunctionreportsPojo.getReportid()==null) pifunctionreportsPojo.setReportid("");
     if(pifunctionreportsPojo.getReportcode()==null) pifunctionreportsPojo.setReportcode("");
     if(pifunctionreportsPojo.getReportname()==null) pifunctionreportsPojo.setReportname("");
     if(pifunctionreportsPojo.getRemark()==null) pifunctionreportsPojo.setRemark("");
     if(pifunctionreportsPojo.getCreateby()==null) pifunctionreportsPojo.setCreateby("");
     if(pifunctionreportsPojo.getCreatedate()==null) pifunctionreportsPojo.setCreatedate(new Date());
     if(pifunctionreportsPojo.getLister()==null) pifunctionreportsPojo.setLister("");
     if(pifunctionreportsPojo.getModifydate()==null) pifunctionreportsPojo.setModifydate(new Date());
     if(pifunctionreportsPojo.getTenantid()==null) pifunctionreportsPojo.setTenantid("");
     if(pifunctionreportsPojo.getRevision()==null) pifunctionreportsPojo.setRevision(0);
        PifunctionreportsEntity pifunctionreportsEntity = new PifunctionreportsEntity(); 
        BeanUtils.copyProperties(pifunctionreportsPojo,pifunctionreportsEntity);
        
          pifunctionreportsEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          pifunctionreportsEntity.setRevision(1);  //乐观锁
          this.pifunctionreportsMapper.insert(pifunctionreportsEntity);
        return this.getEntity(pifunctionreportsEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param pifunctionreportsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PifunctionreportsPojo update(PifunctionreportsPojo pifunctionreportsPojo) {
        PifunctionreportsEntity pifunctionreportsEntity = new PifunctionreportsEntity(); 
        BeanUtils.copyProperties(pifunctionreportsPojo,pifunctionreportsEntity);
        this.pifunctionreportsMapper.update(pifunctionreportsEntity);
        return this.getEntity(pifunctionreportsEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pifunctionreportsMapper.delete(key) ;
    }

    @Override
    public List<PifunctionreportsPojo> getListByFunction(String key){
        return this.pifunctionreportsMapper.getListByFunction(key);
    }
    
                                                                          
}
