package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiuserloginPojo;

import java.util.List;

/**
 * 用户登录表(Piuserlogin)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-08 14:36:11
 */
public interface PiuserloginService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiuserloginPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PiuserloginPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param piuserloginPojo 实例对象
     * @return 实例对象
     */
    PiuserloginPojo insert(PiuserloginPojo piuserloginPojo);

    /**
     * 修改数据
     *
     * @param piuserloginpojo 实例对象
     * @return 实例对象
     */
    PiuserloginPojo update(PiuserloginPojo piuserloginpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiuserloginPojo getEntityByUserid(String key);


    List<PiuserloginPojo> getListByUserid(String key, String tenantid);
}
