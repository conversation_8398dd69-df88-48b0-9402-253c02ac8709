package inks.system.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.system.domain.pojo.PifunctionwebnavPojo;
import inks.system.domain.PifunctionwebnavEntity;
import inks.system.mapper.PifunctionwebnavMapper;
import inks.system.service.PifunctionwebnavService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * web导航(Pifunctionwebnav)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-01 15:36:24
 */
@Service("pifunctionwebnavService")
public class PifunctionwebnavServiceImpl implements PifunctionwebnavService {
    @Resource
    private PifunctionwebnavMapper pifunctionwebnavMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PifunctionwebnavPojo getEntity(String key, String tid) {
        return this.pifunctionwebnavMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PifunctionwebnavPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PifunctionwebnavPojo> lst = pifunctionwebnavMapper.getPageList(queryParam);
            PageInfo<PifunctionwebnavPojo> pageInfo = new PageInfo<PifunctionwebnavPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param pifunctionwebnavPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PifunctionwebnavPojo insert(PifunctionwebnavPojo pifunctionwebnavPojo) {
    //初始化NULL字段
     if(pifunctionwebnavPojo.getFunctionid()==null) pifunctionwebnavPojo.setFunctionid("");
     if(pifunctionwebnavPojo.getFunctioncode()==null) pifunctionwebnavPojo.setFunctioncode("");
     if(pifunctionwebnavPojo.getFunctionname()==null) pifunctionwebnavPojo.setFunctionname("");
     if(pifunctionwebnavPojo.getNavid()==null) pifunctionwebnavPojo.setNavid("");
     if(pifunctionwebnavPojo.getNavcode()==null) pifunctionwebnavPojo.setNavcode("");
     if(pifunctionwebnavPojo.getNavname()==null) pifunctionwebnavPojo.setNavname("");
     if(pifunctionwebnavPojo.getRemark()==null) pifunctionwebnavPojo.setRemark("");
     if(pifunctionwebnavPojo.getCreateby()==null) pifunctionwebnavPojo.setCreateby("");
     if(pifunctionwebnavPojo.getCreatedate()==null) pifunctionwebnavPojo.setCreatedate(new Date());
     if(pifunctionwebnavPojo.getLister()==null) pifunctionwebnavPojo.setLister("");
     if(pifunctionwebnavPojo.getModifydate()==null) pifunctionwebnavPojo.setModifydate(new Date());
     if(pifunctionwebnavPojo.getTenantid()==null) pifunctionwebnavPojo.setTenantid("");
     if(pifunctionwebnavPojo.getRevision()==null) pifunctionwebnavPojo.setRevision(0);
        PifunctionwebnavEntity pifunctionwebnavEntity = new PifunctionwebnavEntity(); 
        BeanUtils.copyProperties(pifunctionwebnavPojo,pifunctionwebnavEntity);
          //生成雪花id
          pifunctionwebnavEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          pifunctionwebnavEntity.setRevision(1);  //乐观锁
          this.pifunctionwebnavMapper.insert(pifunctionwebnavEntity);
        return this.getEntity(pifunctionwebnavEntity.getId(),pifunctionwebnavEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param pifunctionwebnavPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PifunctionwebnavPojo update(PifunctionwebnavPojo pifunctionwebnavPojo) {
        PifunctionwebnavEntity pifunctionwebnavEntity = new PifunctionwebnavEntity(); 
        BeanUtils.copyProperties(pifunctionwebnavPojo,pifunctionwebnavEntity);
        this.pifunctionwebnavMapper.update(pifunctionwebnavEntity);
        return this.getEntity(pifunctionwebnavEntity.getId(),pifunctionwebnavEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.pifunctionwebnavMapper.delete(key,tid) ;
    }

    @Override
    public List<PifunctionwebnavPojo> getListByFunction(String key) {
        return this.pifunctionwebnavMapper.getListByFunction(key);
    }
}
