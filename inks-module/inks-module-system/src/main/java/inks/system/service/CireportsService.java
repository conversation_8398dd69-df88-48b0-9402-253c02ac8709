package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CireportsPojo;

import java.util.List;
import java.util.Map;

/**
 * 报表中心(Cireports)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-08 13:35:26
 */
public interface CireportsService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CireportsPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CireportsPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param cireportsPojo 实例对象
     * @return 实例对象
     */
    CireportsPojo insert(CireportsPojo cireportsPojo);

    /**
     * 修改数据
     *
     * @param cireportspojo 实例对象
     * @return 实例对象
     */
    CireportsPojo update(CireportsPojo cireportspojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

    /**
     * 通过功能号获得报表样式
     *
     * @param moduleCode 主键
     * @return 是否成功
     */
    List<CireportsPojo> getListByModuleCode(String moduleCode, String tid);

    List<CireportsPojo> pullDefault(String moduleCode,LoginUser loginUser);


    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CireportsPojo> getPageListAll(QueryParam queryParam);

    int copyReports(String tid, LoginUser loginUser);

    String initReports(LoginUser loginUser);

    String initPermissions(LoginUser loginUser);
}
