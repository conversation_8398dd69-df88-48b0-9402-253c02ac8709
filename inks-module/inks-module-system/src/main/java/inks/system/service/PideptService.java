package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DeptinfoPojo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PideptPojo;

import java.util.List;

/**
 * 用户组织架构(Pidept)表服务接口
 *
 * <AUTHOR>
 * @since 2022-03-28 11:10:59
 */
public interface PideptService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PideptPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PideptPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pideptPojo 实例对象
     * @return 实例对象
     */
    PideptPojo insert(PideptPojo pideptPojo);

    /**
     * 修改数据
     *
     * @param pideptpojo 实例对象
     * @return 实例对象
     */
    PideptPojo update(PideptPojo pideptpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);


    /**
     * 分页查询
     *
     * @param key 筛选条件
     * @return 查询结果
     */
    List<PideptPojo> getListByParentid(String key, String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param lst 主键
     * @return 实例对象
     */
    void getSubinfoAll(List<DeptinfoPojo> lst, String tid);
}
