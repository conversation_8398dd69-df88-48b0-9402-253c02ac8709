package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PimenuappPojo;

import java.util.List;

/**
 * APP导航(Pimenuapp)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-08 14:07:54
 */
public interface PimenuappService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PimenuappPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PimenuappPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pimenuappPojo 实例对象
     * @return 实例对象
     */
    PimenuappPojo insert(PimenuappPojo pimenuappPojo);

    /**
     * 修改数据
     *
     * @param pimenuapppojo 实例对象
     * @return 实例对象
     */
    PimenuappPojo update(PimenuappPojo pimenuapppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);


    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    List<PimenuappPojo> getListByPid(String key);

    List<PimenuappPojo> getListByNavids(List<String> navids);
}
