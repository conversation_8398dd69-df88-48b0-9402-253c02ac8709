package inks.system.service;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiweblnkcustPojo;
import inks.system.domain.CiweblnkcustEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 自定义webLnk(Ciweblnkcust)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-08 14:57:09
 */
public interface CiweblnkcustService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiweblnkcustPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<CiweblnkcustPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param ciweblnkcustPojo 实例对象
     * @return 实例对象
     */
    CiweblnkcustPojo insert(CiweblnkcustPojo ciweblnkcustPojo);

    /**
     * 修改数据
     *
     * @param ciweblnkcustpojo 实例对象
     * @return 实例对象
     */
    CiweblnkcustPojo update(CiweblnkcustPojo ciweblnkcustpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

    CiweblnkcustPojo getEntityBySelf(String userid, String tenantid);

    List<CiweblnkcustPojo> getListBySelf(String fncode, String userid, String tenantid);
}
