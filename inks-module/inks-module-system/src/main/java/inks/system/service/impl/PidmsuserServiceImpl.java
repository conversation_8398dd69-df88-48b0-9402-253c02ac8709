package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PidmsuserEntity;
import inks.system.domain.pojo.PidmsuserPojo;
import inks.system.mapper.PidmsuserMapper;
import inks.system.service.PidmsuserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * DMS用户(Pidmsuser)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-21 19:05:49
 */
@Service("pidmsuserService")
public class PidmsuserServiceImpl implements PidmsuserService {
    @Resource
    private PidmsuserMapper pidmsuserMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PidmsuserPojo getEntity(String key, String tid) {
        return this.pidmsuserMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PidmsuserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PidmsuserPojo> lst = pidmsuserMapper.getPageList(queryParam);
            PageInfo<PidmsuserPojo> pageInfo = new PageInfo<PidmsuserPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<PidmsuserPojo> getPageListByTen(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PidmsuserPojo> lst = pidmsuserMapper.getPageListByTen(queryParam);
            PageInfo<PidmsuserPojo> pageInfo = new PageInfo<PidmsuserPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param pidmsuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PidmsuserPojo insert(PidmsuserPojo pidmsuserPojo) {
        //初始化NULL字段
        if (pidmsuserPojo.getUsername() == null) pidmsuserPojo.setUsername("");
        if (pidmsuserPojo.getRealname() == null) pidmsuserPojo.setRealname("");
        if (pidmsuserPojo.getNickname() == null) pidmsuserPojo.setNickname("");
        if (pidmsuserPojo.getUserpassword() == null) pidmsuserPojo.setUserpassword("");
        if (pidmsuserPojo.getMobile() == null) pidmsuserPojo.setMobile("");
        if (pidmsuserPojo.getEmail() == null) pidmsuserPojo.setEmail("");
        if (pidmsuserPojo.getSex() == null) pidmsuserPojo.setSex(0);
        if (pidmsuserPojo.getLangcode() == null) pidmsuserPojo.setLangcode("");
        if (pidmsuserPojo.getAvatar() == null) pidmsuserPojo.setAvatar("");
        if (pidmsuserPojo.getUsertype() == null) pidmsuserPojo.setUsertype(0);
        if (pidmsuserPojo.getIsadmin() == null) pidmsuserPojo.setIsadmin(0);
        if (pidmsuserPojo.getDeptid() == null) pidmsuserPojo.setDeptid("");
        if (pidmsuserPojo.getDeptcode() == null) pidmsuserPojo.setDeptcode("");
        if (pidmsuserPojo.getDeptname() == null) pidmsuserPojo.setDeptname("");
        if (pidmsuserPojo.getIsdeptadmin() == null) pidmsuserPojo.setIsdeptadmin(0);
        if (pidmsuserPojo.getDeptrownum() == null) pidmsuserPojo.setDeptrownum(0);
        if (pidmsuserPojo.getRownum() == null) pidmsuserPojo.setRownum(0);
        if (pidmsuserPojo.getUserstatus() == null) pidmsuserPojo.setUserstatus(0);
        if (pidmsuserPojo.getUsercode() == null) pidmsuserPojo.setUsercode("");
        if (pidmsuserPojo.getGroupids() == null) pidmsuserPojo.setGroupids("");
        if (pidmsuserPojo.getGroupnames() == null) pidmsuserPojo.setGroupnames("");
        if (pidmsuserPojo.getDmsfunctids() == null) pidmsuserPojo.setDmsfunctids("");
        if (pidmsuserPojo.getDmsfunctnames() == null) pidmsuserPojo.setDmsfunctnames("");
        if (pidmsuserPojo.getRemark() == null) pidmsuserPojo.setRemark("");
        if (pidmsuserPojo.getDatalabel() == null) pidmsuserPojo.setDatalabel("");
        if (pidmsuserPojo.getCreateby() == null) pidmsuserPojo.setCreateby("");
        if (pidmsuserPojo.getCreatebyid() == null) pidmsuserPojo.setCreatebyid("");
        if (pidmsuserPojo.getCreatedate() == null) pidmsuserPojo.setCreatedate(new Date());
        if (pidmsuserPojo.getLister() == null) pidmsuserPojo.setLister("");
        if (pidmsuserPojo.getListerid() == null) pidmsuserPojo.setListerid("");
        if (pidmsuserPojo.getModifydate() == null) pidmsuserPojo.setModifydate(new Date());
        if (pidmsuserPojo.getTenantid() == null) pidmsuserPojo.setTenantid("");
        if (pidmsuserPojo.getTenantname() == null) pidmsuserPojo.setTenantname("");
        if (pidmsuserPojo.getRevision() == null) pidmsuserPojo.setRevision(0);
        PidmsuserEntity pidmsuserEntity = new PidmsuserEntity();
        BeanUtils.copyProperties(pidmsuserPojo, pidmsuserEntity);
        //生成雪花id
        pidmsuserEntity.setUserid(inksSnowflake.getSnowflake().nextIdStr());
        pidmsuserEntity.setRevision(1);  //乐观锁
        this.pidmsuserMapper.insert(pidmsuserEntity);
        return this.getEntity(pidmsuserEntity.getUserid(), pidmsuserEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param pidmsuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PidmsuserPojo update(PidmsuserPojo pidmsuserPojo) {
        PidmsuserEntity pidmsuserEntity = new PidmsuserEntity();
        BeanUtils.copyProperties(pidmsuserPojo, pidmsuserEntity);
        this.pidmsuserMapper.update(pidmsuserEntity);
        return this.getEntity(pidmsuserEntity.getUserid(), pidmsuserEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        // 还得删除关联表 PiTenantDmsUser
        this.pidmsuserMapper.deletePiTenantDmsUser(key, tid);
        return this.pidmsuserMapper.delete(key, tid);
    }

    @Override
    public PidmsuserPojo getEntityByUserName(String username) {
        return this.pidmsuserMapper.getEntityByUserName(username);
    }

    @Override
    public PidmsuserPojo getEntityByOpenid(String openid, String tenantid) {
        return this.pidmsuserMapper.getEntityByOpenid(openid, tenantid);
    }

    @Override
    public List<PidmsuserPojo> getListByOpenid(String openid) {
        return this.pidmsuserMapper.getListByOpenid(openid);
    }

    @Override
    public PidmsuserPojo getEntityByUserid(String userid, String tid) {
        return this.pidmsuserMapper.getEntityByUserid(userid, tid);
    }
}
