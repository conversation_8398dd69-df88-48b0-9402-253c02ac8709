package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CifninvoiceEntity;
import inks.system.domain.CifninvoiceitemEntity;
import inks.system.domain.pojo.CifninvoicePojo;
import inks.system.domain.pojo.CifninvoiceitemPojo;
import inks.system.domain.pojo.CifninvoiceitemdetailPojo;
import inks.system.domain.pojo.CifnorderitemPojo;
import inks.system.mapper.CifninvoiceMapper;
import inks.system.mapper.CifninvoiceitemMapper;
import inks.system.service.CifninvoiceService;
import inks.system.service.CifninvoiceitemService;
import inks.system.service.CifnorderitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 开票管理(Cifninvoice)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-19 20:01:42
 */
@Service("cifninvoiceService")
public class CifninvoiceServiceImpl implements CifninvoiceService {
    @Resource
    private CifninvoiceMapper cifninvoiceMapper;

    @Resource
    private CifninvoiceitemMapper cifninvoiceitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private CifninvoiceitemService cifninvoiceitemService;

    /**
     * 服务对象Item
     */
    @Resource
    private CifnorderitemService cifnorderitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CifninvoicePojo getEntity(String key, String tid) {
        return this.cifninvoiceMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CifninvoiceitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CifninvoiceitemdetailPojo> lst = cifninvoiceMapper.getPageList(queryParam);
            PageInfo<CifninvoiceitemdetailPojo> pageInfo = new PageInfo<CifninvoiceitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CifninvoicePojo getBillEntity(String key, String tid) {
        try {
            //读取主表
            CifninvoicePojo cifninvoicePojo = this.cifninvoiceMapper.getEntity(key, tid);
            //读取子表
            cifninvoicePojo.setItem(cifninvoiceitemMapper.getList(cifninvoicePojo.getId(), cifninvoicePojo.getTenantid()));
            return cifninvoicePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CifninvoicePojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CifninvoicePojo> lst = cifninvoiceMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(cifninvoiceitemMapper.getList(lst.get(i).getId(), lst.get(i).getTenantid()));
            }
            PageInfo<CifninvoicePojo> pageInfo = new PageInfo<CifninvoicePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CifninvoicePojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CifninvoicePojo> lst = cifninvoiceMapper.getPageTh(queryParam);
            PageInfo<CifninvoicePojo> pageInfo = new PageInfo<CifninvoicePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param cifninvoicePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public CifninvoicePojo insert(CifninvoicePojo cifninvoicePojo) {
//初始化NULL字段
        if (cifninvoicePojo.getRefno() == null) cifninvoicePojo.setRefno("");
        if (cifninvoicePojo.getBilltype() == null) cifninvoicePojo.setBilltype("");
        if (cifninvoicePojo.getBilltitle() == null) cifninvoicePojo.setBilltitle("");
        if (cifninvoicePojo.getBilldate() == null) cifninvoicePojo.setBilldate(new Date());
        if (cifninvoicePojo.getUserid() == null) cifninvoicePojo.setUserid("");
        if (cifninvoicePojo.getUsername() == null) cifninvoicePojo.setUsername("");
        if (cifninvoicePojo.getRealname() == null) cifninvoicePojo.setRealname("");
        if (cifninvoicePojo.getTenantid() == null) cifninvoicePojo.setTenantid("");
        if (cifninvoicePojo.getTenantcode() == null) cifninvoicePojo.setTenantcode("");
        if (cifninvoicePojo.getTenantname() == null) cifninvoicePojo.setTenantname("");
        if (cifninvoicePojo.getCompany() == null) cifninvoicePojo.setCompany("");
        if (cifninvoicePojo.getEmail() == null) cifninvoicePojo.setEmail("");
        if (cifninvoicePojo.getBilltaxamount() == null) cifninvoicePojo.setBilltaxamount(0D);
        if (cifninvoicePojo.getInvotitle() == null) cifninvoicePojo.setInvotitle("");
        if (cifninvoicePojo.getTitletype() == null) cifninvoicePojo.setTitletype("");
        if (cifninvoicePojo.getInvotype() == null) cifninvoicePojo.setInvotype("");
        if (cifninvoicePojo.getCreditnum() == null) cifninvoicePojo.setCreditnum("");
        if (cifninvoicePojo.getBankname() == null) cifninvoicePojo.setBankname("");
        if (cifninvoicePojo.getBandaccount() == null) cifninvoicePojo.setBandaccount("");
        if (cifninvoicePojo.getBusaddress() == null) cifninvoicePojo.setBusaddress("");
        if (cifninvoicePojo.getBustel() == null) cifninvoicePojo.setBustel("");
        if (cifninvoicePojo.getSummary() == null) cifninvoicePojo.setSummary("");
        if (cifninvoicePojo.getCreateby() == null) cifninvoicePojo.setCreateby("");
        if (cifninvoicePojo.getCreatedate() == null) cifninvoicePojo.setCreatedate(new Date());
        if (cifninvoicePojo.getLister() == null) cifninvoicePojo.setLister("");
        if (cifninvoicePojo.getModifydate() == null) cifninvoicePojo.setModifydate(new Date());
        if (cifninvoicePojo.getStatecode() == null) cifninvoicePojo.setStatecode("");
        if (cifninvoicePojo.getStatedate() == null) cifninvoicePojo.setStatedate(new Date());
        if (cifninvoicePojo.getDisannulmark() == null) cifninvoicePojo.setDisannulmark(0);
        if (cifninvoicePojo.getDisannullister() == null) cifninvoicePojo.setDisannullister("");
        if (cifninvoicePojo.getDisannuldate() == null) cifninvoicePojo.setDisannuldate(new Date());
        if (cifninvoicePojo.getInvourl() == null) cifninvoicePojo.setInvourl("");
        if (cifninvoicePojo.getInvolistr() == null) cifninvoicePojo.setInvolistr("");
        if (cifninvoicePojo.getInvodate() == null) cifninvoicePojo.setInvodate(new Date());
        if (cifninvoicePojo.getRevision() == null) cifninvoicePojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        CifninvoiceEntity cifninvoiceEntity = new CifninvoiceEntity();
        BeanUtils.copyProperties(cifninvoicePojo, cifninvoiceEntity);
        //设置id和新建日期
        cifninvoiceEntity.setId(id);
        cifninvoiceEntity.setRevision(1);  //乐观锁
        //插入主表
        this.cifninvoiceMapper.insert(cifninvoiceEntity);
        //Item子表处理
        List<CifninvoiceitemPojo> lst = cifninvoicePojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                //初始化item的NULL
                CifninvoiceitemPojo itemPojo = this.cifninvoiceitemService.clearNull(lst.get(i));
                CifninvoiceitemEntity cifninvoiceitemEntity = new CifninvoiceitemEntity();
                BeanUtils.copyProperties(itemPojo, cifninvoiceitemEntity);
                //设置id和Pid
                cifninvoiceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                cifninvoiceitemEntity.setPid(id);
                cifninvoiceitemEntity.setTenantid(cifninvoicePojo.getTenantid());
                cifninvoiceitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.cifninvoiceitemMapper.insert(cifninvoiceitemEntity);
                // 同步更新订单
                if (cifninvoiceitemEntity.getOrderitemid() != null) {
                    CifnorderitemPojo cifnorderitemPojo = new CifnorderitemPojo();
                    cifnorderitemPojo.setId(cifninvoiceitemEntity.getOrderitemid());
                    cifnorderitemPojo.setInvofinish(1);
                    this.cifnorderitemService.update(cifnorderitemPojo);
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(cifninvoiceEntity.getId(), cifninvoiceEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param cifninvoicePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public CifninvoicePojo update(CifninvoicePojo cifninvoicePojo) {
        //主表更改
        CifninvoiceEntity cifninvoiceEntity = new CifninvoiceEntity();
        BeanUtils.copyProperties(cifninvoicePojo, cifninvoiceEntity);
        this.cifninvoiceMapper.update(cifninvoiceEntity);
        //Item子表处理
        List<CifninvoiceitemPojo> lst = cifninvoicePojo.getItem();
        //获取被删除的Item
        List<String> lstDelIds = cifninvoiceMapper.getDelItemIds(cifninvoicePojo);
        if (lstDelIds != null) {
            //循环每个删除item子表
            for (int i = 0; i < lstDelIds.size(); i++) {
                this.cifninvoiceitemMapper.delete(lstDelIds.get(i), cifninvoiceEntity.getTenantid());
            }
        }
        if (lst != null) {
            //循环每个item子表
            for (int i = 0; i < lst.size(); i++) {
                CifninvoiceitemEntity cifninvoiceitemEntity = new CifninvoiceitemEntity();
                if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                    //初始化item的NULL
                    CifninvoiceitemPojo itemPojo = this.cifninvoiceitemService.clearNull(lst.get(i));
                    BeanUtils.copyProperties(itemPojo, cifninvoiceitemEntity);
                    //设置id和Pid
                    cifninvoiceitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    cifninvoiceitemEntity.setPid(cifninvoiceEntity.getId());  // 主表 id
                    cifninvoiceitemEntity.setTenantid(cifninvoicePojo.getTenantid());   // 租户id
                    cifninvoiceitemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.cifninvoiceitemMapper.insert(cifninvoiceitemEntity);
                    // 同步更新订单
                    if (cifninvoiceitemEntity.getOrderitemid() != null) {
                        CifnorderitemPojo cifnorderitemPojo = new CifnorderitemPojo();
                        cifnorderitemPojo.setId(cifninvoiceitemEntity.getOrderitemid());
                        cifnorderitemPojo.setInvofinish(1);
                        this.cifnorderitemService.update(cifnorderitemPojo);
                    }
                } else {
                    BeanUtils.copyProperties(lst.get(i), cifninvoiceitemEntity);
                    cifninvoiceitemEntity.setTenantid(cifninvoicePojo.getTenantid());
                    this.cifninvoiceitemMapper.update(cifninvoiceitemEntity);
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(cifninvoiceEntity.getId(), cifninvoiceEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key, String tid) {
        CifninvoicePojo cifninvoicePojo = this.getBillEntity(key, tid);
        //Item子表处理
        List<CifninvoiceitemPojo> lst = cifninvoicePojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.cifninvoiceitemMapper.delete(lst.get(i).getId(), tid);
            }
        }
        return this.cifninvoiceMapper.delete(key, tid);
    }



}
