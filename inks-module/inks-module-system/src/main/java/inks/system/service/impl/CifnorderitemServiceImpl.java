package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.CifnorderitemEntity;
import inks.system.domain.pojo.CifnorderitemPojo;
import inks.system.mapper.CifnorderitemMapper;
import inks.system.service.CifnorderitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;
/**
 * 订单明细(Cifnorderitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-23 20:20:03
 */
@Service("cifnorderitemService")
public class CifnorderitemServiceImpl implements CifnorderitemService {
    @Resource
    private CifnorderitemMapper cifnorderitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public CifnorderitemPojo getEntity(String key) {
        return this.cifnorderitemMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<CifnorderitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<CifnorderitemPojo> lst = cifnorderitemMapper.getPageList(queryParam);
            PageInfo<CifnorderitemPojo> pageInfo = new PageInfo<CifnorderitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<CifnorderitemPojo> getList(String Pid) {
        try {
            List<CifnorderitemPojo> lst = cifnorderitemMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param cifnorderitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CifnorderitemPojo insert(CifnorderitemPojo cifnorderitemPojo) {
        //初始化item的NULL
        CifnorderitemPojo itempojo =this.clearNull(cifnorderitemPojo);
        CifnorderitemEntity cifnorderitemEntity = new CifnorderitemEntity(); 
        BeanUtils.copyProperties(itempojo,cifnorderitemEntity);
        
          cifnorderitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          cifnorderitemEntity.setRevision(1);  //乐观锁      
          this.cifnorderitemMapper.insert(cifnorderitemEntity);
        return this.getEntity(cifnorderitemEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param cifnorderitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public CifnorderitemPojo update(CifnorderitemPojo cifnorderitemPojo) {
        CifnorderitemEntity cifnorderitemEntity = new CifnorderitemEntity(); 
        BeanUtils.copyProperties(cifnorderitemPojo,cifnorderitemEntity);
        this.cifnorderitemMapper.update(cifnorderitemEntity);
        return this.getEntity(cifnorderitemEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.cifnorderitemMapper.delete(key) ;
    }
    
     /**
     * 修改数据
     *
     * @param cifnorderitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public CifnorderitemPojo clearNull(CifnorderitemPojo cifnorderitemPojo){
     //初始化NULL字段
     if(cifnorderitemPojo.getPid()==null) cifnorderitemPojo.setPid("");
     if(cifnorderitemPojo.getPricepolicyid()==null) cifnorderitemPojo.setPricepolicyid("");
     if(cifnorderitemPojo.getFunctionid()==null) cifnorderitemPojo.setFunctionid("");
     if(cifnorderitemPojo.getCyclecode()==null) cifnorderitemPojo.setCyclecode("");
     if(cifnorderitemPojo.getContainer()==null) cifnorderitemPojo.setContainer(0);
     if(cifnorderitemPojo.getQuantity()==null) cifnorderitemPojo.setQuantity(0D);
     if(cifnorderitemPojo.getTaxprice()==null) cifnorderitemPojo.setTaxprice(0D);
     if(cifnorderitemPojo.getTaxamount()==null) cifnorderitemPojo.setTaxamount(0D);
     if(cifnorderitemPojo.getRownum()==null) cifnorderitemPojo.setRownum(0);
     if(cifnorderitemPojo.getRemark()==null) cifnorderitemPojo.setRemark("");
     if(cifnorderitemPojo.getInvofinish()==null) cifnorderitemPojo.setInvofinish(0);
     if(cifnorderitemPojo.getInvoclosed()==null) cifnorderitemPojo.setInvoclosed(0);
     if(cifnorderitemPojo.getRevision()==null) cifnorderitemPojo.setRevision(0);
     return cifnorderitemPojo;
     }
}
