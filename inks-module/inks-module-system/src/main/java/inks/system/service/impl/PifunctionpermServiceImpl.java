package inks.system.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.system.domain.PifunctionpermEntity;
import inks.system.domain.pojo.PifunctionpermPojo;
import inks.system.mapper.PifunctionpermMapper;
import inks.system.service.PifunctionpermService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.UUID;
/**
 * 服务权限关系(Pifunctionperm)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-11 17:34:56
 */
@Service("pifunctionpermService")
public class PifunctionpermServiceImpl implements PifunctionpermService {
    @Resource
    private PifunctionpermMapper pifunctionpermMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public PifunctionpermPojo getEntity(String key, String tid) {
        return this.pifunctionpermMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<PifunctionpermPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<PifunctionpermPojo> lst = pifunctionpermMapper.getPageList(queryParam);
            PageInfo<PifunctionpermPojo> pageInfo = new PageInfo<PifunctionpermPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param pifunctionpermPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PifunctionpermPojo insert(PifunctionpermPojo pifunctionpermPojo) {
    //初始化NULL字段
     if(pifunctionpermPojo.getFunctionid()==null) pifunctionpermPojo.setFunctionid("");
     if(pifunctionpermPojo.getFunctioncode()==null) pifunctionpermPojo.setFunctioncode("");
     if(pifunctionpermPojo.getFunctionname()==null) pifunctionpermPojo.setFunctionname("");
     if(pifunctionpermPojo.getPermid()==null) pifunctionpermPojo.setPermid("");
     if(pifunctionpermPojo.getPermcode()==null) pifunctionpermPojo.setPermcode("");
     if(pifunctionpermPojo.getPermname()==null) pifunctionpermPojo.setPermname("");
     if(pifunctionpermPojo.getLister()==null) pifunctionpermPojo.setLister("");
     if(pifunctionpermPojo.getCreatedate()==null) pifunctionpermPojo.setCreatedate(new Date());
     if(pifunctionpermPojo.getModifydate()==null) pifunctionpermPojo.setModifydate(new Date());
     if(pifunctionpermPojo.getRemark()==null) pifunctionpermPojo.setRemark("");
     if(pifunctionpermPojo.getCreateby()==null) pifunctionpermPojo.setCreateby("");
     if(pifunctionpermPojo.getTenantid()==null) pifunctionpermPojo.setTenantid("");
     if(pifunctionpermPojo.getRevision()==null) pifunctionpermPojo.setRevision(0);
        PifunctionpermEntity pifunctionpermEntity = new PifunctionpermEntity(); 
        BeanUtils.copyProperties(pifunctionpermPojo,pifunctionpermEntity);
        
          pifunctionpermEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          pifunctionpermEntity.setRevision(1);  //乐观锁
          this.pifunctionpermMapper.insert(pifunctionpermEntity);
        return this.getEntity(pifunctionpermEntity.getId(),pifunctionpermEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param pifunctionpermPojo 实例对象
     * @return 实例对象
     */
    @Override
    public PifunctionpermPojo update(PifunctionpermPojo pifunctionpermPojo) {
        PifunctionpermEntity pifunctionpermEntity = new PifunctionpermEntity(); 
        BeanUtils.copyProperties(pifunctionpermPojo,pifunctionpermEntity);
        this.pifunctionpermMapper.update(pifunctionpermEntity);
        return this.getEntity(pifunctionpermEntity.getId(),pifunctionpermEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.pifunctionpermMapper.delete(key,tid) ;
    }


    @Override
    public List<PifunctionpermPojo> getListByFunction(String key){
        return this.pifunctionpermMapper.getListByFunction(key);
    }

    @Override
    public HashSet<String> getFunctionPermsByFunctionCode(String functioncode, String tid) {
        return this.pifunctionpermMapper.getFunctionPermsByFunctionCode(functioncode,tid);
    }
}
