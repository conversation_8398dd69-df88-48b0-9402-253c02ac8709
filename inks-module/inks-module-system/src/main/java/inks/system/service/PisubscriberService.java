package inks.system.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.*;

import java.util.List;

/**
 * 订阅信息表(Pisubscriber)表服务接口
 *
 * <AUTHOR>
 * @since 2021-11-10 14:17:25
 */
public interface PisubscriberService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PisubscriberPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PisubscriberPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param pisubscriberPojo 实例对象
     * @return 实例对象
     */
    PisubscriberPojo insert(PisubscriberPojo pisubscriberPojo);

    /**
     * 修改数据
     *
     * @param pisubscriberpojo 实例对象
     * @return 实例对象
     */
    PisubscriberPojo update(PisubscriberPojo pisubscriberpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<PisubscriberPojo> getPageListByTenant(QueryParam queryParam);

    PisubscriberPojo getEntityByFunction(String key, String tid);

    List<PimenuwebPojo> getMenuWebListByTenant(String key,String fncode);

    List<PiweblnkPojo> getWebLnkListByTenant(String key, String fncode);

    List<PimenufrmPojo> getMenuFrmListByTenant(String key, String fncode);

    List<PiwebnavPojo> getWebNavListByTenant(String tenantid, String fncode);

    List<PimenuappPojo> getMenuAppListByTenant(String key);

    List<PipermcodePojo> getPermAllByTenant(String key);

    List<CiconfigPojo> getTenConfigByTenant(String key);

    List<PirolePojo> getDefRoleByTenant(String key);

    List<CibigdataPojo> getBigDataByTenant(String key);

    List<CiwarningPojo> getWarnByTenant(String key);

    List<CidashboardPojo> getDashByTenant(String key);

    List<PisubscriberPojo> createByOrder(CifnorderPojo cifnorderPojo);

    List<CireportsPojo> getReportsByTenant(String key);

}
