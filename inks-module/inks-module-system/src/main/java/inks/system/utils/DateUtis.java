package inks.system.utils;



import java.util.Calendar;
import java.util.Date;

public class DateUtis {
    public static Date getCycleDate(String code){
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);//设置起时间
        String[] value = new String[2];
        for (int i=0;i<code.length();i++){
            value[i]=code.charAt(i)+"";
            System.out.println(value[i]);
        }
        if(value[0].equals("Y")){
            cal.add(Calendar.YEAR,Integer.valueOf(value[1]));
        }
        if(value[0].equals("M")){
            cal.add(Calendar.MONTH, Integer.valueOf(value[1]));
        }
        if(value[0].equals("W")){
            cal.add(Calendar.DATE, Integer.valueOf(value[1])*7);
        }
        return cal.getTime();
    }
}
