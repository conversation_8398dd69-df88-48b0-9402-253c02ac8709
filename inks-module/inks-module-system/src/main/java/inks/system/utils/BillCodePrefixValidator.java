package inks.system.utils;

import inks.system.domain.pojo.CibillcodePojo;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 单据编码格式校验方法
 *
 * 功能说明：
 * 1. prefix1允许自定义值，其他前缀必须为指定格式：yyyy/yy/MM/DD/[00]/[000]/[0000]/[00000]
 * 2. 后缀允许空字符串，但非空时必须为"-"或"/"
 * 3. 最后一个有值的前缀不允许设置后缀（即使为空字符串）
 * 4. 校验前缀必须连续填写，不能有跳开的空值
 * 5. 最后一个有值的前缀必须是[00]、[0000]、[00000]或[000000]格式
 * 6. 根据counttype校验必须包含对应的前缀：
 *    - year类型：必须包含yy或yyyy
 *    - month类型：必须包含MM
 *    - day类型：必须包含DD
 */
public class BillCodePrefixValidator {

    // 定义允许的前缀格式（不包含prefix1）
    private static final List<String> ALLOWED_PREFIXES = Arrays.asList(
            "yyyy", "yy", "MM", "dd",
            "[00]", "[000]", "[0000]", "[00000]"
    );

    // 定义允许的非空后缀格式
    private static final List<String> ALLOWED_SUFFIXES = Arrays.asList("-", "/");

    public static void main(String[] args) {
        // 测试用例1：允许空后缀
        CibillcodePojo pojo1 = new CibillcodePojo();
        pojo1.setCounttype("year");
        pojo1.setPrefix1("TEST");
        pojo1.setSuffix1("");  // 空字符串允许
        pojo1.setPrefix2("YYYY");
        pojo1.setSuffix2("/");
        pojo1.setPrefix3("[0000]");
        testValidate(pojo1, "测试1-允许空后缀");

        // 测试用例2：最后一个前缀不允许后缀（即使为空）
        CibillcodePojo pojo2 = new CibillcodePojo();
        pojo2.setCounttype("month");
        pojo2.setPrefix1("MM");
        pojo2.setPrefix2("[0000]");
        pojo2.setSuffix2("");  // 最后一个前缀不允许任何后缀
        testValidate(pojo2, "测试2-最后一个前缀不允许空后缀");

        // 测试用例3：非法非空后缀
        CibillcodePojo pojo3 = new CibillcodePojo();
        pojo3.setCounttype("day");
        pojo3.setPrefix1("DD");
        pojo3.setSuffix1("_");  // 非空且非法
        pojo3.setPrefix2("[0000]");
        testValidate(pojo3, "测试3-非法非空后缀");

        // 测试用例4：YY不支持 yy支持
        CibillcodePojo pojo4 = new CibillcodePojo();
        pojo4.setCounttype("month");
        pojo4.setPrefix1("WW");
        pojo4.setPrefix2("yy");
        pojo4.setPrefix3("MM");
        pojo4.setPrefix4("[0000]");
        testValidate(pojo4, "测试3-非法非空后缀");
    }

    public static void testValidate(CibillcodePojo pojo, String testCase) {
        System.out.println("\n=== " + testCase + " ===");
        try {
            validatePrefix(pojo);
            System.out.println("测试通过");
        } catch (IllegalArgumentException e) {
            System.out.println("测试失败: " + e.getMessage());
        }
    }

    public static void validatePrefix(CibillcodePojo cibillcodePojo) {
        if (cibillcodePojo == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        // 获取所有前缀和后缀
        String[] prefixArray = {
                cibillcodePojo.getPrefix1(),
                cibillcodePojo.getPrefix2(),
                cibillcodePojo.getPrefix3(),
                cibillcodePojo.getPrefix4(),
                cibillcodePojo.getPrefix5()
        };
        String[] suffixArray = {
                cibillcodePojo.getSuffix1(),
                cibillcodePojo.getSuffix2(),
                cibillcodePojo.getSuffix3(),
                cibillcodePojo.getSuffix4(),
                cibillcodePojo.getSuffix5()
        };

        // 检查前缀连续性（从prefix2开始检查）
        boolean hasEmptyInMiddle = false;
        boolean foundFirstEmpty = false;
        for (int i = 1; i < prefixArray.length; i++) {
            if (StringUtils.isBlank(prefixArray[i])) {
                if (!foundFirstEmpty) foundFirstEmpty = true;
            } else {
                if (foundFirstEmpty) hasEmptyInMiddle = true;
                // 校验非prefix1的前缀格式
                if (!ALLOWED_PREFIXES.contains(prefixArray[i])) {
                    throw new IllegalArgumentException("前缀"+(i+1)+" '"+prefixArray[i]+"' 不是允许的格式");
                }
            }
        }

        if (hasEmptyInMiddle) {
            throw new IllegalArgumentException("前缀值不能有跳开的空值，必须连续填写");
        }

        // 获取所有非空前缀
        List<String> prefixes = Arrays.stream(prefixArray)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (prefixes.isEmpty()) {
            throw new IllegalArgumentException("至少需要设置一个前缀");
        }

        // 校验最后一个前缀格式
        if (prefixes.size() > 1) {
            String lastPrefix = prefixes.get(prefixes.size()-1);
            if (!lastPrefix.matches("\\[0{2,6}]")) {
                throw new IllegalArgumentException("最后一个有值的前缀必须是[00]、[0000]等格式");
            }
        }

        // 校验后缀规则（关键修改点）
        for (int i = 0; i < prefixes.size(); i++) {
            String suffix = suffixArray[i];
            if (i < prefixes.size() - 1) { // 非最后一个前缀
                if (StringUtils.isNotBlank(suffix) &&
                        !ALLOWED_SUFFIXES.contains(suffix)) {
                    throw new IllegalArgumentException("后缀"+(i+1)+"必须是'-'、'/'或空");
                }
            } else { // 最后一个前缀
                if (StringUtils.isNotBlank(suffix)) {
                    throw new IllegalArgumentException("最后一个前缀不能设置后缀（即使是空字符串）");
                }
            }
        }

        // 校验类型规则
        String countType = cibillcodePojo.getCounttype();
        if ("year".equalsIgnoreCase(countType) &&
                !containsAny(prefixes, "yy", "yyyy")) {
            throw new IllegalArgumentException("年类型必须包含yy或yyyy");
        }
        if ("month".equalsIgnoreCase(countType) &&
                !containsAny(prefixes, "MM")) {
            throw new IllegalArgumentException("月类型必须包含MM");
        }
        if ("day".equalsIgnoreCase(countType) &&
                !containsAny(prefixes, "dd")) {
            throw new IllegalArgumentException("日类型必须包含dd");
        }
    }

    private static boolean containsAny(List<String> list, String... keywords) {
        for (String item : list) {
            for (String keyword : keywords) {
                if (keyword.equals(item)) {
                    return true;
                }
            }
        }
        return false;
    }
}
