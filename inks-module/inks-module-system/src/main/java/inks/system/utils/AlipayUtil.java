package inks.system.utils;
import com.alibaba.fastjson.JSON;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayTradePagePayRequest;
import inks.common.core.domain.AlipayBean;


/* 支付宝 */
public class AlipayUtil {
    public String connect(AlipayBean alipayBean,
                          String gatewayUrl,
                          String app_id,
                          String merchant_private_key,
                          String charset,
                          String alipay_public_key,
                          String sign_type,
                          String return_url,
                          String notify_url) throws AlipayApiException {
        //1、获得初始化的AlipayClient
        AlipayClient alipayClient = new DefaultAlipayClient(
                gatewayUrl,//支付宝网关
                app_id,//appid
                merchant_private_key,//商户私钥
                "json",
                charset,//字符编码格式
                alipay_public_key,//支付宝公钥
                sign_type//签名方式
        );
        //2、设置请求参数
        AlipayTradePagePayRequest alipayRequest = new AlipayTradePagePayRequest();
        //页面跳转同步通知页面路径
        alipayRequest.setReturnUrl(return_url);
        // 服务器异步通知页面路径
        alipayRequest.setNotifyUrl(notify_url);
        //封装参数
        alipayRequest.setBizContent(JSON.toJSONString(alipayBean));
        //3、请求支付宝进行付款，并获取支付结果
        String result = alipayClient.pageExecute(alipayRequest).getBody();
        //返回付款信息
        return  result;
    }

}
