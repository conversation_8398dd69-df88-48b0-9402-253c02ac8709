package inks.system.utils;

import inks.common.core.utils.AESUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 弱密码检查器
 * <AUTHOR>
 * @time 2025/3/4 12:51
 */
public class WeakPasswordChecker {

    //public static void main(String[] args) {
    //    long startTime = System.currentTimeMillis();
    //
    //    testPassword("123456");
    //    testPassword("9876aa");
    //    testPassword("12138");
    //    testPassword("asdfgh");
    //    testPassword("demo");
    //    testPassword("zaaaaa1");
    //    testPassword("asd@13579");
    //    testPassword("Aa@13579!");
    //    testPassword("admin");
    //    testPassword("strongPassw0rd!");
    //
    //    long endTime = System.currentTimeMillis();
    //    System.out.println("总耗时: " + (endTime - startTime) + " 毫秒");
    //}

    private static final Set<String> WEAK_PASSWORDS = ConcurrentHashMap.newKeySet();
    private static final Logger logger = LoggerFactory.getLogger(WeakPasswordChecker.class);
    // 是否已初始化
    private static volatile boolean initialized = false;

    private static void ensureInitialized() {
        if (!initialized) {
            synchronized (WeakPasswordChecker.class) {
                if (!initialized) {
                    loadPasswordFile();
                    initialized = true;
                }
            }
        }
    }

    private static void loadPasswordFile() {
        try (InputStream is = WeakPasswordChecker.class.getClassLoader()
                .getResourceAsStream("security/xato-net-10-million-passwords-1000000.txt")) {
            if (is == null) {
                logger.error("找不到弱密码库文件 security/xato-net-10-million-passwords-1000000.txt");
                return;
            }
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    line = line.trim();
                    if (!line.isEmpty() && !line.startsWith("#")) {
                        WEAK_PASSWORDS.add(line);
                    }
                }
            }
            logger.info("弱密码库加载完成，有效密码数：{}", WEAK_PASSWORDS.size());
        } catch (Exception e) {
            logger.error("加载弱密码库失败", e);
        }
    }

    public static boolean isWeakPassword(String encryptedPassword) {
        Objects.requireNonNull(encryptedPassword, "加密密码参数不能为null");
        ensureInitialized();
        try {
            String decrypted = AESUtil.Decrypt(encryptedPassword);
            return WEAK_PASSWORDS.contains(decrypted);
        } catch (Exception e) {
            throw new RuntimeException("解密失败", e);
        }
    }


    private static void testPassword(String password) {
        try {
            String encrypted = AESUtil.Encrypt(password);
            System.out.printf("测试密码: %-15s -> 弱密码: %s%n",
                    password,
                    isWeakPassword(encrypted));
            //String decrypt = AESUtil.Decrypt(encrypted);
            //System.out.printf("解密结果: %s%n", decrypt);
        } catch (Exception e) {
            System.err.println("加密测试失败: " + password);
            e.printStackTrace();
        }
    }
}
