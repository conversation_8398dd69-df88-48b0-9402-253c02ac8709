/**
 * Copyright sp42 <EMAIL>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package inks.system.utils.mysqldump;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.function.Consumer;

/**
 * 完成 SQL 到 Java 的转换
 *
 * <AUTHOR> <EMAIL>
 */
public class JdbcReader {
    private final static Logger LOGGER = LoggerFactory.getLogger(JdbcReader.class);

    /**
     * Statement 工厂
     *
     * @param conn   数据库连接对象
     * @param handle 控制器
     */
    public static void stmt(Connection conn, Consumer<Statement> handle) {
        try (Statement stmt = conn.createStatement()) {
            handle.accept(stmt);
        } catch (SQLException e) {
            LOGGER.warn(String.valueOf(e));
        }
    }

    /**
     * ResultSet 处理器
     *
     * @param stmt   Statement 对象
     * @param sql    SQL 语句
     * @param handle 控制器
     */
    public static void rsHandle(Statement stmt, String sql, Consumer<ResultSet> handle) {
        try (ResultSet rs = stmt.executeQuery(sql)) {
            handle.accept(rs);
        } catch (SQLException e) {
            LOGGER.warn(String.valueOf(e));

        }
    }

    /**
     * stmt + rsHandle
     *
     * @param conn   数据库连接对象
     * @param sql    SQL 语句
     * @param handle 控制器
     */
    public static void query(Connection conn, String sql, Consumer<ResultSet> handle) {
        stmt(conn, stmt -> rsHandle(stmt, sql, handle));
    }
}
