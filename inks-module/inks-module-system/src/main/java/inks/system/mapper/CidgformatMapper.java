package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CidgformatPojo;
import inks.system.domain.pojo.CidgformatitemdetailPojo;
import inks.system.domain.CidgformatEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 列表格式(Cidgformat)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-04-21 15:16:08
 */
@Mapper
public interface CidgformatMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CidgformatPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CidgformatitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CidgformatPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param cidgformatEntity 实例对象
     * @return 影响行数
     */
    int insert(CidgformatEntity cidgformatEntity);


    /**
     * 修改数据
     *
     * @param cidgformatEntity 实例对象
     * @return 影响行数
     */
    int update(CidgformatEntity cidgformatEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param cidgformatPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(CidgformatPojo cidgformatPojo);

    /**
     * 通过ID查询单条数据
     *
     * @param code 主键
     * @return 实例对象
     */
    CidgformatPojo getEntityByCode(@Param("code") String code, @Param("tid") String tid);


    /**
     * 通过ID查询单条数据
     *
     * @param code 主键
     * @return 实例对象
     */
    CidgformatPojo getEntityByCodeUser(@Param("code") String code, @Param("userid") String userid, @Param("tid") String tid);

    CidgformatPojo getTenBillEntityByCode(@Param("code") String code,  @Param("tid") String tid);

    List<String > getIdByCode(String code, String userid, String tid);
}

