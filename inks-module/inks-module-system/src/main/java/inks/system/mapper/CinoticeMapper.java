package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CinoticePojo;
import inks.system.domain.CinoticeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 公告(Cinotice)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-19 19:27:30
 */
@Mapper
public interface CinoticeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CinoticePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CinoticePojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param cinoticeEntity 实例对象
     * @return 影响行数
     */
    int insert(CinoticeEntity cinoticeEntity);

    
    /**
     * 修改数据
     *
     * @param cinoticeEntity 实例对象
     * @return 影响行数
     */
    int update(CinoticeEntity cinoticeEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

