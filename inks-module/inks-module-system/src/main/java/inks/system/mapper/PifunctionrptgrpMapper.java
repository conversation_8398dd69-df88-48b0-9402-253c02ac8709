package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PifunctionrptgrpPojo;
import inks.system.domain.PifunctionrptgrpEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 服务报表分组数据(PiFunctionRptGrp)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-22 10:31:29
 */
@Mapper
public interface PifunctionrptgrpMapper {

    PifunctionrptgrpPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<PifunctionrptgrpPojo> getPageList(QueryParam queryParam);

    List<PifunctionrptgrpPojo> getPageListByFunctionid(QueryParam queryParam);

    int insert(PifunctionrptgrpEntity pifunctionrptgrpEntity);

    int update(PifunctionrptgrpEntity pifunctionrptgrpEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);
    
    List<PifunctionrptgrpPojo> getList(String tid);

}
