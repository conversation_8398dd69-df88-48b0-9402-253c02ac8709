package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PidmsuserPojo;
import inks.system.domain.PidmsuserEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * DMS用户(Pidmsuser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-21 19:08:54
 */
@Mapper
public interface PidmsuserMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PidmsuserPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PidmsuserPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param pidmsuserEntity 实例对象
     * @return 影响行数
     */
    int insert(PidmsuserEntity pidmsuserEntity);

    
    /**
     * 修改数据
     *
     * @param pidmsuserEntity 实例对象
     * @return 影响行数
     */
    int update(PidmsuserEntity pidmsuserEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key ,@Param("tid") String tid);

    PidmsuserPojo getEntityByUserName(@Param("username")String username);

    List<PidmsuserPojo> getPageListByTen(QueryParam queryParam);

    PidmsuserPojo getEntityByOpenid(@Param("openid")String openid, @Param("tid")String tid);

    List<PidmsuserPojo> getListByOpenid(String openid);

    PidmsuserPojo getEntityByUserid(@Param("userid")String userid, @Param("tid")String tid);

    void deletePiTenantDmsUser(@Param("userid") String key, @Param("tid") String tid);
}

