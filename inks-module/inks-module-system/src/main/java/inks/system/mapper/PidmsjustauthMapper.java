package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PidmsjustauthPojo;
import inks.system.domain.PidmsjustauthEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * DMS第三方登录(Pidmsjustauth)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-21 21:25:20
 */
@Mapper
public interface PidmsjustauthMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PidmsjustauthPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PidmsjustauthPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param pidmsjustauthEntity 实例对象
     * @return 影响行数
     */
    int insert(PidmsjustauthEntity pidmsjustauthEntity);

    
    /**
     * 修改数据
     *
     * @param pidmsjustauthEntity 实例对象
     * @return 影响行数
     */
    int update(PidmsjustauthEntity pidmsjustauthEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    int deleteByOpenid(@Param("openid")String openid, @Param("tid")String tid);
}

