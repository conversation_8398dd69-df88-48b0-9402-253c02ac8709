package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CidashboardPojo;
import inks.system.domain.CidashboardEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 工作台(Cidashboard)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-07-15 21:12:58
 */
@Mapper
public interface CidashboardMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CidashboardPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CidashboardPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param cidashboardEntity 实例对象
     * @return 影响行数
     */
    int insert(CidashboardEntity cidashboardEntity);

    
    /**
     * 修改数据
     *
     * @param cidashboardEntity 实例对象
     * @return 影响行数
     */
    int update(CidashboardEntity cidashboardEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
                                                                                          }

