package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiprojectitemPojo;
import inks.system.domain.PiprojectitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 项目服务(Piprojectitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-20 12:52:13
 */
 @Mapper
public interface PiprojectitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiprojectitemPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PiprojectitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<PiprojectitemPojo> getList(@Param("Pid") String Pid);
     
    
    /**
     * 新增数据
     *
     * @param piprojectitemEntity 实例对象
     * @return 影响行数
     */
    int insert(PiprojectitemEntity piprojectitemEntity);

    
    /**
     * 修改数据
     *
     * @param piprojectitemEntity 实例对象
     * @return 影响行数
     */
    int update(PiprojectitemEntity piprojectitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

