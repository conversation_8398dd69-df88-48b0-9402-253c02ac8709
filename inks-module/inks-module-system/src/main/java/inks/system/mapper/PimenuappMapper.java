package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.PimenuappEntity;
import inks.system.domain.pojo.PimenuappPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * APP导航(Pimenuapp)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-08 14:07:53
 */
@Mapper
public interface PimenuappMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PimenuappPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PimenuappPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param pimenuappEntity 实例对象
     * @return 影响行数
     */
    int insert(PimenuappEntity pimenuappEntity);

    
    /**
     * 修改数据
     *
     * @param pimenuappEntity 实例对象
     * @return 影响行数
     */
    int update(PimenuappEntity pimenuappEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param key 查询条件
     * @return 对象列表
     */
    List<PimenuappPojo> getListByPid(String key);

    List<PimenuappPojo> getListByNavids(@Param("navids") List<String> navids);
}

