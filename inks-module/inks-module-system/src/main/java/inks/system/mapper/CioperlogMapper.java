package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CioperlogPojo;
import inks.system.domain.CioperlogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 操作日志(Cioperlog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-22 13:38:48
 */
@Mapper
public interface CioperlogMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CioperlogPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CioperlogPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param cioperlogEntity 实例对象
     * @return 影响行数
     */
    int insert(CioperlogEntity cioperlogEntity);

    
    /**
     * 修改数据
     *
     * @param cioperlogEntity 实例对象
     * @return 影响行数
     */
    int update(CioperlogEntity cioperlogEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    int deleteByTime(QueryParam queryParam);
}

