package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiscenePojo;
import inks.system.domain.CisceneEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 场景管理(Ciscene)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-11-24 11:34:52
 */
@Mapper
public interface CisceneMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiscenePojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CiscenePojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param cisceneEntity 实例对象
     * @return 影响行数
     */
    int insert(CisceneEntity cisceneEntity);


    /**
     * 修改数据
     *
     * @param cisceneEntity 实例对象
     * @return 影响行数
     */
    int update(CisceneEntity cisceneEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);


    /**
     * 查询指定行数据
     *
     * @return 对象列表
     */
    List<CiscenePojo> getListByCode(@Param("code") String code, @Param("userid") String userid, @Param("tid") String tid);

}

