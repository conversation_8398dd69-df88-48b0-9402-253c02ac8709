package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.CiemailEntity;
import inks.system.domain.pojo.CiemailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 邮件模板(Ciemail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-04-29 15:00:42
 */
@Mapper
public interface CiemailMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiemailPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CiemailPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param ciemailEntity 实例对象
     * @return 影响行数
     */
    int insert(CiemailEntity ciemailEntity);


    /**
     * 修改数据
     *
     * @param ciemailEntity 实例对象
     * @return 影响行数
     */
    int update(CiemailEntity ciemailEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    List<CiemailPojo> getListByModuleCode(@Param("moduleCode") String moduleCode, @Param("tid") String tid);


}

