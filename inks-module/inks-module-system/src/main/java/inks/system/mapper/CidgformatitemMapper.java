package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CidgformatitemPojo;
import inks.system.domain.CidgformatitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 列表项目(Cidgformatitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-04-21 15:17:13
 */
 @Mapper
public interface CidgformatitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CidgformatitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CidgformatitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<CidgformatitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param cidgformatitemEntity 实例对象
     * @return 影响行数
     */
    int insert(CidgformatitemEntity cidgformatitemEntity);

    
    /**
     * 修改数据
     *
     * @param cidgformatitemEntity 实例对象
     * @return 影响行数
     */
    int update(CidgformatitemEntity cidgformatitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

