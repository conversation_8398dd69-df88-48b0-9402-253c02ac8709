package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.CibigdataEntity;
import inks.system.domain.pojo.CibigdataPojo;
import inks.system.domain.pojo.CibigdataitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据大屏(Cibigdata)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-25 10:32:02
 */
@Mapper
public interface CibigdataMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CibigdataPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CibigdataitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CibigdataPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param cibigdataEntity 实例对象
     * @return 影响行数
     */
    int insert(CibigdataEntity cibigdataEntity);


    /**
     * 修改数据
     *
     * @param cibigdataEntity 实例对象
     * @return 影响行数
     */
    int update(CibigdataEntity cibigdataEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 查询 被删除的Item
     *
     * @param cibigdataPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(CibigdataPojo cibigdataPojo);


    /**
     * 查询指定行数据
     *
     * @param key 查询条件
     * @return 对象列表
     */
    List<CibigdataPojo> getListByTenant(@Param("key") String key, @Param("date") String date);
}

