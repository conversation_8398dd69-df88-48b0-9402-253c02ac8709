package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PifunctionwebnavPojo;
import inks.system.domain.PifunctionwebnavEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * web导航(Pifunctionwebnav)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-01 15:36:24
 */
@Mapper
public interface PifunctionwebnavMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionwebnavPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PifunctionwebnavPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param pifunctionwebnavEntity 实例对象
     * @return 影响行数
     */
    int insert(PifunctionwebnavEntity pifunctionwebnavEntity);

    
    /**
     * 修改数据
     *
     * @param pifunctionwebnavEntity 实例对象
     * @return 影响行数
     */
    int update(PifunctionwebnavEntity pifunctionwebnavEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    List<PifunctionwebnavPojo> getListByFunction(String key);
}

