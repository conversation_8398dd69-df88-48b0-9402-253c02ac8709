package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CibigdataitemPojo;
import inks.system.domain.CibigdataitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 服务大屏关系(Cibigdataitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-25 10:38:40
 */
 @Mapper
public interface CibigdataitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CibigdataitemPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CibigdataitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<CibigdataitemPojo> getList(@Param("Pid") String Pid);
     
    
    /**
     * 新增数据
     *
     * @param cibigdataitemEntity 实例对象
     * @return 影响行数
     */
    int insert(CibigdataitemEntity cibigdataitemEntity);

    
    /**
     * 修改数据
     *
     * @param cibigdataitemEntity 实例对象
     * @return 影响行数
     */
    int update(CibigdataitemEntity cibigdataitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

