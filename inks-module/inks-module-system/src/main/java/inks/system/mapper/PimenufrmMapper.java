package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PimenufrmPojo;
import inks.system.domain.PimenufrmEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * Frm导航(Pimenufrm)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-05 16:28:26
 */
@Mapper
public interface PimenufrmMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PimenufrmPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PimenufrmPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param pimenufrmEntity 实例对象
     * @return 影响行数
     */
    int insert(PimenufrmEntity pimenufrmEntity);

    
    /**
     * 修改数据
     *
     * @param pimenufrmEntity 实例对象
     * @return 影响行数
     */
    int update(PimenufrmEntity pimenufrmEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<PimenufrmPojo> getListByPid(String key);

    List<PimenufrmPojo> getAllMenus();
}

