package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiweblnkcustPojo;
import inks.system.domain.CiweblnkcustEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 自定义webLnk(Ciweblnkcust)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-08 14:57:09
 */
@Mapper
public interface CiweblnkcustMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiweblnkcustPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CiweblnkcustPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param ciweblnkcustEntity 实例对象
     * @return 影响行数
     */
    int insert(CiweblnkcustEntity ciweblnkcustEntity);

    
    /**
     * 修改数据
     *
     * @param ciweblnkcustEntity 实例对象
     * @return 影响行数
     */
    int update(CiweblnkcustEntity ciweblnkcustEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    /**
     * 通过当前登录用户的Userid查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiweblnkcustPojo getEntityBySelf(@Param("userid")String userid,@Param("tid") String tid);

    List<CiweblnkcustPojo> getListBySelf(String fncode, String userid, String tenantid);
}

