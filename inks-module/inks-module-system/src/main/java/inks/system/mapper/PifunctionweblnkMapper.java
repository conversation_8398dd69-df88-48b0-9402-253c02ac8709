package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PifunctionweblnkPojo;
import inks.system.domain.PifunctionweblnkEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * Web快捷方式(Pifunctionweblnk)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-08 14:48:38
 */
@Mapper
public interface PifunctionweblnkMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionweblnkPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PifunctionweblnkPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param pifunctionweblnkEntity 实例对象
     * @return 影响行数
     */
    int insert(PifunctionweblnkEntity pifunctionweblnkEntity);

    
    /**
     * 修改数据
     *
     * @param pifunctionweblnkEntity 实例对象
     * @return 影响行数
     */
    int update(PifunctionweblnkEntity pifunctionweblnkEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    List<PifunctionweblnkPojo> getListByFunction(String key);
}

