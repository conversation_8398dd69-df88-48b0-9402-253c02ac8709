package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiscmjustauthPojo;
import inks.system.domain.PiscmjustauthEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * SCM第三方登录(Piscmjustauth)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-21 21:25:20
 */
@Mapper
public interface PiscmjustauthMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiscmjustauthPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PiscmjustauthPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param piscmjustauthEntity 实例对象
     * @return 影响行数
     */
    int insert(PiscmjustauthEntity piscmjustauthEntity);

    
    /**
     * 修改数据
     *
     * @param piscmjustauthEntity 实例对象
     * @return 影响行数
     */
    int update(PiscmjustauthEntity piscmjustauthEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    int deleteByOpenid(@Param("openid")String openid, @Param("tid")String tid);
}

