package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiuserloginPojo;
import inks.system.domain.PiuserloginEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 用户登录表(Piuserlogin)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-08 14:36:11
 */
@Mapper
public interface PiuserloginMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiuserloginPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PiuserloginPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param piuserloginEntity 实例对象
     * @return 影响行数
     */
    int insert(PiuserloginEntity piuserloginEntity);

    
    /**
     * 修改数据
     *
     * @param piuserloginEntity 实例对象
     * @return 影响行数
     */
    int update(PiuserloginEntity piuserloginEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiuserloginPojo getEntityByUserid(@Param("key") String key);

    List<PiuserloginPojo> getListByUserid(@Param("key") String key, @Param("tenantid") String tenantid);

    String getPasswordByUserid(String userid);
}

