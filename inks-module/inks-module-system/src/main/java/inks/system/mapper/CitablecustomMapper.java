package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CitablecustomPojo;
import inks.system.domain.CitablecustomEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 自定义字段(Citablecustom)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-08-10 14:26:33
 */
@Mapper
public interface CitablecustomMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CitablecustomPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CitablecustomPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param citablecustomEntity 实例对象
     * @return 影响行数
     */
    int insert(CitablecustomEntity citablecustomEntity);


    /**
     * 修改数据
     *
     * @param citablecustomEntity 实例对象
     * @return 影响行数
     */
    int update(CitablecustomEntity citablecustomEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);


    /**
     * 查询指定行数据
     *
     * @return 对象列表
     */
    List<CitablecustomPojo> getListByCode(@Param("key") String key, @Param("tid") String tid);


    /**
     * 查询指定行数据
     *
     * @return 对象列表
     */
    List<CitablecustomPojo> getListByGroupid(@Param("key") String key, @Param("tid") String tid);

}

