package inks.system.mapper;

import inks.common.core.domain.ChartPojo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiloginlogPojo;
import inks.system.domain.CiloginlogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 登录日志(Ciloginlog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-08 13:56:41
 */
@Mapper
public interface CiloginlogMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiloginlogPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CiloginlogPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param ciloginlogEntity 实例对象
     * @return 影响行数
     */
    int insert(CiloginlogEntity ciloginlogEntity);


    /**
     * 修改数据
     *
     * @param ciloginlogEntity 实例对象
     * @return 影响行数
     */
    int update(CiloginlogEntity ciloginlogEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<ChartPojo> getCountListByPro(QueryParam queryParam);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<ChartPojo> getCountListByCity(QueryParam queryParam);

    List<Map<String,Object>> getEveryAdminLogin(@Param("startdate")Date startdate, @Param("enddate")Date enddate);

    List<Map<String,Object>> getEveryUserLogin(@Param("startdate")Date startdate, @Param("enddate")Date enddate);

    List<Map<String, Object>> getEveryTenantLogin(@Param("startdate")Date startdate, @Param("enddate")Date enddate);


    List<CiloginlogPojo> getAllAdminLogin(@Param("startdate")Date startdate, @Param("enddate")Date enddate);

    List<CiloginlogPojo> getAllUserLogin(@Param("startdate")Date startdate, @Param("enddate")Date enddate);

    List<Map<String, Object>> getCountPageListByTen(QueryParam queryParam);

    int deleteByTime(QueryParam queryParam);

    List<Map<String, Object>> getCountSuccessByUser(Date startDate, Date endDate, String tenantid);
}

