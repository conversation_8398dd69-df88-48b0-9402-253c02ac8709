package inks.system.mapper;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiformpartPojo;
import inks.system.domain.CiformpartEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 表单组件(Ciformpart)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-01-19 13:41:52
 */
@Mapper
public interface CiformpartMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiformpartPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CiformpartPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param ciformpartEntity 实例对象
     * @return 影响行数
     */
    int insert(CiformpartEntity ciformpartEntity);

    
    /**
     * 修改数据
     *
     * @param ciformpartEntity 实例对象
     * @return 影响行数
     */
    int update(CiformpartEntity ciformpartEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    List<CiformpartPojo> getPartList(@Param("code") String code, @Param("type") String type, @Param("tid") String tenantid);
}

