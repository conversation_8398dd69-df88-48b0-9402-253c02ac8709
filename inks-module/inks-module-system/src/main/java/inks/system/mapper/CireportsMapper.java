package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CireportsPojo;
import inks.system.domain.CireportsEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报表中心(Cireports)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-08 13:35:25
 */
@Mapper
public interface CireportsMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CireportsPojo getEntity(@Param("key") String key,@Param("tid")String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CireportsPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param cireportsEntity 实例对象
     * @return 影响行数
     */
    int insert(CireportsEntity cireportsEntity);


    /**
     * 修改数据
     *
     * @param cireportsEntity 实例对象
     * @return 影响行数
     */
    int update(CireportsEntity cireportsEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid")String tid);

    List<CireportsPojo> getListByModuleCode(@Param("moduleCode") String moduleCode, @Param("tid") String tid);

    CireportsPojo getEntityByNameCode(@Param("rptname") String rptname,@Param("moduleCode") String moduleCode, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param moduleCode 查询条件
     * @return 对象列表
     */
    List<CireportsPojo> getListByDef(@Param("moduleCode") String moduleCode);

      /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CireportsPojo> getPageListAll(QueryParam queryParam);

    List<CireportsPojo> getDefaultReports(String tid);

    List<CireportsPojo> getListByTid(String tid);

    int batchInsert(List<CireportsPojo> batch);
}

