package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.CiwarninguserEntity;
import inks.system.domain.pojo.CiwarninguserPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 用户预警(Ciwarninguser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-07-05 20:11:15
 */
@Mapper
public interface CiwarninguserMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiwarninguserPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CiwarninguserPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param ciwarninguserEntity 实例对象
     * @return 影响行数
     */
    int insert(CiwarninguserEntity ciwarninguserEntity);


    /**
     * 修改数据
     *
     * @param ciwarninguserEntity 实例对象
     * @return 影响行数
     */
    int update(CiwarninguserEntity ciwarninguserEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    List<CiwarninguserPojo> getListByUser(@Param("userid") String userid, @Param("tid") String tid);


    List<Map<String, Object>> getBusMachList(@Param("warnfield") String warnfield, @Param("diffnum") int diffnum, @Param("tid") String tid);

    List<Map<String, Object>> getBusInvoList(@Param("warnfield") String warnfield, @Param("diffnum") int diffnum, @Param("tid") String tid);

    List<Map<String, Object>> getBuyOrderList(@Param("warnfield") String warnfield, @Param("diffnum") int diffnum, @Param("tid") String tid);

    List<Map<String, Object>> getBuyInvoList(@Param("warnfield") String warnfield, @Param("diffnum") int diffnum, @Param("tid") String tid);

    // 销售订单 预警生产完工但未发货完成的记录
    List<Map<String, Object>> getBusMachCanOutList(@Param("warnfield") String warnfield, @Param("diffnum") int diffnum, @Param("tid") String tid);
}

