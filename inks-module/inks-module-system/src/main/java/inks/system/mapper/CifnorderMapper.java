package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CifnorderPojo;
import inks.system.domain.pojo.CifnorderitemdetailPojo;
import inks.system.domain.CifnorderEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 销售订单(Cifnorder)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-09 20:42:14
 */
@Mapper
public interface CifnorderMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CifnorderPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CifnorderitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CifnorderPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param cifnorderEntity 实例对象
     * @return 影响行数
     */
    int insert(CifnorderEntity cifnorderEntity);

    
    /**
     * 修改数据
     *
     * @param cifnorderEntity 实例对象
     * @return 影响行数
     */
    int update(CifnorderEntity cifnorderEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param cifnorderPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(CifnorderPojo cifnorderPojo);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CifnorderPojo getEntityByRefno(@Param("key") String key);

}

