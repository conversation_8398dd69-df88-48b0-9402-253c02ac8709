package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CidynamicvalidationPojo;
import inks.system.domain.pojo.CidynamicvalidationitemdetailPojo;
import inks.system.domain.CidynamicvalidationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 动态校验规则(Cidynamicvalidation)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-04 15:54:38
 */
@Mapper
public interface CidynamicvalidationMapper {

    CidynamicvalidationPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<CidynamicvalidationitemdetailPojo> getPageList(QueryParam queryParam);

    List<CidynamicvalidationPojo> getPageTh(QueryParam queryParam);

    int insert(CidynamicvalidationEntity cidynamicvalidationEntity);

    int update(CidynamicvalidationEntity cidynamicvalidationEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

     List<String> getDelItemIds(CidynamicvalidationPojo cidynamicvalidationPojo);
}

