package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiweblnkPojo;
import inks.system.domain.PiweblnkEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 快捷方式(Piweblnk)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-08 14:11:24
 */
@Mapper
public interface PiweblnkMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiweblnkPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PiweblnkPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param piweblnkEntity 实例对象
     * @return 影响行数
     */
    int insert(PiweblnkEntity piweblnkEntity);

    
    /**
     * 修改数据
     *
     * @param piweblnkEntity 实例对象
     * @return 影响行数
     */
    int update(PiweblnkEntity piweblnkEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param key 查询条件
     * @return 对象列表
     */
    List<PiweblnkPojo> getListByPid(String key);
}

