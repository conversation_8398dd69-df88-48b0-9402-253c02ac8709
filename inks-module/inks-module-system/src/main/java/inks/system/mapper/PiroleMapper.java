package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PirolePojo;
import inks.system.domain.PiroleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 角色表(Pirole)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-17 10:24:53
 */
@Mapper
public interface PiroleMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PirolePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PirolePojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param piroleEntity 实例对象
     * @return 影响行数
     */
    int insert(PiroleEntity piroleEntity);

    
    /**
     * 修改数据
     *
     * @param piroleEntity 实例对象
     * @return 影响行数
     */
    int update(PiroleEntity piroleEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
                                                                                          }

