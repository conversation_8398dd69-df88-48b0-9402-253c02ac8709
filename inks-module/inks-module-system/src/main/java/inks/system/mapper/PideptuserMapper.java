package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PideptuserPojo;
import inks.system.domain.PideptuserEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 组织用户表(Pideptuser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-28 11:18:02
 */
@Mapper
public interface PideptuserMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PideptuserPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PideptuserPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param pideptuserEntity 实例对象
     * @return 影响行数
     */
    int insert(PideptuserEntity pideptuserEntity);


    /**
     * 修改数据
     *
     * @param pideptuserEntity 实例对象
     * @return 影响行数
     */
    int update(PideptuserEntity pideptuserEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param userid 主键
     * @return 实例对象
     */
    PideptuserPojo getEntityByUser(@Param("userid") String userid,@Param("tid") String tid);

}

