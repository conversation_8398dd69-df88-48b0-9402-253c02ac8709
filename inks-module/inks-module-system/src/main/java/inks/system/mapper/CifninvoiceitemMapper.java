package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CifninvoiceitemPojo;
import inks.system.domain.CifninvoiceitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 发票项目(Cifninvoiceitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-24 13:32:08
 */
 @Mapper
public interface CifninvoiceitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CifninvoiceitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CifninvoiceitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<CifninvoiceitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param cifninvoiceitemEntity 实例对象
     * @return 影响行数
     */
    int insert(CifninvoiceitemEntity cifninvoiceitemEntity);

    
    /**
     * 修改数据
     *
     * @param cifninvoiceitemEntity 实例对象
     * @return 影响行数
     */
    int update(CifninvoiceitemEntity cifninvoiceitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

