package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CidynamicvalidationitemPojo;
import inks.system.domain.CidynamicvalidationitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 动态校验规则子表(Cidynamicvalidationitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-04 15:55:01
 */
 @Mapper
public interface CidynamicvalidationitemMapper {

    CidynamicvalidationitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<CidynamicvalidationitemPojo> getPageList(QueryParam queryParam);

    List<CidynamicvalidationitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
 
    int insert(CidynamicvalidationitemEntity cidynamicvalidationitemEntity);

    int update(CidynamicvalidationitemEntity cidynamicvalidationitemEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    List<CidynamicvalidationitemPojo> getListByValidationCode(String validationcode,String tid);
}

