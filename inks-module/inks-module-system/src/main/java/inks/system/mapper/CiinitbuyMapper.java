package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import org.apache.ibatis.annotations.Mapper;

/**
 * 初始化数据(无表)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-07-31 11:20:20
 */
@Mapper
public interface CiinitbuyMapper {

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deletePlanItem(QueryParam queryParam);
    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deletePlan(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteOrderItem(QueryParam queryParam);
    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteOrder(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteFiniItem(QueryParam queryParam);
    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteFini(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteDeduItem(QueryParam queryParam);
    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteDedu(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteInvoItem(QueryParam queryParam);
    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteInvo(QueryParam queryParam);


    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deletePrepItem(QueryParam queryParam);
    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deletePrepCash(QueryParam queryParam);
    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deletePrep(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteVoucItem(QueryParam queryParam);
    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteVoucCash(QueryParam queryParam);
    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteVouc(QueryParam queryParam);


    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteAccoItem(QueryParam queryParam);
    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteAcco(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteAccoRec(QueryParam queryParam);

}

