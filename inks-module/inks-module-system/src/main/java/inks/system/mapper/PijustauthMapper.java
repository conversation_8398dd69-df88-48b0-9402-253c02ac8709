package inks.system.mapper;

import inks.common.core.domain.JustauthPojo;
import inks.common.core.domain.QueryParam;
import inks.system.domain.PijustauthEntity;
import inks.system.domain.pojo.PijustauthPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 第三方登录(Pijustauth)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-01-16 14:45:58
 */
@Mapper
public interface PijustauthMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PijustauthPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PijustauthPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param pijustauthEntity 实例对象
     * @return 影响行数
     */
    int insert(PijustauthEntity pijustauthEntity);


    /**
     * 修改数据
     *
     * @param pijustauthEntity 实例对象
     * @return 影响行数
     */
    int update(PijustauthEntity pijustauthEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PijustauthPojo getJustauthByUuid(@Param("key") String key, @Param("type") String type,@Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PijustauthPojo getJustauthByUserid(@Param("key") String key, @Param("type") String type,@Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    List<JustauthPojo> getAdminListByDeptid(@Param("key") String key, @Param("type") String type, @Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    List<JustauthPojo> getListByUnionid(@Param("key") String key);
}

