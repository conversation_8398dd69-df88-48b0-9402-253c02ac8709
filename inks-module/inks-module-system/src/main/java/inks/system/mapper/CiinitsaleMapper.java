package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import org.apache.ibatis.annotations.Mapper;

/**
 * 初始化数据(无表)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-07-31 11:20:20
 */
@Mapper
public interface CiinitsaleMapper {

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteMachItem(QueryParam queryParam);
    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteMach(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteDeliItem(QueryParam queryParam);
    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteDeli(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteInvoItem(QueryParam queryParam);
    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteInvo(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteDepoItem(QueryParam queryParam);
    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteDepoCash(QueryParam queryParam);
    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteDepo(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteReceItem(QueryParam queryParam);
    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteReceCash(QueryParam queryParam);
    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteRece(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteDeduItem(QueryParam queryParam);
    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteDedu(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteAccoItem(QueryParam queryParam);
    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteAcco(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteAccoRec(QueryParam queryParam);

}

