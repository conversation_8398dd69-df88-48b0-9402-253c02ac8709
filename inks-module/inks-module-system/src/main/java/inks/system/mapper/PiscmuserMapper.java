package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiscmuserPojo;
import inks.system.domain.PiscmuserEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * SCM用户(Piscmuser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-21 19:08:54
 */
@Mapper
public interface PiscmuserMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiscmuserPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PiscmuserPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param piscmuserEntity 实例对象
     * @return 影响行数
     */
    int insert(PiscmuserEntity piscmuserEntity);

    
    /**
     * 修改数据
     *
     * @param piscmuserEntity 实例对象
     * @return 影响行数
     */
    int update(PiscmuserEntity piscmuserEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key ,@Param("tid") String tid);

    PiscmuserPojo getEntityByUserName(@Param("username")String username);

    List<PiscmuserPojo> getPageListByTen(QueryParam queryParam);

    PiscmuserPojo getEntityByOpenid(@Param("openid")String openid, @Param("tid")String tid);

    List<PiscmuserPojo> getListByOpenid(String openid);

    PiscmuserPojo getEntityByUserid(@Param("userid")String userid, @Param("tid")String tid);
}

