package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiformvaildPojo;
import inks.system.domain.pojo.CiformvailditemdetailPojo;
import inks.system.domain.CiformvaildEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 窗体验证(Ciformvaild)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-31 17:03:52
 */
@Mapper
public interface CiformvaildMapper {

    CiformvaildPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<CiformvailditemdetailPojo> getPageList(QueryParam queryParam);

    List<CiformvaildPojo> getPageTh(QueryParam queryParam);

    int insert(CiformvaildEntity ciformvaildEntity);

    int update(CiformvaildEntity ciformvaildEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

     List<String> getDelItemIds(CiformvaildPojo ciformvaildPojo);

    CiformvaildPojo getEntityByFormCode(String code, String tid);
}

