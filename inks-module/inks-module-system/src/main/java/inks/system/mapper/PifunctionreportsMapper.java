package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.PifunctionreportsEntity;
import inks.system.domain.pojo.PifunctionreportsPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 服务报表关系(Pifunctionreports)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-17 07:54:30
 */
@Mapper
public interface PifunctionreportsMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionreportsPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PifunctionreportsPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param pifunctionreportsEntity 实例对象
     * @return 影响行数
     */
    int insert(PifunctionreportsEntity pifunctionreportsEntity);


    /**
     * 修改数据
     *
     * @param pifunctionreportsEntity 实例对象
     * @return 影响行数
     */
    int update(PifunctionreportsEntity pifunctionreportsEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<PifunctionreportsPojo> getListByFunction(String key);

}

