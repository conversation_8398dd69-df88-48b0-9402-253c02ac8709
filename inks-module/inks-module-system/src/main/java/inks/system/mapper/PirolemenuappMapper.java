package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.PirolemenuappEntity;
import inks.system.domain.pojo.PirolemenuappPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色菜单App(PiRoleMenuApp)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-11 13:55:54
 */
@Mapper
public interface PirolemenuappMapper {

    PirolemenuappPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<PirolemenuappPojo> getPageList(QueryParam queryParam);

    int insert(PirolemenuappEntity pirolemenuappEntity);

    int update(PirolemenuappEntity pirolemenuappEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    List<String> getNavidsByUserid(String userid, String tid);

    List<String> getNavidsByRoleid(String roleid, String tid);

    int deleteByRoleidAndNavid(String roleid, String navid, String tid);

    Integer batchDelete(@Param("roleid") String roleid, @Param("deleteNavids") List<String> deleteNavids, @Param("tid") String tid);

    Integer batchInsert(@Param("pirolemenuappPojoList") List<PirolemenuappPojo> pirolemenuappPojoList);

    List<PirolemenuappPojo> getListByRole(String key, String tid);
}

