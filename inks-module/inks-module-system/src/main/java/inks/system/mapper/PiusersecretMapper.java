package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiuserauthcodePojo;
import inks.system.domain.pojo.PiusersecretPojo;
import inks.system.domain.PiusersecretEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户授权key(Piusersecret)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-12-28 09:52:24
 */
@Mapper
public interface PiusersecretMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiusersecretPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PiusersecretPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param piusersecretEntity 实例对象
     * @return 影响行数
     */
    int insert(PiusersecretEntity piusersecretEntity);


    /**
     * 修改数据
     *
     * @param piusersecretEntity 实例对象
     * @return 影响行数
     */
    int update(PiusersecretEntity piusersecretEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param userid 主键
     * @return 实例对象
     */
    PiusersecretPojo getEntityByUserid(@Param("userid") String userid, @Param("tid") String tid);

}

