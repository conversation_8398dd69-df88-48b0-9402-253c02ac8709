package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.PitenantrmsuserEntity;
import inks.system.domain.pojo.PitenantrmsuserPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * RMS租户关系表(Pitenantrmsuser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@Mapper
public interface PitenantrmsuserMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PitenantrmsuserPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PitenantrmsuserPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param pitenantrmsuserEntity 实例对象
     * @return 影响行数
     */
    int insert(PitenantrmsuserEntity pitenantrmsuserEntity);


    /**
     * 修改数据
     *
     * @param pitenantrmsuserEntity 实例对象
     * @return 影响行数
     */
    int update(PitenantrmsuserEntity pitenantrmsuserEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param Userid 查询条件
     * @return 对象列表
     */
    List<PitenantrmsuserPojo> getListByUser(@Param("userid") String Userid);

    PitenantrmsuserPojo getEntityByUserid(@Param("key") String key, @Param("tid") String tid);

}

