package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CibillcodePojo;
import inks.system.domain.CibillcodeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 单据编码(Cibillcode)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-08 13:27:44
 */
@Mapper
public interface CibillcodeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CibillcodePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CibillcodePojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param cibillcodeEntity 实例对象
     * @return 影响行数
     */
    int insert(CibillcodeEntity cibillcodeEntity);

    
    /**
     * 修改数据
     *
     * @param cibillcodeEntity 实例对象
     * @return 影响行数
     */
    int update(CibillcodeEntity cibillcodeEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    CibillcodeEntity getEntityByModuleCode(@Param("ModuleCode") String ModuleCode,@Param("tid") String tid);

     Map<String, Object> getSerialNo(@Param("entity") CibillcodeEntity cibillcodeEntity, @Param("subindex")Integer subindex,
                                     @Param("tid")String tid, @Param("currentDate") Date currentDate);

    List<CibillcodePojo> getDefaultBillCodes(@Param("tid")String tid);

}

