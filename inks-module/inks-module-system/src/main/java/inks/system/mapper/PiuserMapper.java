package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiuserPojo;
import inks.system.domain.PiuserEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 用户表(Piuser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-08 14:35:49
 */
@Mapper
public interface PiuserMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiuserPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PiuserPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param piuserEntity 实例对象
     * @return 影响行数
     */
    int insert(PiuserEntity piuserEntity);

    
    /**
     * 修改数据
     *
     * @param piuserEntity 实例对象
     * @return 影响行数
     */
    int update(PiuserEntity piuserEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiuserPojo getEntityByUserName(@Param("key") String key);

    List<String> getUseridsByTid(String tid);
}

