package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.PifunctionconfigEntity;
import inks.system.domain.pojo.PifunctionconfigPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 服务参数关系(Pifunctionconfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-25 11:20:10
 */
@Mapper
public interface PifunctionconfigMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionconfigPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PifunctionconfigPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param pifunctionconfigEntity 实例对象
     * @return 影响行数
     */
    int insert(PifunctionconfigEntity pifunctionconfigEntity);


    /**
     * 修改数据
     *
     * @param pifunctionconfigEntity 实例对象
     * @return 影响行数
     */
    int update(PifunctionconfigEntity pifunctionconfigEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<PifunctionconfigPojo> getListByFunction(String key);



}

