package inks.system.mapper;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.system.domain.PiscmfunctmenuwebEntity;
import inks.system.domain.pojo.PimenuwebPojo;
import inks.system.domain.pojo.PiscmfunctmenuwebPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SCM菜单关系(Piscmfunctmenuweb)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@Mapper
public interface PiscmfunctmenuwebMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiscmfunctmenuwebPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PiscmfunctmenuwebPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param piscmfunctmenuwebEntity 实例对象
     * @return 影响行数
     */
    int insert(PiscmfunctmenuwebEntity piscmfunctmenuwebEntity);


    /**
     * 修改数据
     *
     * @param piscmfunctmenuwebEntity 实例对象
     * @return 影响行数
     */
    int update(PiscmfunctmenuwebEntity piscmfunctmenuwebEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<PiscmfunctmenuwebPojo> getListByFunction(String key);

    List<PimenuwebPojo> getListByLoginUser(LoginUser loginUser);

    List<PimenuwebPojo> getListByScmFunctids(String ids);
}

