package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiwebnavPojo;
import inks.system.domain.PiwebnavEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * Pc导航(Piwebnav)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-01 15:39:10
 */
@Mapper
public interface PiwebnavMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiwebnavPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PiwebnavPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param piwebnavEntity 实例对象
     * @return 影响行数
     */
    int insert(PiwebnavEntity piwebnavEntity);

    
    /**
     * 修改数据
     *
     * @param piwebnavEntity 实例对象
     * @return 影响行数
     */
    int update(PiwebnavEntity piwebnavEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);
    
}

