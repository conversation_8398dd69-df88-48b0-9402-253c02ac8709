package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CixlsinputPojo;
import inks.system.domain.CixlsinputEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * xls导入格式(Cixlsinput)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-07-24 16:29:32
 */
@Mapper
public interface CixlsinputMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CixlsinputPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CixlsinputPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param cixlsinputEntity 实例对象
     * @return 影响行数
     */
    int insert(CixlsinputEntity cixlsinputEntity);

    
    /**
     * 修改数据
     *
     * @param cixlsinputEntity 实例对象
     * @return 影响行数
     */
    int update(CixlsinputEntity cixlsinputEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    List<CixlsinputPojo> getListByModuleCode(@Param("moduleCode") String moduleCode, @Param("tid") String tid);
}

