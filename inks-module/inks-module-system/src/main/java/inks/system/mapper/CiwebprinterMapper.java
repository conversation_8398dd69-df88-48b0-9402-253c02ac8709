package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.CiwebprinterEntity;
import inks.system.domain.pojo.CiwebprinterPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 网络打印机(Ciwebprinter)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-08 15:45:15
 */
@Mapper
public interface CiwebprinterMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiwebprinterPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CiwebprinterPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param ciwebprinterEntity 实例对象
     * @return 影响行数
     */
    int insert(CiwebprinterEntity ciwebprinterEntity);


    /**
     * 修改数据
     *
     * @param ciwebprinterEntity 实例对象
     * @return 影响行数
     */
    int update(CiwebprinterEntity ciwebprinterEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<CiwebprinterPojo> getListByModuleCode(@Param("moduleCode") String moduleCode, @Param("tid") String tid);

}

