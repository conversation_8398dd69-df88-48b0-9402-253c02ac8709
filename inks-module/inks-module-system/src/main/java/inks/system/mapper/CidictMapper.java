package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CidictPojo;
import inks.system.domain.pojo.CidictitemdetailPojo;
import inks.system.domain.CidictEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据字典(Cidict)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-12 07:38:32
 */
@Mapper
public interface CidictMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CidictPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CidictitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CidictPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param cidictEntity 实例对象
     * @return 影响行数
     */
    int insert(CidictEntity cidictEntity);


    /**
     * 修改数据
     *
     * @param cidictEntity 实例对象
     * @return 影响行数
     */
    int update(CidictEntity cidictEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询 被删除的Item
     *
     * @param cidictPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(CidictPojo cidictPojo);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CidictPojo getEntityByDictCode(@Param("key") String key, @Param("tid") String tid);
}

