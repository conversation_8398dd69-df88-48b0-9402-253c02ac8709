package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PitenantPojo;
import inks.system.domain.PitenantEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 租户表(Pitenant)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-08 15:18:55
 */
@Mapper
public interface PitenantMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PitenantPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PitenantPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param pitenantEntity 实例对象
     * @return 影响行数
     */
    int insert(PitenantEntity pitenantEntity);

    
    /**
     * 修改数据
     *
     * @param pitenantEntity 实例对象
     * @return 影响行数
     */
    int update(PitenantEntity pitenantEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

