package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CivalidatorPojo;
import inks.system.domain.CivalidatorEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 数据验证(CiValidator)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-11-15 10:02:13
 */
@Mapper
public interface CivalidatorMapper {

    CivalidatorPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<CivalidatorPojo> getPageList(QueryParam queryParam);

    int insert(CivalidatorEntity civalidatorEntity);

    int update(CivalidatorEntity civalidatorEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    List<CivalidatorPojo> getListByValicodeEnabled(String valicode, String tid);
}

