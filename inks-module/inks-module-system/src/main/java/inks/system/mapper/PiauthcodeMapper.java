package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiauthcodePojo;
import inks.system.domain.PiauthcodeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 授权码(Piauthcode)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-22 13:13:39
 */
@Mapper
public interface PiauthcodeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiauthcodePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PiauthcodePojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param piauthcodeEntity 实例对象
     * @return 影响行数
     */
    int insert(PiauthcodeEntity piauthcodeEntity);

    
    /**
     * 修改数据
     *
     * @param piauthcodeEntity 实例对象
     * @return 影响行数
     */
    int update(PiauthcodeEntity piauthcodeEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
                                                                                                              }

