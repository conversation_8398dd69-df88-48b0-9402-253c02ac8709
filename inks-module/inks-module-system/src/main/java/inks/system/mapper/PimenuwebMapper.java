package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PimenuappPojo;
import inks.system.domain.pojo.PimenuwebPojo;
import inks.system.domain.PimenuwebEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 后台导航(Pimenuweb)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-08 14:23:18
 */
@Mapper
public interface PimenuwebMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PimenuwebPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PimenuwebPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param pimenuwebEntity 实例对象
     * @return 影响行数
     */
    int insert(PimenuwebEntity pimenuwebEntity);

    
    /**
     * 修改数据
     *
     * @param pimenuwebEntity 实例对象
     * @return 影响行数
     */
    int update(PimenuwebEntity pimenuwebEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);


    /**
     * 查询指定行数据
     *
     * @param key 查询条件
     * @return 对象列表
     */
    List<PimenuwebPojo> getListByPid(String key);

    List<PimenuwebPojo> getListByNavids(@Param("navids") List<String> navids);
}

