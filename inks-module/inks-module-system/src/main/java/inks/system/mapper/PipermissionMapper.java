package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PipermissionPojo;
import inks.system.domain.PipermissionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 权限关系表(Pipermission)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-14 15:13:53
 */
@Mapper
public interface PipermissionMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PipermissionPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PipermissionPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param pipermissionEntity 实例对象
     * @return 影响行数
     */
    int insert(PipermissionEntity pipermissionEntity);

    
    /**
     * 修改数据
     *
     * @param pipermissionEntity 实例对象
     * @return 影响行数
     */
    int update(PipermissionEntity pipermissionEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    /**
     * 按角色查询权限
     *
     * @param key 主键
     * @return 影响行数
     */
    List<PipermissionPojo> getListByRole(String key);

    List<PipermissionPojo> getUserAllPerm(@Param("key")String key);

    int batchInsert(List<PipermissionEntity> pipermissionEntityList);

    int batchDelete(List<PipermissionPojo> pipermissionPojoList);
}

