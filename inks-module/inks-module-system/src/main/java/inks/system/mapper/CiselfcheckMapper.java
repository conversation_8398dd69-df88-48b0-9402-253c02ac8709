package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiselfcheckPojo;
import inks.system.domain.CiselfcheckEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

/**
 * 系统自检(Ciselfcheck)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-02 09:08:52
 */
@Mapper
public interface CiselfcheckMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiselfcheckPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CiselfcheckPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param ciselfcheckEntity 实例对象
     * @return 影响行数
     */
    int insert(CiselfcheckEntity ciselfcheckEntity);


    /**
     * 修改数据
     *
     * @param ciselfcheckEntity 实例对象
     * @return 影响行数
     */
    int update(CiselfcheckEntity ciselfcheckEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    String getFunctionidByTid(@Param("tid") String tid, @Param("date") String date);

    List<String> checkPasswordComplexity(@Param("pwdList") ArrayList<String> pwdList, @Param("tid") String tid);

    List<String> checkExpiredMach(@Param("tid") String tid, @Param("date") String date);
}

