package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiadminPojo;
import inks.system.domain.PiadminEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 平台管理员(Piadmin)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-08 13:55:55
 */
@Mapper
public interface PiadminMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiadminPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PiadminPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param piadminEntity 实例对象
     * @return 影响行数
     */
    int insert(PiadminEntity piadminEntity);

    
    /**
     * 修改数据
     *
     * @param piadminEntity 实例对象
     * @return 影响行数
     */
    int update(PiadminEntity piadminEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 修改数据
     *
     * @param piadminPojo 实例对象
     * @return 影响行数
     */
    int initpassword(PiadminPojo piadminPojo);
}

