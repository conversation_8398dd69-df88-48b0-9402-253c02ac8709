package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.CibilldraftEntity;
import inks.system.domain.pojo.CibilldraftPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 单据草稿(CiBillDraft)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-26 10:17:54
 */
@Mapper
public interface CibilldraftMapper {

    CibilldraftPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    List<CibilldraftPojo> getPageList(QueryParam queryParam);

    int insert(CibilldraftEntity cibilldraftEntity);

    int update(CibilldraftEntity cibilldraftEntity);

    int delete(@Param("key") String key, @Param("tid") String tid);

    CibilldraftPojo getEntityByModuleCodeAndListerId(String modulecode, String userid, String tid);

    Integer getDraftCountByListerId(String userid, String tid);

    List<CibilldraftPojo> getDraftList(String userid, String code, String tid);
}

