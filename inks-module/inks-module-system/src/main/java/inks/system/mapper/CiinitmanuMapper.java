package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import org.apache.ibatis.annotations.Mapper;

/**
 * 初始化数据(无表)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-07-31 11:20:20
 */
@Mapper
public interface CiinitmanuMapper {

    int deleteWsItem(QueryParam queryParam);
    int deleteWs(QueryParam queryParam);
    int deleteScItem(QueryParam queryParam);
    int deleteSc(QueryParam queryParam);
    int deleteCompItem(QueryParam queryParam);
    int deleteComp(QueryParam queryParam);
    int deleteMpItem(QueryParam queryParam);
    int deleteMp(QueryParam queryParam);
    int deleteWipItem(QueryParam queryParam);
    int deleteWip(QueryParam queryParam);
    int deleteManuPtItem(QueryParam queryParam);
    int deleteManuPt(QueryParam queryParam);
    int deleteWipEpItem(QueryParam queryParam);
    int deleteWipEp(QueryParam queryParam);
    // 级联删除------------------------------------------------------------------
    int deleteWk_Worksheet(QueryParam queryParam); //D05M01B1 厂制工单
    int deleteWk_Subcontract(QueryParam queryParam); //D05M02B1 委制单据
    int deleteWk_Complete(QueryParam queryParam); //D05M03B1 生产验收/完工验收
    int deleteWk_ScDeduction(QueryParam queryParam); //D05M03B1De 委制扣款
    int deleteWk_ScComplete(QueryParam queryParam); //D05M03B1Sc 委外加工单验收
    int deleteWk_MainPlan(QueryParam queryParam); //D05M04B1 生产主计划
    int deleteWk_WipNote(QueryParam queryParam); //D05M05B1 WIP记录
    int deleteHi_WipNote(QueryParam queryParam); //D05M05H1 WIP记录
    int deleteWk_ManuReport(QueryParam queryParam); //D05M06B1 生产报工
    int deleteWk_WipQty(QueryParam queryParam); //D05M07B1 生产过数
    int deleteHi_WipQty(QueryParam queryParam); //D05M07H1 Hi生产过数
    int deleteWk_WipEpibole(QueryParam queryParam); //D05M09B1 Wip委外
    int deleteWk_WipEpFinishing(QueryParam queryParam); //D05M09B1FIN 工序收货
    int deleteWk_Mrp(QueryParam queryParam); //D05M11B1 MRP运算
    int deleteWk_SmtPart(QueryParam queryParam); //D05M12B1 SMT上料表 D05M12B2 上料记录 合并了
    int deleteWk_CostBudget(QueryParam queryParam); //D05M13B1 成本预测
    int deleteWk_MpCarryover(QueryParam queryParam); //D05M14B1 生产结转
    int deleteWk_ScCarryover(QueryParam queryParam); //D05M14B1SC 加工结转
    int deleteWk_WsCarryover(QueryParam queryParam); //D05M14B1WS 车间结转
    int deleteWk_VisualPlan(QueryParam queryParam); //D05M15B1 可视化排程
    int deleteWk_StepPrice(QueryParam queryParam); //D05M16B1 阶梯工价
    int deleteWk_StepProGroup(QueryParam queryParam); //D05M16B2 阶梯制程
    int deleteWk_StepPriceSet(QueryParam queryParam); //D05M16S1 阶梯工价设置
    int deleteWk_StationState(QueryParam queryParam); //D05M17B1 工位状态
    int deleteWk_Process(QueryParam queryParam); //D05M21S1 生产工序
    int deleteWk_ProGroup(QueryParam queryParam); //D05M21S2 生产制程
    int deleteWk_WipGroup(QueryParam queryParam); //D05M21S3 WIP设定
    int deleteWk_WipQtyRoles(QueryParam queryParam); //D05M21S4 过数角色
    int deleteWk_WipQtyGroup(QueryParam queryParam); //D05M21S5 过数小组
    int deleteWk_Station(QueryParam queryParam); //D05M21S6 工位
    int deleteWk_ProcCost(QueryParam queryParam); //D05M21S8 工序成本
    int deleteWk_ScMachType(QueryParam queryParam); //D05M21S9 委外加工类型
    int deleteWk_ScCostType(QueryParam queryParam); //D05M21S10 委外费用类型
    int deleteWk_GroupLossRate(QueryParam queryParam); //D05M21S11 车间预损率




//    int deleteWipEpFinItem(QueryParam queryParam);
//    int deleteWipEpFin(QueryParam queryParam);
//    int deleteWipDispItem(QueryParam queryParam);
//    int deleteWipDisp(QueryParam queryParam);
}



