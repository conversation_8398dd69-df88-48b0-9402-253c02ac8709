package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.PirmsfunctEntity;
import inks.system.domain.pojo.PirmsfunctPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * RMS功能(Pirmsfunct)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@Mapper
public interface PirmsfunctMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PirmsfunctPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PirmsfunctPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param pirmsfunctEntity 实例对象
     * @return 影响行数
     */
    int insert(PirmsfunctEntity pirmsfunctEntity);


    /**
     * 修改数据
     *
     * @param pirmsfunctEntity 实例对象
     * @return 影响行数
     */
    int update(PirmsfunctEntity pirmsfunctEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

