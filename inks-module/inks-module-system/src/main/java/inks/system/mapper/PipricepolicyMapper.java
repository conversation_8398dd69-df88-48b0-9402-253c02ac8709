package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PipricepolicyPojo;
import inks.system.domain.pojo.PipricepolicyitemdetailPojo;
import inks.system.domain.PipricepolicyEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 功能价格(Pipricepolicy)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-08 14:32:49
 */
@Mapper
public interface PipricepolicyMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PipricepolicyPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PipricepolicyitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PipricepolicyPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param pipricepolicyEntity 实例对象
     * @return 影响行数
     */
    int insert(PipricepolicyEntity pipricepolicyEntity);

    
    /**
     * 修改数据
     *
     * @param pipricepolicyEntity 实例对象
     * @return 影响行数
     */
    int update(PipricepolicyEntity pipricepolicyEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);
    
     /**
     * 查询 被删除的Item
     *
     * @param pipricepolicyPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(PipricepolicyPojo pipricepolicyPojo);

    /** 修改数据
     *
             * @param pipricepolicyEntity 实例对象
     * @return 影响行数
     */
    int approval(PipricepolicyEntity pipricepolicyEntity);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PipricepolicyPojo getEntityByFunction(@Param("key") String key);

}

