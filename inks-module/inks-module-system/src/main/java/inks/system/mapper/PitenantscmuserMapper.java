package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PitenantscmuserPojo;
import inks.system.domain.PitenantscmuserEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SCM租户关系表(Pitenantscmuser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@Mapper
public interface PitenantscmuserMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PitenantscmuserPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PitenantscmuserPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param pitenantscmuserEntity 实例对象
     * @return 影响行数
     */
    int insert(PitenantscmuserEntity pitenantscmuserEntity);


    /**
     * 修改数据
     *
     * @param pitenantscmuserEntity 实例对象
     * @return 影响行数
     */
    int update(PitenantscmuserEntity pitenantscmuserEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param Userid 查询条件
     * @return 对象列表
     */
    List<PitenantscmuserPojo> getListByUser(@Param("userid") String Userid);

    PitenantscmuserPojo getEntityByUserid(@Param("key") String key, @Param("tid") String tid);

}

