package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.PifunctionmenuappEntity;
import inks.system.domain.pojo.PifunctionmenuappPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 服务菜单关系app(Pifunctionmenuapp)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-11 17:34:40
 */
@Mapper
public interface PifunctionmenuappMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionmenuappPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PifunctionmenuappPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param pifunctionmenuappEntity 实例对象
     * @return 影响行数
     */
    int insert(PifunctionmenuappEntity pifunctionmenuappEntity);

    
    /**
     * 修改数据
     *
     * @param pifunctionmenuappEntity 实例对象
     * @return 影响行数
     */
    int update(PifunctionmenuappEntity pifunctionmenuappEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    List<PifunctionmenuappPojo> getListByFunction(String key);
}

