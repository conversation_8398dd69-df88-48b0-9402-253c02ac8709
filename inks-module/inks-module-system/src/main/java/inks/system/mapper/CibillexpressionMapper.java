package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CibillexpressionPojo;
import inks.system.domain.CibillexpressionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 单据公式(Cibillexpression)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-29 10:52:48
 */
@Mapper
public interface CibillexpressionMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CibillexpressionPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CibillexpressionPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param cibillexpressionEntity 实例对象
     * @return 影响行数
     */
    int insert(CibillexpressionEntity cibillexpressionEntity);

    
    /**
     * 修改数据
     *
     * @param cibillexpressionEntity 实例对象
     * @return 影响行数
     */
    int update(CibillexpressionEntity cibillexpressionEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    List<CibillexpressionPojo> getListByCode(@Param("key") String key,@Param("tid") String tid);
}

