package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiconfigPojo;
import inks.system.domain.CiconfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统参数(Ciconfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-27 18:30:51
 */
@Mapper
public interface CiconfigMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiconfigPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CiconfigPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param ciconfigEntity 实例对象
     * @return 影响行数
     */
    int insert(CiconfigEntity ciconfigEntity);


    /**
     * 修改数据
     *
     * @param ciconfigEntity 实例对象
     * @return 影响行数
     */
    int update(CiconfigEntity ciconfigEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiconfigPojo getEntityByKey(@Param("key") String key,@Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiconfigPojo getEntityByKeyUser(@Param("key") String key,@Param("userid") String userid,@Param("tid") String tid);
    /**
     * 通过key获得值
     *
     * @param tid 主键
     * @return 值
     */
    List<CiconfigPojo> getListByTenant(@Param("tid") String tid);

    /**
     * 通过key获得值
     *
     * @param tid 主键
     * @return 值
     */
    List<CiconfigPojo> getListByTenUi(@Param("tid") String tid);

    /**
     * 通过key获得值
     *
     * @param tid 主键
     * @return 值
     */
    List<CiconfigPojo> getListByUserUi(@Param("userid") String userid ,@Param("tid") String tid);


    /**
     * 通过key获得值
     *
     * @return 值
     */
    List<CiconfigPojo>  getListByDefault();

    /**
     * 通过key获得值
     *
     * @return 值
     */
    List<CiconfigPojo>  getListByPrefix(@Param("key") String key,@Param("tid") String tid);

    int unCheckOnlineInTids(String tid);

    List<CiconfigPojo> getMicroAppList();
}
