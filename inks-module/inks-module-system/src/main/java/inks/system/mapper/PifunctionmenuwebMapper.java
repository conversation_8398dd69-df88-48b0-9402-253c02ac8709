package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.PifunctionmenuwebEntity;
import inks.system.domain.pojo.PifunctionmenuwebPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 服务菜单关系(Pifunctionmenuweb)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-11 17:34:48
 */
@Mapper
public interface PifunctionmenuwebMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionmenuwebPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PifunctionmenuwebPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param pifunctionmenuwebEntity 实例对象
     * @return 影响行数
     */
    int insert(PifunctionmenuwebEntity pifunctionmenuwebEntity);

    
    /**
     * 修改数据
     *
     * @param pifunctionmenuwebEntity 实例对象
     * @return 影响行数
     */
    int update(PifunctionmenuwebEntity pifunctionmenuwebEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    List<PifunctionmenuwebPojo> getListByFunction(String key);

}

