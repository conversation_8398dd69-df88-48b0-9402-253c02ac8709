package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CifninvoicePojo;
import inks.system.domain.pojo.CifninvoiceitemdetailPojo;
import inks.system.domain.CifninvoiceEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 开票管理(Cifninvoice)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-19 20:01:41
 */
@Mapper
public interface CifninvoiceMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CifninvoicePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CifninvoiceitemdetailPojo> getPageList(QueryParam queryParam);

    
        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CifninvoicePojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param cifninvoiceEntity 实例对象
     * @return 影响行数
     */
    int insert(CifninvoiceEntity cifninvoiceEntity);

    
    /**
     * 修改数据
     *
     * @param cifninvoiceEntity 实例对象
     * @return 影响行数
     */
    int update(CifninvoiceEntity cifninvoiceEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);
    
     /**
     * 查询 被删除的Item
     *
     * @param cifninvoicePojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(CifninvoicePojo cifninvoicePojo);


                                                                                                                                                                                    }

