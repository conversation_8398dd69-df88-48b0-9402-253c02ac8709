package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CidictitemPojo;
import inks.system.domain.CidictitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 字典项目(Cidictitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-12 07:38:58
 */
 @Mapper
public interface CidictitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CidictitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CidictitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<CidictitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param cidictitemEntity 实例对象
     * @return 影响行数
     */
    int insert(CidictitemEntity cidictitemEntity);

    
    /**
     * 修改数据
     *
     * @param cidictitemEntity 实例对象
     * @return 影响行数
     */
    int update(CidictitemEntity cidictitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

}

