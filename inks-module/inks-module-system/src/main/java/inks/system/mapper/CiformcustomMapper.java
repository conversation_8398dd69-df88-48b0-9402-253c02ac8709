package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiformcustomPojo;
import inks.system.domain.CiformcustomEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 自定义界面(Ciformcustom)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-06 12:57:59
 */
@Mapper
public interface CiformcustomMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiformcustomPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CiformcustomPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param ciformcustomEntity 实例对象
     * @return 影响行数
     */
    int insert(CiformcustomEntity ciformcustomEntity);


    /**
     * 修改数据
     *
     * @param ciformcustomEntity 实例对象
     * @return 影响行数
     */
    int update(CiformcustomEntity ciformcustomEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiformcustomPojo getEntityByCode(@Param("key") String key,@Param("tid") String tid);

}

