package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CitextgeneratorPojo;
import inks.system.domain.CitextgeneratorEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 文本生成器(Citextgenerator)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-12-18 15:00:22
 */
@Mapper
public interface CitextgeneratorMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CitextgeneratorPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CitextgeneratorPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param citextgeneratorEntity 实例对象
     * @return 影响行数
     */
    int insert(CitextgeneratorEntity citextgeneratorEntity);

    
    /**
     * 修改数据
     *
     * @param citextgeneratorEntity 实例对象
     * @return 影响行数
     */
    int update(CitextgeneratorEntity citextgeneratorEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    int orderByRowNum(@Param("ids") List<String> ids, @Param("tid") String tid);

    List<CitextgeneratorPojo> getListByid(@Param("id") String id, @Param("tid") String tid);

    List<CitextgeneratorPojo> getListByParentid(@Param("parentId") String parentId, @Param("tid") String tid);
}

