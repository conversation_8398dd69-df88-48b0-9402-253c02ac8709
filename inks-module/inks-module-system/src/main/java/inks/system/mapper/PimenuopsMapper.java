package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PimenuopsPojo;
import inks.system.domain.PimenuopsEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * Ops导航(Pimenuops)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-04-24 17:07:52
 */
@Mapper
public interface PimenuopsMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PimenuopsPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PimenuopsPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param pimenuopsEntity 实例对象
     * @return 影响行数
     */
    int insert(PimenuopsEntity pimenuopsEntity);

    
    /**
     * 修改数据
     *
     * @param pimenuopsEntity 实例对象
     * @return 影响行数
     */
    int update(PimenuopsEntity pimenuopsEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<PimenuopsPojo> getListByPid(String key);
}

