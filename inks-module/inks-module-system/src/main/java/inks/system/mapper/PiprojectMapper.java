package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiprojectPojo;
import inks.system.domain.pojo.PiprojectitemdetailPojo;
import inks.system.domain.PiprojectEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目(Piproject)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-20 12:51:52
 */
@Mapper
public interface PiprojectMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiprojectPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PiprojectitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PiprojectPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param piprojectEntity 实例对象
     * @return 影响行数
     */
    int insert(PiprojectEntity piprojectEntity);


    /**
     * 修改数据
     *
     * @param piprojectEntity 实例对象
     * @return 影响行数
     */
    int update(PiprojectEntity piprojectEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 查询 被删除的Item
     *
     * @param piprojectPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(PiprojectPojo piprojectPojo);
}

