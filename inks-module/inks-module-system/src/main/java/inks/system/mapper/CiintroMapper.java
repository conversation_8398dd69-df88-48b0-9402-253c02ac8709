package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiintroPojo;
import inks.system.domain.CiintroEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 功能简介(Ciintro)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-07-07 20:44:33
 */
@Mapper
public interface CiintroMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiintroPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CiintroPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param ciintroEntity 实例对象
     * @return 影响行数
     */
    int insert(CiintroEntity ciintroEntity);


    /**
     * 修改数据
     *
     * @param ciintroEntity 实例对象
     * @return 影响行数
     */
    int update(CiintroEntity ciintroEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 通过ID查询单条数据
     *
     * @param code 主键
     * @return 实例对象
     */
    CiintroPojo getEntityByCode(@Param("code") String code);

}

