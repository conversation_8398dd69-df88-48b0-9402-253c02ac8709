package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.PifunctionpermEntity;
import inks.system.domain.pojo.PifunctionpermPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashSet;
import java.util.List;

/**
 * 服务权限关系(Pifunctionperm)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-11 17:34:56
 */
@Mapper
public interface PifunctionpermMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionpermPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PifunctionpermPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param pifunctionpermEntity 实例对象
     * @return 影响行数
     */
    int insert(PifunctionpermEntity pifunctionpermEntity);

    
    /**
     * 修改数据
     *
     * @param pifunctionpermEntity 实例对象
     * @return 影响行数
     */
    int update(PifunctionpermEntity pifunctionpermEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);


    List<PifunctionpermPojo> getListByFunction(String key);

    HashSet<String> getFunctionPermsByFunctionCode(String functioncode, String tid);
}

