package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PifunctionPojo;
import inks.system.domain.PifunctionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 服务总表(Pifunction)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-29 20:02:56
 */
@Mapper
public interface PifunctionMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PifunctionPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param pifunctionEntity 实例对象
     * @return 影响行数
     */
    int insert(PifunctionEntity pifunctionEntity);


    /**
     * 修改数据
     *
     * @param pifunctionEntity 实例对象
     * @return 影响行数
     */
    int update(PifunctionEntity pifunctionEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<PifunctionPojo> getFunctionListBySelf(String tenantid);
}

