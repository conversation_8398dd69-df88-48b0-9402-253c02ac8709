package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CibillgroupPojo;
import inks.system.domain.CibillgroupEntity;
import inks.system.domain.pojo.CireportsPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 通用分组(Cibillgroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-08 13:28:25
 */
@Mapper
public interface CibillgroupMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CibillgroupPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CibillgroupPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param cibillgroupEntity 实例对象
     * @return 影响行数
     */
    int insert(CibillgroupEntity cibillgroupEntity);

    
    /**
     * 修改数据
     *
     * @param cibillgroupEntity 实例对象
     * @return 影响行数
     */
    int update(CibillgroupEntity cibillgroupEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);


    List<CibillgroupPojo> getListByModuleCode(@Param("moduleCode") String moduleCode, @Param("tid") String tid);

}

