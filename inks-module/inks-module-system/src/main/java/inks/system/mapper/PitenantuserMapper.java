package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PitenantPojo;
import inks.system.domain.pojo.PitenantuserPojo;
import inks.system.domain.PitenantuserEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 租户关系表(Pitenantuser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-08 14:31:53
 */
@Mapper
public interface PitenantuserMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PitenantuserPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PitenantuserPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param pitenantuserEntity 实例对象
     * @return 影响行数
     */
    int insert(PitenantuserEntity pitenantuserEntity);


    /**
     * 修改数据
     *
     * @param pitenantuserEntity 实例对象
     * @return 影响行数
     */
    int update(PitenantuserEntity pitenantuserEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    List<PitenantuserPojo> getListByTenant(String key);

    List<PitenantuserPojo> getListByUser(String key);

    List<PitenantuserPojo> getListByUserName(String username);
    /**
     * 通过ID查询单条数据
     *
     * @param userid 主键
     * @return 实例对象
     */
    PitenantuserPojo getEntityByUser(@Param("userid") String userid, @Param("tid") String tid);


    List<Map<String, Object>> getCountRecord(@Param("tables") List<String> tables, @Param("tid") String tid);
}

