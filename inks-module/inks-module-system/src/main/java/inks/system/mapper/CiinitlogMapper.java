package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiinitlogPojo;
import inks.system.domain.CiinitlogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 初始化日志(Ciinitlog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-07-31 11:20:20
 */
@Mapper
public interface CiinitlogMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiinitlogPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CiinitlogPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param ciinitlogEntity 实例对象
     * @return 影响行数
     */
    int insert(CiinitlogEntity ciinitlogEntity);


    /**
     * 修改数据
     *
     * @param ciinitlogEntity 实例对象
     * @return 影响行数
     */
    int update(CiinitlogEntity ciinitlogEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);


}

