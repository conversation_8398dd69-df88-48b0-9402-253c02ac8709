package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.PifunctiondashEntity;
import inks.system.domain.pojo.PifunctiondashPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 服务工作台(Pifunctiondash)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-07-15 21:13:35
 */
@Mapper
public interface PifunctiondashMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctiondashPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PifunctiondashPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param pifunctiondashEntity 实例对象
     * @return 影响行数
     */
    int insert(PifunctiondashEntity pifunctiondashEntity);


    /**
     * 修改数据
     *
     * @param pifunctiondashEntity 实例对象
     * @return 影响行数
     */
    int update(PifunctiondashEntity pifunctiondashEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<PifunctiondashPojo> getListByFunction(@Param("key")String key);
}

