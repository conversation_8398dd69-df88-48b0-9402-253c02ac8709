package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiscenefieldPojo;
import inks.system.domain.CiscenefieldEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 场景字段(Ciscenefield)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-11-24 11:35:06
 */
@Mapper
public interface CiscenefieldMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiscenefieldPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CiscenefieldPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param ciscenefieldEntity 实例对象
     * @return 影响行数
     */
    int insert(CiscenefieldEntity ciscenefieldEntity);


    /**
     * 修改数据
     *
     * @param ciscenefieldEntity 实例对象
     * @return 影响行数
     */
    int update(CiscenefieldEntity ciscenefieldEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param code 查询条件
     * @return 对象列表
     */
    List<CiscenefieldPojo> getListByCode(@Param("code") String code);

}

