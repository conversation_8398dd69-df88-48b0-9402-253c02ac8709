package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.PifunctionwarnEntity;
import inks.system.domain.pojo.PifunctionwarnPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 服务预警关系(Pifunctionwarn)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-07-05 20:11:43
 */
@Mapper
public interface PifunctionwarnMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionwarnPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PifunctionwarnPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param pifunctionwarnEntity 实例对象
     * @return 影响行数
     */
    int insert(PifunctionwarnEntity pifunctionwarnEntity);


    /**
     * 修改数据
     *
     * @param pifunctionwarnEntity 实例对象
     * @return 影响行数
     */
    int update(PifunctionwarnEntity pifunctionwarnEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<PifunctionwarnPojo> getListByFunction(@Param("key")String key);

}

