package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PiadminloginPojo;
import inks.system.domain.PiadminloginEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 管理员登录(Piadminlogin)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-08 13:55:42
 */
@Mapper
public interface PiadminloginMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiadminloginPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PiadminloginPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param piadminloginEntity 实例对象
     * @return 影响行数
     */
    int insert(PiadminloginEntity piadminloginEntity);

    
    /**
     * 修改数据
     *
     * @param piadminloginEntity 实例对象
     * @return 影响行数
     */
    int update(PiadminloginEntity piadminloginEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

