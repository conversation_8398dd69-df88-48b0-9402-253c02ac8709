package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiformvailditemPojo;
import inks.system.domain.CiformvailditemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 窗体验证子表(Ciformvailditem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-31 17:04:04
 */
 @Mapper
public interface CiformvailditemMapper {

    CiformvailditemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<CiformvailditemPojo> getPageList(QueryParam queryParam);

    List<CiformvailditemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
 
    int insert(CiformvailditemEntity ciformvailditemEntity);

    int update(CiformvailditemEntity ciformvailditemEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

}

