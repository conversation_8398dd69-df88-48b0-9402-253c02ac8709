package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.PiuserroleEntity;
import inks.system.domain.pojo.PipermcodePojo;
import inks.system.domain.pojo.PiuserrolePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色关系表(Piuserrole)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-08 15:26:53
 */
@Mapper
public interface PiuserroleMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiuserrolePojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PiuserrolePojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param piuserroleEntity 实例对象
     * @return 影响行数
     */
    int insert(PiuserroleEntity piuserroleEntity);


    /**
     * 修改数据
     *
     * @param piuserroleEntity 实例对象
     * @return 影响行数
     */
    int update(PiuserroleEntity piuserroleEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);


    List<PiuserrolePojo> getListByRole(String key);

    List<PiuserrolePojo> getListByUser(@Param("key") String key, @Param("tid") String tid);

    List<PipermcodePojo> getPermByUser(@Param("key") String key, @Param("tid") String tid);

    // resourceid就是roleid
    List<String> getUseridsByRoleid(@Param("resourceid") String resourceid, @Param("tid") String tid);

    List<String> getRoleidsByUserid(@Param("userid") String userid, @Param("tid") String tid);
}

