package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.PisubscriberEntity;
import inks.system.domain.pojo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订阅信息表(Pisubscriber)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-10 14:17:25
 */
@Mapper
public interface PisubscriberMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PisubscriberPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PisubscriberPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param pisubscriberEntity 实例对象
     * @return 影响行数
     */
    int insert(PisubscriberEntity pisubscriberEntity);

    
    /**
     * 修改数据
     *
     * @param pisubscriberEntity 实例对象
     * @return 影响行数
     */
    int update(PisubscriberEntity pisubscriberEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PisubscriberPojo> getPageListByTenant(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PisubscriberPojo getEntityByFunction(@Param("key") String key,@Param("tid") String tid,@Param("date") String date);

    /**
     * 查询指定行数据
     *
     * @param key 查询条件
     * @return 对象列表
     */
    List<PimenuwebPojo> getMenuWebListByTenant(@Param("key") String key,@Param("date") String date);

    /**
     * 查询指定行数据
     *
     * @param key 查询条件
     * @param fncode 服务编码
     * @return 对象列表
     */
    List<PimenuwebPojo> getMenuWebListByTenFnCode(@Param("key") String key,@Param("date") String date,@Param("fncode") String fncode);

    List<PimenufrmPojo> getMenuFrmListByTenant(String key, String date);

    List<PimenufrmPojo> getMenuFrmListByTenFnCode(String key, String date, String fncode);
    /**
     * 查询指定行数据
     *
     * @param key 查询条件
     * @return 对象列表
     */
    List<PimenuappPojo> getMenuAppListByTenant(@Param("key") String key,@Param("date") String date);

    List<PiweblnkPojo> getWebLnkListByTenant(@Param("key") String key,@Param("date") String date);
    List<PiwebnavPojo> getWebNavListByTenant(@Param("key") String key,@Param("date") String date);

    List<PiweblnkPojo> getWebLnkListByTenFnCode(@Param("key") String key,@Param("date") String date,@Param("fncode") String fncode);
    List<PiwebnavPojo> getWebNavListByTenFnCode(@Param("key") String key,@Param("date") String date,@Param("fncode") String fncode);
    /**
     * 查询指定行数据
     *
     * @param key 查询条件
     * @return 对象列表
     */
    List<PipermcodePojo> getPermAllByTenant(@Param("key") String key,@Param("date") String date);

    /**
     * 查询指定行数据
     *
     * @param key 查询条件
     * @return 对象列表
     */
    List<CiconfigPojo> getDefConfigByTenant(@Param("key") String key,@Param("date") String date);

    /**
     * 查询指定行数据
     *
     * @param key 查询条件
     * @return 对象列表
     */
    List<CiconfigPojo> getTenConfigByTenant(@Param("key") String key,@Param("date") String date);
    /**
     * 查询指定行数据
     *
     * @param key 查询条件
     * @return 对象列表
     */
    List<PirolePojo> getDefRoleByTenant(@Param("key") String key,@Param("date") String date);


    /**
     * 查询指定行数据
     *
     * @param key 查询条件
     * @return 对象列表
     */
    List<CibigdataPojo> getBigDataByTenant(@Param("key") String key,@Param("date") String date);

    /**
     * 查询指定行数据
     *
     * @param key 查询条件
     * @return 对象列表
     */
    List<CiwarningPojo> getWarnByTenant(@Param("key") String key,@Param("date") String date);

    /**
     * 查询指定行数据
     *
     * @param key 查询条件
     * @return 对象列表
     */
    List<CidashboardPojo> getDashByTenant(@Param("key") String key,@Param("date") String date);


    /**
     * 查询指定行数据
     *
     * @param key 查询条件
     * @return 对象列表
     */
    List<CireportsPojo> getReportsByTenant(@Param("key") String key,@Param("date") String date);



}

