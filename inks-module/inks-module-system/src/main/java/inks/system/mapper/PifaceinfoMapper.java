//package inks.system.mapper;
//
//import inks.common.core.domain.QueryParam;
//import inks.system.config.face.entity.FaceInfoBo;
//import inks.system.domain.pojo.PifaceinfoPojo;
//import inks.system.domain.PifaceinfoEntity;
//import org.apache.ibatis.annotations.Mapper;
//import org.apache.ibatis.annotations.Param;
//import org.apache.ibatis.executor.BatchResult;
//
//import java.util.List;
//
///**
// * 人脸信息表(PiFaceInfo)表数据库访问层
// *
// * <AUTHOR>
// * @since 2024-11-19 17:02:10
// */
//@Mapper
//public interface PifaceinfoMapper {
//
//    PifaceinfoPojo getEntity(@Param("key") String key,@Param("tid") String tid);
//
//    List<PifaceinfoPojo> getPageList(QueryParam queryParam);
//
//    int insert(PifaceinfoEntity pifaceinfoEntity);
//
//    int update(PifaceinfoEntity pifaceinfoEntity);
//
//    int delete(@Param("key") String key,@Param("tid") String tid);
//
//    int insertBatch(@Param("faceInfoList") List<PifaceinfoPojo> faceInfoList);
//
//    List<FaceInfoBo> queryAllList();
//}
//
