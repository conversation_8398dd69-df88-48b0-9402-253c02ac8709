package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PifunctionmenufrmPojo;
import inks.system.domain.PifunctionmenufrmEntity;
import inks.system.domain.pojo.PifunctionmenuwebPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 服务Frm关系(PiFunctionMenuFrm)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-05 16:28:10
 */
@Mapper
public interface PifunctionmenufrmMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionmenufrmPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PifunctionmenufrmPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param pifunctionmenufrmEntity 实例对象
     * @return 影响行数
     */
    int insert(PifunctionmenufrmEntity pifunctionmenufrmEntity);

    
    /**
     * 修改数据
     *
     * @param pifunctionmenufrmEntity 实例对象
     * @return 影响行数
     */
    int update(PifunctionmenufrmEntity pifunctionmenufrmEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    List<PifunctionmenuwebPojo> getListByFunction(String key);
}

