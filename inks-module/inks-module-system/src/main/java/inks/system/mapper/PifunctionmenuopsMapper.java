package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PifunctionmenuopsPojo;
import inks.system.domain.PifunctionmenuopsEntity;
import inks.system.domain.pojo.PifunctionmenuwebPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 服务Form关系(PiFunctionMenuOps)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-04-24 17:03:52
 */
@Mapper
public interface PifunctionmenuopsMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PifunctionmenuopsPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PifunctionmenuopsPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param pifunctionmenuopsEntity 实例对象
     * @return 影响行数
     */
    int insert(PifunctionmenuopsEntity pifunctionmenuopsEntity);

    
    /**
     * 修改数据
     *
     * @param pifunctionmenuopsEntity 实例对象
     * @return 影响行数
     */
    int update(PifunctionmenuopsEntity pifunctionmenuopsEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    List<PifunctionmenuwebPojo> getListByFunction(String key);
}

