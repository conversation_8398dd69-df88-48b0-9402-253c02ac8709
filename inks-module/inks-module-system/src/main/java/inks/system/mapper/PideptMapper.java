package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PideptPojo;
import inks.system.domain.PideptEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户组织架构(Pidept)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-03-28 11:10:59
 */
@Mapper
public interface PideptMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PideptPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PideptPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param pideptEntity 实例对象
     * @return 影响行数
     */
    int insert(PideptEntity pideptEntity);


    /**
     * 修改数据
     *
     * @param pideptEntity 实例对象
     * @return 影响行数
     */
    int update(PideptEntity pideptEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);


    /**
     * 查询指定行数据
     *
     * @param key 主键
     * @return 对象列表
     */
    List<PideptPojo> getListByParentid(@Param("key") String key, @Param("tid") String tid);

}

