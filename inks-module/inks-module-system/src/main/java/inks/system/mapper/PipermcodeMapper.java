package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PipermcodePojo;
import inks.system.domain.PipermcodeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 权限编码表(Pipermcode)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-12-27 10:01:53
 */
@Mapper
public interface PipermcodeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PipermcodePojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PipermcodePojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param pipermcodeEntity 实例对象
     * @return 影响行数
     */
    int insert(PipermcodeEntity pipermcodeEntity);

    
    /**
     * 修改数据
     *
     * @param pipermcodeEntity 实例对象
     * @return 影响行数
     */
    int update(PipermcodeEntity pipermcodeEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

