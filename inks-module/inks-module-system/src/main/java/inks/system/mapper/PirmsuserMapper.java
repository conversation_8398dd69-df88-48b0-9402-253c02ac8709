package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.PirmsuserEntity;
import inks.system.domain.pojo.PirmsuserPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * RMS用户(Pirmsuser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-21 19:08:54
 */
@Mapper
public interface PirmsuserMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PirmsuserPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PirmsuserPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param pirmsuserEntity 实例对象
     * @return 影响行数
     */
    int insert(PirmsuserEntity pirmsuserEntity);

    
    /**
     * 修改数据
     *
     * @param pirmsuserEntity 实例对象
     * @return 影响行数
     */
    int update(PirmsuserEntity pirmsuserEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key ,@Param("tid") String tid);

    PirmsuserPojo getEntityByUserName(@Param("username")String username);

    List<PirmsuserPojo> getPageListByTen(QueryParam queryParam);

    PirmsuserPojo getEntityByOpenid(@Param("openid")String openid, @Param("tid")String tid);

    List<PirmsuserPojo> getListByOpenid(String openid);

    PirmsuserPojo getEntityByUserid(@Param("userid")String userid, @Param("tid")String tid);

    void deletePiTenantRmsUser(@Param("userid") String key, @Param("tid") String tid);
}

