package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PirolemenuwebPojo;
import inks.system.domain.PirolemenuwebEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 角色菜单Web(PiRoleMenuWeb)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-11 13:55:54
 */
@Mapper
public interface PirolemenuwebMapper {

    PirolemenuwebPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    List<PirolemenuwebPojo> getPageList(QueryParam queryParam);

    int insert(PirolemenuwebEntity pirolemenuwebEntity);

    int update(PirolemenuwebEntity pirolemenuwebEntity);

    int delete(@Param("key") String key,@Param("tid") String tid);

    List<String> getNavidsByUserid(String userid, String tid);

    List<String> getNavidsByRoleid(String roleid, String tid);

    int deleteByRoleidAndNavid(String roleid, String navid, String tid);

    Integer batchDelete(@Param("roleid") String roleid, @Param("deleteNavids") List<String> deleteNavids, @Param("tid") String tid);

    Integer batchInsert(@Param("pirolemenuwebPojoList") List<PirolemenuwebPojo> pirolemenuwebPojoList);

    List<PirolemenuwebPojo> getListByRole(String key, String tid);
}

