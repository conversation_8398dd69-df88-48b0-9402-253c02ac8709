package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.CiwarningPojo;
import inks.system.domain.CiwarningEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 预警项目(Ciwarning)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-07-05 20:11:00
 */
@Mapper
public interface CiwarningMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    CiwarningPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<CiwarningPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param ciwarningEntity 实例对象
     * @return 影响行数
     */
    int insert(CiwarningEntity ciwarningEntity);

    
    /**
     * 修改数据
     *
     * @param ciwarningEntity 实例对象
     * @return 影响行数
     */
    int update(CiwarningEntity ciwarningEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);
    
                                                                                                              }

