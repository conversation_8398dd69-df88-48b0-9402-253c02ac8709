package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PipricepolicyitemPojo;
import inks.system.domain.PipricepolicyitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 价格项目(Pipricepolicyitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2021-11-10 15:33:38
 */
 @Mapper
public interface PipricepolicyitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PipricepolicyitemPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PipricepolicyitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<PipricepolicyitemPojo> getList(@Param("Pid") String Pid);
     
    
    /**
     * 新增数据
     *
     * @param pipricepolicyitemEntity 实例对象
     * @return 影响行数
     */
    int insert(PipricepolicyitemEntity pipricepolicyitemEntity);

    
    /**
     * 修改数据
     *
     * @param pipricepolicyitemEntity 实例对象
     * @return 影响行数
     */
    int update(PipricepolicyitemEntity pipricepolicyitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

