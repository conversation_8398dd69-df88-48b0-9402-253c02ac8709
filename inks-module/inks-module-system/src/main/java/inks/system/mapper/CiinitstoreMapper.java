package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import org.apache.ibatis.annotations.Mapper;

/**
 * 初始化数据(无表)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-07-31 11:20:20
 */
@Mapper
public interface CiinitstoreMapper {

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteAcceItem(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteAcce(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteRequItem(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteRequ(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteReqRItem(QueryParam queryParam);

    /**
     * 通过主键删除数据
     *
     * @return 影响行数
     */
    int deleteReqR(QueryParam queryParam);
}



