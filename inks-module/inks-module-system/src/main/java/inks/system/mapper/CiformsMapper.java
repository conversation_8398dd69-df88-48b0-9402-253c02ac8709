package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.CiformsEntity;
import inks.system.domain.pojo.CiformsPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 窗体中心(CiForms)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-18 15:43:44
 */
@Mapper
public interface CiformsMapper {

    CiformsPojo getEntity(@Param("key") String key);

    List<CiformsPojo> getPageList(QueryParam queryParam);

    List<CiformsPojo> getPageListAll(QueryParam queryParam);

    int insert(CiformsEntity ciformsEntity);

    int update(CiformsEntity ciformsEntity);

    int delete(@Param("key") String key);

    List<CiformsPojo> getListByCode(String key, String tid);

    CiformsPojo getEntityByCode(String key, int type, String tid);

}

