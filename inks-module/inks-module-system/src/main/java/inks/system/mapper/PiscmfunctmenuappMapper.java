package inks.system.mapper;

import inks.common.core.domain.QueryParam;
import inks.system.domain.pojo.PimenuappPojo;
import inks.system.domain.pojo.PiscmfunctmenuappPojo;
import inks.system.domain.PiscmfunctmenuappEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * SCMAPP关系(Piscmfunctmenuapp)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@Mapper
public interface PiscmfunctmenuappMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    PiscmfunctmenuappPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<PiscmfunctmenuappPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param piscmfunctmenuappEntity 实例对象
     * @return 影响行数
     */
    int insert(PiscmfunctmenuappEntity piscmfunctmenuappEntity);

    
    /**
     * 修改数据
     *
     * @param piscmfunctmenuappEntity 实例对象
     * @return 影响行数
     */
    int update(PiscmfunctmenuappEntity piscmfunctmenuappEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<PiscmfunctmenuappPojo> getListByFunction(String key);

    List<PimenuappPojo> getListByScmFunctids(String ids);
}

