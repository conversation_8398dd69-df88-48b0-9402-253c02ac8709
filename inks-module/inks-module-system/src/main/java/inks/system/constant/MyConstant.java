package inks.system.constant;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 常量
 */
public interface MyConstant {


    //"refno_lock:" 生成(释放)编码的Redis锁   String redisLock_Key = MyConstant.REFNO_LOCK + moduleCode + tid;
    String REFNO_LOCK = "refno_lock:";



    // Bus_Machining, Bus_Delivery, Buy_Order, Mat_Access, Wk_WipNote, Wk_Worksheet
    List<String> FN_OMS_TABLES = Arrays.asList(
            "Bus_Machining",
            "Bus_Deliery",
            "Buy_Order",
            "Mat_Access",
            "Wk_WipNote",
            "Wk_Worksheet"
    );
    // 表名和注释的映射
    Map<String, String> TABLE_NAME_COMMENTS = new HashMap<String, String>() {{
        put("Bus_Machining", "销售订单");
        put("Bus_Deliery", "发货单");
        put("Buy_Order", "采购订单");
        put("Mat_Access", "出入库单");
        put("Wk_WipNote", "WIP");
        put("Wk_Worksheet", "生产加工单");
    }};

    // 你可以根据需要添加更多的常量表名列表： fn系统名对应的表名列表
    Map<String, List<String>> FN_TABLES_MAP = new HashMap<String, List<String>>() {{
        put("oms", FN_OMS_TABLES);
        // 如果以后有更多的 fn 值，可以继续添加：
        // put("mes", FN_MES_TABLES);
    }};

}
