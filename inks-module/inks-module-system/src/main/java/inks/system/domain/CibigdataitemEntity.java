package inks.system.domain;

import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 服务大屏关系(Cibigdataitem)Entity
 *
 * <AUTHOR>
 * @since 2021-12-25 10:38:39
 */
public class CibigdataitemEntity implements Serializable {
    private static final long serialVersionUID = 977876397240711366L;
          // id
         private String id;
          // 大屏id
         private String pid;
          // 服务id
         private String functionid;
          // 服务编码
         private String functioncode;
          // 服务名称
         private String functionname;
          // 行号
         private Integer rownum;
          // 备注
         private String remark;
          // 乐观锁
         private Integer revision;

    // id
      public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
    // 大屏id
      public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
    // 服务id
      public String getFunctionid() {
        return functionid;
    }
    
    public void setFunctionid(String functionid) {
        this.functionid = functionid;
    }
        
    // 服务编码
      public String getFunctioncode() {
        return functioncode;
    }
    
    public void setFunctioncode(String functioncode) {
        this.functioncode = functioncode;
    }
        
    // 服务名称
      public String getFunctionname() {
        return functionname;
    }
    
    public void setFunctionname(String functionname) {
        this.functionname = functionname;
    }
        
    // 行号
      public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
    // 备注
      public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
    // 乐观锁
      public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

