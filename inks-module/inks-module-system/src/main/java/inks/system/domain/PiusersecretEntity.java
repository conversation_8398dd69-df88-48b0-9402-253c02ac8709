package inks.system.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 用户授权key(Piusersecret)实体类
 *
 * <AUTHOR>
 * @since 2022-12-28 09:52:24
 */
public class PiusersecretEntity implements Serializable {
    private static final long serialVersionUID = 824758330389989475L;
         // ID
         private String id;
         // 授权key
         private String secretcode;
         // 用户ID
         private String userid;
         // 启用IP访问限制
         private Integer checkipaddr;
         // IP地址
         private String ipaddress;
         // MAC地址
         private String macaddress;
         // 第一次登录
         private Date firstvisit;
         // 上一次登录
         private Date previouvisit;
         // 浏览器名称
         private String browsername;
         // 操作系统
         private String hostsystem;
         // 创建者
         private String createby;
         // 创建者id
         private String createbyid;
         // 新建日期
         private Date createdate;
         // 制表
         private String lister;
         // 制表id
         private String listerid;
         // 修改日期
         private Date modifydate;
         // 租户id
         private String tenantid;
         // 租户名称
         private String tenantname;
         // 乐观锁
         private Integer revision;

// ID
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 授权key
    public String getSecretcode() {
        return secretcode;
    }
    
    public void setSecretcode(String secretcode) {
        this.secretcode = secretcode;
    }
        
// 用户ID
    public String getUserid() {
        return userid;
    }
    
    public void setUserid(String userid) {
        this.userid = userid;
    }
        
// 启用IP访问限制
    public Integer getCheckipaddr() {
        return checkipaddr;
    }
    
    public void setCheckipaddr(Integer checkipaddr) {
        this.checkipaddr = checkipaddr;
    }
        
// IP地址
    public String getIpaddress() {
        return ipaddress;
    }
    
    public void setIpaddress(String ipaddress) {
        this.ipaddress = ipaddress;
    }
        
// MAC地址
    public String getMacaddress() {
        return macaddress;
    }
    
    public void setMacaddress(String macaddress) {
        this.macaddress = macaddress;
    }
        
// 第一次登录
    public Date getFirstvisit() {
        return firstvisit;
    }
    
    public void setFirstvisit(Date firstvisit) {
        this.firstvisit = firstvisit;
    }
        
// 上一次登录
    public Date getPreviouvisit() {
        return previouvisit;
    }
    
    public void setPreviouvisit(Date previouvisit) {
        this.previouvisit = previouvisit;
    }
        
// 浏览器名称
    public String getBrowsername() {
        return browsername;
    }
    
    public void setBrowsername(String browsername) {
        this.browsername = browsername;
    }
        
// 操作系统
    public String getHostsystem() {
        return hostsystem;
    }
    
    public void setHostsystem(String hostsystem) {
        this.hostsystem = hostsystem;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

