package inks.system.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 登录日志(Ciloginlog)实体类
 *
 * <AUTHOR>
 * @since 2023-05-29 08:52:38
 */
public class CiloginlogEntity implements Serializable {
    private static final long serialVersionUID = -64506691904648351L;
         // id
         private String id;
         // 用户ID
         private String userid;
         // 登录号
         private String username;
         // 中文名
         private String realname;
         // 主机IP
         private String ipaddr;
         // 主机地址
         private String loginlocation;
         // 浏览器名称
         private String browsername;
         // 操作系统
         private String hostsystem;
         // 登录/登出
         private String direction;
         // 登录状态0成功 1失败
         private Integer loginstatus;
         // 操作信息
         private String loginmsg;
         // 访问时间
         private Date logintime;
         // 租户id
         private String tenantid;
         // 租户名称
         private String tenantname;

// id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 用户ID
    public String getUserid() {
        return userid;
    }
    
    public void setUserid(String userid) {
        this.userid = userid;
    }
        
// 登录号
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
        
// 中文名
    public String getRealname() {
        return realname;
    }
    
    public void setRealname(String realname) {
        this.realname = realname;
    }
        
// 主机IP
    public String getIpaddr() {
        return ipaddr;
    }
    
    public void setIpaddr(String ipaddr) {
        this.ipaddr = ipaddr;
    }
        
// 主机地址
    public String getLoginlocation() {
        return loginlocation;
    }
    
    public void setLoginlocation(String loginlocation) {
        this.loginlocation = loginlocation;
    }
        
// 浏览器名称
    public String getBrowsername() {
        return browsername;
    }
    
    public void setBrowsername(String browsername) {
        this.browsername = browsername;
    }
        
// 操作系统
    public String getHostsystem() {
        return hostsystem;
    }
    
    public void setHostsystem(String hostsystem) {
        this.hostsystem = hostsystem;
    }
        
// 登录/登出
    public String getDirection() {
        return direction;
    }
    
    public void setDirection(String direction) {
        this.direction = direction;
    }
        
// 登录状态0成功 1失败
    public Integer getLoginstatus() {
        return loginstatus;
    }
    
    public void setLoginstatus(Integer loginstatus) {
        this.loginstatus = loginstatus;
    }
        
// 操作信息
    public String getLoginmsg() {
        return loginmsg;
    }
    
    public void setLoginmsg(String loginmsg) {
        this.loginmsg = loginmsg;
    }
        
// 访问时间
    public Date getLogintime() {
        return logintime;
    }
    
    public void setLogintime(Date logintime) {
        this.logintime = logintime;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        

}

