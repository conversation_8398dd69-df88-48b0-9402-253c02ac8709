package inks.system.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户登录表(Piuserlogin)实体类
 *
 * <AUTHOR>
 * @since 2021-11-08 14:36:11
 */
public class PiuserloginPojo implements Serializable {
    private static final long serialVersionUID = -84274723783985765L;
    // ID
    @Excel(name = "ID")
    private String id;
    // 用户ID
    @Excel(name = "用户ID")
    private String userid;
    // 密码
    @Excel(name = "密码")
    private String userpassword;
    // 启用IP访问限制
    @Excel(name = "启用IP访问限制")
    private Integer checkipaddr;
    // IP地址
    @Excel(name = "IP地址")
    private String ipaddress;
    // MAC地址
    @Excel(name = "MAC地址")
    private String macaddress;
    // 第一次登录
    @Excel(name = "第一次登录")
    private Date firstvisit;
    // 上一次登录
    @Excel(name = "上一次登录")
    private Date previouvisit;
    // 浏览器名称
    @Excel(name = "浏览器名称")
    private String browsername;
    // 操作系统
    @Excel(name = "操作系统")
    private String hostsystem;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;

    // ID
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 用户ID
    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    // 密码
    public String getUserpassword() {
        return userpassword;
    }

    public void setUserpassword(String userpassword) {
        this.userpassword = userpassword;
    }

    // 启用IP访问限制
    public Integer getCheckipaddr() {
        return checkipaddr;
    }

    public void setCheckipaddr(Integer checkipaddr) {
        this.checkipaddr = checkipaddr;
    }

    // IP地址
    public String getIpaddress() {
        return ipaddress;
    }

    public void setIpaddress(String ipaddress) {
        this.ipaddress = ipaddress;
    }

    // MAC地址
    public String getMacaddress() {
        return macaddress;
    }

    public void setMacaddress(String macaddress) {
        this.macaddress = macaddress;
    }

    // 第一次登录
    public Date getFirstvisit() {
        return firstvisit;
    }

    public void setFirstvisit(Date firstvisit) {
        this.firstvisit = firstvisit;
    }

    // 上一次登录
    public Date getPreviouvisit() {
        return previouvisit;
    }

    public void setPreviouvisit(Date previouvisit) {
        this.previouvisit = previouvisit;
    }

    // 浏览器名称
    public String getBrowsername() {
        return browsername;
    }

    public void setBrowsername(String browsername) {
        this.browsername = browsername;
    }

    // 操作系统
    public String getHostsystem() {
        return hostsystem;
    }

    public void setHostsystem(String hostsystem) {
        this.hostsystem = hostsystem;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }


}

