package inks.system.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 初始化日志(Ciinitlog)实体类
 *
 * <AUTHOR>
 * @since 2022-07-31 11:20:20
 */
public class CiinitlogEntity implements Serializable {
    private static final long serialVersionUID = 474289344042036634L;
         // id
         private String id;
         // 功能编码
         private String modulecode;
         // 初始化标题
         private String inittitle;
         // 操作状态（0正常 1异常）
         private Integer initstatus;
         // 记录数
         private Integer reccount;
         // 错误消息
         private String errormsg;
         // 备注
         private String remark;
         // 租户id
         private String tenantid;
         // 租户名称
         private String tenantname;
         // 创建者
         private String createby;
         // 创建者id
         private String createbyid;
         // 新建日期
         private Date createdate;

// id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 功能编码
    public String getModulecode() {
        return modulecode;
    }
    
    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }
        
// 初始化标题
    public String getInittitle() {
        return inittitle;
    }
    
    public void setInittitle(String inittitle) {
        this.inittitle = inittitle;
    }
        
// 操作状态（0正常 1异常）
    public Integer getInitstatus() {
        return initstatus;
    }
    
    public void setInitstatus(Integer initstatus) {
        this.initstatus = initstatus;
    }
        
// 记录数
    public Integer getReccount() {
        return reccount;
    }
    
    public void setReccount(Integer reccount) {
        this.reccount = reccount;
    }
        
// 错误消息
    public String getErrormsg() {
        return errormsg;
    }
    
    public void setErrormsg(String errormsg) {
        this.errormsg = errormsg;
    }
        
// 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        

}

