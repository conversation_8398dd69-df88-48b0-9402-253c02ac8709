package inks.system.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 功能价格(Pipricepolicy)实体类
 *
 * <AUTHOR>
 * @since 2022-02-14 08:10:03
 */
public class PipricepolicyPojo implements Serializable {
    private static final long serialVersionUID = 970063479110709119L;
         // ID
          @Excel(name = "ID")
    private String id;
         // 服务id
          @Excel(name = "服务id")
    private String functionid;
         // 对象等级
          @Excel(name = "对象等级")
    private String objectlevel;
         // 发布域:0私有/1公共/2一级销售商
          @Excel(name = "发布域:0私有/1公共/2一级销售商")
    private Integer releasedomain;
         // 有效期
          @Excel(name = "有效期")
    private Date expirationdate;
         // 有效性
          @Excel(name = "有效性")
    private Integer enabledmark;
         // 排列序号
          @Excel(name = "排列序号")
    private Integer rownum;
         // 优先级1>0
          @Excel(name = "优先级1>0")
    private Integer levelnum;
         // 删除标识
          @Excel(name = "删除标识")
    private Integer deletemark;
         // 删除人员
          @Excel(name = "删除人员")
    private String deletelister;
         // 删除日期
          @Excel(name = "删除日期")
    private Date deletedate;
         // 简述
          @Excel(name = "简述")
    private String summary;
         // 创建者
          @Excel(name = "创建者")
    private String createby;
         // 创建者id
          @Excel(name = "创建者id")
    private String createbyid;
         // 新建日期
          @Excel(name = "新建日期")
    private Date createdate;
         // 制表
          @Excel(name = "制表")
    private String lister;
         // 制表id
          @Excel(name = "制表id")
    private String listerid;
         // 修改日期
          @Excel(name = "修改日期")
    private Date modifydate;
         // 审核员
          @Excel(name = "审核员")
    private String assessor;
         // 审核员id
          @Excel(name = "审核员id")
    private String assessorid;
         // 审核日期
          @Excel(name = "审核日期")
    private Date assessdate;
         // 乐观锁
          @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<PipricepolicyitemPojo> item;
    
   // ID
     public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // 服务id
     public String getFunctionid() {
        return functionid;
    }
    
    public void setFunctionid(String functionid) {
        this.functionid = functionid;
    }
        
   // 对象等级
     public String getObjectlevel() {
        return objectlevel;
    }
    
    public void setObjectlevel(String objectlevel) {
        this.objectlevel = objectlevel;
    }
        
   // 发布域:0私有/1公共/2一级销售商
     public Integer getReleasedomain() {
        return releasedomain;
    }
    
    public void setReleasedomain(Integer releasedomain) {
        this.releasedomain = releasedomain;
    }
        
   // 有效期
     public Date getExpirationdate() {
        return expirationdate;
    }
    
    public void setExpirationdate(Date expirationdate) {
        this.expirationdate = expirationdate;
    }
        
   // 有效性
     public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
   // 排列序号
     public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 优先级1>0
     public Integer getLevelnum() {
        return levelnum;
    }
    
    public void setLevelnum(Integer levelnum) {
        this.levelnum = levelnum;
    }
        
   // 删除标识
     public Integer getDeletemark() {
        return deletemark;
    }
    
    public void setDeletemark(Integer deletemark) {
        this.deletemark = deletemark;
    }
        
   // 删除人员
     public String getDeletelister() {
        return deletelister;
    }
    
    public void setDeletelister(String deletelister) {
        this.deletelister = deletelister;
    }
        
   // 删除日期
     public Date getDeletedate() {
        return deletedate;
    }
    
    public void setDeletedate(Date deletedate) {
        this.deletedate = deletedate;
    }
        
   // 简述
     public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
        
   // 创建者
     public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 创建者id
     public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
   // 新建日期
     public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表
     public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 制表id
     public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
   // 修改日期
     public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
   // 审核员
     public String getAssessor() {
        return assessor;
    }
    
    public void setAssessor(String assessor) {
        this.assessor = assessor;
    }
        
   // 审核员id
     public String getAssessorid() {
        return assessorid;
    }
    
    public void setAssessorid(String assessorid) {
        this.assessorid = assessorid;
    }
        
   // 审核日期
     public Date getAssessdate() {
        return assessdate;
    }
    
    public void setAssessdate(Date assessdate) {
        this.assessdate = assessdate;
    }
        
   // 乐观锁
     public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

    public List<PipricepolicyitemPojo> getItem() {
        return item;
    }

    public void setItem(List<PipricepolicyitemPojo> item) {
        this.item = item;
    }

    // 服务编码
    @Excel(name = "服务编码")
    private String functioncode;
    // 服务名称
    @Excel(name = "服务名称")
    private String functionname;
    // 描述
    @Excel(name = "描述")
    private String description;
    // 封面图片
    @Excel(name = "封面图片")
    private String frontphoto;

    // 服务编码
    public String getFunctioncode() {
        return functioncode;
    }

    public void setFunctioncode(String functioncode) {
        this.functioncode = functioncode;
    }

    // 服务名称
    public String getFunctionname() {
        return functionname;
    }

    public void setFunctionname(String functionname) {
        this.functionname = functionname;
    }

    // 描述
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    // 封面图片
    public String getFrontphoto() {
        return frontphoto;
    }

    public void setFrontphoto(String frontphoto) {
        this.frontphoto = frontphoto;
    }
}

