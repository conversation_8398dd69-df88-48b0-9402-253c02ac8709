package inks.system.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * SCM功能(Piscmfunct)实体类
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
public class PiscmfunctEntity implements Serializable {
    private static final long serialVersionUID = 685577537428427455L;
         // 服务id
         private String scmfunctid;
         // 服务编码
         private String scmfunctcode;
         // 服务名称
         private String scmfunctname;
         // 描述
         private String description;
         // 主服务id
         private String functionid;
         // 主服务编码
         private String functioncode;
         // 主服务名称
         private String functionname;
         // 有效性
         private Integer enabledmark;
         // 行号
         private Integer rownum;
         // 备注
         private String remark;
         // 创建者
         private String createby;
         // 创建者id
         private String createbyid;
         // 新建日期
         private Date createdate;
         // 制表
         private String lister;
         // 制表id
         private String listerid;
         // 修改日期
         private Date modifydate;
         // 乐观锁
         private Integer revision;

// 服务id
    public String getScmfunctid() {
        return scmfunctid;
    }
    
    public void setScmfunctid(String scmfunctid) {
        this.scmfunctid = scmfunctid;
    }
        
// 服务编码
    public String getScmfunctcode() {
        return scmfunctcode;
    }
    
    public void setScmfunctcode(String scmfunctcode) {
        this.scmfunctcode = scmfunctcode;
    }
        
// 服务名称
    public String getScmfunctname() {
        return scmfunctname;
    }
    
    public void setScmfunctname(String scmfunctname) {
        this.scmfunctname = scmfunctname;
    }
        
// 描述
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
        
// 主服务id
    public String getFunctionid() {
        return functionid;
    }
    
    public void setFunctionid(String functionid) {
        this.functionid = functionid;
    }
        
// 主服务编码
    public String getFunctioncode() {
        return functioncode;
    }
    
    public void setFunctioncode(String functioncode) {
        this.functioncode = functioncode;
    }
        
// 主服务名称
    public String getFunctionname() {
        return functionname;
    }
    
    public void setFunctionname(String functionname) {
        this.functionname = functionname;
    }
        
// 有效性
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
// 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
// 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

