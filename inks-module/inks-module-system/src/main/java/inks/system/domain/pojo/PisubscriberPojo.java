package inks.system.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 订阅信息表(Pisubscriber)实体类
 *
 * <AUTHOR>
 * @since 2021-11-10 14:17:25
 */
public class PisubscriberPojo implements Serializable {
    private static final long serialVersionUID = -20758941977215139L;
    // 订阅号
    @Excel(name = "订阅号")
    private String id;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 服务id
    @Excel(name = "服务id")
    private String functionid;
    // 数量
    @Excel(name = "数量")
    private Double quantity;
    // 开始时间
    @Excel(name = "开始时间")
    private Date startdate;
    // 结束时间
    @Excel(name = "结束时间")
    private Date enddate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 租户号
    @Excel(name = "租户号")
    private String tenantcode;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;
    // 租户公司
    @Excel(name = "租户公司")
    private String company;
    // 服务编码
    @Excel(name = "服务编码")
    private String functioncode;
    // 服务名称
    @Excel(name = "服务名称")
    private String functionname;
    // 封面图片
    @Excel(name = "封面图片")
    private String frontphoto;
    // 周期编码
    @Excel(name = "周期编码")
    private String cyclecode;
    // 容量大小
    @Excel(name = "容量大小")
    private Integer container;
    // 含税单价
    @Excel(name = "含税单价")
    private Double taxprice;
    // 含税金额
    @Excel(name = "含税金额")
    private Double taxamount;
    // 订单号
    @Excel(name = "订单号")
    private String orderno;
    // 订单Itemid
    @Excel(name = "订单Itemid")
    private String orderitemid;
    // 有效性
    @Excel(name = "有效性")
    private Integer enabledmark;

    // 订阅号
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 服务id
    public String getFunctionid() {
        return functionid;
    }

    public void setFunctionid(String functionid) {
        this.functionid = functionid;
    }

    // 数量
    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    // 开始时间
    public Date getStartdate() {
        return startdate;
    }

    public void setStartdate(Date startdate) {
        this.startdate = startdate;
    }

    // 结束时间
    public Date getEnddate() {
        return enddate;
    }

    public void setEnddate(Date enddate) {
        this.enddate = enddate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 租户号
    public String getTenantcode() {
        return tenantcode;
    }

    public void setTenantcode(String tenantcode) {
        this.tenantcode = tenantcode;
    }

    // 租户名称
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

    // 租户公司
    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    // 服务编码
    public String getFunctioncode() {
        return functioncode;
    }

    public void setFunctioncode(String functioncode) {
        this.functioncode = functioncode;
    }

    // 服务名称
    public String getFunctionname() {
        return functionname;
    }

    public void setFunctionname(String functionname) {
        this.functionname = functionname;
    }

    // 封面图片
    public String getFrontphoto() {
        return frontphoto;
    }

    public void setFrontphoto(String frontphoto) {
        this.frontphoto = frontphoto;
    }

    // 周期编码
    public String getCyclecode() {
        return cyclecode;
    }

    public void setCyclecode(String cyclecode) {
        this.cyclecode = cyclecode;
    }

    // 容量大小
    public Integer getContainer() {
        return container;
    }

    public void setContainer(Integer container) {
        this.container = container;
    }

    // 含税单价
    public Double getTaxprice() {
        return taxprice;
    }

    public void setTaxprice(Double taxprice) {
        this.taxprice = taxprice;
    }

    // 含税金额
    public Double getTaxamount() {
        return taxamount;
    }

    public void setTaxamount(Double taxamount) {
        this.taxamount = taxamount;
    }

    // 订单号
    public String getOrderno() {
        return orderno;
    }

    public void setOrderno(String orderno) {
        this.orderno = orderno;
    }

    // 订单Itemid
    public String getOrderitemid() {
        return orderitemid;
    }

    public void setOrderitemid(String orderitemid) {
        this.orderitemid = orderitemid;
    }

    // 有效性
    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }


}

