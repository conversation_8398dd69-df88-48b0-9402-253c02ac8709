package inks.system.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 组织用户表(Pideptuser)实体类
 *
 * <AUTHOR>
 * @since 2022-03-28 11:18:02
 */
public class PideptuserPojo implements Serializable {
    private static final long serialVersionUID = -43820438576419023L;
         // ID
         @Excel(name = "ID") 
    private String id;
         // 组织id
         @Excel(name = "组织id") 
    private String deptid;
         // 组织编码
         @Excel(name = "组织编码") 
    private String deptcode;
         // 组织名称
         @Excel(name = "组织名称") 
    private String deptname;
         // 用户ID
         @Excel(name = "用户ID") 
    private String userid;
         // 登录名
         @Excel(name = "登录名") 
    private String username;
         // 姓名
         @Excel(name = "姓名") 
    private String realname;
         // 是否主管
         @Excel(name = "是否主管") 
    private Integer isadmin;
         // 行号
         @Excel(name = "行号") 
    private Integer rownum;
         // 创建者
         @Excel(name = "创建者") 
    private String createby;
         // 创建者id
         @Excel(name = "创建者id") 
    private String createbyid;
         // 新建日期
         @Excel(name = "新建日期") 
    private Date createdate;
         // 制表
         @Excel(name = "制表") 
    private String lister;
         // 制表id
         @Excel(name = "制表id") 
    private String listerid;
         // 修改日期
         @Excel(name = "修改日期") 
    private Date modifydate;
         // 自定义1
         @Excel(name = "自定义1") 
    private String custom1;
         // 自定义2
         @Excel(name = "自定义2") 
    private String custom2;
         // 自定义3
         @Excel(name = "自定义3") 
    private String custom3;
         // 自定义4
         @Excel(name = "自定义4") 
    private String custom4;
         // 自定义5
         @Excel(name = "自定义5") 
    private String custom5;
         // 租户id
         @Excel(name = "租户id") 
    private String tenantid;
         // 租户名称
         @Excel(name = "租户名称") 
    private String tenantname;
         // 乐观锁
         @Excel(name = "乐观锁") 
    private Integer revision;

     // ID
       public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
     // 组织id
       public String getDeptid() {
        return deptid;
    }
    
    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }
        
     // 组织编码
       public String getDeptcode() {
        return deptcode;
    }
    
    public void setDeptcode(String deptcode) {
        this.deptcode = deptcode;
    }
        
     // 组织名称
       public String getDeptname() {
        return deptname;
    }
    
    public void setDeptname(String deptname) {
        this.deptname = deptname;
    }
        
     // 用户ID
       public String getUserid() {
        return userid;
    }
    
    public void setUserid(String userid) {
        this.userid = userid;
    }
        
     // 登录名
       public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
        
     // 姓名
       public String getRealname() {
        return realname;
    }
    
    public void setRealname(String realname) {
        this.realname = realname;
    }
        
     // 是否主管
       public Integer getIsadmin() {
        return isadmin;
    }
    
    public void setIsadmin(Integer isadmin) {
        this.isadmin = isadmin;
    }
        
     // 行号
       public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
     // 创建者
       public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
     // 创建者id
       public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
     // 新建日期
       public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
     // 制表
       public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
     // 制表id
       public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
     // 修改日期
       public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
     // 自定义1
       public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
     // 自定义2
       public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
     // 自定义3
       public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
     // 自定义4
       public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
     // 自定义5
       public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
     // 租户id
       public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
     // 租户名称
       public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
     // 乐观锁
       public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

