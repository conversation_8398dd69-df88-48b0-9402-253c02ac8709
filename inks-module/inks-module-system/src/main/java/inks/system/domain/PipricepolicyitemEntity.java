package inks.system.domain;

import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 价格项目(Pipricepolicyitem)Entity
 *
 * <AUTHOR>
 * @since 2021-12-09 14:05:21
 */
public class PipricepolicyitemEntity implements Serializable {
    private static final long serialVersionUID = -87752182199319706L;
          // ID
         private String id;
          // Pid
         private String pid;
          // 周期编码
         private String cyclecode;
          // 容量大小
         private Integer container;
          // 单价
         private Double taxprice;
          // 有效性
         private Integer enabledmark;
          // 行号
         private Integer rownum;
          // 备注
         private String remark;
          // 乐观锁
         private Integer revision;

    // ID
      public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
    // Pid
      public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
    // 周期编码
      public String getCyclecode() {
        return cyclecode;
    }
    
    public void setCyclecode(String cyclecode) {
        this.cyclecode = cyclecode;
    }
        
    // 容量大小
      public Integer getContainer() {
        return container;
    }
    
    public void setContainer(Integer container) {
        this.container = container;
    }
        
    // 单价
      public Double getTaxprice() {
        return taxprice;
    }
    
    public void setTaxprice(Double taxprice) {
        this.taxprice = taxprice;
    }
        
    // 有效性
      public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
    // 行号
      public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
    // 备注
      public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
    // 乐观锁
      public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

