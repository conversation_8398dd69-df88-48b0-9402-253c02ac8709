package inks.system.domain.vo;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class ValidationResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    private int total;                // 总数
    private int requcount;            // 验证失败数
    private List<Map<String, Object>> list; // 错误信息列表

    // 无参构造函数
    public ValidationResponse() {}

    // 带参构造函数
    public ValidationResponse(int total, int requcount, List<Map<String, Object>> list) {
        this.total = total;
        this.requcount = requcount;
        this.list = list;
    }

    // Getters and Setters
    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getRequcount() {
        return requcount;
    }

    public void setRequcount(int requcount) {
        this.requcount = requcount;
    }

    public List<Map<String, Object>> getList() {
        return list;
    }

    public void setList(List<Map<String, Object>> list) {
        this.list = list;
    }

    // toString 方法
    @Override
    public String toString() {
        return "ValidationResponse{" +
                "total=" + total +
                ", requcount=" + requcount +
                ", list=" + list +
                '}';
    }

    // 静态内部类 Builder，用于构建 ValidationResponse 实例
    public static class Builder {
        private int total;
        private int requcount;
        private List<Map<String, Object>> list;

        public Builder() {}

        public Builder total(int total) {
            this.total = total;
            return this;
        }

        public Builder requcount(int requcount) {
            this.requcount = requcount;
            return this;
        }

        public Builder list(List<Map<String, Object>> list) {
            this.list = list;
            return this;
        }

        public ValidationResponse build() {
            return new ValidationResponse(total, requcount, list);
        }
    }
}
