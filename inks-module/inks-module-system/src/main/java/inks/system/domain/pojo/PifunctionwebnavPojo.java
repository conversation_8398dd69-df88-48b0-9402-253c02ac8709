package inks.system.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * web导航(Pifunctionwebnav)实体类
 *
 * <AUTHOR>
 * @since 2024-03-01 15:36:24
 */
public class PifunctionwebnavPojo implements Serializable {
    private static final long serialVersionUID = 769016659164171542L;
     // id
    @Excel(name = "id") 
    private String id;
     // 服务id
    @Excel(name = "服务id") 
    private String functionid;
     // 服务编码
    @Excel(name = "服务编码") 
    private String functioncode;
     // 服务名称
    @Excel(name = "服务名称") 
    private String functionname;
     // 菜单id
    @Excel(name = "菜单id") 
    private String navid;
     // 导航编码
    @Excel(name = "导航编码") 
    private String navcode;
     // 导航名称
    @Excel(name = "导航名称") 
    private String navname;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 默认租户
    @Excel(name = "默认租户") 
    private String tenantid;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // 服务id
    public String getFunctionid() {
        return functionid;
    }
    
    public void setFunctionid(String functionid) {
        this.functionid = functionid;
    }
        
   // 服务编码
    public String getFunctioncode() {
        return functioncode;
    }
    
    public void setFunctioncode(String functioncode) {
        this.functioncode = functioncode;
    }
        
   // 服务名称
    public String getFunctionname() {
        return functionname;
    }
    
    public void setFunctionname(String functionname) {
        this.functionname = functionname;
    }
        
   // 菜单id
    public String getNavid() {
        return navid;
    }
    
    public void setNavid(String navid) {
        this.navid = navid;
    }
        
   // 导航编码
    public String getNavcode() {
        return navcode;
    }
    
    public void setNavcode(String navcode) {
        this.navcode = navcode;
    }
        
   // 导航名称
    public String getNavname() {
        return navname;
    }
    
    public void setNavname(String navname) {
        this.navname = navname;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
   // 默认租户
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

