package inks.system.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * Frm导航(Pimenufrm)实体类
 *
 * <AUTHOR>
 * @since 2024-05-05 16:28:26
 */
public class PimenufrmPojo implements Serializable {
    private static final long serialVersionUID = -10359928722186603L;
     // Navid
    @Excel(name = "Navid") 
    private String navid;
     // 父级id
    @Excel(name = "父级id") 
    private String navpid;
     // 导航类型
    @Excel(name = "导航类型") 
    private String navtype;
     // 导航编码
    @Excel(name = "导航编码") 
    private String navcode;
     // 导航名称
    @Excel(name = "导航名称") 
    private String navname;
     // 分组(备用)
    @Excel(name = "分组(备用)") 
    private String navgroup;
     // 排列序号
    @Excel(name = "排列序号") 
    private Integer rownum;
     // 动态文件
    @Excel(name = "动态文件") 
    private String assemblyname;
     // 窗口名称
    @Excel(name = "窗口名称") 
    private String formname;
     // Css图标
    @Excel(name = "Css图标") 
    private String imagecss;
     // 模块类型
    @Excel(name = "模块类型") 
    private String moduletype;
     // 模块编码
    @Excel(name = "模块编码") 
    private String modulecode;
     // 角色编码
    @Excel(name = "角色编码") 
    private String rolecode;
     // 图标
    @Excel(name = "图标") 
    private String imageindex;
     // 图标样式
    @Excel(name = "图标样式") 
    private String imagestyle;
     // 有效标识
    @Excel(name = "有效标识") 
    private Integer enabledmark;
     // Web图标
    @Excel(name = "Web图标") 
    private String iconurl;
     // Web位置
    @Excel(name = "Web位置") 
    private String navigateurl;
     // MVC位置
    @Excel(name = "MVC位置") 
    private String mvcurl;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 许可编码
    @Excel(name = "许可编码") 
    private String permissioncode;
     // 页号1000+
    @Excel(name = "页号1000+")
    private Integer pageindex;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;
    //tenandid
    @Excel(name = "租户id")
    private String tenantid;

    private List<PimenufrmPojo> children;

   // Navid
    public String getNavid() {
        return navid;
    }
    
    public void setNavid(String navid) {
        this.navid = navid;
    }
        
   // 父级id
    public String getNavpid() {
        return navpid;
    }
    
    public void setNavpid(String navpid) {
        this.navpid = navpid;
    }
        
   // 导航类型
    public String getNavtype() {
        return navtype;
    }
    
    public void setNavtype(String navtype) {
        this.navtype = navtype;
    }
        
   // 导航编码
    public String getNavcode() {
        return navcode;
    }
    
    public void setNavcode(String navcode) {
        this.navcode = navcode;
    }
        
   // 导航名称
    public String getNavname() {
        return navname;
    }
    
    public void setNavname(String navname) {
        this.navname = navname;
    }

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 分组(备用)
    public String getNavgroup() {
        return navgroup;
    }
    
    public void setNavgroup(String navgroup) {
        this.navgroup = navgroup;
    }
        
   // 排列序号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 动态文件
    public String getAssemblyname() {
        return assemblyname;
    }
    
    public void setAssemblyname(String assemblyname) {
        this.assemblyname = assemblyname;
    }
        
   // 窗口名称
    public String getFormname() {
        return formname;
    }
    
    public void setFormname(String formname) {
        this.formname = formname;
    }
        
   // Css图标
    public String getImagecss() {
        return imagecss;
    }
    
    public void setImagecss(String imagecss) {
        this.imagecss = imagecss;
    }
        
   // 模块类型
    public String getModuletype() {
        return moduletype;
    }
    
    public void setModuletype(String moduletype) {
        this.moduletype = moduletype;
    }
        
   // 模块编码
    public String getModulecode() {
        return modulecode;
    }
    
    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }
        
   // 角色编码
    public String getRolecode() {
        return rolecode;
    }
    
    public void setRolecode(String rolecode) {
        this.rolecode = rolecode;
    }
        
   // 图标
    public String getImageindex() {
        return imageindex;
    }
    
    public void setImageindex(String imageindex) {
        this.imageindex = imageindex;
    }
        
   // 图标样式
    public String getImagestyle() {
        return imagestyle;
    }
    
    public void setImagestyle(String imagestyle) {
        this.imagestyle = imagestyle;
    }
        
   // 有效标识
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
   // Web图标
    public String getIconurl() {
        return iconurl;
    }
    
    public void setIconurl(String iconurl) {
        this.iconurl = iconurl;
    }
        
   // Web位置
    public String getNavigateurl() {
        return navigateurl;
    }
    
    public void setNavigateurl(String navigateurl) {
        this.navigateurl = navigateurl;
    }
        
   // MVC位置
    public String getMvcurl() {
        return mvcurl;
    }
    
    public void setMvcurl(String mvcurl) {
        this.mvcurl = mvcurl;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 许可编码
    public String getPermissioncode() {
        return permissioncode;
    }
    
    public void setPermissioncode(String permissioncode) {
        this.permissioncode = permissioncode;
    }
        
   // 页号1000+
    public Integer getPageindex() {
        return pageindex;
    }

    public void setPageindex(Integer pageindex) {
        this.pageindex = pageindex;
    }

   // 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
   // 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
   // 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public List<PimenufrmPojo> getChildren() {
        return children;
    }

    public void setChildren(List<PimenufrmPojo> pimenufrmPojos) {
    }
}

