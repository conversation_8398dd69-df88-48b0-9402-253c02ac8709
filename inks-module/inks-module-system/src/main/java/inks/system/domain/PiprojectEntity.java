package inks.system.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 项目(Piproject)实体类
 *
 * <AUTHOR>
 * @since 2021-12-20 12:51:52
 */
public class PiprojectEntity implements Serializable {
    private static final long serialVersionUID = 696308474720181215L;
      // 服务id
      private String id;
      // 服务编码
      private String projectcode;
      // 服务名称
      private String projectname;
      // 描述
      private String description;
      // 封面图片
      private String frontphoto;
      // 有效性
      private Integer enabledmark;
      // 允许删除1
      private Integer allowdelete;
      // 行号
      private Integer rownum;
      // 摘要
      private String summary;
      // 创建者
      private String createby;
      // 创建者id
      private String createbyid;
      // 新建日期
      private Date createdate;
      // 制表
      private String lister;
      // 制表id
      private String listerid;
      // 修改日期
      private Date modifydate;
      // 乐观锁
      private Integer revision;

       // 服务id
         public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
       // 服务编码
         public String getProjectcode() {
        return projectcode;
    }
    
    public void setProjectcode(String projectcode) {
        this.projectcode = projectcode;
    }
        
       // 服务名称
         public String getProjectname() {
        return projectname;
    }
    
    public void setProjectname(String projectname) {
        this.projectname = projectname;
    }
        
       // 描述
         public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
        
       // 封面图片
         public String getFrontphoto() {
        return frontphoto;
    }
    
    public void setFrontphoto(String frontphoto) {
        this.frontphoto = frontphoto;
    }
        
       // 有效性
         public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
       // 允许删除1
         public Integer getAllowdelete() {
        return allowdelete;
    }
    
    public void setAllowdelete(Integer allowdelete) {
        this.allowdelete = allowdelete;
    }
        
       // 行号
         public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
       // 摘要
         public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
        
       // 创建者
         public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
       // 创建者id
         public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
       // 新建日期
         public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
       // 制表
         public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
       // 制表id
         public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
       // 修改日期
         public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
       // 乐观锁
         public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

