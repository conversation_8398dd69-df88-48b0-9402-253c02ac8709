package inks.system.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 开票管理(Cifninvoice)实体类
 *
 * <AUTHOR>
 * @since 2021-11-19 20:01:41
 */
public class CifninvoiceEntity implements Serializable {
    private static final long serialVersionUID = 637086373281548646L;
      // ID
      private String id;
      // 单据编码
      private String refno;
      // 单据类型
      private String billtype;
      // 单据标题
      private String billtitle;
      // 单据日期
      private Date billdate;
      // 用户ID
      private String userid;
      // 登录名
      private String username;
      // 姓名
      private String realname;
      // 租户id
      private String tenantid;
      // 租户号
      private String tenantcode;
      // 租户名称
      private String tenantname;
      // 租户公司
      private String company;
      // 邮箱地址
      private String email;
      // 金额
      private Double billtaxamount;
      // 发票抬头
      private String invotitle;
      // 抬头类型
      private String titletype;
      // 发票类型
      private String invotype;
      // 信用代码
      private String creditnum;
      // 开户银行
      private String bankname;
      // 开户账号
      private String bandaccount;
      // 注册地址
      private String busaddress;
      // 注册电话
      private String bustel;
      // 简述
      private String summary;
      // 创建者
      private String createby;
      // 新建日期
      private Date createdate;
      // 制表
      private String lister;
      // 修改日期
      private Date modifydate;
      // 单据状态
      private String statecode;
      // 状态日期
      private Date statedate;
      // 作废
      private Integer disannulmark;
      // 作废制表
      private String disannullister;
      // 作废日期
      private Date disannuldate;
      // 发票地址
      private String invourl;
      // 上传员
      private String involistr;
      // 上传时间
      private Date invodate;
      // 乐观锁
      private Integer revision;

       // ID
         public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
       // 单据编码
         public String getRefno() {
        return refno;
    }
    
    public void setRefno(String refno) {
        this.refno = refno;
    }
        
       // 单据类型
         public String getBilltype() {
        return billtype;
    }
    
    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }
        
       // 单据标题
         public String getBilltitle() {
        return billtitle;
    }
    
    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }
        
       // 单据日期
         public Date getBilldate() {
        return billdate;
    }
    
    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }
        
       // 用户ID
         public String getUserid() {
        return userid;
    }
    
    public void setUserid(String userid) {
        this.userid = userid;
    }
        
       // 登录名
         public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
        
       // 姓名
         public String getRealname() {
        return realname;
    }
    
    public void setRealname(String realname) {
        this.realname = realname;
    }
        
       // 租户id
         public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
       // 租户号
         public String getTenantcode() {
        return tenantcode;
    }
    
    public void setTenantcode(String tenantcode) {
        this.tenantcode = tenantcode;
    }
        
       // 租户名称
         public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
       // 租户公司
         public String getCompany() {
        return company;
    }
    
    public void setCompany(String company) {
        this.company = company;
    }
        
       // 邮箱地址
         public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
        
       // 金额
         public Double getBilltaxamount() {
        return billtaxamount;
    }
    
    public void setBilltaxamount(Double billtaxamount) {
        this.billtaxamount = billtaxamount;
    }
        
       // 发票抬头
         public String getInvotitle() {
        return invotitle;
    }
    
    public void setInvotitle(String invotitle) {
        this.invotitle = invotitle;
    }
        
       // 抬头类型
         public String getTitletype() {
        return titletype;
    }
    
    public void setTitletype(String titletype) {
        this.titletype = titletype;
    }
        
       // 发票类型
         public String getInvotype() {
        return invotype;
    }
    
    public void setInvotype(String invotype) {
        this.invotype = invotype;
    }
        
       // 信用代码
         public String getCreditnum() {
        return creditnum;
    }
    
    public void setCreditnum(String creditnum) {
        this.creditnum = creditnum;
    }
        
       // 开户银行
         public String getBankname() {
        return bankname;
    }
    
    public void setBankname(String bankname) {
        this.bankname = bankname;
    }
        
       // 开户账号
         public String getBandaccount() {
        return bandaccount;
    }
    
    public void setBandaccount(String bandaccount) {
        this.bandaccount = bandaccount;
    }
        
       // 注册地址
         public String getBusaddress() {
        return busaddress;
    }
    
    public void setBusaddress(String busaddress) {
        this.busaddress = busaddress;
    }
        
       // 注册电话
         public String getBustel() {
        return bustel;
    }
    
    public void setBustel(String bustel) {
        this.bustel = bustel;
    }
        
       // 简述
         public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
        
       // 创建者
         public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
       // 新建日期
         public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
       // 制表
         public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
       // 修改日期
         public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
       // 单据状态
         public String getStatecode() {
        return statecode;
    }
    
    public void setStatecode(String statecode) {
        this.statecode = statecode;
    }
        
       // 状态日期
         public Date getStatedate() {
        return statedate;
    }
    
    public void setStatedate(Date statedate) {
        this.statedate = statedate;
    }
        
       // 作废
         public Integer getDisannulmark() {
        return disannulmark;
    }
    
    public void setDisannulmark(Integer disannulmark) {
        this.disannulmark = disannulmark;
    }
        
       // 作废制表
         public String getDisannullister() {
        return disannullister;
    }
    
    public void setDisannullister(String disannullister) {
        this.disannullister = disannullister;
    }
        
       // 作废日期
         public Date getDisannuldate() {
        return disannuldate;
    }
    
    public void setDisannuldate(Date disannuldate) {
        this.disannuldate = disannuldate;
    }
        
       // 发票地址
         public String getInvourl() {
        return invourl;
    }
    
    public void setInvourl(String invourl) {
        this.invourl = invourl;
    }
        
       // 上传员
         public String getInvolistr() {
        return involistr;
    }
    
    public void setInvolistr(String involistr) {
        this.involistr = involistr;
    }
        
       // 上传时间
         public Date getInvodate() {
        return invodate;
    }
    
    public void setInvodate(Date invodate) {
        this.invodate = invodate;
    }
        
       // 乐观锁
         public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

