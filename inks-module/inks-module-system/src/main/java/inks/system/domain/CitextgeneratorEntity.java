package inks.system.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 文本生成器(Citextgenerator)实体类
 *
 * <AUTHOR>
 * @since 2023-12-20 17:29:11
 */
public class CitextgeneratorEntity implements Serializable {
    private static final long serialVersionUID = -16021675197732332L;
     // id
    private String id;
     // Pid
    private String parentid;
     // 编码
    private String tgcode;
     // 编码
    private String tgbillcode;
     // 数值
    private String tgvalue;
     // 层数
    private Integer tglevel;
     // 必要的(禁止删除)
    private Integer essential;
     // CSS样式
    private String cssclass;
     // RowNum
    private Integer rownum;
     // 默认的
    private Integer defaultmark;
     // 有效性
    private Integer enabledmark;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;

// id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// Pid
    public String getParentid() {
        return parentid;
    }
    
    public void setParentid(String parentid) {
        this.parentid = parentid;
    }
        
// 编码
    public String getTgcode() {
        return tgcode;
    }
    
    public void setTgcode(String tgcode) {
        this.tgcode = tgcode;
    }
        
// 编码
    public String getTgbillcode() {
        return tgbillcode;
    }
    
    public void setTgbillcode(String tgbillcode) {
        this.tgbillcode = tgbillcode;
    }
        
// 数值
    public String getTgvalue() {
        return tgvalue;
    }
    
    public void setTgvalue(String tgvalue) {
        this.tgvalue = tgvalue;
    }
        
// 层数
    public Integer getTglevel() {
        return tglevel;
    }
    
    public void setTglevel(Integer tglevel) {
        this.tglevel = tglevel;
    }
        
// 必要的(禁止删除)
    public Integer getEssential() {
        return essential;
    }
    
    public void setEssential(Integer essential) {
        this.essential = essential;
    }
        
// CSS样式
    public String getCssclass() {
        return cssclass;
    }
    
    public void setCssclass(String cssclass) {
        this.cssclass = cssclass;
    }
        
// RowNum
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
// 默认的
    public Integer getDefaultmark() {
        return defaultmark;
    }
    
    public void setDefaultmark(Integer defaultmark) {
        this.defaultmark = defaultmark;
    }
        
// 有效性
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
// 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
// 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
// 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
// 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
// 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

