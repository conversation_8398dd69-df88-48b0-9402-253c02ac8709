package inks.system.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 系统自检(Ciselfcheck)实体类
 *
 * <AUTHOR>
 * @since 2023-09-02 09:08:52
 */
public class CiselfcheckEntity implements Serializable {
    private static final long serialVersionUID = -91481234216339460L;
        private String id;
         // 服务ID
         private String functionid;
         // 服务编码
         private String functioncode;
         // 服务名称
         private String functionname;
         // 接口名
         private String apiurl;
         // 分值
         private Integer score;
         // 致命缺陷
         private Integer critical;
         // 摘要
         private String remark;
         // 创建者
         private String createby;
         // 创建者id
         private String createbyid;
         // 新建日期
         private Date createdate;
         // 制表
         private String lister;
         // 制表id
         private String listerid;
         // 修改日期
         private Date modifydate;
         // 自定义1
         private String custom1;
         // 自定义2
         private String custom2;
         // 自定义3
         private String custom3;
         // 自定义4
         private String custom4;
         // 自定义5
         private String custom5;
         // 乐观锁
         private Integer revision;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
// 服务ID

    public String getFunctionid() {
        return functionid;
    }

    public void setFunctionid(String functionid) {
        this.functionid = functionid;
    }
// 服务编码

    public String getFunctioncode() {
        return functioncode;
    }

    public void setFunctioncode(String functioncode) {
        this.functioncode = functioncode;
    }
// 服务名称

    public String getFunctionname() {
        return functionname;
    }

    public void setFunctionname(String functionname) {
        this.functionname = functionname;
    }
// 接口名

    public String getApiurl() {
        return apiurl;
    }

    public void setApiurl(String apiurl) {
        this.apiurl = apiurl;
    }
// 分值

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }
// 致命缺陷

    public Integer getCritical() {
        return critical;
    }

    public void setCritical(Integer critical) {
        this.critical = critical;
    }
// 摘要

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
// 创建者

    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
// 创建者id

    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
// 新建日期

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
// 制表

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
// 制表id

    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
// 修改日期

    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
// 自定义1

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
// 自定义2

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
// 自定义3

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
// 自定义4

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
// 自定义5

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
// 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

