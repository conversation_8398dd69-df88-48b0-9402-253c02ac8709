package inks.system.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 服务总表(Pifunction)实体类
 *
 * <AUTHOR>
 * @since 2021-12-29 20:02:55
 */
public class PifunctionEntity implements Serializable {
    private static final long serialVersionUID = -96410604602285916L;
         // 服务id
         private String functionid;
         // 服务编码
         private String functioncode;
         // 服务名称
         private String functionname;
         // 描述
         private String description;
         // 封面图片
         private String frontphoto;
         // 公共
         private Integer publicmark;
         // 有效性
         private Integer enabledmark;
         // 发布的
         private Integer releasemark;
         // 行号
         private Integer rownum;
         // 权重
         private Integer weight;
         // 备注
         private String remark;
         // 功能URL
         private String functionurl;
         // 创建者
         private String createby;
         // 创建者id
         private String createbyid;
         // 新建日期
         private Date createdate;
         // 制表
         private String lister;
         // 制表id
         private String listerid;
         // 修改日期
         private Date modifydate;
         // 删除标识
         private Integer deletemark;
         // 删除人员
         private String deletelister;
         // 删除人员id
         private String deletelisterid;
         // 删除日期
         private Date deletedate;
         // 乐观锁
         private Integer revision;

// 服务id
    public String getFunctionid() {
        return functionid;
    }
    
    public void setFunctionid(String functionid) {
        this.functionid = functionid;
    }
        
// 服务编码
    public String getFunctioncode() {
        return functioncode;
    }
    
    public void setFunctioncode(String functioncode) {
        this.functioncode = functioncode;
    }
        
// 服务名称
    public String getFunctionname() {
        return functionname;
    }
    
    public void setFunctionname(String functionname) {
        this.functionname = functionname;
    }
        
// 描述
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
        
// 封面图片
    public String getFrontphoto() {
        return frontphoto;
    }
    
    public void setFrontphoto(String frontphoto) {
        this.frontphoto = frontphoto;
    }
        
// 公共
    public Integer getPublicmark() {
        return publicmark;
    }
    
    public void setPublicmark(Integer publicmark) {
        this.publicmark = publicmark;
    }
        
// 有效性
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
// 发布的
    public Integer getReleasemark() {
        return releasemark;
    }
    
    public void setReleasemark(Integer releasemark) {
        this.releasemark = releasemark;
    }
        
// 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
// 权重
    public Integer getWeight() {
        return weight;
    }
    
    public void setWeight(Integer weight) {
        this.weight = weight;
    }
        
// 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 功能URL
    public String getFunctionurl() {
        return functionurl;
    }
    
    public void setFunctionurl(String functionurl) {
        this.functionurl = functionurl;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 删除标识
    public Integer getDeletemark() {
        return deletemark;
    }
    
    public void setDeletemark(Integer deletemark) {
        this.deletemark = deletemark;
    }
        
// 删除人员
    public String getDeletelister() {
        return deletelister;
    }
    
    public void setDeletelister(String deletelister) {
        this.deletelister = deletelister;
    }
        
// 删除人员id
    public String getDeletelisterid() {
        return deletelisterid;
    }
    
    public void setDeletelisterid(String deletelisterid) {
        this.deletelisterid = deletelisterid;
    }
        
// 删除日期
    public Date getDeletedate() {
        return deletedate;
    }
    
    public void setDeletedate(Date deletedate) {
        this.deletedate = deletedate;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

