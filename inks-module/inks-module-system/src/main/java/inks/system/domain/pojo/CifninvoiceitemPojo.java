package inks.system.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 发票项目(Cifninvoiceitem)Pojo
 *
 * <AUTHOR>
 * @since 2021-11-24 13:32:07
 */
public class CifninvoiceitemPojo implements Serializable {
    private static final long serialVersionUID = -96754385560513566L;
         // id
       @Excel(name = "id")    
  private String id;
         // Pid
       @Excel(name = "Pid")    
  private String pid;
         // 订单编码(Refno)
       @Excel(name = "订单编码(Refno)")    
  private String orderuid;
         // 单据类型
       @Excel(name = "单据类型")    
  private String ordertype;
         // 单据标题
       @Excel(name = "单据标题")    
  private String ordertitle;
         // 单据日期
       @Excel(name = "单据日期")    
  private Date orderdate;
         // 服务id
       @Excel(name = "服务id")    
  private String functionid;
         // 服务编码
       @Excel(name = "服务编码")    
  private String functioncode;
         // 服务名称
       @Excel(name = "服务名称")    
  private String functionname;
         // 周期编码
       @Excel(name = "周期编码")    
  private String cyclecode;
         // 容量大小
       @Excel(name = "容量大小")    
  private Integer container;
         // 数量
       @Excel(name = "数量")    
  private Double quantity;
         // 含税单价
       @Excel(name = "含税单价")    
  private Double taxprice;
         // 含税金额
       @Excel(name = "含税金额")    
  private Double taxamount;
         // 行号
       @Excel(name = "行号")    
  private Integer rownum;
         // 备注
       @Excel(name = "备注")    
  private String remark;
         // 乐观锁
       @Excel(name = "乐观锁")    
  private Integer revision;
         // 租户id
       @Excel(name = "租户id")    
  private String tenantid;
         // 订单Itemid
       @Excel(name = "订单Itemid")    
  private String orderitemid;

     // id
       public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
     // Pid
       public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
     // 订单编码(Refno)
       public String getOrderuid() {
        return orderuid;
    }
    
    public void setOrderuid(String orderuid) {
        this.orderuid = orderuid;
    }
        
     // 单据类型
       public String getOrdertype() {
        return ordertype;
    }
    
    public void setOrdertype(String ordertype) {
        this.ordertype = ordertype;
    }
        
     // 单据标题
       public String getOrdertitle() {
        return ordertitle;
    }
    
    public void setOrdertitle(String ordertitle) {
        this.ordertitle = ordertitle;
    }
        
     // 单据日期
       public Date getOrderdate() {
        return orderdate;
    }
    
    public void setOrderdate(Date orderdate) {
        this.orderdate = orderdate;
    }
        
     // 服务id
       public String getFunctionid() {
        return functionid;
    }
    
    public void setFunctionid(String functionid) {
        this.functionid = functionid;
    }
        
     // 服务编码
       public String getFunctioncode() {
        return functioncode;
    }
    
    public void setFunctioncode(String functioncode) {
        this.functioncode = functioncode;
    }
        
     // 服务名称
       public String getFunctionname() {
        return functionname;
    }
    
    public void setFunctionname(String functionname) {
        this.functionname = functionname;
    }
        
     // 周期编码
       public String getCyclecode() {
        return cyclecode;
    }
    
    public void setCyclecode(String cyclecode) {
        this.cyclecode = cyclecode;
    }
        
     // 容量大小
       public Integer getContainer() {
        return container;
    }
    
    public void setContainer(Integer container) {
        this.container = container;
    }
        
     // 数量
       public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
     // 含税单价
       public Double getTaxprice() {
        return taxprice;
    }
    
    public void setTaxprice(Double taxprice) {
        this.taxprice = taxprice;
    }
        
     // 含税金额
       public Double getTaxamount() {
        return taxamount;
    }
    
    public void setTaxamount(Double taxamount) {
        this.taxamount = taxamount;
    }
        
     // 行号
       public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
     // 备注
       public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
     // 乐观锁
       public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        
     // 租户id
       public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
     // 订单Itemid
       public String getOrderitemid() {
        return orderitemid;
    }
    
    public void setOrderitemid(String orderitemid) {
        this.orderitemid = orderitemid;
    }
        

}

