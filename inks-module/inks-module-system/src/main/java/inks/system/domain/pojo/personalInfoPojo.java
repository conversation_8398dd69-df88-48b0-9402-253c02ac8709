package inks.system.domain.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class personalInfoPojo {
    @ApiModelProperty(value = "用户ID")
    /** 用户ID */
    private String Userid;
    @ApiModelProperty(value = "编码")
    /** 编码 */
    private String UserCode;
    @ApiModelProperty(value = "登录号")
    /** 登录号 */
    private String UserName;
    @ApiModelProperty(value = "中文名")
    /** 中文名 */
    private String RealName;
    @ApiModelProperty(value = "手机")
    /** 手机 */
    private String Mobile;
    @ApiModelProperty(value = "制表")
    /** 制表 */
    private String Lister;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty(value = "新建日期")
    /** 新建日期 */
    private Date CreateDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty(value = "修改日期")
    /** 修改日期 */
    private Date ModifyDate;
    @ApiModelProperty(value = "租户id")
    private String Tid;

    @ApiModelProperty(value = "组织架构名")
    private String DeptName;



    @JsonProperty("DeptName")
    public String getDeptName() {
        return DeptName;
    }

    public void setDeptName(String deptName) {
        DeptName = deptName;
    }

    @JsonProperty("Tid")
    public String getTid() {
        return Tid;
    }

    public void setTid(String tid) {
        Tid = tid;
    }

    @JsonProperty("Userid")
    public String getUserid() {
        return Userid;
    }

    public void setUserid(String userid) {
        Userid = userid;
    }
    @JsonProperty("UserCode")
    public String getUserCode() {
        return UserCode;
    }

    public void setUserCode(String userCode) {
        UserCode = userCode;
    }
    @JsonProperty("UserName")
    public String getUserName() {
        return UserName;
    }

    public void setUserName(String userName) {
        UserName = userName;
    }
    @JsonProperty("RealName")
    public String getRealName() {
        return RealName;
    }

    public void setRealName(String realName) {
        RealName = realName;
    }
    @JsonProperty("Mobile")
    public String getMobile() {
        return Mobile;
    }

    public void setMobile(String mobile) {
        Mobile = mobile;
    }
    @JsonProperty("Lister")
    public String getLister() {
        return Lister;
    }

    public void setLister(String lister) {
        Lister = lister;
    }
    @JsonProperty("CreateDate")
    public Date getCreateDate() {
        return CreateDate;
    }

    public void setCreateDate(Date createDate) {
        CreateDate = createDate;
    }
    @JsonProperty("ModifyDate")
    public Date getModifyDate() {
        return ModifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        ModifyDate = modifyDate;
    }
}
