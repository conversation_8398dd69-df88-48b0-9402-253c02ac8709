package inks.system.domain;

import java.io.Serializable;

/**
 * 列表项目(Cidgformatitem)Entity
 *
 * <AUTHOR>
 * @since 2024-04-24 13:56:10
 */
public class CidgformatitemEntity implements Serializable {
    private static final long serialVersionUID = 346687679677278465L;
     // id
    private String id;
     // Pid
    private String pid;
     // 编码
    private String itemcode;
     // 名称
    private String itemname;
     // 默认宽度
    private String defwidth;
     // 最小宽度
    private String minwidth;
     // 1为显示
    private Integer displaymark;
     // 1固定0否
    private Integer fixed;
     // 1可排序
    private Integer sortable;
     // 排序表.字段
    private String orderfield;
     // 1溢出隐藏
    private Integer overflow;
     // 格式化
    private String formatter;
     // 自定义类
    private String classname;
     // left/center/right
    private String aligntype;
     // 事件名称
    private String eventname;
     // 可编辑
    private Integer editmark;
     // 可操作
    private Integer operationmark;
     // 显示位
    private Integer displayindex;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // Pid
    public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
   // 编码
    public String getItemcode() {
        return itemcode;
    }
    
    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }
        
   // 名称
    public String getItemname() {
        return itemname;
    }
    
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
        
   // 默认宽度
    public String getDefwidth() {
        return defwidth;
    }
    
    public void setDefwidth(String defwidth) {
        this.defwidth = defwidth;
    }
        
   // 最小宽度
    public String getMinwidth() {
        return minwidth;
    }
    
    public void setMinwidth(String minwidth) {
        this.minwidth = minwidth;
    }
        
   // 1为显示
    public Integer getDisplaymark() {
        return displaymark;
    }
    
    public void setDisplaymark(Integer displaymark) {
        this.displaymark = displaymark;
    }
        
   // 1固定0否
    public Integer getFixed() {
        return fixed;
    }
    
    public void setFixed(Integer fixed) {
        this.fixed = fixed;
    }
        
   // 1可排序
    public Integer getSortable() {
        return sortable;
    }
    
    public void setSortable(Integer sortable) {
        this.sortable = sortable;
    }
        
   // 排序表.字段
    public String getOrderfield() {
        return orderfield;
    }
    
    public void setOrderfield(String orderfield) {
        this.orderfield = orderfield;
    }
        
   // 1溢出隐藏
    public Integer getOverflow() {
        return overflow;
    }
    
    public void setOverflow(Integer overflow) {
        this.overflow = overflow;
    }
        
   // 格式化
    public String getFormatter() {
        return formatter;
    }
    
    public void setFormatter(String formatter) {
        this.formatter = formatter;
    }
        
   // 自定义类
    public String getClassname() {
        return classname;
    }
    
    public void setClassname(String classname) {
        this.classname = classname;
    }
        
   // left/center/right
    public String getAligntype() {
        return aligntype;
    }
    
    public void setAligntype(String aligntype) {
        this.aligntype = aligntype;
    }
        
   // 事件名称
    public String getEventname() {
        return eventname;
    }
    
    public void setEventname(String eventname) {
        this.eventname = eventname;
    }
        
   // 可编辑
    public Integer getEditmark() {
        return editmark;
    }
    
    public void setEditmark(Integer editmark) {
        this.editmark = editmark;
    }
        
   // 可操作
    public Integer getOperationmark() {
        return operationmark;
    }
    
    public void setOperationmark(Integer operationmark) {
        this.operationmark = operationmark;
    }
        
   // 显示位
    public Integer getDisplayindex() {
        return displayindex;
    }
    
    public void setDisplayindex(Integer displayindex) {
        this.displayindex = displayindex;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

