package inks.system.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 用户表(Piuser)实体类
 *
 * <AUTHOR>
 * @since 2021-12-09 15:40:05
 */
public class PiuserEntity implements Serializable {
    private static final long serialVersionUID = 132938303095586716L;
         // 用户id
         private String userid;
         // 登录名
         private String username;
         // 姓名
         private String realname;
         // 昵称
         private String nickname;
         // 手机
         private String mobile;
         // 用户邮箱
         private String email;
         // 性别
         private Integer sex;
         // 系统语言选择
         private String langcode;
         // 头像
         private String avatar;
         // 状态:0正常,1禁用
         private Integer userstatus;
         // 编码(备用)
         private String usercode;
         // 备注
         private String remark;
         // 创建者
         private String createby;
         // 新建日期
         private Date createdate;
         // 制表
         private String lister;
         // 修改日期
         private Date modifydate;
         // 乐观锁
         private Integer revision;

// 用户id
    public String getUserid() {
        return userid;
    }
    
    public void setUserid(String userid) {
        this.userid = userid;
    }
        
// 登录名
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
        
// 姓名
    public String getRealname() {
        return realname;
    }
    
    public void setRealname(String realname) {
        this.realname = realname;
    }
        
// 昵称
    public String getNickname() {
        return nickname;
    }
    
    public void setNickname(String nickname) {
        this.nickname = nickname;
    }
        
// 手机
    public String getMobile() {
        return mobile;
    }
    
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
        
// 用户邮箱
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
        
// 性别
    public Integer getSex() {
        return sex;
    }
    
    public void setSex(Integer sex) {
        this.sex = sex;
    }
        
// 系统语言选择
    public String getLangcode() {
        return langcode;
    }
    
    public void setLangcode(String langcode) {
        this.langcode = langcode;
    }
        
// 头像
    public String getAvatar() {
        return avatar;
    }
    
    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }
        
// 状态:0正常,1禁用
    public Integer getUserstatus() {
        return userstatus;
    }
    
    public void setUserstatus(Integer userstatus) {
        this.userstatus = userstatus;
    }
        
// 编码(备用)
    public String getUsercode() {
        return usercode;
    }
    
    public void setUsercode(String usercode) {
        this.usercode = usercode;
    }
        
// 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

