package inks.system.domain.pojo;

import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;
import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;

/**
 * 字典项目(Cidictitem)Pojo
 *
 * <AUTHOR>
 * @since 2024-02-17 13:22:15
 */
public class CidictitemdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = -75251515221710977L;
     // id
  @Excel(name = "id")    
  private String id;
     // Pid
  @Excel(name = "Pid")    
  private String pid;
     // 编码
  @Excel(name = "编码")    
  private String dictcode;
     // 数值
  @Excel(name = "数值")    
  private String dictvalue;
     // 必要的(禁止删除)
  @Excel(name = "必要的(禁止删除)")    
  private Integer essential;
     // CSS样式
  @Excel(name = "CSS样式")    
  private String cssclass;
     // RowNum
  @Excel(name = "RowNum")    
  private Integer rownum;
     // 默认的
  @Excel(name = "默认的")    
  private Integer defaultmark;
     // 有效性
  @Excel(name = "有效性")    
  private Integer enabledmark;
     // 备注
  @Excel(name = "备注")    
  private String remark;
     // 背景颜色
  @Excel(name = "背景颜色")    
  private String backgroundcolor;
     // 字体颜色
  @Excel(name = "字体颜色")    
  private String fontcolor;
     // 图标
  @Excel(name = "图标")    
  private String icon;
     // 自定义1
  @Excel(name = "自定义1")    
  private String custom1;
     // 自定义2
  @Excel(name = "自定义2")    
  private String custom2;
     // 自定义3
  @Excel(name = "自定义3")    
  private String custom3;
     // 自定义4
  @Excel(name = "自定义4")    
  private String custom4;
     // 自定义5
  @Excel(name = "自定义5")    
  private String custom5;
     // 租户id
  @Excel(name = "租户id")    
  private String tenantid;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // Pid
    public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
   // 编码
    public String getDictcode() {
        return dictcode;
    }
    
    public void setDictcode(String dictcode) {
        this.dictcode = dictcode;
    }
        
   // 数值
    public String getDictvalue() {
        return dictvalue;
    }
    
    public void setDictvalue(String dictvalue) {
        this.dictvalue = dictvalue;
    }
        
   // 必要的(禁止删除)
    public Integer getEssential() {
        return essential;
    }
    
    public void setEssential(Integer essential) {
        this.essential = essential;
    }
        
   // CSS样式
    public String getCssclass() {
        return cssclass;
    }
    
    public void setCssclass(String cssclass) {
        this.cssclass = cssclass;
    }
        
   // RowNum
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 默认的
    public Integer getDefaultmark() {
        return defaultmark;
    }
    
    public void setDefaultmark(Integer defaultmark) {
        this.defaultmark = defaultmark;
    }
        
   // 有效性
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 背景颜色
    public String getBackgroundcolor() {
        return backgroundcolor;
    }
    
    public void setBackgroundcolor(String backgroundcolor) {
        this.backgroundcolor = backgroundcolor;
    }
        
   // 字体颜色
    public String getFontcolor() {
        return fontcolor;
    }
    
    public void setFontcolor(String fontcolor) {
        this.fontcolor = fontcolor;
    }
        
   // 图标
    public String getIcon() {
        return icon;
    }
    
    public void setIcon(String icon) {
        this.icon = icon;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

