package inks.system.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 服务工作台(Pifunctiondash)实体类
 *
 * <AUTHOR>
 * @since 2022-07-16 16:20:00
 */
public class PifunctiondashPojo implements Serializable {
    private static final long serialVersionUID = -52454429055054967L;
         // id
         @Excel(name = "id") 
    private String id;
         // 服务id
         @Excel(name = "服务id") 
    private String functionid;
         // 服务编码
         @Excel(name = "服务编码") 
    private String functioncode;
         // 服务名称
         @Excel(name = "服务名称") 
    private String functionname;
         // 工作台id
         @Excel(name = "工作台id") 
    private String dashid;
         // 工作台编码
         @Excel(name = "工作台编码") 
    private String dashcode;
         // 工作台名称
         @Excel(name = "工作台名称") 
    private String dashname;
         // 备注
         @Excel(name = "备注") 
    private String remark;
         // 创建者
         @Excel(name = "创建者") 
    private String createby;
         // 创建者id
         @Excel(name = "创建者id") 
    private String createbyid;
         // 新建日期
         @Excel(name = "新建日期") 
    private Date createdate;
         // 制表
         @Excel(name = "制表") 
    private String lister;
         // 制表id
         @Excel(name = "制表id") 
    private String listerid;
         // 修改日期
         @Excel(name = "修改日期") 
    private Date modifydate;
         // 乐观锁
         @Excel(name = "乐观锁") 
    private Integer revision;

     // id
       public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
     // 服务id
       public String getFunctionid() {
        return functionid;
    }
    
    public void setFunctionid(String functionid) {
        this.functionid = functionid;
    }
        
     // 服务编码
       public String getFunctioncode() {
        return functioncode;
    }
    
    public void setFunctioncode(String functioncode) {
        this.functioncode = functioncode;
    }
        
     // 服务名称
       public String getFunctionname() {
        return functionname;
    }
    
    public void setFunctionname(String functionname) {
        this.functionname = functionname;
    }
        
     // 工作台id
       public String getDashid() {
        return dashid;
    }
    
    public void setDashid(String dashid) {
        this.dashid = dashid;
    }
        
     // 工作台编码
       public String getDashcode() {
        return dashcode;
    }
    
    public void setDashcode(String dashcode) {
        this.dashcode = dashcode;
    }
        
     // 工作台名称
       public String getDashname() {
        return dashname;
    }
    
    public void setDashname(String dashname) {
        this.dashname = dashname;
    }
        
     // 备注
       public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
     // 创建者
       public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
     // 创建者id
       public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
     // 新建日期
       public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
     // 制表
       public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
     // 制表id
       public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
     // 修改日期
       public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
     // 乐观锁
       public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

