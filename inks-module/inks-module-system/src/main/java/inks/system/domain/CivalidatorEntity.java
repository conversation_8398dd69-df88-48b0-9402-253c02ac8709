package inks.system.domain;

import java.util.Date;
import java.io.Serializable;

/**
 * 数据验证(Civalidator)实体类
 *
 * <AUTHOR>
 * @since 2024-11-18 12:55:37
 */
public class CivalidatorEntity implements Serializable {
    private static final long serialVersionUID = -57140640004565245L;
     // id
    private String id;
     // 验证编码
    private String valicode;
     // 标题
    private String valititle;
     // 是否SQL
    private Integer sqlmark;
     // SQL语句
    private String sqlstr;
     // 表达式
    private String expression;
     // 提示语
    private String tipmsg;
     // 提示语(英文)
    private String tipmsgen;
     // 是否必要
    private Integer requiredmark;
     // 是否item循环
    private Integer itemloopmark;
     // 有效标识
    private Integer enabledmark;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;

// id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 验证编码
    public String getValicode() {
        return valicode;
    }
    
    public void setValicode(String valicode) {
        this.valicode = valicode;
    }
        
// 标题
    public String getValititle() {
        return valititle;
    }
    
    public void setValititle(String valititle) {
        this.valititle = valititle;
    }
        
// 是否SQL
    public Integer getSqlmark() {
        return sqlmark;
    }
    
    public void setSqlmark(Integer sqlmark) {
        this.sqlmark = sqlmark;
    }
        
// SQL语句
    public String getSqlstr() {
        return sqlstr;
    }
    
    public void setSqlstr(String sqlstr) {
        this.sqlstr = sqlstr;
    }
        
// 表达式
    public String getExpression() {
        return expression;
    }
    
    public void setExpression(String expression) {
        this.expression = expression;
    }
        
// 提示语
    public String getTipmsg() {
        return tipmsg;
    }
    
    public void setTipmsg(String tipmsg) {
        this.tipmsg = tipmsg;
    }
        
// 提示语(英文)
    public String getTipmsgen() {
        return tipmsgen;
    }
    
    public void setTipmsgen(String tipmsgen) {
        this.tipmsgen = tipmsgen;
    }
        
// 是否必要
    public Integer getRequiredmark() {
        return requiredmark;
    }
    
    public void setRequiredmark(Integer requiredmark) {
        this.requiredmark = requiredmark;
    }
        
// 是否item循环
    public Integer getItemloopmark() {
        return itemloopmark;
    }
    
    public void setItemloopmark(Integer itemloopmark) {
        this.itemloopmark = itemloopmark;
    }
        
// 有效标识
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
// 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
// 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
// 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
// 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
// 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
// 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

