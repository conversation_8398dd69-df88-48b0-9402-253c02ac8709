package inks.system.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 功能订单(Cifnorder)实体类
 *
 * <AUTHOR>
 * @since 2021-11-23 20:18:16
 */
public class CifnorderPojo implements Serializable {
    private static final long serialVersionUID = 287322928735011501L;
    // ID
    @Excel(name = "ID")
    private String id;
    // 单据编码
    @Excel(name = "单据编码")
    private String refno;
    // 单据类型
    @Excel(name = "单据类型")
    private String billtype;
    // 单据标题
    @Excel(name = "单据标题")
    private String billtitle;
    // 单据日期
    @Excel(name = "单据日期")
    private Date billdate;
    // 销售ID
    @Excel(name = "销售ID")
    private String sellerid;
    // 用户ID
    @Excel(name = "用户ID")
    private String userid;
    // 登录名
    @Excel(name = "登录名")
    private String username;
    // 姓名
    @Excel(name = "姓名")
    private String realname;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 租户号
    @Excel(name = "租户号")
    private String tenantcode;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;
    // 简述
    @Excel(name = "简述")
    private String summary;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 金额
    @Excel(name = "金额")
    private Double billtaxamount;
    // 付款金额
    @Excel(name = "付款金额")
    private Double payamount;
    // 付款单号
    @Excel(name = "付款单号")
    private String paybillcode;
    // 单据状态
    @Excel(name = "单据状态")
    private String statecode;
    // 状态日期
    @Excel(name = "状态日期")
    private Date statedate;
    // 作废
    @Excel(name = "作废")
    private Integer disannulmark;
    // 作废制表
    @Excel(name = "作废制表")
    private String disannullister;
    // 作废日期
    @Excel(name = "作废日期")
    private Date disannuldate;
    // 租户公司
    @Excel(name = "租户公司")
    private String company;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;


    // 子表
    private List<CifnorderitemPojo> item;

    // ID
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 单据编码
    public String getRefno() {
        return refno;
    }

    public void setRefno(String refno) {
        this.refno = refno;
    }

    // 单据类型
    public String getBilltype() {
        return billtype;
    }

    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }

    // 单据标题
    public String getBilltitle() {
        return billtitle;
    }

    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }

    // 单据日期
    public Date getBilldate() {
        return billdate;
    }

    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }

    // 销售ID
    public String getSellerid() {
        return sellerid;
    }

    public void setSellerid(String sellerid) {
        this.sellerid = sellerid;
    }

    // 用户ID
    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    // 登录名
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    // 姓名
    public String getRealname() {
        return realname;
    }

    public void setRealname(String realname) {
        this.realname = realname;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 租户号
    public String getTenantcode() {
        return tenantcode;
    }

    public void setTenantcode(String tenantcode) {
        this.tenantcode = tenantcode;
    }

    // 租户名称
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

    // 简述
    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 金额
    public Double getBilltaxamount() {
        return billtaxamount;
    }

    public void setBilltaxamount(Double billtaxamount) {
        this.billtaxamount = billtaxamount;
    }

    // 付款金额
    public Double getPayamount() {
        return payamount;
    }

    public void setPayamount(Double payamount) {
        this.payamount = payamount;
    }

    // 付款单号
    public String getPaybillcode() {
        return paybillcode;
    }

    public void setPaybillcode(String paybillcode) {
        this.paybillcode = paybillcode;
    }

    // 单据状态
    public String getStatecode() {
        return statecode;
    }

    public void setStatecode(String statecode) {
        this.statecode = statecode;
    }

    // 状态日期
    public Date getStatedate() {
        return statedate;
    }

    public void setStatedate(Date statedate) {
        this.statedate = statedate;
    }

    // 作废
    public Integer getDisannulmark() {
        return disannulmark;
    }

    public void setDisannulmark(Integer disannulmark) {
        this.disannulmark = disannulmark;
    }

    // 作废制表
    public String getDisannullister() {
        return disannullister;
    }

    public void setDisannullister(String disannullister) {
        this.disannullister = disannullister;
    }

    // 作废日期
    public Date getDisannuldate() {
        return disannuldate;
    }

    public void setDisannuldate(Date disannuldate) {
        this.disannuldate = disannuldate;
    }

    // 租户公司
    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


    public List<CifnorderitemPojo> getItem() {
        return item;
    }

    public void setItem(List<CifnorderitemPojo> item) {
        this.item = item;
    }


}

