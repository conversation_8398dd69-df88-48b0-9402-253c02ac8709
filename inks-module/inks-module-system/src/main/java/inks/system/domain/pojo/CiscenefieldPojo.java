package inks.system.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 场景字段(Ciscenefield)实体类
 *
 * <AUTHOR>
 * @since 2022-12-03 11:14:03
 */
public class CiscenefieldPojo implements Serializable {
    private static final long serialVersionUID = 378092029506800422L;
         // id
         @Excel(name = "id") 
    private String id;
         // 通用分组
         @Excel(name = "通用分组") 
    private String gengroupid;
         // 模块编码
         @Excel(name = "模块编码") 
    private String modulecode;
         // 编码
         @Excel(name = "编码") 
    private String fieldcode;
         // 名称
         @Excel(name = "名称") 
    private String fieldname;
         // 0文本1为数字
         @Excel(name = "0文本1为数字") 
    private Integer fieldtype;
         // 搜索中应用
         @Excel(name = "搜索中应用") 
    private Integer searchmark;
         // 序号
         @Excel(name = "序号") 
    private Integer rownum;
         // 备注
         @Excel(name = "备注") 
    private String remark;
         // 创建者
         @Excel(name = "创建者") 
    private String createby;
         // 创建者id
         @Excel(name = "创建者id") 
    private String createbyid;
         // 新建日期
         @Excel(name = "新建日期") 
    private Date createdate;
         // 制表
         @Excel(name = "制表") 
    private String lister;
         // 制表id
         @Excel(name = "制表id") 
    private String listerid;
         // 修改日期
         @Excel(name = "修改日期") 
    private Date modifydate;
         // 自定义1
         @Excel(name = "自定义1") 
    private String custom1;
         // 自定义2
         @Excel(name = "自定义2") 
    private String custom2;
         // 自定义3
         @Excel(name = "自定义3") 
    private String custom3;
         // 自定义4
         @Excel(name = "自定义4") 
    private String custom4;
         // 自定义5
         @Excel(name = "自定义5") 
    private String custom5;
         // 乐观锁
         @Excel(name = "乐观锁") 
    private Integer revision;

     // id
       public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
     // 通用分组
       public String getGengroupid() {
        return gengroupid;
    }
    
    public void setGengroupid(String gengroupid) {
        this.gengroupid = gengroupid;
    }
        
     // 模块编码
       public String getModulecode() {
        return modulecode;
    }
    
    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }
        
     // 编码
       public String getFieldcode() {
        return fieldcode;
    }
    
    public void setFieldcode(String fieldcode) {
        this.fieldcode = fieldcode;
    }
        
     // 名称
       public String getFieldname() {
        return fieldname;
    }
    
    public void setFieldname(String fieldname) {
        this.fieldname = fieldname;
    }
        
     // 0文本1为数字
       public Integer getFieldtype() {
        return fieldtype;
    }
    
    public void setFieldtype(Integer fieldtype) {
        this.fieldtype = fieldtype;
    }
        
     // 搜索中应用
       public Integer getSearchmark() {
        return searchmark;
    }
    
    public void setSearchmark(Integer searchmark) {
        this.searchmark = searchmark;
    }
        
     // 序号
       public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
     // 备注
       public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
     // 创建者
       public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
     // 创建者id
       public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
     // 新建日期
       public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
     // 制表
       public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
     // 制表id
       public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
     // 修改日期
       public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
     // 自定义1
       public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
     // 自定义2
       public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
     // 自定义3
       public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
     // 自定义4
       public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
     // 自定义5
       public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
     // 乐观锁
       public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

