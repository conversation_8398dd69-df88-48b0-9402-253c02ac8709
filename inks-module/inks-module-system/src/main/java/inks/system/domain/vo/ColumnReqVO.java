package inks.system.domain.vo;

import java.util.List;

//前端传入的列设置静态json数据的格式
public class ColumnReqVO {

    //private String id; 自己的列设置id
    private String formcode; // 表单 code 大写 D01M03B1
    private String subcode; // 部件 code  首字母大写 Th/List/Item
    private String sensitivecode;// 敏感编码 code Mat_Access.Amount
    private List<ColumnItem> item; // 前端静态html中的字段列表
    private List<ColumnItem> spec; // 前端查询的其他字段列表：如SPU、货品、核算分组


    public static class ColumnItem {
        private String id;
        private String pid;
        private String itemcode;   // 字段 code
        private String itemname;   // 字段名称
        private String defwidth;   // 默认宽度
        private String minwidth;   // 最小宽度
        private Integer displaymark; // 显示/隐藏
        private Integer fixed;     // 列固定
        private Integer sortable;  // 排序
        private Integer overflow;  // 溢出隐藏
        private String aligntype;  // 对齐方式
        private Integer rownum;    // 行号
        private String datasheet;  // 数据表中的字段，用于搜索，【注意！！前端叫 datasheet，对应后端表中的 orderfield】
        private Integer sensitivemark; // 是否敏感字段
        private Integer editmark; // 前端是否显示编辑按钮

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getPid() {
            return pid;
        }

        public void setPid(String pid) {
            this.pid = pid;
        }

        public String getItemcode() {
            return itemcode;
        }

        public void setItemcode(String itemcode) {
            this.itemcode = itemcode;
        }

        public String getItemname() {
            return itemname;
        }

        public void setItemname(String itemname) {
            this.itemname = itemname;
        }

        public String getDefwidth() {
            return defwidth;
        }

        public void setDefwidth(String defwidth) {
            this.defwidth = defwidth;
        }

        public String getMinwidth() {
            return minwidth;
        }

        public void setMinwidth(String minwidth) {
            this.minwidth = minwidth;
        }

        public Integer getDisplaymark() {
            return displaymark;
        }

        public void setDisplaymark(Integer displaymark) {
            this.displaymark = displaymark;
        }

        public Integer getRownum() {
            return rownum;
        }

        public void setRownum(Integer rownum) {
            this.rownum = rownum;
        }

        public Integer getSensitivemark() {
            return sensitivemark;
        }

        public void setSensitivemark(Integer sensitivemark) {
            this.sensitivemark = sensitivemark;
        }

        public Integer getFixed() {
            return fixed;
        }

        public void setFixed(Integer fixed) {
            this.fixed = fixed;
        }

        public Integer getSortable() {
            return sortable;
        }

        public void setSortable(Integer sortable) {
            this.sortable = sortable;
        }

        public Integer getOverflow() {
            return overflow;
        }

        public void setOverflow(Integer overflow) {
            this.overflow = overflow;
        }

        public String getAligntype() {
            return aligntype;
        }

        public void setAligntype(String aligntype) {
            this.aligntype = aligntype;
        }

        public String getDatasheet() {
            return datasheet;
        }

        public void setDatasheet(String datasheet) {
            this.datasheet = datasheet;
        }

        public Integer getEditmark() {
            return editmark;
        }

        public void setEditmark(Integer editmark) {
            this.editmark = editmark;
        }

        @Override
        public String toString() {
            return "ColumnItem{" +
                    "id='" + id + '\'' +
                    ", pid='" + pid + '\'' +
                    ", itemcode='" + itemcode + '\'' +
                    ", itemname='" + itemname + '\'' +
                    ", defwidth='" + defwidth + '\'' +
                    ", minwidth='" + minwidth + '\'' +
                    ", displaymark=" + displaymark +
                    ", sensitivemark=" + sensitivemark +
                    ", fixed=" + fixed +
                    ", sortable=" + sortable +
                    ", overflow=" + overflow +
                    ", aligntype='" + aligntype + '\'' +
                    ", datasheet='" + datasheet + '\'' +
                    '}';
        }
    }


    public String getFormcode() {
        return formcode;
    }

    public void setFormcode(String formcode) {
        this.formcode = formcode;
    }

    public String getSubcode() {
        return subcode;
    }

    public void setSubcode(String subcode) {
        this.subcode = subcode;
    }

    public String getSensitivecode() {
        return sensitivecode;
    }

    public void setSensitivecode(String sensitivecode) {
        this.sensitivecode = sensitivecode;
    }

    public List<ColumnItem> getItem() {
        return item;
    }

    public void setItem(List<ColumnItem> item) {
        this.item = item;
    }

    public List<ColumnItem> getSpec() {
        return spec;
    }

    public void setSpec(List<ColumnItem> spec) {
        this.spec = spec;
    }

    @Override
    public String toString() {
        return "ColumnReqVO{" +
                "formcode='" + formcode + '\'' +
                ", subcode='" + subcode + '\'' +
                ", sensitivecode='" + sensitivecode + '\'' +
                ", item=" + item +
                ", spu=" + spec +
                '}';
    }
}
