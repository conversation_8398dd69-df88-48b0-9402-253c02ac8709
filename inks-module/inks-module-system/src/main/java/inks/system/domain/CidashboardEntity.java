package inks.system.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 工作台(Cidashboard)实体类
 *
 * <AUTHOR>
 * @since 2022-07-16 16:19:12
 */
public class CidashboardEntity implements Serializable {
    private static final long serialVersionUID = 168284099473258969L;
         // id
         private String id;
         // 通用分组
         private String gengroupid;
         // 工作台编码
         private String dashcode;
         // 工作台名称
         private String dashname;
         // 描述
         private String dashdesc;
         // 封面图片
         private String frontphoto;
         // MVC位置
         private String mvcurl;
         // 许可编码
         private String permcode;
         // 有效标识
         private Integer enabledmark;
         // 摘要
         private String remark;
         // 创建者
         private String createby;
         // 创建者id
         private String createbyid;
         // 新建日期
         private Date createdate;
         // 制表
         private String lister;
         // 制表id
         private String listerid;
         // 修改日期
         private Date modifydate;
         // 通用/定制
         private String tenantid;
         // 租户名称
         private String tenantname;
         // 乐观锁
         private Integer revision;

// id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 通用分组
    public String getGengroupid() {
        return gengroupid;
    }
    
    public void setGengroupid(String gengroupid) {
        this.gengroupid = gengroupid;
    }
        
// 工作台编码
    public String getDashcode() {
        return dashcode;
    }
    
    public void setDashcode(String dashcode) {
        this.dashcode = dashcode;
    }
        
// 工作台名称
    public String getDashname() {
        return dashname;
    }
    
    public void setDashname(String dashname) {
        this.dashname = dashname;
    }
        
// 描述
    public String getDashdesc() {
        return dashdesc;
    }
    
    public void setDashdesc(String dashdesc) {
        this.dashdesc = dashdesc;
    }
        
// 封面图片
    public String getFrontphoto() {
        return frontphoto;
    }
    
    public void setFrontphoto(String frontphoto) {
        this.frontphoto = frontphoto;
    }
        
// MVC位置
    public String getMvcurl() {
        return mvcurl;
    }
    
    public void setMvcurl(String mvcurl) {
        this.mvcurl = mvcurl;
    }
        
// 许可编码
    public String getPermcode() {
        return permcode;
    }
    
    public void setPermcode(String permcode) {
        this.permcode = permcode;
    }
        
// 有效标识
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
// 摘要
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 通用/定制
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

