package inks.system.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 邮件模板(Ciemail)实体类
 *
 * <AUTHOR>
 * @since 2022-04-29 16:20:08
 */
public class CiemailEntity implements Serializable {
    private static final long serialVersionUID = -20554925230794481L;
         // id
         private String id;
         // 模块编码
         private String modulecode;
         // 邮件名称
         private String emailname;
         // 邮件编码
         private String emailcode;
         // 邮件类型
         private String emailtype;
         // 邮件模版
         private String emailtemplate;
         // 默认收件人
         private String deftojson;
         // 默认抄送人
         private String defccjson;
         // 单页行数
         private Integer pagerow;
         // 序号
         private Integer rownum;
         // 有效标识
         private Integer enabledmark;
         // 备注
         private String remark;
         // 创建者
         private String createby;
         // 创建者id
         private String createbyid;
         // 新建日期
         private Date createdate;
         // 制表id
         private String listerid;
         // 制表
         private String lister;
         // 修改日期
         private Date modifydate;
         // 自定义1
         private String custom1;
         // 自定义2
         private String custom2;
         // 自定义3
         private String custom3;
         // 自定义4
         private String custom4;
         // 自定义5
         private String custom5;
         // 租户id
         private String tenantid;
         // 租户名称
         private String tenantname;
         // 乐观锁
         private Integer revision;

// id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 模块编码
    public String getModulecode() {
        return modulecode;
    }
    
    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }
        
// 邮件名称
    public String getEmailname() {
        return emailname;
    }
    
    public void setEmailname(String emailname) {
        this.emailname = emailname;
    }
        
// 邮件编码
    public String getEmailcode() {
        return emailcode;
    }
    
    public void setEmailcode(String emailcode) {
        this.emailcode = emailcode;
    }
        
// 邮件类型
    public String getEmailtype() {
        return emailtype;
    }
    
    public void setEmailtype(String emailtype) {
        this.emailtype = emailtype;
    }
        
// 邮件模版
    public String getEmailtemplate() {
        return emailtemplate;
    }
    
    public void setEmailtemplate(String emailtemplate) {
        this.emailtemplate = emailtemplate;
    }
        
// 默认收件人
    public String getDeftojson() {
        return deftojson;
    }
    
    public void setDeftojson(String deftojson) {
        this.deftojson = deftojson;
    }
        
// 默认抄送人
    public String getDefccjson() {
        return defccjson;
    }
    
    public void setDefccjson(String defccjson) {
        this.defccjson = defccjson;
    }
        
// 单页行数
    public Integer getPagerow() {
        return pagerow;
    }
    
    public void setPagerow(Integer pagerow) {
        this.pagerow = pagerow;
    }
        
// 序号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
// 有效标识
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
// 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
// 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
// 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
// 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
// 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

