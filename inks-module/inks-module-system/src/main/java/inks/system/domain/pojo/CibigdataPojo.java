package inks.system.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.util.List;

/**
 * 数据大屏(Cibigdata)实体类
 *
 * <AUTHOR>
 * @since 2021-12-25 10:32:02
 */
public class CibigdataPojo implements Serializable {
    private static final long serialVersionUID = -95226009497156063L;
         // id
          @Excel(name = "id")
    private String id;
         // 大屏类型
          @Excel(name = "大屏类型")
    private String bdtype;
         // 大屏编码
          @Excel(name = "大屏编码")
    private String bdcode;
         // 大屏名称
          @Excel(name = "大屏名称")
    private String bdname;
         // 大屏标题
          @Excel(name = "大屏标题")
    private String bdtitle;
         // 封面图片
          @Excel(name = "封面图片")
    private String frontphoto;
         // Css图标
          @Excel(name = "Css图标")
    private String imagecss;
         // MVC位置
          @Excel(name = "MVC位置")
    private String mvcurl;
         // 排列序号
          @Excel(name = "排列序号")
    private Integer rownum;
         // 有效标识
          @Excel(name = "有效标识")
    private Integer enabledmark;
         // 是否公共
          @Excel(name = "是否公共")
    private Integer ispublic;
         // 许可编码
          @Excel(name = "许可编码")
    private String permissioncode;
         // 订制或默认
          @Excel(name = "订制或默认")
    private String tenantid;
         // 备注
          @Excel(name = "备注")
    private String remark;
         // 创建者
          @Excel(name = "创建者")
    private String createby;
         // 创建者id
          @Excel(name = "创建者id")
    private String createbyid;
         // 新建日期
          @Excel(name = "新建日期")
    private Date createdate;
         // 制表
          @Excel(name = "制表")
    private String lister;
         // 制表id
          @Excel(name = "制表id")
    private String listerid;
         // 修改日期
          @Excel(name = "修改日期")
    private Date modifydate;
         // 乐观锁
          @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<CibigdataitemPojo> item;
    
   // id
     public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // 大屏类型
     public String getBdtype() {
        return bdtype;
    }
    
    public void setBdtype(String bdtype) {
        this.bdtype = bdtype;
    }
        
   // 大屏编码
     public String getBdcode() {
        return bdcode;
    }
    
    public void setBdcode(String bdcode) {
        this.bdcode = bdcode;
    }
        
   // 大屏名称
     public String getBdname() {
        return bdname;
    }
    
    public void setBdname(String bdname) {
        this.bdname = bdname;
    }
        
   // 大屏标题
     public String getBdtitle() {
        return bdtitle;
    }
    
    public void setBdtitle(String bdtitle) {
        this.bdtitle = bdtitle;
    }
        
   // 封面图片
     public String getFrontphoto() {
        return frontphoto;
    }
    
    public void setFrontphoto(String frontphoto) {
        this.frontphoto = frontphoto;
    }
        
   // Css图标
     public String getImagecss() {
        return imagecss;
    }
    
    public void setImagecss(String imagecss) {
        this.imagecss = imagecss;
    }
        
   // MVC位置
     public String getMvcurl() {
        return mvcurl;
    }
    
    public void setMvcurl(String mvcurl) {
        this.mvcurl = mvcurl;
    }
        
   // 排列序号
     public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 有效标识
     public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
   // 是否公共
     public Integer getIspublic() {
        return ispublic;
    }
    
    public void setIspublic(Integer ispublic) {
        this.ispublic = ispublic;
    }
        
   // 许可编码
     public String getPermissioncode() {
        return permissioncode;
    }
    
    public void setPermissioncode(String permissioncode) {
        this.permissioncode = permissioncode;
    }
        
   // 订制或默认
     public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 备注
     public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 创建者
     public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 创建者id
     public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
   // 新建日期
     public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表
     public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 制表id
     public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
   // 修改日期
     public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
   // 乐观锁
     public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

    public List<CibigdataitemPojo> getItem() {
        return item;
    }

    public void setItem(List<CibigdataitemPojo> item) {
        this.item = item;
    }


}

