package inks.system.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * RMS功能(Pirmsfunct)实体类
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
public class PirmsfunctPojo implements Serializable {
    private static final long serialVersionUID = -45992153967693425L;
         // 服务id
         @Excel(name = "服务id") 
    private String rmsfunctid;
         // 服务编码
         @Excel(name = "服务编码") 
    private String rmsfunctcode;
         // 服务名称
         @Excel(name = "服务名称") 
    private String rmsfunctname;
         // 描述
         @Excel(name = "描述") 
    private String description;
         // 主服务id
         @Excel(name = "主服务id") 
    private String functionid;
         // 主服务编码
         @Excel(name = "主服务编码") 
    private String functioncode;
         // 主服务名称
         @Excel(name = "主服务名称") 
    private String functionname;
         // 有效性
         @Excel(name = "有效性") 
    private Integer enabledmark;
         // 行号
         @Excel(name = "行号") 
    private Integer rownum;
         // 备注
         @Excel(name = "备注") 
    private String remark;
         // 创建者
         @Excel(name = "创建者") 
    private String createby;
         // 创建者id
         @Excel(name = "创建者id") 
    private String createbyid;
         // 新建日期
         @Excel(name = "新建日期") 
    private Date createdate;
         // 制表
         @Excel(name = "制表") 
    private String lister;
         // 制表id
         @Excel(name = "制表id") 
    private String listerid;
         // 修改日期
         @Excel(name = "修改日期") 
    private Date modifydate;
         // 乐观锁
         @Excel(name = "乐观锁") 
    private Integer revision;

     // 服务id
       public String getRmsfunctid() {
        return rmsfunctid;
    }
    
    public void setRmsfunctid(String rmsfunctid) {
        this.rmsfunctid = rmsfunctid;
    }
        
     // 服务编码
       public String getRmsfunctcode() {
        return rmsfunctcode;
    }
    
    public void setRmsfunctcode(String rmsfunctcode) {
        this.rmsfunctcode = rmsfunctcode;
    }
        
     // 服务名称
       public String getRmsfunctname() {
        return rmsfunctname;
    }
    
    public void setRmsfunctname(String rmsfunctname) {
        this.rmsfunctname = rmsfunctname;
    }
        
     // 描述
       public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
        
     // 主服务id
       public String getFunctionid() {
        return functionid;
    }
    
    public void setFunctionid(String functionid) {
        this.functionid = functionid;
    }
        
     // 主服务编码
       public String getFunctioncode() {
        return functioncode;
    }
    
    public void setFunctioncode(String functioncode) {
        this.functioncode = functioncode;
    }
        
     // 主服务名称
       public String getFunctionname() {
        return functionname;
    }
    
    public void setFunctionname(String functionname) {
        this.functionname = functionname;
    }
        
     // 有效性
       public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
     // 行号
       public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
     // 备注
       public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
     // 创建者
       public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
     // 创建者id
       public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
     // 新建日期
       public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
     // 制表
       public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
     // 制表id
       public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
     // 修改日期
       public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
     // 乐观锁
       public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

