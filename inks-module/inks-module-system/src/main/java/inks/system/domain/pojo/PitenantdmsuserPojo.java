package inks.system.domain.pojo;

import java.util.Date;
import java.io.Serializable;

import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * DMS租户关系表(Pitenantdmsuser)实体类
 *
 * <AUTHOR>
 * @since 2023-04-11 09:55:43
 */
public class PitenantdmsuserPojo implements Serializable {
    private static final long serialVersionUID = 400989546678537400L;
    // ID
    @Excel(name = "ID")
    private String id;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;
    // 用户ID
    @Excel(name = "用户ID")
    private String userid;
    // 登录名
    @Excel(name = "登录名")
    private String username;
    // 姓名
    @Excel(name = "姓名")
    private String realname;
    // 类型 1客户2供应商3加工厂商
    @Excel(name = "类型 1客户2供应商3加工厂商")
    private Integer usertype;
    // 是否管理员
    @Excel(name = "是否管理员")
    private Integer isadmin;
    // 组织id
    @Excel(name = "组织id")
    private String deptid;
    // 组织编码
    @Excel(name = "组织编码")
    private String deptcode;
    // 组织名称
    @Excel(name = "组织名称")
    private String deptname;
    // 是否部门主管
    @Excel(name = "是否部门主管")
    private Integer isdeptadmin;
    // 部门内行号
    @Excel(name = "部门内行号")
    private Integer deptrownum;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 状态:0正常,1禁用
    @Excel(name = "状态:0正常,1禁用")
    private Integer userstatus;
    // 编码(备用)
    @Excel(name = "编码(备用)")
    private String usercode;
    // 往来单位id
    @Excel(name = "往来单位id")
    private String groupids;
    // 往来单位
    @Excel(name = "往来单位")
    private String groupnames;
    // DMS服务ids
    @Excel(name = "DMS服务ids")
    private String dmsfunctids;
    // DMS服务
    @Excel(name = "DMS服务")
    private String dmsfunctnames;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // ---------PiDmsUser表字段---------
    // 昵称
    private String nickname;
    // 手机
    private String mobile;
    // 用户邮箱
    private String email;
    // 性别
    private Integer sex;
    // 头像
    private String avatar;
    // ID
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 租户名称
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

    // 用户ID
    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }


    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    // 登录名
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    // 姓名
    public String getRealname() {
        return realname;
    }

    public void setRealname(String realname) {
        this.realname = realname;
    }

    // 类型 1客户2供应商3加工厂商
    public Integer getUsertype() {
        return usertype;
    }

    public void setUsertype(Integer usertype) {
        this.usertype = usertype;
    }

    // 是否管理员
    public Integer getIsadmin() {
        return isadmin;
    }

    public void setIsadmin(Integer isadmin) {
        this.isadmin = isadmin;
    }

    // 组织id
    public String getDeptid() {
        return deptid;
    }

    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }

    // 组织编码
    public String getDeptcode() {
        return deptcode;
    }

    public void setDeptcode(String deptcode) {
        this.deptcode = deptcode;
    }

    // 组织名称
    public String getDeptname() {
        return deptname;
    }

    public void setDeptname(String deptname) {
        this.deptname = deptname;
    }

    // 是否部门主管
    public Integer getIsdeptadmin() {
        return isdeptadmin;
    }

    public void setIsdeptadmin(Integer isdeptadmin) {
        this.isdeptadmin = isdeptadmin;
    }

    // 部门内行号
    public Integer getDeptrownum() {
        return deptrownum;
    }

    public void setDeptrownum(Integer deptrownum) {
        this.deptrownum = deptrownum;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 状态:0正常,1禁用
    public Integer getUserstatus() {
        return userstatus;
    }

    public void setUserstatus(Integer userstatus) {
        this.userstatus = userstatus;
    }

    // 编码(备用)
    public String getUsercode() {
        return usercode;
    }

    public void setUsercode(String usercode) {
        this.usercode = usercode;
    }

    // 往来单位id
    public String getGroupids() {
        return groupids;
    }

    public void setGroupids(String groupids) {
        this.groupids = groupids;
    }

    // 往来单位
    public String getGroupnames() {
        return groupnames;
    }

    public void setGroupnames(String groupnames) {
        this.groupnames = groupnames;
    }

    // DMS服务ids
    public String getDmsfunctids() {
        return dmsfunctids;
    }

    public void setDmsfunctids(String dmsfunctids) {
        this.dmsfunctids = dmsfunctids;
    }

    // DMS服务
    public String getDmsfunctnames() {
        return dmsfunctnames;
    }

    public void setDmsfunctnames(String dmsfunctnames) {
        this.dmsfunctnames = dmsfunctnames;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

