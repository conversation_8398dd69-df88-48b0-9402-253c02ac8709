package inks.system.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 服务参数关系(Pifunctionconfig)实体类
 *
 * <AUTHOR>
 * @since 2022-03-25 11:20:10
 */
public class PifunctionconfigPojo implements Serializable {
    private static final long serialVersionUID = -79759234989380953L;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 0平台/1租户/2用户
    @Excel(name = "0平台/1租户/2用户")
    private Integer cfglevel;
    // 0System/1Module
    @Excel(name = "0System/1Module")
    private Integer cfgtype;
    // value
    @Excel(name = "value")
    private String cfgvalue;
    // 模块.key
    @Excel(name = "模块.key")
    private String cfgkey;
    // 参数名称
    @Excel(name = "参数名称")
    private String cfgname;
    // 参数id
    @Excel(name = "参数id")
    private String cfgid;
    // 服务名称
    @Excel(name = "服务名称")
    private String functionname;
    // 服务编码
    @Excel(name = "服务编码")
    private String functioncode;
    // 服务id
    @Excel(name = "服务id")
    private String functionid;
    // id
    private String id;

    // 父级主键
    private String parentid;

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 0平台/1租户/2用户
    public Integer getCfglevel() {
        return cfglevel;
    }

    public void setCfglevel(Integer cfglevel) {
        this.cfglevel = cfglevel;
    }

    // 0System/1Module
    public Integer getCfgtype() {
        return cfgtype;
    }

    public void setCfgtype(Integer cfgtype) {
        this.cfgtype = cfgtype;
    }

    // value
    public String getCfgvalue() {
        return cfgvalue;
    }

    public void setCfgvalue(String cfgvalue) {
        this.cfgvalue = cfgvalue;
    }

    // 模块.key
    public String getCfgkey() {
        return cfgkey;
    }

    public void setCfgkey(String cfgkey) {
        this.cfgkey = cfgkey;
    }

    // 参数名称
    public String getCfgname() {
        return cfgname;
    }

    public void setCfgname(String cfgname) {
        this.cfgname = cfgname;
    }

    // 参数id
    public String getCfgid() {
        return cfgid;
    }

    public void setCfgid(String cfgid) {
        this.cfgid = cfgid;
    }

    // 服务名称
    public String getFunctionname() {
        return functionname;
    }

    public void setFunctionname(String functionname) {
        this.functionname = functionname;
    }

    // 服务编码
    public String getFunctioncode() {
        return functioncode;
    }

    public void setFunctioncode(String functioncode) {
        this.functioncode = functioncode;
    }

    // 服务id
    public String getFunctionid() {
        return functionid;
    }

    public void setFunctionid(String functionid) {
        this.functionid = functionid;
    }

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }


    public String getParentid() {
        return parentid;
    }

    public void setParentid(String parentid) {
        this.parentid = parentid;
    }
}

