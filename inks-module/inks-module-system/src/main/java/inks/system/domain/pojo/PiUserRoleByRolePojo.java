package inks.system.domain.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;
public class PiUserRoleByRolePojo {
    @JsonProperty("id")
    @ApiModelProperty(value = "ID")
    /** ID */
    private String id;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRoleid() {
        return Roleid;
    }

    public void setRoleid(String roleid) {
        Roleid = roleid;
    }

    public String getUserid() {
        return Userid;
    }

    public void setUserid(String userid) {
        Userid = userid;
    }

    public String getLister() {
        return Lister;
    }

    public void setLister(String lister) {
        Lister = lister;
    }

    public Date getCreateDate() {
        return CreateDate;
    }

    public void setCreateDate(Date createDate) {
        CreateDate = createDate;
    }

    public Date getModifyDate() {
        return ModifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        ModifyDate = modifyDate;
    }

    public String getTenantid() {
        return Tenantid;
    }

    public void setTenantid(String tenantid) {
        Tenantid = tenantid;
    }

    public List<PirolePojo> getItem() {
        return Item;
    }

    public void setItem(List<PirolePojo> item) {
        Item = item;
    }

    public String getRoleCode() {
        return RoleCode;
    }

    public void setRoleCode(String roleCode) {
        RoleCode = roleCode;
    }

    public String getRealName() {
        return RealName;
    }

    public void setRealName(String realName) {
        RealName = realName;
    }

    public List<PiuserPojo> getUsers() {
        return Users;
    }

    public void setUsers(List<PiuserPojo> users) {
        Users = users;
    }

    @JsonProperty("Roleid")
    @ApiModelProperty(value = "角色ID")
    /** 角色ID */
    private String Roleid;

    @JsonProperty("Userid")
    @ApiModelProperty(value = "用户ID")
    /** 用户ID */
    private String Userid;

    @JsonProperty("Lister")
    @ApiModelProperty(value = "制表")
    /** 制表 */
    private String Lister;

    @JsonProperty("CreateDate")
    @ApiModelProperty(value = "新建日期")
    /** 新建日期 */
    private Date CreateDate;

    @JsonProperty("ModifyDate")
    @ApiModelProperty(value = "修改日期")
    /** 修改日期 */
    private Date ModifyDate;

    @JsonProperty("Tenantid")
    @ApiModelProperty(value = "租户id")
    /** 租户id */
    private String Tenantid;
    //角色表
    @JsonProperty("Item")
    private List<PirolePojo> Item;
    @JsonProperty("RoleCode")
    @ApiModelProperty(value = "编码")
    /** 编码 */
    private String RoleCode;

    @JsonProperty("RealName")
    @ApiModelProperty(value = "中文名")
    /** 中文名 */
    private String RealName;

    //角色表
    @JsonProperty("Users")
    private List<PiuserPojo> Users;

}
