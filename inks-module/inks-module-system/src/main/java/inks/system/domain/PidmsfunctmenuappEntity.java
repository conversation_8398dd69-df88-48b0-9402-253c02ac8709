package inks.system.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * DMSAPP关系(Pidmsfunctmenuapp)实体类
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
public class PidmsfunctmenuappEntity implements Serializable {
    private static final long serialVersionUID = -36593274415269642L;
         // id
         private String id;
         // 服务id
         private String dmsfunctid;
         // 服务编码
         private String dmsfunctcode;
         // 服务名称
         private String dmsfunctname;
         // 菜单id
         private String navid;
         // 导航编码
         private String navcode;
         // 导航名称
         private String navname;
         // 备注
         private String remark;
         // 创建者
         private String createby;
         // 创建者id
         private String createbyid;
         // 新建日期
         private Date createdate;
         // 制表
         private String lister;
         // 制表id
         private String listerid;
         // 修改日期
         private Date modifydate;
         // 乐观锁
         private Integer revision;

// id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 服务id
    public String getDmsfunctid() {
        return dmsfunctid;
    }
    
    public void setDmsfunctid(String dmsfunctid) {
        this.dmsfunctid = dmsfunctid;
    }
        
// 服务编码
    public String getDmsfunctcode() {
        return dmsfunctcode;
    }
    
    public void setDmsfunctcode(String dmsfunctcode) {
        this.dmsfunctcode = dmsfunctcode;
    }
        
// 服务名称
    public String getDmsfunctname() {
        return dmsfunctname;
    }
    
    public void setDmsfunctname(String dmsfunctname) {
        this.dmsfunctname = dmsfunctname;
    }
        
// 菜单id
    public String getNavid() {
        return navid;
    }
    
    public void setNavid(String navid) {
        this.navid = navid;
    }
        
// 导航编码
    public String getNavcode() {
        return navcode;
    }
    
    public void setNavcode(String navcode) {
        this.navcode = navcode;
    }
        
// 导航名称
    public String getNavname() {
        return navname;
    }
    
    public void setNavname(String navname) {
        this.navname = navname;
    }
        
// 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

