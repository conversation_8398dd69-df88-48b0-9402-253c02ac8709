package inks.system.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 数据字典(Cidict)实体类
 *
 * <AUTHOR>
 * @since 2022-03-12 08:03:19
 */
public class CidictEntity implements Serializable {
    private static final long serialVersionUID = -67555137402902914L;
      // id
      private String id;
      // 字典分组
      private String dictgroupid;
      // 对应字段
      private String dictcode;
      // 字典名称
      private String dictname;
      // 功能模块(备用)
      private String modulecode;
      // 行号
      private Integer rownum;
      // 有效性
      private Integer enabledmark;
      // 摘要
      private String summary;
      // 创建者
      private String createby;
      // 创建者id
      private String createbyid;
      // 新建日期
      private Date createdate;
      // 制表
      private String lister;
      // 制表id
      private String listerid;
      // 修改日期
      private Date modifydate;
      // 自定义1
      private String custom1;
      // 自定义2
      private String custom2;
      // 自定义3
      private String custom3;
      // 自定义4
      private String custom4;
      // 自定义5
      private String custom5;
      // 租户id
      private String tenantid;
      // 租户名称
      private String tenantname;
      // 乐观锁
      private Integer revision;

       // id
         public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
       // 字典分组
         public String getDictgroupid() {
        return dictgroupid;
    }
    
    public void setDictgroupid(String dictgroupid) {
        this.dictgroupid = dictgroupid;
    }
        
       // 对应字段
         public String getDictcode() {
        return dictcode;
    }
    
    public void setDictcode(String dictcode) {
        this.dictcode = dictcode;
    }
        
       // 字典名称
         public String getDictname() {
        return dictname;
    }
    
    public void setDictname(String dictname) {
        this.dictname = dictname;
    }
        
       // 功能模块(备用)
         public String getModulecode() {
        return modulecode;
    }
    
    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }
        
       // 行号
         public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
       // 有效性
         public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
       // 摘要
         public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
        
       // 创建者
         public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
       // 创建者id
         public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
       // 新建日期
         public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
       // 制表
         public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
       // 制表id
         public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
       // 修改日期
         public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
       // 自定义1
         public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
       // 自定义2
         public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
       // 自定义3
         public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
       // 自定义4
         public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
       // 自定义5
         public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
       // 租户id
         public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
       // 租户名称
         public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
       // 乐观锁
         public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

