package inks.system.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 用户组织架构(Pidept)实体类
 *
 * <AUTHOR>
 * @since 2022-03-28 11:10:58
 */
public class PideptEntity implements Serializable {
    private static final long serialVersionUID = -35155075695276332L;
         // id
         private String id;
         // Parentid
         private String parentid;
         // 祖组列表
         private String ancestors;
         // 编码
         private String deptcode;
         // 名称
         private String deptname;
         // 有效性
         private Integer enabledmark;
         // 负责人
         private String leader;
         // 电话
         private String phone;
         // 邮箱
         private String email;
         // 行号
         private Integer rownum;
         // 备注
         private String remark;
         // 创建者
         private String createby;
         // 创建者id
         private String createbyid;
         // 新建日期
         private Date createdate;
         // 制表
         private String lister;
         // 制表id
         private String listerid;
         // 修改日期
         private Date modifydate;
         // 自定义1
         private String custom1;
         // 自定义2
         private String custom2;
         // 自定义3
         private String custom3;
         // 自定义4
         private String custom4;
         // 自定义5
         private String custom5;
         // 租户id
         private String tenantid;
         // 乐观锁
         private Integer revision;

// id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// Parentid
    public String getParentid() {
        return parentid;
    }
    
    public void setParentid(String parentid) {
        this.parentid = parentid;
    }
        
// 祖组列表
    public String getAncestors() {
        return ancestors;
    }
    
    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }
        
// 编码
    public String getDeptcode() {
        return deptcode;
    }
    
    public void setDeptcode(String deptcode) {
        this.deptcode = deptcode;
    }
        
// 名称
    public String getDeptname() {
        return deptname;
    }
    
    public void setDeptname(String deptname) {
        this.deptname = deptname;
    }
        
// 有效性
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
// 负责人
    public String getLeader() {
        return leader;
    }
    
    public void setLeader(String leader) {
        this.leader = leader;
    }
        
// 电话
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
        
// 邮箱
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
        
// 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
// 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
// 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
// 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
// 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
// 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

