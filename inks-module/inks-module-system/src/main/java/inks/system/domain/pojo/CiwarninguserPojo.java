package inks.system.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户预警(Ciwarninguser)实体类
 *
 * <AUTHOR>
 * @since 2022-07-05 20:11:15
 */
public class CiwarninguserPojo implements Serializable {
    private static final long serialVersionUID = -86467074870898739L;
    // id
    @Excel(name = "id")
    private String id;
    // 预警id
    @Excel(name = "预警id")
    private String warnid;
    // 时间差
    @Excel(name = "时间差")
    private Integer diffnum;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 摘要
    @Excel(name = "摘要")
    private String remark;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 用户id
    @Excel(name = "用户id")
    private String userid;
    // 姓名
    @Excel(name = "姓名")
    private String realname;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    // 模块编码

    private String modulecode;
    // 预警编码

    private String warncode;
    // 预警名称

    private String warnname;
    // 预警字段

    private String warnfield;
    // 服务编码

    private String svccode;
    // 预警接口

    private String warnapi;
    // web文件

    private String webpath;
    // Css图标

    private String imagecss;
    // 标签文本

    private String tagtitle;
    // 许可编码

    private String permcode;

    private String tagdata;
    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 预警id
    public String getWarnid() {
        return warnid;
    }

    public void setWarnid(String warnid) {
        this.warnid = warnid;
    }

    // 时间差
    public Integer getDiffnum() {
        return diffnum;
    }

    public void setDiffnum(Integer diffnum) {
        this.diffnum = diffnum;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 摘要
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 用户id
    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    // 姓名
    public String getRealname() {
        return realname;
    }

    public void setRealname(String realname) {
        this.realname = realname;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 租户名称
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public String getModulecode() {
        return modulecode;
    }

    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }

    public String getWarncode() {
        return warncode;
    }

    public void setWarncode(String warncode) {
        this.warncode = warncode;
    }

    public String getWarnname() {
        return warnname;
    }

    public void setWarnname(String warnname) {
        this.warnname = warnname;
    }

    public String getWarnfield() {
        return warnfield;
    }

    public void setWarnfield(String warnfield) {
        this.warnfield = warnfield;
    }

    public String getSvccode() {
        return svccode;
    }

    public void setSvccode(String svccode) {
        this.svccode = svccode;
    }

    public String getWarnapi() {
        return warnapi;
    }

    public void setWarnapi(String warnapi) {
        this.warnapi = warnapi;
    }

    public String getWebpath() {
        return webpath;
    }

    public void setWebpath(String webpath) {
        this.webpath = webpath;
    }

    public String getImagecss() {
        return imagecss;
    }

    public void setImagecss(String imagecss) {
        this.imagecss = imagecss;
    }

    public String getTagtitle() {
        return tagtitle;
    }

    public void setTagtitle(String tagtitle) {
        this.tagtitle = tagtitle;
    }

    public String getPermcode() {
        return permcode;
    }

    public void setPermcode(String permcode) {
        this.permcode = permcode;
    }

    public String getTagdata() {
        return tagdata;
    }

    public void setTagdata(String tagdata) {
        this.tagdata = tagdata;
    }
}

