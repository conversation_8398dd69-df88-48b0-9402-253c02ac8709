package inks.system.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * xls导入格式(Cixlsinput)实体类
 *
 * <AUTHOR>
 * @since 2023-07-26 08:55:23
 */
public class CixlsinputPojo implements Serializable {
    private static final long serialVersionUID = 537193559455241852L;
         // id
         @Excel(name = "id")
    private String id;
         // 模块编码
         @Excel(name = "模块编码")
    private String modulecode;
         // 类型(备用)
         @Excel(name = "类型(备用)")
    private String filetype;
         // XLS名称
         @Excel(name = "XLS名称")
    private String filename;
         // 列设定
         @Excel(name = "列设定")
    private String titlejson;
         // 序号
         @Excel(name = "序号")
    private Integer rownum;
         // 有效标识
         @Excel(name = "有效标识")
    private Integer enabledmark;
         // 备注
         @Excel(name = "备注")
    private String remark;
         // 创建者
         @Excel(name = "创建者")
    private String createby;
         // 创建者id
         @Excel(name = "创建者id")
    private String createbyid;
         // 新建日期
         @Excel(name = "新建日期")
    private Date createdate;
         // 制表
         @Excel(name = "制表")
    private String lister;
         // 制表id
         @Excel(name = "制表id")
    private String listerid;
         // 修改日期
         @Excel(name = "修改日期")
    private Date modifydate;
         // 自定义1
         @Excel(name = "自定义1")
    private String custom1;
         // 自定义2
         @Excel(name = "自定义2")
    private String custom2;
         // 自定义3
         @Excel(name = "自定义3")
    private String custom3;
         // 自定义4
         @Excel(name = "自定义4")
    private String custom4;
         // 自定义5
         @Excel(name = "自定义5")
    private String custom5;
         // 默认/定制
         @Excel(name = "默认/定制")
    private String tenantid;
         // 租户名称
         @Excel(name = "租户名称")
    private String tenantname;
         // 乐观锁
         @Excel(name = "乐观锁")
    private Integer revision;

     // id
       public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

     // 模块编码
       public String getModulecode() {
        return modulecode;
    }

    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }

     // 类型(备用)
       public String getFiletype() {
        return filetype;
    }

    public void setFiletype(String filetype) {
        this.filetype = filetype;
    }

     // XLS名称
       public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

     // 列设定
       public String getTitlejson() {
        return titlejson;
    }

    public void setTitlejson(String titlejson) {
        this.titlejson = titlejson;
    }

     // 序号
       public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

     // 有效标识
       public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }

     // 备注
       public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

     // 创建者
       public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

     // 创建者id
       public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

     // 新建日期
       public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

     // 制表
       public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

     // 制表id
       public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

     // 修改日期
       public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

     // 自定义1
       public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

     // 自定义2
       public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

     // 自定义3
       public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

     // 自定义4
       public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

     // 自定义5
       public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

     // 默认/定制
       public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

     // 租户名称
       public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

     // 乐观锁
       public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

