package inks.system.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 租户表(Pitenant)实体类
 *
 * <AUTHOR>
 * @since 2021-11-08 14:29:19
 */
public class PitenantPojo implements Serializable {
    private static final long serialVersionUID = -71076071042872433L;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 租户号
    @Excel(name = "租户号")
    private String tenantcode;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;
    // 租户公司
    @Excel(name = "租户公司")
    private String company;
    // 公司地址
    @Excel(name = "公司地址")
    private String companyadd;
    // 公司电话
    @Excel(name = "公司电话")
    private String companytel;
    // 联系人
    @Excel(name = "联系人")
    private String contactor;
    // 状态
    @Excel(name = "状态")
    private Integer tenantstate;
    // 销售id
    @Excel(name = "销售id")
    private String sellerid;
    // 销售编码
    @Excel(name = "销售编码")
    private String sellercode;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 上一次登录
    @Excel(name = "上一次登录")
    private Date previouvisit;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 租户号
    public String getTenantcode() {
        return tenantcode;
    }

    public void setTenantcode(String tenantcode) {
        this.tenantcode = tenantcode;
    }

    // 租户名称
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

    // 租户公司
    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    // 公司地址
    public String getCompanyadd() {
        return companyadd;
    }

    public void setCompanyadd(String companyadd) {
        this.companyadd = companyadd;
    }

    // 公司电话
    public String getCompanytel() {
        return companytel;
    }

    public void setCompanytel(String companytel) {
        this.companytel = companytel;
    }

    // 联系人
    public String getContactor() {
        return contactor;
    }

    public void setContactor(String contactor) {
        this.contactor = contactor;
    }

    // 状态
    public Integer getTenantstate() {
        return tenantstate;
    }

    public void setTenantstate(Integer tenantstate) {
        this.tenantstate = tenantstate;
    }

    // 销售id
    public String getSellerid() {
        return sellerid;
    }

    public void setSellerid(String sellerid) {
        this.sellerid = sellerid;
    }

    // 销售编码
    public String getSellercode() {
        return sellercode;
    }

    public void setSellercode(String sellercode) {
        this.sellercode = sellercode;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 上一次登录
    public Date getPreviouvisit() {
        return previouvisit;
    }

    public void setPreviouvisit(Date previouvisit) {
        this.previouvisit = previouvisit;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    // 子表
    private List<PitenantuserPojo> item;

    public List<PitenantuserPojo> getItem() {
        return item;
    }

    public void setItem(List<PitenantuserPojo> item) {
        this.item = item;
    }

}

