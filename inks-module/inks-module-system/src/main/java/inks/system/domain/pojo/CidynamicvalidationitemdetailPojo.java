package inks.system.domain.pojo;

import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;

/**
 * 动态校验规则子表(Cidynamicvalidationitem)Pojo
 *
 * <AUTHOR>
 * @since 2025-03-06 13:13:01
 */
public class CidynamicvalidationitemdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = -80533448090947556L;
     // 主键ID
  @Excel(name = "主键ID")    
  private String id;
     // 关联主表ID
  @Excel(name = "关联主表ID")    
  private String pid;
     // 字段名
  @Excel(name = "字段名")    
  private String fieldname;
     // 字段注释
  @Excel(name = "字段注释")    
  private String comment;
     // 规则类型（如NotNull、Regex、Size）
  @Excel(name = "规则类型（如NotNull、Regex、Size）")    
  private String ruletype;
     // 规则值（如正则表达式或范围值）
  @Excel(name = "规则值（如正则表达式或范围值）")    
  private String rulevalue;
     // 校验失败时的错误信息
  @Excel(name = "校验失败时的错误信息")    
  private String errormessage;
     // 哪些方法检查CRETE,UPDATE,DELETE,READ;默认全校验
  @Excel(name = "哪些方法检查CRETE,UPDATE,DELETE,READ;默认全校验")    
  private String checktype;
     // 备注
  @Excel(name = "备注")    
  private String remark;
     // 行号
  @Excel(name = "行号")    
  private Integer rownum;
     // 自定义1
  @Excel(name = "自定义1")    
  private String custom1;
     // 自定义2
  @Excel(name = "自定义2")    
  private String custom2;
     // 自定义3
  @Excel(name = "自定义3")    
  private String custom3;
     // 自定义4
  @Excel(name = "自定义4")    
  private String custom4;
     // 自定义5
  @Excel(name = "自定义5")    
  private String custom5;
     // 自定义6
  @Excel(name = "自定义6")    
  private String custom6;
     // 自定义7
  @Excel(name = "自定义7")    
  private String custom7;
     // 自定义8
  @Excel(name = "自定义8")    
  private String custom8;
     // 自定义9
  @Excel(name = "自定义9")    
  private String custom9;
     // 自定义10
  @Excel(name = "自定义10")    
  private String custom10;
     // 租户id
  @Excel(name = "租户id")    
  private String tenantid;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;

   // 主键ID
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // 关联主表ID
    public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
   // 字段名
    public String getFieldname() {
        return fieldname;
    }
    
    public void setFieldname(String fieldname) {
        this.fieldname = fieldname;
    }
        
   // 字段注释
    public String getComment() {
        return comment;
    }
    
    public void setComment(String comment) {
        this.comment = comment;
    }
        
   // 规则类型（如NotNull、Regex、Size）
    public String getRuletype() {
        return ruletype;
    }
    
    public void setRuletype(String ruletype) {
        this.ruletype = ruletype;
    }
        
   // 规则值（如正则表达式或范围值）
    public String getRulevalue() {
        return rulevalue;
    }
    
    public void setRulevalue(String rulevalue) {
        this.rulevalue = rulevalue;
    }
        
   // 校验失败时的错误信息
    public String getErrormessage() {
        return errormessage;
    }
    
    public void setErrormessage(String errormessage) {
        this.errormessage = errormessage;
    }
        
   // 哪些方法检查CRETE,UPDATE,DELETE,READ;默认全校验
    public String getChecktype() {
        return checktype;
    }
    
    public void setChecktype(String checktype) {
        this.checktype = checktype;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

