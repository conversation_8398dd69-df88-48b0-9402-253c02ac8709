package inks.system.domain;

import java.util.Date;
import java.io.Serializable;

/**
 * 后台导航(Pimenuweb)实体类
 *
 * <AUTHOR>
 * @since 2025-07-09 15:33:35
 */
public class PimenuwebEntity implements Serializable {
    private static final long serialVersionUID = 889045446658090410L;
     // Navid
    private String navid;
     // 父级id
    private String navpid;
     // 导航类型
    private String navtype;
     // 导航编码
    private String navcode;
     // 导航名称
    private String navname;
     // 分组(备用)
    private String navgroup;
     // 排列序号
    private Integer rownum;
     // Css图标
    private String imagecss;
     // Web图标
    private String iconurl;
     // Web位置
    private String navigateurl;
     // MVC位置
    private String mvcurl;
     // 路由组件
    private String routecomp;
     // 路由名称
    private String routename;
     // 路由路径
    private String routepath;
     // 模块类型
    private String moduletype;
     // 模块编码
    private String modulecode;
     // 角色编码
    private String rolecode;
     // 图标
    private String imageindex;
     // 图标样式
    private String imagestyle;
     // 有效标识
    private Integer enabledmark;
     // 菜单显示
    private Integer hiddenmark;
     // 备注
    private String remark;
     // 许可编码
    private String permissioncode;
     // 服务id
    private String functionid;
     // 服务编码
    private String functioncode;
     // 服务名称
    private String functionname;
     // 1为子应用
    private Integer ismicroapp;
     // 子应用名称
    private String microappname;
     // 子应用入口
    private String microappentry;
     // 子应用前缀
    private String microapprule;
     // 本地注册
    private String microapplocal;
     // 制表
    private String lister;
     // 新建日期
    private Date createdate;
     // 修改日期
    private Date modifydate;
     // 删除标识
    private Integer deletemark;
     // 制表
    private String deletelister;
     // 新建日期
    private Date deletedate;

// Navid
    public String getNavid() {
        return navid;
    }
    
    public void setNavid(String navid) {
        this.navid = navid;
    }
        
// 父级id
    public String getNavpid() {
        return navpid;
    }
    
    public void setNavpid(String navpid) {
        this.navpid = navpid;
    }
        
// 导航类型
    public String getNavtype() {
        return navtype;
    }
    
    public void setNavtype(String navtype) {
        this.navtype = navtype;
    }
        
// 导航编码
    public String getNavcode() {
        return navcode;
    }
    
    public void setNavcode(String navcode) {
        this.navcode = navcode;
    }
        
// 导航名称
    public String getNavname() {
        return navname;
    }
    
    public void setNavname(String navname) {
        this.navname = navname;
    }
        
// 分组(备用)
    public String getNavgroup() {
        return navgroup;
    }
    
    public void setNavgroup(String navgroup) {
        this.navgroup = navgroup;
    }
        
// 排列序号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
// Css图标
    public String getImagecss() {
        return imagecss;
    }
    
    public void setImagecss(String imagecss) {
        this.imagecss = imagecss;
    }
        
// Web图标
    public String getIconurl() {
        return iconurl;
    }
    
    public void setIconurl(String iconurl) {
        this.iconurl = iconurl;
    }
        
// Web位置
    public String getNavigateurl() {
        return navigateurl;
    }
    
    public void setNavigateurl(String navigateurl) {
        this.navigateurl = navigateurl;
    }
        
// MVC位置
    public String getMvcurl() {
        return mvcurl;
    }
    
    public void setMvcurl(String mvcurl) {
        this.mvcurl = mvcurl;
    }
        
// 路由组件
    public String getRoutecomp() {
        return routecomp;
    }
    
    public void setRoutecomp(String routecomp) {
        this.routecomp = routecomp;
    }
        
// 路由名称
    public String getRoutename() {
        return routename;
    }
    
    public void setRoutename(String routename) {
        this.routename = routename;
    }
        
// 路由路径
    public String getRoutepath() {
        return routepath;
    }
    
    public void setRoutepath(String routepath) {
        this.routepath = routepath;
    }
        
// 模块类型
    public String getModuletype() {
        return moduletype;
    }
    
    public void setModuletype(String moduletype) {
        this.moduletype = moduletype;
    }
        
// 模块编码
    public String getModulecode() {
        return modulecode;
    }
    
    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }
        
// 角色编码
    public String getRolecode() {
        return rolecode;
    }
    
    public void setRolecode(String rolecode) {
        this.rolecode = rolecode;
    }
        
// 图标
    public String getImageindex() {
        return imageindex;
    }
    
    public void setImageindex(String imageindex) {
        this.imageindex = imageindex;
    }
        
// 图标样式
    public String getImagestyle() {
        return imagestyle;
    }
    
    public void setImagestyle(String imagestyle) {
        this.imagestyle = imagestyle;
    }
        
// 有效标识
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
// 菜单显示
    public Integer getHiddenmark() {
        return hiddenmark;
    }
    
    public void setHiddenmark(Integer hiddenmark) {
        this.hiddenmark = hiddenmark;
    }
        
// 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 许可编码
    public String getPermissioncode() {
        return permissioncode;
    }
    
    public void setPermissioncode(String permissioncode) {
        this.permissioncode = permissioncode;
    }
        
// 服务id
    public String getFunctionid() {
        return functionid;
    }
    
    public void setFunctionid(String functionid) {
        this.functionid = functionid;
    }
        
// 服务编码
    public String getFunctioncode() {
        return functioncode;
    }
    
    public void setFunctioncode(String functioncode) {
        this.functioncode = functioncode;
    }
        
// 服务名称
    public String getFunctionname() {
        return functionname;
    }
    
    public void setFunctionname(String functionname) {
        this.functionname = functionname;
    }
        
// 1为子应用
    public Integer getIsmicroapp() {
        return ismicroapp;
    }
    
    public void setIsmicroapp(Integer ismicroapp) {
        this.ismicroapp = ismicroapp;
    }
        
// 子应用名称
    public String getMicroappname() {
        return microappname;
    }
    
    public void setMicroappname(String microappname) {
        this.microappname = microappname;
    }
        
// 子应用入口
    public String getMicroappentry() {
        return microappentry;
    }
    
    public void setMicroappentry(String microappentry) {
        this.microappentry = microappentry;
    }
        
// 子应用前缀
    public String getMicroapprule() {
        return microapprule;
    }
    
    public void setMicroapprule(String microapprule) {
        this.microapprule = microapprule;
    }
        
// 本地注册
    public String getMicroapplocal() {
        return microapplocal;
    }
    
    public void setMicroapplocal(String microapplocal) {
        this.microapplocal = microapplocal;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 删除标识
    public Integer getDeletemark() {
        return deletemark;
    }
    
    public void setDeletemark(Integer deletemark) {
        this.deletemark = deletemark;
    }
        
// 制表
    public String getDeletelister() {
        return deletelister;
    }
    
    public void setDeletelister(String deletelister) {
        this.deletelister = deletelister;
    }
        
// 新建日期
    public Date getDeletedate() {
        return deletedate;
    }
    
    public void setDeletedate(Date deletedate) {
        this.deletedate = deletedate;
    }
        

}

