package inks.system.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 预警项目(Ciwarning)实体类
 *
 * <AUTHOR>
 * @since 2022-07-05 20:11:00
 */
public class CiwarningPojo implements Serializable {
    private static final long serialVersionUID = -34876434615411571L;
    // id
    @Excel(name = "id")
    private String id;
    // 通用分组
    @Excel(name = "通用分组")
    private String gengroupid;
    // 模块编码
    @Excel(name = "模块编码")
    private String modulecode;
    // 预警编码
    @Excel(name = "预警编码")
    private String warncode;
    // 预警名称
    @Excel(name = "预警名称")
    private String warnname;
    // 预警字段
    @Excel(name = "预警字段")
    private String warnfield;
    // 服务编码
    @Excel(name = "服务编码")
    private String svccode;
    // 预警接口
    @Excel(name = "预警接口")
    private String warnapi;
    // web文件
    @Excel(name = "web文件")
    private String webpath;
    // Css图标
    @Excel(name = "Css图标")
    private String imagecss;
    // 标签文本
    @Excel(name = "标签文本")
    private String tagtitle;
    // 许可编码
    @Excel(name = "许可编码")
    private String permcode;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 有效性1
    @Excel(name = "有效性1")
    private Integer enabledmark;
    // 摘要
    @Excel(name = "摘要")
    private String remark;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 通用分组
    public String getGengroupid() {
        return gengroupid;
    }

    public void setGengroupid(String gengroupid) {
        this.gengroupid = gengroupid;
    }

    // 模块编码
    public String getModulecode() {
        return modulecode;
    }

    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }

    // 预警编码
    public String getWarncode() {
        return warncode;
    }

    public void setWarncode(String warncode) {
        this.warncode = warncode;
    }

    // 预警名称
    public String getWarnname() {
        return warnname;
    }

    public void setWarnname(String warnname) {
        this.warnname = warnname;
    }

    // 预警字段
    public String getWarnfield() {
        return warnfield;
    }

    public void setWarnfield(String warnfield) {
        this.warnfield = warnfield;
    }

    // 服务编码
    public String getSvccode() {
        return svccode;
    }

    public void setSvccode(String svccode) {
        this.svccode = svccode;
    }

    // 预警接口
    public String getWarnapi() {
        return warnapi;
    }

    public void setWarnapi(String warnapi) {
        this.warnapi = warnapi;
    }

    // web文件
    public String getWebpath() {
        return webpath;
    }

    public void setWebpath(String webpath) {
        this.webpath = webpath;
    }

    // Css图标
    public String getImagecss() {
        return imagecss;
    }

    public void setImagecss(String imagecss) {
        this.imagecss = imagecss;
    }

    // 标签文本
    public String getTagtitle() {
        return tagtitle;
    }

    public void setTagtitle(String tagtitle) {
        this.tagtitle = tagtitle;
    }

    // 许可编码
    public String getPermcode() {
        return permcode;
    }

    public void setPermcode(String permcode) {
        this.permcode = permcode;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 有效性1
    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }

    // 摘要
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

