package inks.system.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 公告(Cinotice)实体类
 *
 * <AUTHOR>
 * @since 2021-12-19 19:27:30
 */
public class CinoticeEntity implements Serializable {
    private static final long serialVersionUID = -89759793757419396L;
         // id
         private String id;
         // 公告编码
         private String noticecode;
         // 公告类型
         private String noticetype;
         // 单据标题
         private String noticetitle;
         // 公告内容
         private String noticecontent;
         // 公告日期
         private Date noticedate;
         // 行号
         private Integer rownum;
         // 有效性
         private Integer enabledmark;
         // 默认或指定
         private String tenantid;
         // 指定服务
         private String functionid;
         // 服务编码
         private String functioncode;
         // 服务名称
         private String functionname;
         // 摘要
         private String remark;
         // 创建者
         private String createby;
         // 创建者id
         private String createbyid;
         // 新建日期
         private Date createdate;
         // 制表
         private String lister;
         // 制表id
         private String listerid;
         // 修改日期
         private Date modifydate;
         // 乐观锁
         private Integer revision;

// id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 公告编码
    public String getNoticecode() {
        return noticecode;
    }
    
    public void setNoticecode(String noticecode) {
        this.noticecode = noticecode;
    }
        
// 公告类型
    public String getNoticetype() {
        return noticetype;
    }
    
    public void setNoticetype(String noticetype) {
        this.noticetype = noticetype;
    }
        
// 单据标题
    public String getNoticetitle() {
        return noticetitle;
    }
    
    public void setNoticetitle(String noticetitle) {
        this.noticetitle = noticetitle;
    }
        
// 公告内容
    public String getNoticecontent() {
        return noticecontent;
    }
    
    public void setNoticecontent(String noticecontent) {
        this.noticecontent = noticecontent;
    }
        
// 公告日期
    public Date getNoticedate() {
        return noticedate;
    }
    
    public void setNoticedate(Date noticedate) {
        this.noticedate = noticedate;
    }
        
// 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
// 有效性
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
// 默认或指定
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 指定服务
    public String getFunctionid() {
        return functionid;
    }
    
    public void setFunctionid(String functionid) {
        this.functionid = functionid;
    }
        
// 服务编码
    public String getFunctioncode() {
        return functioncode;
    }
    
    public void setFunctioncode(String functioncode) {
        this.functioncode = functioncode;
    }
        
// 服务名称
    public String getFunctionname() {
        return functionname;
    }
    
    public void setFunctionname(String functionname) {
        this.functionname = functionname;
    }
        
// 摘要
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

