package inks.system.domain;

import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 订单明细(Cifnorderitem)Entity
 *
 * <AUTHOR>
 * @since 2021-11-23 20:17:47
 */
public class CifnorderitemEntity implements Serializable {
    private static final long serialVersionUID = 137046682045773576L;
          // id
         private String id;
          // Pid
         private String pid;
          // 定价表
         private String pricepolicyid;
          // 服务id
         private String functionid;
          // 周期编码
         private String cyclecode;
          // 容量大小
         private Integer container;
          // 数量
         private Double quantity;
          // 含税单价
         private Double taxprice;
          // 含税金额
         private Double taxamount;
          // 行号
         private Integer rownum;
          // 备注
         private String remark;
          // 发票已开
         private Integer invofinish;
          // 放弃开票
         private Integer invoclosed;
          // 乐观锁
         private Integer revision;

    // id
      public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
    // Pid
      public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
    // 定价表
      public String getPricepolicyid() {
        return pricepolicyid;
    }
    
    public void setPricepolicyid(String pricepolicyid) {
        this.pricepolicyid = pricepolicyid;
    }
        
    // 服务id
      public String getFunctionid() {
        return functionid;
    }
    
    public void setFunctionid(String functionid) {
        this.functionid = functionid;
    }
        
    // 周期编码
      public String getCyclecode() {
        return cyclecode;
    }
    
    public void setCyclecode(String cyclecode) {
        this.cyclecode = cyclecode;
    }
        
    // 容量大小
      public Integer getContainer() {
        return container;
    }
    
    public void setContainer(Integer container) {
        this.container = container;
    }
        
    // 数量
      public Double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }
        
    // 含税单价
      public Double getTaxprice() {
        return taxprice;
    }
    
    public void setTaxprice(Double taxprice) {
        this.taxprice = taxprice;
    }
        
    // 含税金额
      public Double getTaxamount() {
        return taxamount;
    }
    
    public void setTaxamount(Double taxamount) {
        this.taxamount = taxamount;
    }
        
    // 行号
      public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
    // 备注
      public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
    // 发票已开
      public Integer getInvofinish() {
        return invofinish;
    }
    
    public void setInvofinish(Integer invofinish) {
        this.invofinish = invofinish;
    }
        
    // 放弃开票
      public Integer getInvoclosed() {
        return invoclosed;
    }
    
    public void setInvoclosed(Integer invoclosed) {
        this.invoclosed = invoclosed;
    }
        
    // 乐观锁
      public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

