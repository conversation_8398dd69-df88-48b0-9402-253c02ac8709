package inks.system.domain;

import java.io.Serializable;

/**
 * 窗体验证子表(Ciformvailditem)Entity
 *
 * <AUTHOR>
 * @since 2025-07-31 17:04:04
 */
public class CiformvailditemEntity implements Serializable {
    private static final long serialVersionUID = 367839195264928261L;
     // id
    private String id;
     // Pid
    private String pid;
     // 字段名
    private String itemfield;
     // 字段注释
    private String itemlabel;
     // 是否必要
    private Integer required;
     // 最小字符长度
    private Integer minlimit;
     // 最大字符长度
    private Integer maxlimit;
     // 正则表达式
    private String regular;
     // 提示语
    private String tipmsg;
     // 提示语(英文)
    private String tipmsgen;
     // 有效标识
    private Integer enabledmark;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // Pid
    public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
   // 字段名
    public String getItemfield() {
        return itemfield;
    }
    
    public void setItemfield(String itemfield) {
        this.itemfield = itemfield;
    }
        
   // 字段注释
    public String getItemlabel() {
        return itemlabel;
    }
    
    public void setItemlabel(String itemlabel) {
        this.itemlabel = itemlabel;
    }
        
   // 是否必要
    public Integer getRequired() {
        return required;
    }
    
    public void setRequired(Integer required) {
        this.required = required;
    }
        
   // 最小字符长度
    public Integer getMinlimit() {
        return minlimit;
    }
    
    public void setMinlimit(Integer minlimit) {
        this.minlimit = minlimit;
    }
        
   // 最大字符长度
    public Integer getMaxlimit() {
        return maxlimit;
    }
    
    public void setMaxlimit(Integer maxlimit) {
        this.maxlimit = maxlimit;
    }
        
   // 正则表达式
    public String getRegular() {
        return regular;
    }
    
    public void setRegular(String regular) {
        this.regular = regular;
    }
        
   // 提示语
    public String getTipmsg() {
        return tipmsg;
    }
    
    public void setTipmsg(String tipmsg) {
        this.tipmsg = tipmsg;
    }
        
   // 提示语(英文)
    public String getTipmsgen() {
        return tipmsgen;
    }
    
    public void setTipmsgen(String tipmsgen) {
        this.tipmsgen = tipmsgen;
    }
        
   // 有效标识
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

