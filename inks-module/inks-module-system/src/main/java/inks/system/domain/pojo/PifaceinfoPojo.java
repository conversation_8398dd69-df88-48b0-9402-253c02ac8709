//package inks.system.domain.pojo;
//
//import cn.afterturn.easypoi.excel.annotation.Excel;
//import inks.system.config.face.PointsConverter;
//import inks.system.config.face.VectorConverter;
//
//import javax.persistence.Convert;
//import java.io.Serializable;
//import java.util.Date;
//
///**
// * 人脸信息表(Pifaceinfo)实体类
// *
// * <AUTHOR>
// * @since 2024-11-19 17:02:10
// */
//public class PifaceinfoPojo implements Serializable {
//    private static final long serialVersionUID = 396467047830483880L;
//     // 自增ID
//    @Excel(name = "自增ID")
//    private String id;
//     // 人脸向量
//    @Excel(name = "人脸向量")
//    @Convert(converter = VectorConverter.class)
//    private float[] features;
//     // 原始文件名
//    @Excel(name = "原始文件名")
//    private String filename;
//     // 文件保存路径
//    @Excel(name = "文件保存路径")
//    private String filepath;
//     // 人脸在图片中的x轴位置
//    @Excel(name = "人脸在图片中的x轴位置")
//    private Integer x;
//     // 人脸在图片中的y轴位置
//    @Excel(name = "人脸在图片中的y轴位置")
//    private Integer y;
//     // 宽
//    @Excel(name = "宽")
//    private Integer width;
//     // 高
//    @Excel(name = "高")
//    private Integer height;
//     // 人脸五点关键点
//    @Excel(name = "人脸五点关键点")
//    @Convert(converter = PointsConverter.class)
//    private double[][] points;
//     // 创建者
//    @Excel(name = "创建者")
//    private String createby;
//     // 创建者id
//    @Excel(name = "创建者id")
//    private String createbyid;
//     // 新建日期
//    @Excel(name = "新建日期")
//    private Date createdate;
//     // 制表
//    @Excel(name = "制表")
//    private String lister;
//     // 制表id
//    @Excel(name = "制表id")
//    private String listerid;
//     // 修改日期
//    @Excel(name = "修改日期")
//    private Date modifydate;
//     // 租户id
//    @Excel(name = "租户id")
//    private String tenantid;
//     // 租户名称
//    @Excel(name = "租户名称")
//    private String tenantname;
//     // 乐观锁
//    @Excel(name = "乐观锁")
//    private Integer revision;
//
//   // 自增ID
//    public String getId() {
//        return id;
//    }
//
//    public void setId(String id) {
//        this.id = id;
//    }
//
//    public float[] getFeatures() {
//        return features;
//    }
//
//    public void setFeatures(float[] features) {
//        this.features = features;
//    }
//
//    // 原始文件名
//    public String getFilename() {
//        return filename;
//    }
//
//    public void setFilename(String filename) {
//        this.filename = filename;
//    }
//
//   // 文件保存路径
//    public String getFilepath() {
//        return filepath;
//    }
//
//    public void setFilepath(String filepath) {
//        this.filepath = filepath;
//    }
//
//   // 人脸在图片中的x轴位置
//    public Integer getX() {
//        return x;
//    }
//
//    public void setX(Integer x) {
//        this.x = x;
//    }
//
//   // 人脸在图片中的y轴位置
//    public Integer getY() {
//        return y;
//    }
//
//    public void setY(Integer y) {
//        this.y = y;
//    }
//
//   // 宽
//    public Integer getWidth() {
//        return width;
//    }
//
//    public void setWidth(Integer width) {
//        this.width = width;
//    }
//
//   // 高
//    public Integer getHeight() {
//        return height;
//    }
//
//    public void setHeight(Integer height) {
//        this.height = height;
//    }
//
//    public double[][] getPoints() {
//        return points;
//    }
//
//    public void setPoints(double[][] points) {
//        this.points = points;
//    }
//
//    // 创建者
//    public String getCreateby() {
//        return createby;
//    }
//
//    public void setCreateby(String createby) {
//        this.createby = createby;
//    }
//
//   // 创建者id
//    public String getCreatebyid() {
//        return createbyid;
//    }
//
//    public void setCreatebyid(String createbyid) {
//        this.createbyid = createbyid;
//    }
//
//   // 新建日期
//    public Date getCreatedate() {
//        return createdate;
//    }
//
//    public void setCreatedate(Date createdate) {
//        this.createdate = createdate;
//    }
//
//   // 制表
//    public String getLister() {
//        return lister;
//    }
//
//    public void setLister(String lister) {
//        this.lister = lister;
//    }
//
//   // 制表id
//    public String getListerid() {
//        return listerid;
//    }
//
//    public void setListerid(String listerid) {
//        this.listerid = listerid;
//    }
//
//   // 修改日期
//    public Date getModifydate() {
//        return modifydate;
//    }
//
//    public void setModifydate(Date modifydate) {
//        this.modifydate = modifydate;
//    }
//
//   // 租户id
//    public String getTenantid() {
//        return tenantid;
//    }
//
//    public void setTenantid(String tenantid) {
//        this.tenantid = tenantid;
//    }
//
//   // 租户名称
//    public String getTenantname() {
//        return tenantname;
//    }
//
//    public void setTenantname(String tenantname) {
//        this.tenantname = tenantname;
//    }
//
//   // 乐观锁
//    public Integer getRevision() {
//        return revision;
//    }
//
//    public void setRevision(Integer revision) {
//        this.revision = revision;
//    }
//
//
//}
//
