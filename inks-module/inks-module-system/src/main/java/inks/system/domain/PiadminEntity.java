package inks.system.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 平台管理员(Piadmin)实体类
 *
 * <AUTHOR>
 * @since 2021-11-08 13:55:51
 */
public class PiadminEntity implements Serializable {
    private static final long serialVersionUID = 524709806331574300L;
         // 管理员ID
         private String adminid;
         // 登录号
         private String username;
         // 中文名
         private String realname;
         // 手机
         private String mobile;
         // 用户邮箱
         private String email;
         // 系统语言选择
         private String langcode;
         // 头像
         private String avatar;
         // 状态:0正常,1禁用
         private Integer userstatus;
         // 编码(备用)
         private String admincode;
         // 备注
         private String remark;
         //wxopenid
         private String wxopenid;
         //角色类型
         private Integer roletype;
         // 制表
         private String lister;
         // 新建日期
         private Date createdate;
         // 修改日期
         private Date modifydate;

// 管理员ID
    public String getAdminid() {
        return adminid;
    }

    public void setAdminid(String adminid) {
        this.adminid = adminid;
    }

// 登录号
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

// 中文名
    public String getRealname() {
        return realname;
    }

    public void setRealname(String realname) {
        this.realname = realname;
    }

// 手机
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

// 用户邮箱
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

// 系统语言选择
    public String getLangcode() {
        return langcode;
    }

    public void setLangcode(String langcode) {
        this.langcode = langcode;
    }

// 头像
    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

// 状态:0正常,1禁用
    public Integer getUserstatus() {
        return userstatus;
    }

    public void setUserstatus(Integer userstatus) {
        this.userstatus = userstatus;
    }

// 编码(备用)
    public String getAdmincode() {
        return admincode;
    }

    public void setAdmincode(String admincode) {
        this.admincode = admincode;
    }

// 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getWxopenid() {
        return wxopenid;
    }

    public void setWxopenid(String wxopenid) {
        this.wxopenid = wxopenid;
    }

    public Integer getRoletype() {
        return roletype;
    }

    public void setRoletype(Integer roletype) {
        this.roletype = roletype;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

// 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

// 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }


}

