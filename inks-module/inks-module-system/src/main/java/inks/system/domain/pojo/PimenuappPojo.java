package inks.system.domain.pojo;

import java.util.Date;
import java.io.Serializable;

import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * APP导航(Pimenuapp)实体类
 *
 * <AUTHOR>
 * @since 2021-11-08 14:07:53
 */
public class PimenuappPojo implements Serializable {
    private static final long serialVersionUID = -86753649209316849L;
    // Navid
    @Excel(name = "Navid")
    private String navid;
    // 父级id
    @Excel(name = "父级id")
    private String navpid;
    // 导航类型
    @Excel(name = "导航类型")
    private String navtype;
    // 导航编码
    @Excel(name = "导航编码")
    private String navcode;
    // 导航名称
    @Excel(name = "导航名称")
    private String navname;
    // 分组(备用)
    @Excel(name = "分组(备用)")
    private String navgroup;
    // 排列序号
    @Excel(name = "排列序号")
    private Integer rownum;
    // Css图标
    @Excel(name = "Css图标")
    private String imagecss;
    // Web图标
    @Excel(name = "Web图标")
    private String iconurl;
    // Web位置
    @Excel(name = "Web位置")
    private String navigateurl;
    // MVC位置
    @Excel(name = "MVC位置")
    private String mvcurl;
    // 模块类型
    @Excel(name = "模块类型")
    private String moduletype;
    // 模块编码
    @Excel(name = "模块编码")
    private String modulecode;
    // 角色编码
    @Excel(name = "角色编码")
    private String rolecode;
    // 图标
    @Excel(name = "图标")
    private String imageindex;
    // 图标样式
    @Excel(name = "图标样式")
    private String imagestyle;
    // 有效标识
    @Excel(name = "有效标识")
    private Integer enabledmark;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 许可编码
    @Excel(name = "许可编码")
    private String permissioncode;
    // 服务id
    @Excel(name = "服务id")
    private String functionid;
    // 服务编码
    @Excel(name = "服务编码")
    private String functioncode;
    // 服务名称
    @Excel(name = "服务名称")
    private String functionname;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 删除标识
    @Excel(name = "删除标识")
    private Integer deletemark;
    // 制表
    @Excel(name = "制表")
    private String deletelister;
    // 新建日期
    @Excel(name = "新建日期")
    private Date deletedate;

    // Navid
    public String getNavid() {
        return navid;
    }

    public void setNavid(String navid) {
        this.navid = navid;
    }

    // 父级id
    public String getNavpid() {
        return navpid;
    }

    public void setNavpid(String navpid) {
        this.navpid = navpid;
    }

    // 导航类型
    public String getNavtype() {
        return navtype;
    }

    public void setNavtype(String navtype) {
        this.navtype = navtype;
    }

    // 导航编码
    public String getNavcode() {
        return navcode;
    }

    public void setNavcode(String navcode) {
        this.navcode = navcode;
    }

    // 导航名称
    public String getNavname() {
        return navname;
    }

    public void setNavname(String navname) {
        this.navname = navname;
    }

    // 分组(备用)
    public String getNavgroup() {
        return navgroup;
    }

    public void setNavgroup(String navgroup) {
        this.navgroup = navgroup;
    }

    // 排列序号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // Css图标
    public String getImagecss() {
        return imagecss;
    }

    public void setImagecss(String imagecss) {
        this.imagecss = imagecss;
    }

    // Web图标
    public String getIconurl() {
        return iconurl;
    }

    public void setIconurl(String iconurl) {
        this.iconurl = iconurl;
    }

    // Web位置
    public String getNavigateurl() {
        return navigateurl;
    }

    public void setNavigateurl(String navigateurl) {
        this.navigateurl = navigateurl;
    }

    // MVC位置
    public String getMvcurl() {
        return mvcurl;
    }

    public void setMvcurl(String mvcurl) {
        this.mvcurl = mvcurl;
    }

    // 模块类型
    public String getModuletype() {
        return moduletype;
    }

    public void setModuletype(String moduletype) {
        this.moduletype = moduletype;
    }

    // 模块编码
    public String getModulecode() {
        return modulecode;
    }

    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }

    // 角色编码
    public String getRolecode() {
        return rolecode;
    }

    public void setRolecode(String rolecode) {
        this.rolecode = rolecode;
    }

    // 图标
    public String getImageindex() {
        return imageindex;
    }

    public void setImageindex(String imageindex) {
        this.imageindex = imageindex;
    }

    // 图标样式
    public String getImagestyle() {
        return imagestyle;
    }

    public void setImagestyle(String imagestyle) {
        this.imagestyle = imagestyle;
    }

    // 有效标识
    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 许可编码
    public String getPermissioncode() {
        return permissioncode;
    }

    public void setPermissioncode(String permissioncode) {
        this.permissioncode = permissioncode;
    }

    // 服务id
    public String getFunctionid() {
        return functionid;
    }

    public void setFunctionid(String functionid) {
        this.functionid = functionid;
    }

    // 服务编码
    public String getFunctioncode() {
        return functioncode;
    }

    public void setFunctioncode(String functioncode) {
        this.functioncode = functioncode;
    }

    // 服务名称
    public String getFunctionname() {
        return functionname;
    }

    public void setFunctionname(String functionname) {
        this.functionname = functionname;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 删除标识
    public Integer getDeletemark() {
        return deletemark;
    }

    public void setDeletemark(Integer deletemark) {
        this.deletemark = deletemark;
    }

    // 制表
    public String getDeletelister() {
        return deletelister;
    }

    public void setDeletelister(String deletelister) {
        this.deletelister = deletelister;
    }

    // 新建日期
    public Date getDeletedate() {
        return deletedate;
    }

    public void setDeletedate(Date deletedate) {
        this.deletedate = deletedate;
    }


}

