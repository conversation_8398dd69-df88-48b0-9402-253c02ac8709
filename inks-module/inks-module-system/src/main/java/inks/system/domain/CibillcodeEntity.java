package inks.system.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 单据编码(Cibillcode)实体类
 *
 * <AUTHOR>
 * @since 2021-11-08 13:27:43
 */
public class CibillcodeEntity implements Serializable {
    private static final long serialVersionUID = -34944229029178783L;
         // id
         private String id;
         // 功能编码
         private String modulecode;
         // 单据名称
         private String billname;
         // 前缀1
         private String prefix1;
         // 后缀1
         private String suffix1;
         // 前缀2
         private String prefix2;
         // 后缀2
         private String suffix2;
         // 前缀3
         private String prefix3;
         // 后缀3
         private String suffix3;
         // 前缀4
         private String prefix4;
         // 后缀4
         private String suffix4;
         // 前缀5
         private String prefix5;
         // 后缀5
         private String suffix5;
         // 年月日
         private String counttype;
         // 跳步
         private Integer step;
         // 当前序号
         private Integer currentnum;
         // 数据表
         private String tablename;
         // 时间字段
         private String datecolumn;
         // 列名
         private String columnname;
         // 数据过滤
         private String dbfilter;
         // 编号允许修改
         private Integer allowedit;
         // 允许删除复用
         private Integer allowdelete;
         // 参数1
         private String param1;
         // 参数2
         private String param2;
         // 参数3
         private String param3;
         // 参数4
         private String param4;
         // 参数5
         private String param5;
         // 备注
         private String remark;
         // 制表
         private String lister;
         // 新建日期
         private Date createdate;
         // 修改日期
         private Date modifydate;
         // 租户id
         private String tenantid;

// id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 功能编码
    public String getModulecode() {
        return modulecode;
    }
    
    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }
        
// 单据名称
    public String getBillname() {
        return billname;
    }
    
    public void setBillname(String billname) {
        this.billname = billname;
    }
        
// 前缀1
    public String getPrefix1() {
        return prefix1;
    }
    
    public void setPrefix1(String prefix1) {
        this.prefix1 = prefix1;
    }
        
// 后缀1
    public String getSuffix1() {
        return suffix1;
    }
    
    public void setSuffix1(String suffix1) {
        this.suffix1 = suffix1;
    }
        
// 前缀2
    public String getPrefix2() {
        return prefix2;
    }
    
    public void setPrefix2(String prefix2) {
        this.prefix2 = prefix2;
    }
        
// 后缀2
    public String getSuffix2() {
        return suffix2;
    }
    
    public void setSuffix2(String suffix2) {
        this.suffix2 = suffix2;
    }
        
// 前缀3
    public String getPrefix3() {
        return prefix3;
    }
    
    public void setPrefix3(String prefix3) {
        this.prefix3 = prefix3;
    }
        
// 后缀3
    public String getSuffix3() {
        return suffix3;
    }
    
    public void setSuffix3(String suffix3) {
        this.suffix3 = suffix3;
    }
        
// 前缀4
    public String getPrefix4() {
        return prefix4;
    }
    
    public void setPrefix4(String prefix4) {
        this.prefix4 = prefix4;
    }
        
// 后缀4
    public String getSuffix4() {
        return suffix4;
    }
    
    public void setSuffix4(String suffix4) {
        this.suffix4 = suffix4;
    }
        
// 前缀5
    public String getPrefix5() {
        return prefix5;
    }
    
    public void setPrefix5(String prefix5) {
        this.prefix5 = prefix5;
    }
        
// 后缀5
    public String getSuffix5() {
        return suffix5;
    }
    
    public void setSuffix5(String suffix5) {
        this.suffix5 = suffix5;
    }
        
// 年月日
    public String getCounttype() {
        return counttype;
    }
    
    public void setCounttype(String counttype) {
        this.counttype = counttype;
    }
        
// 跳步
    public Integer getStep() {
        return step;
    }
    
    public void setStep(Integer step) {
        this.step = step;
    }
        
// 当前序号
    public Integer getCurrentnum() {
        return currentnum;
    }
    
    public void setCurrentnum(Integer currentnum) {
        this.currentnum = currentnum;
    }
        
// 数据表
    public String getTablename() {
        return tablename;
    }
    
    public void setTablename(String tablename) {
        this.tablename = tablename;
    }
        
// 时间字段
    public String getDatecolumn() {
        return datecolumn;
    }
    
    public void setDatecolumn(String datecolumn) {
        this.datecolumn = datecolumn;
    }
        
// 列名
    public String getColumnname() {
        return columnname;
    }
    
    public void setColumnname(String columnname) {
        this.columnname = columnname;
    }
        
// 数据过滤
    public String getDbfilter() {
        return dbfilter;
    }
    
    public void setDbfilter(String dbfilter) {
        this.dbfilter = dbfilter;
    }
        
// 编号允许修改
    public Integer getAllowedit() {
        return allowedit;
    }
    
    public void setAllowedit(Integer allowedit) {
        this.allowedit = allowedit;
    }
        
// 允许删除复用
    public Integer getAllowdelete() {
        return allowdelete;
    }
    
    public void setAllowdelete(Integer allowdelete) {
        this.allowdelete = allowdelete;
    }
        
// 参数1
    public String getParam1() {
        return param1;
    }
    
    public void setParam1(String param1) {
        this.param1 = param1;
    }
        
// 参数2
    public String getParam2() {
        return param2;
    }
    
    public void setParam2(String param2) {
        this.param2 = param2;
    }
        
// 参数3
    public String getParam3() {
        return param3;
    }
    
    public void setParam3(String param3) {
        this.param3 = param3;
    }
        
// 参数4
    public String getParam4() {
        return param4;
    }
    
    public void setParam4(String param4) {
        this.param4 = param4;
    }
        
// 参数5
    public String getParam5() {
        return param5;
    }
    
    public void setParam5(String param5) {
        this.param5 = param5;
    }
        
// 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        

}

