package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.log.annotation.OperLog;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.service.CiinitlogService;
import inks.system.service.CireportsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 数据初始化表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 13:55:37
 */
@RestController
@RequestMapping("SYSM04B3")
@Api(tags = "SYSM04B3:数据初始化")
public class SYSM04B3Controller extends CiinitlogController {

    /**
     * 服务对象
     */
    @Resource
    private CiinitlogService ciinitlogService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    @Resource
    private CireportsService cireportsService;

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = "初始化销售", notes = "初始化销售", produces = "application/json")
    @RequestMapping(value = "/initSale", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiInitLog.Add")
    @OperLog(title = "初始化销售")
    public R<Integer> initSale(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);

            queryParam.setTenantid(loginUser.getTenantid());
            int initNum = this.ciinitlogService.initSale(queryParam);
            return R.ok(initNum, "初始化销售," + initNum);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 初始化采购", notes = "初始化采购", produces = "application/json")
    @RequestMapping(value = "/initBuy", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiInitLog.Add")
    @OperLog(title = "初始化采购")
    public R<Integer> initBuy(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);

            queryParam.setTenantid(loginUser.getTenantid());
            int initNum = this.ciinitlogService.initBuy(queryParam);
            return R.ok(initNum, "初始化采购," + initNum);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 初始化仓库记录", notes = "初始化仓库记录", produces = "application/json")
    @RequestMapping(value = "/initStore", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiInitLog.Add")
    @OperLog(title = "初始化仓库记录")
    public R<Integer> initStore(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);

            queryParam.setTenantid(loginUser.getTenantid());
            int initNum = this.ciinitlogService.initStore(queryParam);
            return R.ok(initNum, "初始化仓库记录," + initNum);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 初始化生产记录", notes = "初始化生产记录", produces = "application/json")
    @RequestMapping(value = "/initManu", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiInitLog.Add")
    @OperLog(title = "初始化生产记录")
    public R<Integer> initManu(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);

            queryParam.setTenantid(loginUser.getTenantid());
            int initNum = this.ciinitlogService.initManu(queryParam);
            return R.ok(initNum, "初始化生产记录," + initNum);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 初始化创建报表 拷贝默认到tid", notes = "", produces = "application/json")
    @RequestMapping(value = "/initReports", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiInitLog.Add")
    @OperLog(title = "初始化创建报表 拷贝默认到tid")
    public R<String> initReports() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.cireportsService.initReports(loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //租户首次注册后，权限code、角色、打印模板，数据从默认同步到tid，          前端做个mrp一样的进度窗口；
    @ApiOperation(value = " 初始化创建权限 拷贝默认到tid", notes = "", produces = "application/json")
    @RequestMapping(value = "/initPermissions", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiInitLog.Add")
    @OperLog(title = "初始化创建报表 拷贝默认到tid")
    public R<String> initPermissions() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.cireportsService.initPermissions(loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}





















