package inks.system.controller;

import inks.common.core.domain.R;
import inks.system.domain.pojo.PifunctionreportsPojo;
import inks.system.service.PifunctionreportsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 服务报表关系(PiFunctionReports)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-17 07:51:01
 */
@RestController
@RequestMapping("SYSM02B12")
@Api(tags = "SYSM02B12:服务报表关系")
public class SYSM02B12Controller extends PifunctionreportsController {

    /**
     * 服务对象
     */
    @Resource
    private PifunctionreportsService pifunctionreportsService;


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据服务号获取服务报表关系List", notes = "根据服务号获取服务报表关系List", produces = "application/json")
    @RequestMapping(value = "/getListByFunction", method = RequestMethod.GET)
    public R<List<PifunctionreportsPojo>> getListByFunction(String key) {
        return R.ok(this.pifunctionreportsService.getListByFunction(key));
    }
}
