package inks.system.controller;

import inks.common.core.domain.AppMenuPojo;
import inks.common.core.domain.AppMetaPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PidmsfunctmenuappPojo;
import inks.system.domain.pojo.PimenuappPojo;
import inks.system.domain.pojo.PitenantdmsuserPojo;
import inks.system.service.PidmsfunctmenuappService;
import inks.system.service.PitenantdmsuserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * DMSAPP关系(PiDmsFunctMenuApp)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@RestController
@RequestMapping("SYSM12B6")
@Api(tags = "SYSM12B6:DMS功能App菜单")
public class SYSM12B6Controller extends PidmsfunctmenuappController {

    @Resource
    private PidmsfunctmenuappService pidmsfunctmenuappService;
    @Resource
    private TokenService tokenService;
    @Resource
    private PitenantdmsuserService pitenantdmsuserService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据服务号获取app菜单关系List", notes = "根据服务号获取app菜单关系List", produces = "application/json")
    @RequestMapping(value = "/getListByFunction", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiDmsFunctMenuApp.List")
    public R<List<PidmsfunctmenuappPojo>> getListByFunction(String key) {
        try {
            return R.ok(this.pidmsfunctmenuappService.getListByFunction(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据  Eric 20230408
     *
     * @return 单条数据
     */
    @ApiOperation(value = "根据当前用户信息获取app菜单关系List", notes = "根据当前用户信息获取web菜单关系List", produces = "application/json")
    @RequestMapping(value = "/getListByLoginUser", method = RequestMethod.GET)
    public R<List<AppMenuPojo>> getListByLoginUser(Integer db) {
        try {

            LoginUser loginUser = this.tokenService.getLoginUser(ServletUtils.getRequest());
            PitenantdmsuserPojo entityByUserid = pitenantdmsuserService.getEntityByUserid(loginUser.getUserid(), loginUser.getTenantid());
            if (entityByUserid == null || StringUtils.isBlank(entityByUserid.getDmsfunctids())) {
                return R.fail("用户未分配服务号");
            }
            List<AppMenuPojo> list = null; //this.redisService.getCacheObject("tenant_manuweb:" + loginUser.getTenantid());
            if (db != null && db == 1) list = null;  // db=1,重读数据库
            if (list == null) {
                List<PimenuappPojo> lst = this.pidmsfunctmenuappService.getListByDmsFunctids(entityByUserid.getDmsfunctids());
                list = new ArrayList<>();
                for (int i = 0; i < lst.size(); i++) {
                    if (lst.get(i).getNavtype().equals("1")) {
                        AppMenuPojo appMenuPojo = new AppMenuPojo();
                        appMenuPojo.setName(lst.get(i).getNavname());
                        appMenuPojo.setPath(lst.get(i).getMvcurl());
                        appMenuPojo.setMeta(new AppMetaPojo(lst.get(i).getNavname(), lst.get(i).getImagecss(), lst.get(i).getImagestyle()));
                        List<AppMenuPojo> lstChil = new ArrayList<>();
                        for (int j = 0; j < lst.size(); j++) {
                            if (lst.get(j).getNavpid().equals(lst.get(i).getNavid())) {
                                AppMenuPojo appMenuPojo2 = new AppMenuPojo();
                                appMenuPojo2.setName(lst.get(j).getNavname());
                                appMenuPojo2.setPath(lst.get(j).getMvcurl());
                                appMenuPojo2.setMeta(new AppMetaPojo(lst.get(j).getNavname(), lst.get(j).getImagecss(), lst.get(i).getImagestyle()));
                                List<AppMenuPojo> lstChil2 = new ArrayList<>();
                                for (int k = 0; k < lst.size(); k++) {
                                    if (lst.get(k).getNavpid().equals(lst.get(j).getNavid())) {
                                        AppMenuPojo appMenuPojo3 = new AppMenuPojo();
                                        appMenuPojo3.setName(lst.get(k).getNavname());
                                        appMenuPojo3.setPath(lst.get(k).getMvcurl());
                                        appMenuPojo3.setMeta(new AppMetaPojo(lst.get(k).getNavname(), lst.get(k).getImagecss(), lst.get(i).getImagestyle()));
                                        lstChil2.add(appMenuPojo3);
                                    }
                                }
                                appMenuPojo2.setChildren(lstChil2);
                                lstChil.add(appMenuPojo2);
                            }
                        }
                        appMenuPojo.setChildren(lstChil);
                        list.add(appMenuPojo);
                    }
                }
                //this.redisService.setCacheObject("tenant_manuweb:" + loginUser.getTenantid(), list, (long) (1), TimeUnit.DAYS);
            }
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
