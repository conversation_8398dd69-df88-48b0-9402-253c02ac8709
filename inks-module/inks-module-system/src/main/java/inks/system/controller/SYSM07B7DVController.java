package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.config.DynamicValidation;
import inks.system.config.Operation;
import inks.system.domain.pojo.CidynamicvalidationPojo;
import inks.system.service.CidynamicvalidationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 动态校验规则(CiDynamicValidation)表控制层
 *
 * <AUTHOR>
 * @since 2025-03-04 15:54:38
 */
@RestController
@RequestMapping("SYSM07B7DV")
@Api(tags = "SYSM07B7DV:动态校验规则")
public class SYSM07B7DVController extends CidynamicvalidationController {

    @Resource
    private CidynamicvalidationService cidynamicvalidationService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = " 新增动态校验规则", notes = "新增动态校验规则", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiDynamicValidation.Add")
    @DynamicValidation(code = "CiDynamicValidationBill", type = Operation.CREATE) // 对应主表的 validationcode
    public R<CidynamicvalidationPojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            CidynamicvalidationPojo cidynamicvalidationPojo = JSONArray.parseObject(json, CidynamicvalidationPojo.class);

            cidynamicvalidationPojo.setCreateby(loginUser.getRealName());   // 创建者
            cidynamicvalidationPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            cidynamicvalidationPojo.setCreatedate(new Date());   // 创建时间
            cidynamicvalidationPojo.setLister(loginUser.getRealname());   // 制表
            cidynamicvalidationPojo.setListerid(loginUser.getUserid());    // 制表id
            cidynamicvalidationPojo.setModifydate(new Date());   //修改时间
            cidynamicvalidationPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.cidynamicvalidationService.insert(cidynamicvalidationPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
