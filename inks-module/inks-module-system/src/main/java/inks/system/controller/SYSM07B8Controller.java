package inks.system.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CibillgroupPojo;
import inks.system.service.CibillgroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 通用分组(CiBillGroup)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 13:28:23
 */
@RestController
@RequestMapping("SYSM07B8")
@Api(tags = "SYSM07B8:通用分组")
public class SYSM07B8Controller extends CibillgroupController {
    /**
     * 服务对象
     */
    @Resource
    private CibillgroupService cibillgroupService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 按模块编码查询报表
     *
     * @param Code 主键
     * @return 查询结果
     */
    @ApiOperation(value = "按模块编码查询分组", notes = "按模块编码查询分组", produces = "application/json")
    @RequestMapping(value = "/getListByModuleCode", method = RequestMethod.GET)
    public R<List<CibillgroupPojo>> getListByModuleCode(String Code) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<CibillgroupPojo> list = this.cibillgroupService.getListByModuleCode(Code, loginUser.getTenantid());
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 按模块编码查询报表
     *
     * @param Code 主键
     * @return 查询结果
     */
    @ApiOperation(value = "按模块编码查询Def分组", notes = "按模块编码查询Def分组", produces = "application/json")
    @RequestMapping(value = "/getDefListByModuleCode", method = RequestMethod.GET)
    public R<List<CibillgroupPojo>> getDefListByModuleCode(String Code) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<CibillgroupPojo> list = this.cibillgroupService.getListByModuleCode(Code, "default");
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
