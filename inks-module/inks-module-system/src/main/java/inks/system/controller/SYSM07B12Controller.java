package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiscenePojo;
import inks.system.domain.pojo.CiscenefieldPojo;
import inks.system.service.CisceneService;
import inks.system.service.CiscenefieldService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 场景管理(CiScene)表控制层
 *
 * <AUTHOR>
 * @since 2022-11-24 11:34:51
 */
@RestController
@RequestMapping("SYSM07B12")
@Api(tags = "SYSM07B12:场景管理")
public class SYSM07B12Controller extends CisceneController {
    /**
     * 服务对象
     */
    @Resource
    private CisceneService cisceneService;
    /**
     * 服务对象
     */
    @Resource
    private CiscenefieldService ciscenefieldService;

    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取场景字段详细信息", notes = "获取场景字段详细信息", produces = "application/json")
    @RequestMapping(value = "/getFieldEntity", method = RequestMethod.GET)
    public R<CiscenefieldPojo> getFieldEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.ciscenefieldService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getFieldPageList", method = RequestMethod.POST)
    public R<PageInfo<CiscenefieldPojo>> getFieldPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiSceneField.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.ciscenefieldService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增场景字段", notes = "新增场景字段", produces = "application/json")
    @RequestMapping(value = "/createField", method = RequestMethod.POST)
    public R<CiscenefieldPojo> createField(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            CiscenefieldPojo ciscenefieldPojo = JSONArray.parseObject(json, CiscenefieldPojo.class);

            ciscenefieldPojo.setCreateby(loginUser.getRealName());   // 创建者
            ciscenefieldPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            ciscenefieldPojo.setCreatedate(new Date());   // 创建时间
            ciscenefieldPojo.setLister(loginUser.getRealname());   // 制表
            ciscenefieldPojo.setListerid(loginUser.getUserid());    // 制表id
            ciscenefieldPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.ciscenefieldService.insert(ciscenefieldPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改场景字段", notes = "修改场景字段", produces = "application/json")
    @RequestMapping(value = "/updateField", method = RequestMethod.POST)
    public R<CiscenefieldPojo> updateField(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            CiscenefieldPojo ciscenefieldPojo = JSONArray.parseObject(json, CiscenefieldPojo.class);

            ciscenefieldPojo.setLister(loginUser.getRealname());   // 制表
            ciscenefieldPojo.setListerid(loginUser.getUserid());    // 制表id
            ciscenefieldPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.ciscenefieldService.update(ciscenefieldPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除场景字段", notes = "删除场景字段", produces = "application/json")
    @RequestMapping(value = "/deleteField", method = RequestMethod.GET)
    public R<Integer> deleteField(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.ciscenefieldService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "按code查询场景字段", notes = "按code查询场景字段", produces = "application/json")
    @RequestMapping(value = "/getFieldListByCode", method = RequestMethod.GET)
    public R<List<CiscenefieldPojo>> getFieldListByCode(String code) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.ciscenefieldService.getListByCode(code));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "按code查询用户场景列表", notes = "按code查询用户场景列表", produces = "application/json")
    @RequestMapping(value = "/getListByCode", method = RequestMethod.GET)
    public R<List<CiscenePojo>> getListByCode(String code) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.cisceneService.getListByCode(code, loginUser.getUserid(), loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
