package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PitenantrmsuserPojo;
import inks.system.service.PitenantrmsuserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * RMS租户关系表(PiTenantRmsUser)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@RestController
@RequestMapping("SYSM13B4")
@Api(tags = "SYSM13B4:租户关系表")
public class SYSM13B4Controller extends PitenantrmsuserController {
    @Resource
    private PitenantrmsuserService pitenantrmsuserService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = " 获取RMS租户关系表详细信息", notes = "获取RMS租户关系表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByUserid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiTenantRmsUser.List")
    public R<PitenantrmsuserPojo> getEntityByUserid(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pitenantrmsuserService.getEntityByUserid(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 获取RMS租户关系表详细信息", notes = "获取RMS租户关系表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityMapByUserid", method = RequestMethod.GET)
    public R<Map<String, Object>> getEntityMapByUserid(String key, String tid) {
        try {

            //LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            PitenantrmsuserPojo entityByUserid = this.pitenantrmsuserService.getEntityByUserid(key, tid);
            Map<String, Object> stringObjectMap = BeanUtils.beanToMap(entityByUserid);
            return R.ok(stringObjectMap);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增RMS(租户-用户)关联
     *
     * @param userid PiRmsUser.userid
     * @return 新增结果
     */
    @ApiOperation(value = " 新增RMS(租户-用户)关联", notes = "新增RMS租户关系表", produces = "application/json")
    @RequestMapping(value = "/createByUserId", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiTenantRmsUser.Add")
    public R<PitenantrmsuserPojo> createByUserId(String userid) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            PitenantrmsuserPojo pitenantrmsuserPojo = new PitenantrmsuserPojo();
            //(租户-用户)关联
            pitenantrmsuserPojo.setUserid(userid);
            pitenantrmsuserPojo.setUsername(loginUser.getUsername());
            pitenantrmsuserPojo.setTenantid(loginUser.getTenantid());
            pitenantrmsuserPojo.setRealname(loginUser.getRealname());
            pitenantrmsuserPojo.setIsadmin(loginUser.getIsadmin());
            if (loginUser.getTenantinfo() != null) {
                pitenantrmsuserPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
                pitenantrmsuserPojo.setDeptcode(loginUser.getTenantinfo().getDeptcode());
                pitenantrmsuserPojo.setDeptid(loginUser.getTenantinfo().getDeptid());
                pitenantrmsuserPojo.setDeptname(loginUser.getTenantinfo().getDeptname());
            }
            pitenantrmsuserPojo.setCreateby(loginUser.getRealName());   // 创建者
            pitenantrmsuserPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            pitenantrmsuserPojo.setCreatedate(new Date());   // 创建时间
            pitenantrmsuserPojo.setLister(loginUser.getRealname());   // 制表
            pitenantrmsuserPojo.setListerid(loginUser.getUserid());    // 制表id
            pitenantrmsuserPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.pitenantrmsuserService.insert(pitenantrmsuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "根据当前用户获得租户关系表", notes = "根据当前用户获得租户关系表", produces = "application/json")
    @RequestMapping(value = "/getListByUser", method = RequestMethod.GET)
    public R<List<PitenantrmsuserPojo>> getListByUser() {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pitenantrmsuserService.getListByUser(loginUser.getUserid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增RMS(租户-用户)关联
     *
     * @param json PiRmsUser.userid
     * @return 新增结果
     */
    @ApiOperation(value = " 新增RMS(租户-用户)关联", notes = "新增RMS租户关系表", produces = "application/json")
    @RequestMapping(value = "/createRmsUser", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiTenantRmsUser.Add")
    public R<PitenantrmsuserPojo> createRmsUser(@RequestBody String json) {
        try {
            PitenantrmsuserPojo pitenantrmsuserPojo = JSONArray.parseObject(json, PitenantrmsuserPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pitenantrmsuserPojo.setTenantid(loginUser.getTenantid());
            if (loginUser.getTenantinfo() != null) {
                pitenantrmsuserPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
                pitenantrmsuserPojo.setDeptcode(loginUser.getTenantinfo().getDeptcode());
                pitenantrmsuserPojo.setDeptid(loginUser.getTenantinfo().getDeptid());
                pitenantrmsuserPojo.setDeptname(loginUser.getTenantinfo().getDeptname());
            }
            pitenantrmsuserPojo.setCreateby(loginUser.getRealName());   // 创建者
            pitenantrmsuserPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            pitenantrmsuserPojo.setCreatedate(new Date());   // 创建时间
            pitenantrmsuserPojo.setLister(loginUser.getRealname());   // 制表
            pitenantrmsuserPojo.setListerid(loginUser.getUserid());    // 制表id
            pitenantrmsuserPojo.setModifydate(new Date());   //修改时间

            //判断是否传入userid;有，修改；无，新增
            PitenantrmsuserPojo pitenantrmsuser = pitenantrmsuserService.createRmsUser(pitenantrmsuserPojo);
            return R.ok(pitenantrmsuser);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
