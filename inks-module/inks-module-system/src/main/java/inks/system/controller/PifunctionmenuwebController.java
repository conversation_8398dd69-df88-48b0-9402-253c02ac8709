package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PifunctionmenuwebPojo;
import inks.system.service.PifunctionmenuwebService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 服务菜单关系(PiFunctionMenuWeb)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-11 17:34:47
 */

public class PifunctionmenuwebController {
    /**
     * 服务对象
     */
    @Resource
    private PifunctionmenuwebService pifunctionmenuwebService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取服务菜单关系详细信息", notes = "获取服务菜单关系详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionMenuWeb.List")
    public R<PifunctionmenuwebPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pifunctionmenuwebService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiFunctionMenuWeb.List")
    public R<PageInfo<PifunctionmenuwebPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiFunctionMenuWeb.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.pifunctionmenuwebService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增服务菜单关系", notes = "新增服务菜单关系", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiFunctionMenuWeb.Add")
    public R<PifunctionmenuwebPojo> create(@RequestBody String json) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            this.redisService.deleteObject("tenant_manuweb:" + loginUser.getTenantid());
            PifunctionmenuwebPojo pifunctionmenuwebPojo = JSONArray.parseObject(json, PifunctionmenuwebPojo.class);
            pifunctionmenuwebPojo.setCreateby(loginUser.getRealname());   //创建者
            pifunctionmenuwebPojo.setCreatedate(new Date());   //创建时间
            pifunctionmenuwebPojo.setLister(loginUser.getRealname());   //用户名
            pifunctionmenuwebPojo.setModifydate(new Date());   //修改时间
            pifunctionmenuwebPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.pifunctionmenuwebService.insert(pifunctionmenuwebPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改服务菜单关系", notes = "修改服务菜单关系", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiFunctionMenuWeb.Edit")
    public R<PifunctionmenuwebPojo> update(@RequestBody String json) {
        try {
            PifunctionmenuwebPojo pifunctionmenuwebPojo = JSONArray.parseObject(json, PifunctionmenuwebPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            this.redisService.deleteObject("tenant_manuweb:" + loginUser.getTenantid());
            pifunctionmenuwebPojo.setLister(loginUser.getRealname());   //用户名
            pifunctionmenuwebPojo.setTenantid(loginUser.getTenantid());   //租户id
            pifunctionmenuwebPojo.setModifydate(new Date());   //修改时间
//            pifunctionmenuwebPojo.setAssessor(""); //审核员
//            pifunctionmenuwebPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.pifunctionmenuwebService.update(pifunctionmenuwebPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除服务菜单关系", notes = "删除服务菜单关系", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionMenuWeb.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            this.redisService.deleteObject("tenant_manuweb:" + loginUser.getTenantid());
            return R.ok(this.pifunctionmenuwebService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据服务号获取web菜单关系List", notes = "根据服务号获取web菜单关系List", produces = "application/json")
    @RequestMapping(value = "/getListByFunction", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionMenuWeb.List")
    public R<List<PifunctionmenuwebPojo>> getListByFunction(String key) {
        try {
            return R.ok(this.pifunctionmenuwebService.getListByFunction(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}

