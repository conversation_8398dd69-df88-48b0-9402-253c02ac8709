package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PiweblnkPojo;
import inks.system.service.PiweblnkService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 快捷方式(PiWebLnk)表控制层
 *
 * <AUTHOR>
 * @since 2023-05-08 14:11:24
 */
@RestController
@RequestMapping("piweblnk")
public class PiweblnkController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(PiweblnkController.class);
    /**
     * 服务对象
     */
    @Resource
    private PiweblnkService piweblnkService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取快捷方式详细信息", notes = "获取快捷方式详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiWebLnk.List")
    public R<PiweblnkPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.piweblnkService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiWebLnk.List")
    public R<PageInfo<PiweblnkPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiWebLnk.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.piweblnkService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增快捷方式", notes = "新增快捷方式", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiWebLnk.Add")
    public R<PiweblnkPojo> create(@RequestBody String json) {
        try {
            PiweblnkPojo piweblnkPojo = JSONArray.parseObject(json, PiweblnkPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            piweblnkPojo.setCreateby(loginUser.getRealName());   // 创建者
//            piweblnkPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            piweblnkPojo.setCreatedate(new Date());   // 创建时间
            piweblnkPojo.setLister(loginUser.getRealname());   // 制表
//            piweblnkPojo.setListerid(loginUser.getUserid());    // 制表id
            piweblnkPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.piweblnkService.insert(piweblnkPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改快捷方式", notes = "修改快捷方式", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiWebLnk.Edit")
    public R<PiweblnkPojo> update(@RequestBody String json) {
        try {
            PiweblnkPojo piweblnkPojo = JSONArray.parseObject(json, PiweblnkPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            piweblnkPojo.setLister(loginUser.getRealname());   // 制表
//            piweblnkPojo.setListerid(loginUser.getUserid());    // 制表id
            piweblnkPojo.setModifydate(new Date());   //修改时间
//            piweblnkPojo.setAssessor(""); // 审核员
//            piweblnkPojo.setAssessorid(""); // 审核员id
//            piweblnkPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.piweblnkService.update(piweblnkPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除快捷方式", notes = "删除快捷方式", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiWebLnk.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.piweblnkService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiWebLnk.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        PiweblnkPojo piweblnkPojo = this.piweblnkService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(piweblnkPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 分页查询
     *
     * @param key 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "项目全菜单", notes = "项目全菜单", produces = "application/json")
    @RequestMapping(value = "/getTreeList", method = RequestMethod.GET)
    public R<List<WebMenuPojo>> getTreeList(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<PiweblnkPojo> lst = this.piweblnkService.getListByPid(key);
            List<WebMenuPojo> list = new ArrayList<>();
            for (int i = 0; i < lst.size(); i++) {
                WebMenuPojo webMenuPojo = new WebMenuPojo();
                webMenuPojo.setName(lst.get(i).getNavname());
                webMenuPojo.setPath(lst.get(i).getMvcurl());
                webMenuPojo.setMeta(new WebMetaPojo(lst.get(i).getNavname(), lst.get(i).getImagecss()));
                List<WebMenuPojo> lstChil = new ArrayList<>();
                List<PiweblnkPojo> lst2 = this.piweblnkService.getListByPid(lst.get(i).getNavid());
                for (int j = 0; j < lst2.size(); j++) {
                    WebMenuPojo webMenuPojo2 = new WebMenuPojo();
                    webMenuPojo2.setName(lst2.get(j).getNavname());
                    webMenuPojo2.setPath(lst2.get(j).getMvcurl());
                    webMenuPojo2.setMeta(new WebMetaPojo(lst2.get(j).getNavname(), lst2.get(j).getImagecss()));
                    List<WebMenuPojo> lstChil2 = new ArrayList<>();
                    List<PiweblnkPojo> lst3 = this.piweblnkService.getListByPid(lst2.get(j).getNavid());
                    for (int k = 0; k < lst3.size(); k++) {
                        WebMenuPojo webMenuPojo3 = new WebMenuPojo();
                        webMenuPojo3.setName(lst3.get(k).getNavname());
                        webMenuPojo3.setPath(lst3.get(k).getMvcurl());
                        webMenuPojo3.setMeta(new WebMetaPojo(lst3.get(k).getNavname(), lst3.get(k).getImagecss()));
                        lstChil2.add(webMenuPojo3);
                    }
                    webMenuPojo2.setChildren(lstChil2);
                    lstChil.add(webMenuPojo2);
                }

                webMenuPojo.setChildren(lstChil);
                list.add(webMenuPojo);
            }
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}

