package inks.system.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.domain.WebMenuPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PimenuwebPojo;
import inks.system.mapper.PirolemenuwebMapper;
import inks.system.service.PimenuwebService;
import inks.system.service.PisubscriberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Web导航(PiMenuWeb)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 14:23:16
 */
@RestController
@RequestMapping("SYSM05B2")
@Api(tags = "SYSM05B2:Web导航")
public class SYSM05B2Controller extends PimenuwebController {
    @Resource
    private PisubscriberService pisubscriberService;
    @Resource
    private PimenuwebService pimenuwebService;

    @Resource
    private TokenService tokenService;
    @Resource
    private PirolemenuwebMapper pirolemenuwebMapper;

    /**
     * 分页查询
     *
     * @param key 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "项目全菜单 传root时为全菜单", notes = "项目全菜单", produces = "application/json")
    @RequestMapping(value = "/getAllListByPid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiMenuWeb.List")
    public R<List<PimenuwebPojo>> getAllListByPid(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            List<PimenuwebPojo> lst = this.pimenuwebService.getListByPid(key);
            return R.ok(flattenMenuList(lst));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    // 抄接口/getMenuWebListBySelf,只不过把返回改为平行的List<PimenuwebPojo>了
    @ApiOperation(value = "项目全菜单 当前租户的", notes = "项目全菜单", produces = "application/json")
    @RequestMapping(value = "/getMenuWebListByTen", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiMenuWeb.List")
    public R<List<PimenuwebPojo>> getMenuWebListByTen(@RequestParam(required = false) String fncode) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            List<PimenuwebPojo> lst = this.pisubscriberService.getMenuWebListByTenant(loginUser.getTenantid(), fncode);
            return R.ok(lst);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "返回层级结构：项目菜单(Children格式) 登录用户userid->roleids->navids->menus  admin拿全部菜单", notes = "", produces = "application/json")
    @RequestMapping(value = "/getMenuWebListByLoginUser", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "PiMenuApp.List")
    public R<List<WebMenuPojo>> getMenuWebListByLoginUser() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            List<PimenuwebPojo> lst;
            if (Objects.equals(loginUser.getIsadmin(), 1)) {//admin拿全部菜单
                lst = this.pisubscriberService.getMenuWebListByTenant(loginUser.getTenantid(), null);
            } else {
                List<String> navids = pirolemenuwebMapper.getNavidsByUserid(loginUser.getUserid(), loginUser.getTenantid());
                if (CollectionUtils.isEmpty(navids)) {
//                    throw new Exception("用户未关联菜单权限Navid");
                    return R.ok(new ArrayList<>());
                }
                lst = pimenuwebService.getListByNavids(navids);
            }
            // 将Web菜单列表List 转为树形结构List
            return R.ok(PisubscriberController.buildWebMenuToChildren(lst));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "返回平铺结构：项目菜单 roleid->navids->menus", notes = "", produces = "application/json")
    @RequestMapping(value = "/getMenuWebListByRole", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiMenuApp.List")
    public R<List<PimenuwebPojo>> getMenuAppListByRole(String roleid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            List<String> navids = pirolemenuwebMapper.getNavidsByRoleid(roleid, loginUser.getTenantid());
            if (CollectionUtils.isEmpty(navids)) {
                return R.ok(new ArrayList<>());
            }
            List<PimenuwebPojo> lst = pimenuwebService.getListByNavids(navids);
            return R.ok(lst);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "返回平铺结构：项目菜单 userid->roleids->navids->menus", notes = "", produces = "application/json")
    @RequestMapping(value = "/getMenuWebListByUser", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiMenuApp.List")
    public R<List<PimenuwebPojo>> getMenuWebListByUser(String userid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            List<String> navids = pirolemenuwebMapper.getNavidsByUserid(userid, loginUser.getTenantid());
            if (CollectionUtils.isEmpty(navids)) {
                return R.ok(new ArrayList<>());
            }
            List<PimenuwebPojo> lst = pimenuwebService.getListByNavids(navids);
            return R.ok(lst);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    private List<PimenuwebPojo> flattenMenuList(List<PimenuwebPojo> lst) {
        List<PimenuwebPojo> list = new ArrayList<>();
        for (PimenuwebPojo pimenuwebPojo : lst) {
            PimenuwebPojo webMenuPojo = new PimenuwebPojo();
            BeanUtils.copyProperties(pimenuwebPojo, webMenuPojo);
            list.add(webMenuPojo);
            List<PimenuwebPojo> lst2 = pimenuwebService.getListByPid(pimenuwebPojo.getNavid());
            for (PimenuwebPojo pojo : lst2) {
                PimenuwebPojo webMenuPojo2 = new PimenuwebPojo();
                BeanUtils.copyProperties(pojo, webMenuPojo2);
                list.add(webMenuPojo2);
                List<PimenuwebPojo> lst3 = pimenuwebService.getListByPid(pojo.getNavid());
                for (PimenuwebPojo value : lst3) {
                    PimenuwebPojo webMenuPojo3 = new PimenuwebPojo();
                    BeanUtils.copyProperties(value, webMenuPojo3);
                    list.add(webMenuPojo3);
                }
            }
        }
        // 使用Stream API根据Navid去重 这种方法利用了Map键的唯一性特性来实现去重。
        return new ArrayList<>(list.stream()
                .collect(Collectors.toMap(
                        PimenuwebPojo::getNavid, // 使用Navid作为键
                        pojo -> pojo,            // 保留Pojo对象
                        (existing, replacement) -> existing // 如果有重复，保留现有的
                ))
                .values());
    }

}
