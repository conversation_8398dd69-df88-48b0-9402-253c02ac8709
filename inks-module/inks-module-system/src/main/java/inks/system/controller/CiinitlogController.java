package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiinitlogPojo;
import inks.system.service.CiinitlogService;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 初始化日志(CiInitLog)表控制层
 *
 * <AUTHOR>
 * @since 2022-07-31 11:20:20
 */
public class CiinitlogController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(CiinitlogController.class);
    /**
     * 服务对象
     */
    @Resource
    private CiinitlogService ciinitlogService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取初始化日志详细信息", notes = "获取初始化日志详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiInitLog.List")
    public R<CiinitlogPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.ciinitlogService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiInitLog.List")
    public R<PageInfo<CiinitlogPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiInitLog.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.ciinitlogService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增初始化日志", notes = "新增初始化日志", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiInitLog.Add")
    public R<CiinitlogPojo> create(@RequestBody String json) {
        try {
            CiinitlogPojo ciinitlogPojo = JSONArray.parseObject(json, CiinitlogPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            ciinitlogPojo.setCreateby(loginUser.getRealName());   // 创建者
            ciinitlogPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            ciinitlogPojo.setCreatedate(new Date());   // 创建时间
            ciinitlogPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.ciinitlogService.insert(ciinitlogPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除初始化日志", notes = "删除初始化日志", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiInitLog.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.ciinitlogService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}

