package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.AESUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PiadminPojo;
import inks.system.service.PiadminService;
import inks.system.service.PiadminloginService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 平台管理员(PiAdmin)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 13:55:51
 */

public class PiadminController {
    /**
     * 服务对象
     */
    @Resource
    private PiadminService piadminService;

    /**
     * 服务对象
     */
    @Resource
    private PiadminloginService piadminloginService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取平台管理员详细信息", notes = "获取平台管理员详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiAdmin.List")
    public R<PiadminPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.piadminService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiAdmin.List")
    public R<PageInfo<PiadminPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiAdmin.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.piadminService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增平台管理员", notes = "新增平台管理员", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiAdmin.Add")
    public R<PiadminPojo> create(@RequestBody String json) {
        try {
            PiadminPojo piadminPojo = JSONArray.parseObject(json, PiadminPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            piadminPojo.setLister(loginUser.getRealname());   //用户名
            // piadminPojo.setTenantid(loginUser.getTenantid());   //租户id
            piadminPojo.setModifydate(new Date());   //修改时间   
            return R.ok(this.piadminService.insert(piadminPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改平台管理员", notes = "修改平台管理员", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiAdmin.Edit")
    public R<PiadminPojo> update(@RequestBody String json) {
        try {
            PiadminPojo piadminPojo = JSONArray.parseObject(json, PiadminPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            piadminPojo.setLister(loginUser.getRealname());   //用户名
            //  piadminPojo.setTenantid(loginUser.getTenantid());   //租户id
            piadminPojo.setModifydate(new Date());   //修改时间
//            piadminPojo.setAssessor(""); //审核员
//            piadminPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.piadminService.update(piadminPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除平台管理员", notes = "删除平台管理员", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiAdmin.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String userid = loginUser.getUserid();
            if ("admin".equals(userid)) {
                return R.fail("admin管理员禁止删除");
            } else if (key.equals(userid)) {
                return R.fail("用户不能删除自己");
            }
            return R.ok(this.piadminService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiAdmin.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        PiadminPojo piadminPojo = this.piadminService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(piadminPojo);

        //从redis中获取XML内容
        String xml = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (xml != null || !xml.equals("") || xml != "") {
            content = xml;
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "初始化密码", notes = "初始化密码", produces = "application/json")
    @RequestMapping(value = "/initpassword", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiAdmin.Edit")
    public R<Integer> InitPassword(@RequestBody String json) {
        try {
            PiadminPojo piadminPojo = JSONArray.parseObject(json, PiadminPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            piadminPojo.setPassword(AESUtil.Encrypt(piadminPojo.getPassword())); //加密
            piadminPojo.setLister(loginUser.getRealname());   //用户名
            piadminPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.piadminService.initpassword(piadminPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

