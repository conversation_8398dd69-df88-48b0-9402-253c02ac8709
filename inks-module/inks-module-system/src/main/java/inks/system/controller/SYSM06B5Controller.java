package inks.system.controller;


import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CixlsinputPojo;
import inks.system.service.CixlsinputService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * xls导入格式(CiXlsInput)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-29 10:40:17
 */
@RestController
@RequestMapping("SYSM06B5")
@Api(tags = "SYSM06B5:xls导入格式")
public class SYSM06B5Controller extends CixlsinputController {

    @Resource
    private CixlsinputService cixlsinputService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "按模块编码查询样式", notes = "按模块编码查询样式", produces = "application/json")
    @RequestMapping(value = "/getListByModuleCode", method = RequestMethod.GET)
    public R<List<CixlsinputPojo>> getListByModuleCode(String code) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<CixlsinputPojo> list = this.cixlsinputService.getListByModuleCode(code, loginUser.getTenantid());
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * @return List<Map < String>>
     * @Description 接收传入Excel文件，返回List<Map<String, String>>对象
     * <AUTHOR>
     * @param[1] file
     * @param[2] id xls模板的id
     * @time 2023/7/25 12:36
     */
    @PostMapping("upload")
    @ResponseBody
    public List<Map<String, Object>> upload(MultipartFile file, String id) throws IOException {
        LoginUser loginUser = this.tokenService.getLoginUser(ServletUtils.getRequest());

        // 【关键方法】将上传的Excel文件转为List<Map<Integer, String>>(不需要监听器对数据进行额外处理)
        List<Map<Integer, String>> dataList = EasyExcel.read(file.getInputStream()).sheet().doReadSync();
        // 获取xls模板的titlejson并转换为List<Map<String, Object>>,titlejson中的fieldname即为英文字段名
        String titlejson = cixlsinputService.getEntity(id, loginUser.getTenantid()).getTitlejson();
        List<Map<String, Object>> titleList = JSON.parseObject(titlejson, new TypeReference<List<Map<String, Object>>>() {
        });
        // 创建一个映射关系Map，将键下标0, 1, 2, 3等按顺序映射为titleList中的fieldname值
        Map<Integer, String> indexToFieldNameMap = new HashMap<>();
        int index = 0;
        for (Map<String, Object> title : titleList) {
            String fieldName = (String) title.get("fieldname");
            indexToFieldNameMap.put(index, fieldName);
            index++;
        }

        // 创建一个新的List<Map<String, String>>对象用于存储Excel下标转换为英文字段后的数据
        List<Map<String, Object>> resultDataList = new ArrayList<>();
        // 遍历dataList中的每个Map<Integer, String>对象
        for (Map<Integer, String> data : dataList) {
            // 创建一个新的Map<String, Object>对象用于存储转换后的键值对(Map禁止修改键，因此创建新的Map)
            Map<String, Object> newDataMap = new HashMap<>();
            // 遍历Map<Integer, String>中的每个键值对
            for (Map.Entry<Integer, String> entry : data.entrySet()) {
                int index2 = entry.getKey();
                String value = entry.getValue();
                // 查找对应的fieldname
                String fieldName = indexToFieldNameMap.get(index2);
                if (fieldName != null) {
                    // 如果存在映射关系，则使用fieldname作为新的键，值保持不变
                    newDataMap.put(fieldName, value);
                } else {
                    // 如果不存在映射关系，则保持原先的键值对不变
                    newDataMap.put(String.valueOf(index2), value);
                }
            }
            // 将新的Map<String, String>添加到List<Map<String, String>>中
            resultDataList.add(newDataMap);
        }
        return resultDataList;
    }


//    /**  监听器使用范例 EasyExcel官网：https://easyexcel.opensource.alibaba.com/docs/current/quickstart/read
//     * 文件上传
//     * <p>
//     * 1. 创建excel对应的实体对象 参照{@link }
//     * <p>
//     * 2. 由于默认一行行的读取excel，所以需要创建excel一行一行的回调监听器，参照{@link }
//     * <p>
//     * 3. 直接读即可
//     */
//    @PostMapping("upload")
//    @ResponseBody
//    public String upload(MultipartFile file) throws IOException {
//        EasyExcel.read(file.getInputStream(),  new NoModelDataListener()).sheet().doRead();
//        return "success";
//    }
//
//
//    public class NoModelDataListener extends AnalysisEventListener<Map<Integer, String>> {
//        /**
//         * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
//         */
//        private static final int BATCH_COUNT = 5;
//        private List<Map<Integer, String>> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
//        @Override
//        public void invoke(Map<Integer, String> data, AnalysisContext context) {
//            log.info("解析到一条数据:{}", JSON.toJSONString(data));
//            cachedDataList.add(data);
//            if (cachedDataList.size() >= BATCH_COUNT) {
//                saveData();
//                cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
//            }
//        }
//
//        @Override
//        public void doAfterAllAnalysed(AnalysisContext context) {
//            saveData();
//            log.info("所有数据解析完成！");
//        }
//
//        /**
//         * 加上存储数据库
//         */
//        private void saveData() {
//            log.info("{}条数据，开始存储数据库！", cachedDataList.size());
//            log.info("存储数据库成功！");
//        }
//    }


}
