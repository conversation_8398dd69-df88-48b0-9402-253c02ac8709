package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.AESUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PiadminPojo;
import inks.system.service.PiadminService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 平台管理员(PiAdmin)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 13:55:51
 */
@RestController
@RequestMapping("SYSM04B1")
@Api(tags = "SYSM04B1:平台管理员")
public class SYSM04B1Controller extends PiadminController {
    @Resource
    private PiadminService piadminService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;
    @Resource
    private JavaMailSender javaMailSender;

    /**
     * @return R<String>
     * @Description admin用户修改密码请求
     * <AUTHOR>
     * @param[1] json 接收adminid和password
     * @time 2023/3/30 13:21
     */
    @ApiOperation(value = "初始化密码(admin邮件确认)", notes = "初始化密码", produces = "application/json")
    @RequestMapping(value = "/initPasswordRequest", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiAdmin.Edit")
    public R<String> initPasswordByEmail(@RequestBody String json) {
        try {
            PiadminPojo piadminPojo = JSONArray.parseObject(json, PiadminPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //piadminService.initPasswordByEmail(piadminPojo, loginUser);

            // 生成UUID 作为 key
            String uuid = inksSnowflake.getSnowflake().nextIdStr();
            String key = "changePassword:" + uuid;

            // 将修改密码请求信息存入 Redis 中
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("adminid", piadminPojo.getAdminid());
            requestData.put("password", piadminPojo.getPassword());
            requestData.put("username", loginUser.getUsername());
            redisService.setCacheMap(key, requestData);
            // 设置过期时间为 300 秒
            redisService.expire(key, 300);

            //发送邮件给管理员确认admin用户的密码修改请求
            String toAdminEmail = "<EMAIL>";
            String subject = "用户 " + loginUser.getRealName() + " 请求修改密码";
            String content = "<p>尊敬的管理员：</p>" +
                    "<p>用户 " + loginUser.getRealName() + " 请求修改密码为: <strong>" +
                    piadminPojo.getPassword() + "</strong></p>" +
                    "<p>请在5分钟内点击下面的链接确认修改密码请求：</p>" +
                    "<a href='http://dev.inksyun.com:31080/system/SYSM04B1/initPasswordAgree?key=" + key + "'>点击此处确认修改密码</a>";
            sendEmail(toAdminEmail, subject, content);
            return R.ok("等待管理员确认密码修改...");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "初始化密码(admin邮件确认)", notes = "初始化密码", produces = "application/json")
    @RequestMapping(value = "/initPasswordAgree", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "PiAdmin.Edit")
    public R<String> initPasswordAgree(String key) {
        try {
            Map<String, Object> requestData = redisService.getCacheMap(key);
            if (requestData == null) {
                System.out.println("key不存在或请求已过期");
                R.fail("key不存在或请求已过期");
            }
            // 验证通过，更新用户密码
            String adminid = (String) requestData.get("adminid");
            String password = (String) requestData.get("password");
            System.out.println("----------- 确认修改密码adminid:" + adminid + ",password:" + password);
            PiadminPojo piadminPojo = new PiadminPojo();
            piadminPojo.setAdminid(adminid);
            //密码加密
            piadminPojo.setPassword(AESUtil.Encrypt(password));
            piadminPojo.setLister("admin");
            piadminPojo.setModifydate(new Date());
            //修改密码
            this.piadminService.initpassword(piadminPojo);
            return R.ok("密码修改成功!");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    public void sendEmail(String to, String subject, String content) throws MessagingException {
        MimeMessage message = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
        helper.setFrom("<EMAIL>");
        helper.setTo(to);
        helper.setSubject(subject);
        //String content111 = "<h1>一号html</h1><h3>3号html</h3>";
        helper.setText(content, true); // true表示使用HTML格式
        javaMailSender.send(message);
    }

}
