package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiconfigPojo;
import inks.system.service.CiconfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 系统参数(CiConfig)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-27 18:30:49
 */
@RestController
@RequestMapping("SYSM06B4")
@Api(tags = "SYSM06B4:系统参数:所有")
public class SYSM06B4Controller extends CiconfigController {
    /**
     * 服务对象
     */
    @Resource
    private CiconfigService ciconfigService;

    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiConfig.List")
    public R<PageInfo<CiconfigPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiConfig.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            // queryParam.setFilterstr(" and Cfglevel=1"); 不加过滤，查看所有
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.ciconfigService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "取消登录检查 传入username则关联所有tid均不检查，或指定tid (ischeck传true时为加入登录检查)", produces = "application/json")
    @RequestMapping(value = "/unCheckOnline", method = RequestMethod.GET)
    public R<String> unCheckOnline(@RequestParam(required = false) String username, @RequestParam(required = false) String tid, String password, boolean ischeck) {
        try {
            if (!"12138".equals(password)) {
                return R.fail("密码错误");
            }
            return R.ok(this.ciconfigService.unCheckOnline(username, ischeck, tid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

