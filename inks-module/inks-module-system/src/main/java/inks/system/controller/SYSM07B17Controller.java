package inks.system.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.AESUtil;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiselfcheckPojo;
import inks.system.domain.pojo.PiuserPojo;
import inks.system.mapper.CiselfcheckMapper;
import inks.system.service.CiselfcheckService;
import inks.system.utils.WeakPasswordChecker;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 系统自检(CiSelfCheck)表控制层
 *
 * <AUTHOR>
 * @since 2023-09-01 17:04:17
 */
@RestController
@RequestMapping("SYSM07B17")
@Api(tags = "SYSM07B17:系统自检")
public class SYSM07B17Controller extends CiselfcheckController {
    @Resource
    private CiselfcheckService ciselfcheckService;
    @Resource
    private CiselfcheckMapper ciselfcheckMapper;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "获得用户tid关联的服务id", notes = "获得用户tid关联的服务id", produces = "application/json")
    @RequestMapping(value = "/getFunctionidByTid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiSelfCheck.List")
    public R<String> getFunctionidByTid() {
        try {
            // 获得用户tid关联的服务id
            String functionId = ciselfcheckService.getFunctionidByTid(tokenService.getLoginUser().getTenantid());
            return R.ok(functionId);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "检查服务下所有用户的密码复杂度,返回code=0表示检查不通过", notes = "检查服务下所有用户的密码复杂度", produces = "application/json")
    @RequestMapping(value = "/checkPasswordComplexity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiSelfCheck.List")
    public R<CiselfcheckPojo> checkPasswordComplexity() {
        LoginUser loginUser = tokenService.getLoginUser();
        try {
            // 查询密码过于简单的用户RealName集合
            List<String> realNames = ciselfcheckService.checkPasswordComplexity(loginUser.getTenantid());
            if (CollectionUtils.isNotEmpty(realNames)) { //返回code=0表示检查不通过
                // 仅展示前3个RealName
                int limit = Math.min(3, realNames.size());
                List<String> limitedRealNames = realNames.subList(0, limit);
                return R.fail(0, "密码过于简单的用户有" + realNames.size() + "人: " + (realNames.size() > 3 ? limitedRealNames + "等..." : limitedRealNames.toString()));
            }
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "检查服务下所有过期订单,返回code=0表示检查不通过", notes = "检查服务下所有过期订单", produces = "application/json")
    @RequestMapping(value = "/checkExpiredMach", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiSelfCheck.List")
    public R<CiselfcheckPojo> checkExpiredOrder() {
        LoginUser loginUser = tokenService.getLoginUser();
        try {
            // 检查是否有过期的销售订单,过滤到Online订单且计划时间超时
            List<String> expiredMachRefNos = ciselfcheckService.checkExpiredMach(loginUser.getTenantid());
            if (CollectionUtils.isNotEmpty(expiredMachRefNos)) { //返回code=0表示检查不通过
                // 仅展示前3个
                int limit = Math.min(3, expiredMachRefNos.size());
                List<String> limitedExpiredOrderNos = expiredMachRefNos.subList(0, limit);
                return R.fail(0, "过期销售订单有" + expiredMachRefNos.size() + "条: " + (expiredMachRefNos.size() > 3 ? limitedExpiredOrderNos + "等..." : limitedExpiredOrderNos.toString()));
            }
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    // 新增弱密码校验接口
    @ApiOperation(value = "校验弱密码 type默认0：校验当前userid; type=1：校验当前租户下所有用户", notes = "校验传入的密码是否为弱密码，返回 true 表示弱密码，false 表示密码强度较高", produces = "application/json")
    @RequestMapping(value = "/checkWeakPassword", method = RequestMethod.POST)
    public R<List<PiuserPojo>> checkWeakPassword(@RequestParam(defaultValue = "0") Integer type) {
        LoginUser loginUser = tokenService.getLoginUser();
        try {
            return R.ok(ciselfcheckService.checkWeakPassword(type, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    // 弱密码校验接口：传入密码，返回是否为弱密码true/false
    @ApiOperation(value = "校验弱密码 传入密码字符串", notes = "校验传入的密码是否为弱密码，返回 true 表示弱密码，false 表示密码强度较高", produces = "application/json")
    @RequestMapping(value = "/isWeakPassword", method = RequestMethod.POST)
    public R<Boolean> isWeakPassword(@RequestBody String json) {
        try {
            JSONObject jsonObject = JSON.parseObject(json);
            String password = jsonObject.getString("password");
            if (StringUtils.isBlank(password)) {
                return R.fail("密码不能为空");
            }
            String encrypted = AESUtil.Encrypt(password);
            return R.ok(WeakPasswordChecker.isWeakPassword(encrypted));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
