package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.AESUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PirmsuserPojo;
import inks.system.service.PirmsuserService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * RMS用户(PiRmsUser)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-21 19:46:58
 */
@RestController
@RequestMapping("pirmsuser")
public class PirmsuserController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(PirmsuserController.class);
    /**
     * 服务对象
     */
    @Resource
    private PirmsuserService pirmsuserService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取RMS用户详细信息", notes = "获取RMS用户详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "PiRmsUser.List")
    public R<PirmsuserPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pirmsuserService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiRmsUser.List")
    public R<PageInfo<PirmsuserPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiRmsUser.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.pirmsuserService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增RMS用户", notes = "新增RMS用户", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiRmsUser.Add")
    public R<PirmsuserPojo> create(@RequestBody String json) {
        try {
            PirmsuserPojo pirmsuserPojo = JSONArray.parseObject(json, PirmsuserPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pirmsuserPojo.setCreateby(loginUser.getRealName());   // 创建者
            pirmsuserPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            pirmsuserPojo.setCreatedate(new Date());   // 创建时间
            pirmsuserPojo.setLister(loginUser.getRealname());   // 制表
            pirmsuserPojo.setListerid(loginUser.getUserid());    // 制表id  
            pirmsuserPojo.setModifydate(new Date());   //修改时间
            pirmsuserPojo.setTenantid(loginUser.getTenantid());   //租户id

            //加密密码
            pirmsuserPojo.setUserpassword(AESUtil.Encrypt(pirmsuserPojo.getUserpassword()));
            return R.ok(this.pirmsuserService.insert(pirmsuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改RMS用户", notes = "修改RMS用户", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "PiRmsUser.Edit")
    public R<PirmsuserPojo> update(@RequestBody String json) {
        try {
            PirmsuserPojo pirmsuserPojo = JSONArray.parseObject(json, PirmsuserPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pirmsuserPojo.setLister(loginUser.getRealname());   // 制表
            pirmsuserPojo.setListerid(loginUser.getUserid());    // 制表id  
            pirmsuserPojo.setTenantid(loginUser.getTenantid());   //租户id
            pirmsuserPojo.setModifydate(new Date());   //修改时间
            //加密密码
            pirmsuserPojo.setUserpassword(AESUtil.Encrypt(pirmsuserPojo.getUserpassword()));
            return R.ok(this.pirmsuserService.update(pirmsuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除RMS用户", notes = "删除RMS用户", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiRmsUser.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pirmsuserService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiRmsUser.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        PirmsuserPojo pirmsuserPojo = this.pirmsuserService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(pirmsuserPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

