package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.RefNoUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.NoRepeatSubmit;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiformvaildPojo;
import inks.system.domain.pojo.CiformvailditemPojo;
import inks.system.domain.pojo.CiformvailditemdetailPojo;
import inks.system.service.CiformvaildService;
import inks.system.service.CiformvailditemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;


/**
 * 窗体验证(CiFormVaild)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-31 17:03:52
 */
//@RestController
//@RequestMapping("ciformvaild")
public class CiformvaildController {

    @Resource
    private CiformvaildService ciformvaildService;
    @Resource
    private CiformvailditemService ciformvailditemService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;


    private final static Logger logger = LoggerFactory.getLogger(CiformvaildController.class);


    @ApiOperation(value = " 获取窗体验证详细信息", notes = "获取窗体验证详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiFormVaild.List")
    public R<CiformvaildPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.ciformvaildService.getEntity(key, loginUser.getTenantid()));
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiFormVaild.List")
    public R<PageInfo<CiformvailditemdetailPojo>> getPageList(@RequestBody String json) {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("CiFormVaild.CreateDate");
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        queryParam.setTenantid(loginUser.getTenantid());
        String qpfilter = "";
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        return R.ok(this.ciformvaildService.getPageList(queryParam));
    }


    @ApiOperation(value = " 获取窗体验证详细信息", notes = "获取窗体验证详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiFormVaild.List")
    public R<CiformvaildPojo> getBillEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.ciformvaildService.getBillEntity(key, loginUser.getTenantid()));
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiFormVaild.List")
    public R<PageInfo<CiformvaildPojo>> getBillList(@RequestBody String json) {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("CiFormVaild.CreateDate");
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        String qpfilter = "";
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        return R.ok(this.ciformvaildService.getBillList(queryParam));
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiFormVaild.List")
    public R<PageInfo<CiformvaildPojo>> getPageTh(@RequestBody String json) {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("CiFormVaild.CreateDate");
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        String qpfilter = "";
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        return R.ok(this.ciformvaildService.getPageTh(queryParam));
    }


    @ApiOperation(value = " 新增窗体验证", notes = "新增窗体验证", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiFormVaild.Add")
    @NoRepeatSubmit
    public R<CiformvaildPojo> create(@RequestBody String json) {
        CiformvaildPojo ciformvaildPojo = JSONArray.parseObject(json, CiformvaildPojo.class);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String tid = loginUser.getTenantid();
        ciformvaildPojo.setCreateby(loginUser.getRealName());   // 创建者
        ciformvaildPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
        ciformvaildPojo.setCreatedate(new Date());   // 创建时间
        ciformvaildPojo.setLister(loginUser.getRealname());   // 制表
        ciformvaildPojo.setListerid(loginUser.getUserid());    // 制表id
        ciformvaildPojo.setModifydate(new Date());   //修改时间
        ciformvaildPojo.setTenantid(tid);   //租户id
        CiformvaildPojo insertDB = this.ciformvaildService.insert(ciformvaildPojo);
        return R.ok(insertDB);
    }


    @ApiOperation(value = "修改窗体验证", notes = "修改窗体验证", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiFormVaild.Edit")
    public R<CiformvaildPojo> update(@RequestBody String json) {
        CiformvaildPojo ciformvaildPojo = JSONArray.parseObject(json, CiformvaildPojo.class);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        ciformvaildPojo.setLister(loginUser.getRealname());   // 制表
        ciformvaildPojo.setListerid(loginUser.getUserid());    // 制表id
        ciformvaildPojo.setModifydate(new Date());   //修改时间
        ciformvaildPojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.ciformvaildService.update(ciformvaildPojo));
    }


    @ApiOperation(value = "删除窗体验证", notes = "删除窗体验证", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiFormVaild.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        this.ciformvaildService.delete(key, loginUser.getTenantid());
        return R.ok(1);
    }

    /*子表操作 */

    @ApiOperation(value = " 新增窗体验证Item", notes = "新增窗体验证Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiFormVaild.Add")
    public R<CiformvailditemPojo> createItem(@RequestBody String json) {
        CiformvailditemPojo ciformvailditemPojo = JSONArray.parseObject(json, CiformvailditemPojo.class);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        ciformvailditemPojo.setTenantid(loginUser.getTenantid());
        return R.ok(this.ciformvailditemService.insert(ciformvailditemPojo));
    }

    @ApiOperation(value = " 修改窗体验证Item", notes = "修改窗体验证Item", produces = "application/json")
    @RequestMapping(value = "/updateItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiFormVaild.Edit")
    public R<CiformvailditemPojo> updateItem(@RequestBody String json) {
        CiformvailditemPojo ciformvailditemPojo = JSONArray.parseObject(json, CiformvailditemPojo.class);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        ciformvailditemPojo.setTenantid(loginUser.getTenantid());
        return R.ok(this.ciformvailditemService.update(ciformvailditemPojo));
    }

    @ApiOperation(value = "删除窗体验证Item", notes = "删除窗体验证Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiFormVaild.Delete")
    public R<Integer> deleteItem(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.ciformvailditemService.delete(key, loginUser.getTenantid()));
    }


    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiFormVaild.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        CiformvaildPojo ciformvaildPojo = this.ciformvaildService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(ciformvaildPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = ciformvaildPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    CiformvailditemPojo ciformvailditemPojo = new CiformvailditemPojo();
                    ciformvaildPojo.getItem().add(ciformvailditemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(ciformvaildPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

