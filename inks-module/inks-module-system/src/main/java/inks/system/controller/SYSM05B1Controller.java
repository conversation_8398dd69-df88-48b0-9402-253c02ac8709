package inks.system.controller;

import inks.common.core.domain.AppMenuPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PimenuappPojo;
import inks.system.mapper.PirolemenuappMapper;
import inks.system.mapper.PiuserroleMapper;
import inks.system.service.PimenuappService;
import inks.system.service.PisubscriberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * APP导航(PiMenuApp)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 14:07:52
 */
@RestController
@RequestMapping("SYSM05B1")
@Api(tags = "SYSM05B1:APP导航")
public class SYSM05B1Controller extends PimenuappController {
    @Resource
    private PisubscriberService pisubscriberService;

    @Resource
    private PirolemenuappMapper pirolemenuappMapper;

    @Resource
    private PimenuappService pimenuappService;

    @Resource
    private TokenService tokenService;

    @Resource
    private PiuserroleMapper piuserroleMapper;


    @ApiOperation(value = "项目全菜单 传root时为全菜单", notes = "项目全菜单", produces = "application/json")
    @RequestMapping(value = "/getAllListByPid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiMenuApp.List")
    public R<List<PimenuappPojo>> getAllListByPid(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            List<PimenuappPojo> lst = pimenuappService.getListByPid(key);
            // 向下递归获取子菜单
            return R.ok(flattenMenuList(lst));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    // 抄接口/getMenuAppListBySelf,只不过把返回改为平行的List<PimenuappPojo>了
    @ApiOperation(value = "项目全菜单 当前租户的", notes = "项目全菜单", produces = "application/json")
    @RequestMapping(value = "/getMenuAppListByTen", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiMenuApp.List")
    public R<List<PimenuappPojo>> getMenuAppListByTen() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            List<PimenuappPojo> lst = this.pisubscriberService.getMenuAppListByTenant(loginUser.getTenantid());
            return R.ok(lst);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "返回层级结构：项目菜单(Children格式) 登录用户userid->roleids->navids->menus  admin拿全部菜单", notes = "", produces = "application/json")
    @RequestMapping(value = "/getMenuAppListByLoginUser", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "PiMenuApp.List")
    public R<List<AppMenuPojo>> getMenuAppListByLoginUser() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            List<PimenuappPojo> lst;
            if (Objects.equals(loginUser.getIsadmin(), 1)) {//admin拿全部菜单
                lst = this.pisubscriberService.getMenuAppListByTenant(loginUser.getTenantid());
            } else {
                List<String> navids = pirolemenuappMapper.getNavidsByUserid(loginUser.getUserid(), loginUser.getTenantid());
                if (CollectionUtils.isEmpty(navids)) {
//                    throw new Exception("用户未关联菜单权限Navid");
                    return R.ok(new ArrayList<>());
                }
                lst = pimenuappService.getListByNavids(navids);
            }
            // 将Web菜单列表List 转为树形结构List
            return R.ok(PisubscriberController.buildAppMenuToChildren(lst));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "返回平铺结构：项目菜单 roleid->navids->menus", notes = "", produces = "application/json")
    @RequestMapping(value = "/getMenuAppListByRole", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiMenuApp.List")
    public R<List<PimenuappPojo>> getMenuAppListByRole(String roleid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            List<String> navids = pirolemenuappMapper.getNavidsByRoleid(roleid, loginUser.getTenantid());
            if (CollectionUtils.isEmpty(navids)) {
//                throw new Exception("角色未关联菜单权限");
                return R.ok(new ArrayList<>());
            }
            List<PimenuappPojo> lst = pimenuappService.getListByNavids(navids);
            // 不用向下递归获取子菜单
//            return R.ok(flattenMenuList(lst));
            return R.ok(lst);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "返回平铺结构：项目菜单 userid->roleids->navids->menus", notes = "", produces = "application/json")
    @RequestMapping(value = "/getMenuAppListByUser", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiMenuApp.List")
    public R<List<PimenuappPojo>> getMenuAppListByUser(String userid) {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            List<String> navids = pirolemenuappMapper.getNavidsByUserid(userid, loginUser.getTenantid());
            if (CollectionUtils.isEmpty(navids)) {
                return R.ok(new ArrayList<>());
            }
            List<PimenuappPojo> lst = pimenuappService.getListByNavids(navids);
            return R.ok(lst);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    private List<PimenuappPojo> flattenMenuList(List<PimenuappPojo> lst) {
        List<PimenuappPojo> list = new ArrayList<>();
        for (PimenuappPojo pimenuappPojo : lst) {
            PimenuappPojo webMenuPojo = new PimenuappPojo();
            BeanUtils.copyProperties(pimenuappPojo, webMenuPojo);
            list.add(webMenuPojo);
            List<PimenuappPojo> lst2 = pimenuappService.getListByPid(pimenuappPojo.getNavid());
            for (PimenuappPojo pojo : lst2) {
                PimenuappPojo webMenuPojo2 = new PimenuappPojo();
                BeanUtils.copyProperties(pojo, webMenuPojo2);
                list.add(webMenuPojo2);
                List<PimenuappPojo> lst3 = pimenuappService.getListByPid(pojo.getNavid());
                for (PimenuappPojo value : lst3) {
                    PimenuappPojo webMenuPojo3 = new PimenuappPojo();
                    BeanUtils.copyProperties(value, webMenuPojo3);
                    list.add(webMenuPojo3);
                }
            }
        }
        // 使用Stream API根据Navid去重 这种方法利用了Map键的唯一性特性来实现去重。
        return new ArrayList<>(list.stream()
                .collect(Collectors.toMap(
                        PimenuappPojo::getNavid, // 使用Navid作为键
                        pojo -> pojo,            // 保留Pojo对象
                        (existing, replacement) -> existing // 如果有重复，保留现有的
                ))
                .values());
    }


}
