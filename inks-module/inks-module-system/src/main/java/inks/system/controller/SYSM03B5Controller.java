package inks.system.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PirolemenuappPojo;
import inks.system.service.PirolemenuappService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 角色菜单App(PiRoleMenuApp)表控制层
 *
 * <AUTHOR>
 * @since 2024-06-11 13:55:54
 */
@RestController
@RequestMapping("SYSM03B5")
@Api(tags = "SYSM03B5:角色菜单App")
public class SYSM03B5Controller extends PirolemenuappController {

    @Resource
    private PirolemenuappService pirolemenuappService;

    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "删除角色菜单App  ByRoleidAndNavidAndTid", notes = "删除角色菜单App", produces = "application/json")
    @RequestMapping(value = "/deleteByRoleidAndNavid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiRoleMenuApp.Delete")
    public R<Integer> deleteByRoleidAndNavid(String roleid, String navid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.pirolemenuappService.deleteByRoleidAndNavid(roleid, navid, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    // json示例 "    {\" +
    //                "    \"roleid\": \"12345\",\n" +
    //                "    \"delete\": [\"navid1\", \"navid2\", \"navid3\"],\n" +
    //                "    \"create\": [\"navi4\", \"navi5\"]\n" +
    //                "}"
    @ApiOperation(value = "批量删除角色菜单Web  通过一个Roleid和多个Navids", notes = "", produces = "application/json")
    @RequestMapping(value = "/batchCreateDelete", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiRoleMenuWeb.Delete")
    public R<Integer> batchCreateDelete(@RequestBody String json) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            // 解析JSON字符串
            JSONObject jsonObject = JSON.parseObject(json);
            String roleid = jsonObject.getString("roleid");
            JSONArray deleteArray = jsonObject.getJSONArray("delete");
            List<String> deleteNavids = deleteArray != null ? deleteArray.toJavaList(String.class) : null;
            JSONArray createArray = jsonObject.getJSONArray("create");
            List<String> createNavids = createArray != null ? createArray.toJavaList(String.class) : null;
            return R.ok(this.pirolemenuappService.batchCreateDelete(roleid, deleteNavids, createNavids, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按角色查询权限", notes = "按角色查询权限", produces = "application/json")
    @RequestMapping(value = "/getListByRole", method = RequestMethod.GET)
    public R<List<PirolemenuappPojo>> getListByRole(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.pirolemenuappService.getListByRole(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
