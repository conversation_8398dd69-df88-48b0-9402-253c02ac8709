package inks.system.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiformcustomPojo;
import inks.system.service.CiformcustomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 自定义界面(CiFormCustom)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-06 12:57:59
 */
@RestController
@RequestMapping("SYSM07B13")
@Api(tags = "SYSM07B13:自定义界面")
public class SYSM07B13Controller extends CiformcustomController {

    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(CiformcustomController.class);
    /**
     * 服务对象
     */
    @Resource
    private CiformcustomService ciformcustomService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取自定义界面详细信息 ByCode", notes = "获取自定义界面详细信息 ByCode", produces = "application/json")
    @RequestMapping(value = "/getEntityByCode", method = RequestMethod.GET)
    public R<CiformcustomPojo> getEntityByCode(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.ciformcustomService.getEntityByCode(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
