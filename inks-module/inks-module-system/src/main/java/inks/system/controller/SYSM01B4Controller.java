package inks.system.controller;

import inks.common.core.domain.DeptinfoPojo;
import inks.common.core.domain.R;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PitenantPojo;
import inks.system.domain.pojo.PitenantuserPojo;
import inks.system.domain.pojo.PiuserPojo;
import inks.system.service.PitenantService;
import inks.system.service.PitenantuserService;
import inks.system.service.PiuserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 租户关系表(PiTenantUser)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 14:31:51
 */
@RestController
@RequestMapping("SYSM01B4")
@Api(tags = "SYSM01B4:租户关系表")
public class SYSM01B4Controller extends PitenantuserController {
    /**
     * 服务对象
     */
    @Resource
    private PitenantuserService pitenantuserService;

    /**
     * 服务对象
     */
    @Resource
    private PitenantService pitenantService;

    /**
     * 服务对象
     */
    @Resource
    private PiuserService piuserService;


    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据租户获取关系List", notes = "根据租户获取关系List", produces = "application/json")
    @RequestMapping(value = "/getListByTenant", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiTenantUser.List")
    public R<List<PitenantuserPojo>> getListByTenant(String key) {
        try {
            return R.ok(this.pitenantuserService.getListByTenant(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据用户获取关系List", notes = "根据用户获取关系List", produces = "application/json")
    @RequestMapping(value = "/getListByUser", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiTenantUser.List")
    public R<List<PitenantuserPojo>> getListByUser(String key) {
        try {
            return R.ok(this.pitenantuserService.getListByUser(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据租户获取用户Bill", notes = "根据租户获取用户Bill", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByTenant", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiTenantUser.List")
    public R<PitenantPojo> getBillEntityByTenant(String key) {
        try {

            PitenantPojo pitenantPojo = this.pitenantService.getEntity(key);
            List<PitenantuserPojo> item = this.pitenantuserService.getListByTenant(key);
            pitenantPojo.setItem(item);
            return R.ok(pitenantPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据用户获取租户Bill", notes = "根据用户获取租户Bill", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByUser", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiTenantUser.List")
    public R<PiuserPojo> getBillEntityByUser(String key) {
        try {

            PiuserPojo piuserPojo = piuserService.getEntity(key);
            List<PitenantuserPojo> item = this.pitenantuserService.getListByUser(key);
            piuserPojo.setItem(item);
            return R.ok(piuserPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = " 获取组织用户表详细信息", notes = "获取组织用户表详细信息", produces = "application/json")
    @RequestMapping(value = "/getDeptinfolistByUser", method = RequestMethod.GET)
    public R<List<DeptinfoPojo>> getDeptinfolistByUser(String key, String tid) {
        try {

            return R.ok(this.pitenantuserService.getDeptinfoList(key, tid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
