//package inks.system.controller;
//
//import com.seeta.proxy.GenderPredictorProxy;
//import com.seeta.proxy.MaskDetectorProxy;
//import com.seeta.sdk.FaceAntiSpoofing;
//import inks.common.core.domain.LoginUser;
//import inks.common.core.domain.R;
//import inks.common.security.service.TokenService;
//import inks.system.config.face.entity.FaceInfoBo;
//import inks.system.config.face.exception.Seetaface6Exception;
//import inks.system.domain.pojo.PifaceinfoPojo;
//import inks.system.service.PifaceinfoService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.http.ResponseEntity;
//import org.springframework.util.Assert;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletRequest;
//import java.util.List;
//
/// **
// * 人脸信息表(PiFaceInfo)表控制层
// *
// * <AUTHOR>
// * @since 2024-11-19 17:02:02
// */
//@RestController
//@RequestMapping("SYSM01B10")
//@Api(tags = "SYSM01B10:人脸信息表")
//public class SYSM01B10Controller extends PifaceinfoController {
//    @Resource
//    private TokenService tokenService;
//
//    @Resource
//    private PifaceinfoService pifaceinfoService;
//
//    @ApiOperation(value = "直接上传文件", notes = "直接上传文件")
//    @PostMapping("/uploadFile")
//    public R<Integer> uploadFile(MultipartFile face, HttpServletRequest request) throws Exception {
//        Assert.notNull(face, "上传人脸照片不能为空");
//
//        LoginUser loginUser = tokenService.getLoginUser();
//        int save = pifaceinfoService.save(face, request,loginUser);
//        return R.ok(save);
//    }
//
//    @ApiOperation(value = "人脸相似度1:1", notes = "人脸相似度1:1")
//    @PostMapping("/faceRecognizer")
//    public R<Float> faceRecognizer(MultipartFile face1, MultipartFile face2) throws Seetaface6Exception {
//        Assert.notNull(face1, "上传人脸照片1不能为空");
//        Assert.notNull(face2, "上传人脸照片2不能为空");
//        Float calculateSimilarity = pifaceinfoService.faceRecognizer(face1, face2);
//        return R.ok(calculateSimilarity);
//    }
//
//    @ApiOperation(value = "人脸识别 查询前N个·", notes = "查询前N个")
//    @PostMapping("/queryTopN")
//    public R<List<FaceInfoBo>> queryTopN(MultipartFile face, Integer topN) throws Seetaface6Exception {
//        Assert.notNull(face, "上传人脸照片不能为空");
//        List<FaceInfoBo> list = pifaceinfoService.queryTopN(face, topN);
//        return R.ok(list);
//    }
//
//    //@ApiOperation(value = "人脸识别 对比数据库中人脸 相似度1:1", notes = "人脸相似度1:1")
//    //@PostMapping("/faceDB")
//    //public R<Float> faceDB(MultipartFile face1) throws Seetaface6Exception {
//    //    Assert.notNull(face1, "上传人脸照片1不能为空");
//    //    pifaceinfoService.faceDB(face1);
//    //    return R.ok();
//    //}
//
//    @ApiOperation(value = "攻击人脸检测", notes = "攻击人脸检测")
//    @PostMapping("/faceAntiSpoofing")
//    public R<List<FaceAntiSpoofing.Status>> faceAntiSpoofing(MultipartFile faceImage) throws Seetaface6Exception {
//
//        Assert.notNull(faceImage, "上传人脸照片不能为空");
//        List<FaceAntiSpoofing.Status> statuses = pifaceinfoService.faceAntiSpoofing(faceImage);
//        return R.ok(statuses);
//    }
//
//    @ApiOperation(value = "识别人脸是否戴口罩", notes = "识别人脸是否戴口罩")
//    @PostMapping("/maskDetector")
//    public R<List<MaskDetectorProxy.MaskItem>> maskDetector(MultipartFile faceImage) throws Seetaface6Exception {
//        Assert.notNull(faceImage, "上传人脸照片不能为空");
//        List<MaskDetectorProxy.MaskItem> list = pifaceinfoService.maskDetector(faceImage);
//
//        return R.ok(list);
//    }
//
//    @ApiOperation(value = "识别人脸照片性别", notes = "识别人脸照片性别")
//    @PostMapping("/genderPredictor")
//    public R<List<GenderPredictorProxy.GenderItem>> genderPredictor(MultipartFile faceImage) throws Seetaface6Exception {
//        Assert.notNull(faceImage, "上传人脸照片不能为空");
//        List<GenderPredictorProxy.GenderItem> list = pifaceinfoService.genderPredictor(faceImage);
//        return R.ok(list);
//    }
//
//    @ApiOperation(value = "识别人脸照片年龄", notes = "识别人脸照片年龄")
//    @PostMapping("/agePredictor")
//    public R<List<Integer>> agePredictor(MultipartFile faceImage) throws Seetaface6Exception {
//        Assert.notNull(faceImage, "上传人脸照片不能为空");
//        List<Integer> ages = pifaceinfoService.agePredictor(faceImage);
//        return R.ok(ages);
//    }
//
//
//}
