package inks.system.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

@RestController
public class MailController {
    @Resource
    private JavaMailSender javaMailSender;
    @Value("${spring.mail.toEmail}")
    private String toEmail;

    /**
     * @Description 发邮件
     * <AUTHOR>
     * @param[1] to 收件人邮箱
     * @param[2] subject 主题
     * @param[3] content 内容
     * @time 2023/5/24 22:08
     */
    public void sendEmail(String to, String subject, String content) throws MessagingException {
        MimeMessage message = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
        helper.setFrom("<EMAIL>");
        helper.setTo(to);
        helper.setSubject(subject);
        //String content111 = "<h1>一号html</h1><h3>3号html</h3>";
        helper.setText(content, true); // true表示使用HTML格式
        javaMailSender.send(message);
    }
}
