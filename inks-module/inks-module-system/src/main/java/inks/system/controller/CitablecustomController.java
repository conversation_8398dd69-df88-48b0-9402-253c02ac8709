package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CitablecustomPojo;
import inks.system.service.CitablecustomService;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 自定义字段(CiTableCustom)表控制层
 *
 * <AUTHOR>
 * @since 2022-08-10 14:26:32
 */
@RestController
@RequestMapping("citablecustom")
public class CitablecustomController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(CitablecustomController.class);
    /**
     * 服务对象
     */
    @Resource
    private CitablecustomService citablecustomService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取自定义字段详细信息", notes = "获取自定义字段详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    public R<CitablecustomPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.citablecustomService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    public R<PageInfo<CitablecustomPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiTableCustom.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.citablecustomService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增自定义字段", notes = "新增自定义字段", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public R<CitablecustomPojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            CitablecustomPojo citablecustomPojo = JSONArray.parseObject(json, CitablecustomPojo.class);

            citablecustomPojo.setCreateby(loginUser.getRealName());   // 创建者
            citablecustomPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            citablecustomPojo.setCreatedate(new Date());   // 创建时间
            citablecustomPojo.setLister(loginUser.getRealname());   // 制表
            citablecustomPojo.setListerid(loginUser.getUserid());    // 制表id  
            citablecustomPojo.setModifydate(new Date());   //修改时间
            citablecustomPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.citablecustomService.insert(citablecustomPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改自定义字段", notes = "修改自定义字段", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<CitablecustomPojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            CitablecustomPojo citablecustomPojo = JSONArray.parseObject(json, CitablecustomPojo.class);

            citablecustomPojo.setLister(loginUser.getRealname());   // 制表
            citablecustomPojo.setListerid(loginUser.getUserid());    // 制表id  
            citablecustomPojo.setTenantid(loginUser.getTenantid());   //租户id
            citablecustomPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.citablecustomService.update(citablecustomPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除自定义字段", notes = "删除自定义字段", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.citablecustomService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

