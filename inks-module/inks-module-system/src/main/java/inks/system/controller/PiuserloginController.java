package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.constant.CacheConstants;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.UserAgentUtil;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PiuserloginPojo;
import inks.system.service.PiuserloginService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * 用户登录表(PiUserLogin)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 14:36:11
 */

public class PiuserloginController {
    /**
     * 服务对象
     */
    @Resource
    private PiuserloginService piuserloginService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取用户登录表详细信息", notes = "获取用户登录表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    public R<PiuserloginPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.piuserloginService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiUserLogin.List")
    public R<PageInfo<PiuserloginPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiUserLogin.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.piuserloginService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增用户登录表", notes = "新增用户登录表", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiUserLogin.Add")
    public R<PiuserloginPojo> create(@RequestBody String json) {
        try {
            PiuserloginPojo piuserloginPojo = JSONArray.parseObject(json, PiuserloginPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            piuserloginPojo.setLister(loginUser.getRealname());   //用户名
            // piuserloginPojo.setTenantid(loginUser.getTenantid());   //租户id
            piuserloginPojo.setModifydate(new Date());   //修改时间   
            return R.ok(this.piuserloginService.insert(piuserloginPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改用户登录表", notes = "修改用户登录表", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiUserLogin.Edit")
    public R<PiuserloginPojo> update(@RequestBody String json) {
        try {
            PiuserloginPojo piuserloginPojo = JSONArray.parseObject(json, PiuserloginPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            piuserloginPojo.setLister(loginUser.getRealname());   //用户名
            //  piuserloginPojo.setTenantid(loginUser.getTenantid());   //租户id
            piuserloginPojo.setModifydate(new Date());   //修改时间
//            piuserloginPojo.setAssessor(""); //审核员
//            piuserloginPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.piuserloginService.update(piuserloginPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除用户登录表", notes = "删除用户登录表", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiUserLogin.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.piuserloginService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiUserLogin.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        PiuserloginPojo piuserloginPojo = this.piuserloginService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(piuserloginPojo);

        //从redis中获取XML内容
        String xml = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (xml != null || !xml.equals("") || xml != "") {
            content = xml;
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "查询在线用户", notes = "查询在线用户", produces = "application/json")
    @RequestMapping(value = "/online", method = RequestMethod.GET)
    public R<List<LoginUser>> online() {
        try {

            //读取当前用户
            //  LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //读取所有在线
            Collection<String> keys = redisService.keys(CacheConstants.LOGIN_TOKEN_KEY + "*");
            List<LoginUser> list = new ArrayList<>();
            for (String key : keys) {
                LoginUser user = redisService.getCacheObject(key);
                user.setIpaddr(UserAgentUtil.getRealAddressByIP(user.getIpaddr()));
                user.setPermissions(null);
                user.setPassword(null);
                //当为default是合加入，或本租户的添加
                //  if (loginUser.getTenantid().equals(user.getTenantid()) || loginUser.getTenantid().equals("default")) {
                list.add(user);
                //  }
            }

//            //读取当前用户
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            //读取所有在线
//            Collection<String> keys = redisService.keys(CacheConstants.LOGIN_TOKEN_KEY + "*");
//            //创建一个map记录list中以保存的用户的id
//            Map<String, Integer> map = new HashMap<>();
//            List<LoginUser> list = new ArrayList<>();
//            int i = 0;
//            for (String key : keys) {
//                LoginUser user = redisService.getCacheObject(key);
//                //当为default是合加入，或本租户的添加
//                if (loginUser.getTenantid().equals(user.getTenantid()) || loginUser.getTenantid().equals("default")) {
//                    //判断map是否为空 为空表示新数据添加进入list在加入map中保存
//                    user.setIpaddr(UserAgentUtil.getRealAddressByIP(user.getIpaddr()));
//                    user.setPermissions(null);
//                    user.setPassword(null);
//                    if (map.get(user.getUserid()) == null) {
//                        map.put(user.getUserid(), i);
//                        list.add(user);
//                        i++;
//                    } else {
//                        //如果不为空判断有效时间
//                        if (user.getExpireTime() > list.get(map.get(user.getUserid())).getExpireTime()) {
//                            list.get(map.get(user.getUserid())).setIsAdmin(user.getIsAdmin());
//                            list.get(map.get(user.getUserid())).setExpireTime(user.getExpireTime());
//                            list.get(map.get(user.getUserid())).setToken(user.getToken());
//                            list.get(map.get(user.getUserid())).setIpaddr(user.getIpaddr());
//                            list.get(map.get(user.getUserid())).setRealName(user.getRealName());
//                            list.get(map.get(user.getUserid())).setAvatar(user.getAvatar());
//                            list.get(map.get(user.getUserid())).setLoginTime(user.getLoginTime());
//                            list.get(map.get(user.getUserid())).setTenantid(user.getTenantid());
//                            list.get(map.get(user.getUserid())).setUserid(user.getUserid());
//                            list.get(map.get(user.getUserid())).setUserName(user.getUserName());
//                        }
//                    }
//                }
//            }
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 强退用户
     */
    @ApiOperation(value = "踢出在线用户", notes = "踢出在线用户", produces = "application/json")
    @RequestMapping(value = "/forceLogout", method = RequestMethod.GET)
    public R forceLogout(String key) {
        try {
            LoginUser user = redisService.getCacheObject(CacheConstants.LOGIN_TOKEN_KEY + key);
            redisService.deleteObject(CacheConstants.LOGIN_TOKEN_KEY + key);
            return R.ok("用户：" + user.getRealName() + "，已被踢出下线");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

