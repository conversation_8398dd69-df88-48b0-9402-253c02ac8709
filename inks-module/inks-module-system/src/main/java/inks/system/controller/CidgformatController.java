package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CidgformatPojo;
import inks.system.domain.pojo.CidgformatitemPojo;
import inks.system.service.CidgformatService;
import inks.system.service.CidgformatitemService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 列表格式(Cidgformat)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-20 12:43:04
 */

public class CidgformatController {
    /**
     * 服务对象
     */
    @Resource
    private CidgformatService cidgformatService;

    /**
     * 服务对象Item
     */
    @Resource
    private CidgformatitemService cidgformatitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取列表格式详细信息", notes = "获取列表格式详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiDgFormat.List")
    public R<CidgformatPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cidgformatService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取列表格式详细信息", notes = "获取列表格式详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiDgFormat.List")
    public R<CidgformatPojo> getBillEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cidgformatService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiDgFormat.List")
    public R<PageInfo<CidgformatPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiDgFormat.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.cidgformatService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiDgFormat.List")
    public R<PageInfo<CidgformatPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiDgFormat.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.cidgformatService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增列表格式", notes = "新增列表格式", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public R<CidgformatPojo> create(@RequestBody String json) {
        try {
            CidgformatPojo cidgformatPojo = JSONArray.parseObject(json, CidgformatPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            cidgformatPojo.setCreateby(loginUser.getRealname());   // 创建者
            cidgformatPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            cidgformatPojo.setCreatedate(new Date());   // 创建时间
            cidgformatPojo.setLister(loginUser.getRealname());   // 制表
            cidgformatPojo.setListerid(loginUser.getUserid());    // 制表id            
            cidgformatPojo.setModifydate(new Date());   //修改时间
            cidgformatPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.cidgformatService.insert(cidgformatPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改列表格式", notes = "修改列表格式", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiDgFormat.Edit")
    public R<CidgformatPojo> update(@RequestBody String json) {
        try {
            CidgformatPojo cidgformatPojo = JSONArray.parseObject(json, CidgformatPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            cidgformatPojo.setLister(loginUser.getRealname());   // 制表
            cidgformatPojo.setListerid(loginUser.getUserid());    // 制表id   
            cidgformatPojo.setModifydate(new Date());   //修改时间
            cidgformatPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.cidgformatService.update(cidgformatPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除列表格式", notes = "删除列表格式", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "CiDgFormat.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cidgformatService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增列表格式Item", notes = "新增列表格式Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiDgFormat.Add")
    public R<CidgformatitemPojo> createItem(@RequestBody String json) {
        try {
            CidgformatitemPojo cidgformatitemPojo = JSONArray.parseObject(json, CidgformatitemPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            cidgformatitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.cidgformatitemService.insert(cidgformatitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除列表格式Item", notes = "删除列表格式Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiDgFormat.Delete")
    public R<Integer> deleteItem(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cidgformatitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}

