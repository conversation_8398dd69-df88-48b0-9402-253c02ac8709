package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiconfigPojo;
import inks.system.service.CiconfigService;
import inks.system.service.PisubscriberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统参数(CiConfig)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-27 18:30:49
 */
@RestController
@RequestMapping("SYSM06B2")
@Api(tags = "SYSM06B1:系统参数:租户级")
public class SYSM06B2Controller extends CiconfigController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(SYSM06B1Controller.class);
    /**
     * 服务对象
     */
    @Resource
    private CiconfigService ciconfigService;
    /**
     * 服务对象
     */
    @Resource
    private PisubscriberService pisubscriberService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiConfig.List")
    public R<PageInfo<CiconfigPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiConfig.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Cfglevel=1";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.ciconfigService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "批量更新参数", notes = "批量更新参数", produces = "application/json")
    @RequestMapping(value = "/updateList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiConfig.Edit")
    public R<List<CiconfigPojo>> updateList(@RequestBody String json) {
        try {
            List<CiconfigPojo> lst = JSONArray.parseArray(json, CiconfigPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            int num = this.ciconfigService.updateList(lst, loginUser);
            if (num > 0) {
                //重建从redis中获取UI参数内容
                this.ciconfigService.getMapByTenant(1, loginUser.getTenantid());
                //重建从redis中获取UI参数内容
                this.ciconfigService.getMapByTenUi(1, loginUser.getTenantid());
                return R.ok(this.pisubscriberService.getTenConfigByTenant(loginUser.getTenantid()));
            } else {
                return R.fail("没有数据被更新");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "默认参数表", notes = "默认参数表", produces = "application/json")
    @RequestMapping(value = "/getListByDefault", method = RequestMethod.GET)
    public R<List<CiconfigPojo>> getListByDefault() {
        try {
            return R.ok(this.ciconfigService.getListByDefault());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "根据前缀Get一批参数", notes = "根据前缀Get一批参数", produces = "application/json")
    @RequestMapping(value = "/getListByPrefix", method = RequestMethod.GET)
    public R<Map<String, String>> getListByPrefix(String key) {
        try {
            Map<String, String> map = new HashMap<>();

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            if (loginUser == null) {
                return R.fail("用户信息丢失,请重新登录");
            }
            List<CiconfigPojo> lst = this.ciconfigService.getListByPrefix(key, loginUser.getTenantid());
            if (!lst.isEmpty()) {
                for (CiconfigPojo pojo : lst) {
                    map.put(pojo.getCfgkey(), pojo.getCfgvalue());
                }
                return R.ok(map);
            } else {
                return R.ok(null);
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

