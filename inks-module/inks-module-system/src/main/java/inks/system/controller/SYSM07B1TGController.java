package inks.system.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CitextgeneratorPojo;
import inks.system.service.CitextgeneratorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 文本生成器(CiTextGenerator)表控制层
 *
 * <AUTHOR>
 * @since 2023-12-18 14:54:20
 */
@RestController
@RequestMapping("SYSM07B1TG")
@Api(tags = "SYSM07B1TG:文本生成器")
public class SYSM07B1TGController extends CitextgeneratorController {

    @Resource
    private CitextgeneratorService citextgeneratorService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "RowNum重新排序(0开始)，传入Parentid相同的List<id>", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/orderByRowNum", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiTextGenerator.List")
    public R<List<CitextgeneratorPojo>> orderByRowNum(@RequestBody String json) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<String> ids = JSON.parseObject(json, new TypeReference<List<String>>() {
            });
            List<CitextgeneratorPojo> citextgeneratorPojos = this.citextgeneratorService.orderByRowNum(ids, loginUser.getTenantid());
            return R.ok(citextgeneratorPojos);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 获取树状结构视图", notes = "", produces = "application/json")
    @RequestMapping(value = "/getTree", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiTextGenerator.List")
    public R<List<CitextgeneratorPojo>> getTree() {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.citextgeneratorService.getTree(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
