package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.constant.CacheConstants;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiconfigPojo;
import inks.system.service.CiconfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 系统参数(CiConfig)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-27 18:30:49
 */
@RestController
@RequestMapping("SYSM06B3")
@Api(tags = "SYSM06B1:系统参数:用户级")
public class SYSM06B3Controller extends CiconfigController {
    /**
     * 服务对象
     */
    @Resource
    private CiconfigService ciconfigService;

    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiConfig.List")
    public R<PageInfo<CiconfigPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiConfig.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Cfglevel=3";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.ciconfigService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取系统参数详细信息", notes = "获取系统参数详细信息", produces = "application/json")
    @RequestMapping(value = "/getConfigValue", method = RequestMethod.GET)
    public R<String> getConfigValue(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.ciconfigService.getConfigValue(key, loginUser.getTenantid(), loginUser.getUserid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增系统参数", notes = "新增系统参数", produces = "application/json")
    @RequestMapping(value = "/setConfig", method = RequestMethod.POST)
    public R<CiconfigPojo> setConfig(@RequestBody String json) {
        try {
            CiconfigPojo ciconfigPojo = JSONArray.parseObject(json, CiconfigPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            CiconfigPojo dbPojo = this.ciconfigService.getEntityByKeyUser(ciconfigPojo.getCfgkey(), loginUser.getUserid(), loginUser.getTenantid());
            // 清除 redis缓存
            this.redisService.deleteObject(CacheConstants.USER_CONFIG_KEY + "_UI_" + loginUser.getTenantid() + "_" + loginUser.getUserid());
            if (dbPojo == null) {
                ciconfigPojo.setCfglevel(3);
                ciconfigPojo.setUserid(loginUser.getUserid());
                ciconfigPojo.setCreateby(loginUser.getRealname());   // 创建者
                ciconfigPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
                ciconfigPojo.setCreatedate(new Date());   // 创建时间
                ciconfigPojo.setLister(loginUser.getRealname());   // 制表
                ciconfigPojo.setListerid(loginUser.getUserid());    // 制表id
                ciconfigPojo.setAllowui(1); // 前端参数
                ciconfigPojo.setModifydate(new Date());   //修改时间
                ciconfigPojo.setTenantid(loginUser.getTenantid());   //租户id
                ciconfigPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
                return R.ok(this.ciconfigService.insert(ciconfigPojo));
            } else {
                CiconfigPojo setPojo = new CiconfigPojo();
                setPojo.setId(dbPojo.getId());
                setPojo.setCfgvalue(ciconfigPojo.getCfgvalue());
                setPojo.setTenantid(loginUser.getTenantid());
                setPojo.setLister(loginUser.getRealname());   // 制表
                setPojo.setListerid(loginUser.getUserid());    // 制表id
                setPojo.setModifydate(new Date());   //修改时间
                return R.ok(this.ciconfigService.update(setPojo));
            }

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

