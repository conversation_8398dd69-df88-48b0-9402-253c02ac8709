package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PimenuwebPojo;
import inks.system.service.PimenuwebService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 后台导航(PiMenuWeb)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 14:23:16
 */

public class PimenuwebController {
    /**
     * 服务对象
     */
    @Resource
    private PimenuwebService pimenuwebService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取后台导航详细信息", notes = "获取后台导航详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiMenuWeb.List")
    public R<PimenuwebPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.pimenuwebService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiMenuWeb.List")
    public R<PageInfo<PimenuwebPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiMenuWeb.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.pimenuwebService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增后台导航", notes = "新增后台导航", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiMenuWeb.Add")
    public R<PimenuwebPojo> create(@RequestBody String json) {
        try {
            PimenuwebPojo pimenuwebPojo = JSONArray.parseObject(json, PimenuwebPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pimenuwebPojo.setLister(loginUser.getRealname());   //用户名
            //  pimenuwebPojo.setTenantid(loginUser.getTenantid());   //租户id
            pimenuwebPojo.setModifydate(new Date());   //修改时间   
            return R.ok(this.pimenuwebService.insert(pimenuwebPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改后台导航", notes = "修改后台导航", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiMenuWeb.Edit")
    public R<PimenuwebPojo> update(@RequestBody String json) {
        try {
            PimenuwebPojo pimenuwebPojo = JSONArray.parseObject(json, PimenuwebPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pimenuwebPojo.setLister(loginUser.getRealname());   //用户名
            //   pimenuwebPojo.setTenantid(loginUser.getTenantid());   //租户id
            pimenuwebPojo.setModifydate(new Date());   //修改时间
//            pimenuwebPojo.setAssessor(""); //审核员
//            pimenuwebPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.pimenuwebService.update(pimenuwebPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除后台导航", notes = "删除后台导航", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiMenuWeb.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pimenuwebService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiMenuWeb.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        PimenuwebPojo pimenuwebPojo = this.pimenuwebService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(pimenuwebPojo);

        //从redis中获取XML内容
        String xml = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (xml != null || !xml.equals("") || xml != "") {
            content = xml;
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 分页查询
     *
     * @param key 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "项目全菜单", notes = "项目全菜单", produces = "application/json")
    @RequestMapping(value = "/getTreeList", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "PiMenuWeb.List")
    public R<List<WebMenuPojo>> getTreeList(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<PimenuwebPojo> lst = this.pimenuwebService.getListByPid(key);
            List<WebMenuPojo> list = new ArrayList<>();
            for (PimenuwebPojo pimenuwebPojo : lst) {
                WebMenuPojo webMenuPojo = new WebMenuPojo();
                webMenuPojo.setName(pimenuwebPojo.getNavname());
                webMenuPojo.setPath(pimenuwebPojo.getMvcurl());
                webMenuPojo.setMeta(new WebMetaPojo(pimenuwebPojo.getNavname(), pimenuwebPojo.getImagecss()));
                List<WebMenuPojo> lstChil = new ArrayList<>();
                List<PimenuwebPojo> lst2 = this.pimenuwebService.getListByPid(pimenuwebPojo.getNavid());
                for (PimenuwebPojo pojo : lst2) {
                    WebMenuPojo webMenuPojo2 = new WebMenuPojo();
                    webMenuPojo2.setName(pojo.getNavname());
                    webMenuPojo2.setPath(pojo.getMvcurl());
                    webMenuPojo2.setMeta(new WebMetaPojo(pojo.getNavname(), pojo.getImagecss()));
                    List<WebMenuPojo> lstChil2 = new ArrayList<>();
                    List<PimenuwebPojo> lst3 = this.pimenuwebService.getListByPid(pojo.getNavid());
                    for (PimenuwebPojo value : lst3) {
                        WebMenuPojo webMenuPojo3 = new WebMenuPojo();
                        webMenuPojo3.setName(value.getNavname());
                        webMenuPojo3.setPath(value.getMvcurl());
                        webMenuPojo3.setMeta(new WebMetaPojo(value.getNavname(), value.getImagecss()));
                        lstChil2.add(webMenuPojo3);
                    }
                    webMenuPojo2.setChildren(lstChil2);
                    lstChil.add(webMenuPojo2);
                }

                webMenuPojo.setChildren(lstChil);
                list.add(webMenuPojo);
            }
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}

