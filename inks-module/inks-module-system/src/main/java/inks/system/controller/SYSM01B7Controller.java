package inks.system.controller;

import inks.common.core.domain.JustauthPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PijustauthPojo;
import inks.system.service.PijustauthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 第三方登录(PiJustAuth)表控制层
 *
 * <AUTHOR>
 * @since 2022-01-16 14:45:57
 */

@RestController
@RequestMapping("SYSM01B7")
@Api(tags = "SYSM01B7:第三方登录")
public class SYSM01B7Controller extends PijustauthController {


    @Resource
    private PijustauthService pijustauthService;

    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "通过第三方id获取实例", notes = "通过第三方id获取实例", produces = "application/json")
    @RequestMapping(value = "/getJustauthByUuid", method = RequestMethod.GET)
    public R<JustauthPojo> getJustauthByUuid(String key, String type, String tid) {
        try {
            if (tid == null || tid.isEmpty()) {

                LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
                tid = loginUser.getTenantid();
            }
            PijustauthPojo pijustauthPojo = this.pijustauthService.getJustauthByUuid(key, type, tid);
            JustauthPojo justauthPojo = new JustauthPojo();
            org.springframework.beans.BeanUtils.copyProperties(pijustauthPojo, justauthPojo);
            return R.ok(justauthPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "通过第三方Unionid获取列表", notes = "通过第三方id获取列表", produces = "application/json")
    @RequestMapping(value = "/getListByUnionid", method = RequestMethod.GET)
    public R<List<JustauthPojo>> getListByUnionid(String key) {
        try {
            return R.ok(this.pijustauthService.getListByUnionid(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "通过Userid获取实例", notes = "通过Userid获取实例", produces = "application/json")
    @RequestMapping(value = "/getJustauthByUserid", method = RequestMethod.GET)
    public R<JustauthPojo> getJustauthByUserid(String key, String type, String tid) {
        try {
            if (StringUtils.isBlank(tid)) {

                LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
                tid = loginUser.getTenantid();
            }
            PijustauthPojo pijustauthPojo = this.pijustauthService.getJustauthByUserid(key, type, tid);
            if (pijustauthPojo != null) {
                JustauthPojo justauthPojo = new JustauthPojo();
                org.springframework.beans.BeanUtils.copyProperties(pijustauthPojo, justauthPojo);
                return R.ok(justauthPojo);
            } else {
                return R.ok(null);
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "通过Deptid获取主管实例", notes = "通过Userid获取主管实例", produces = "application/json")
    @RequestMapping(value = "/getAdminListByDeptid", method = RequestMethod.GET)
    public R<List<JustauthPojo>> getAdminListByDeptid(String key, String type) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pijustauthService.getAdminListByDeptid(key, type, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
