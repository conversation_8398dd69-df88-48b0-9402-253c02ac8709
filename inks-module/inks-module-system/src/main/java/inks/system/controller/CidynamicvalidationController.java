package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.config.DynamicValidation;
import inks.system.config.Operation;
import inks.system.domain.pojo.CidynamicvalidationPojo;
import inks.system.domain.pojo.CidynamicvalidationitemPojo;
import inks.system.domain.pojo.CidynamicvalidationitemdetailPojo;
import inks.system.service.CidynamicvalidationService;
import inks.system.service.CidynamicvalidationitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 动态校验规则(CiDynamicValidation)表控制层
 *
 * <AUTHOR>
 * @since 2025-03-04 15:54:38
 */
//@RestController
//@RequestMapping("cidynamicvalidation")
public class CidynamicvalidationController {

    private final static Logger logger = LoggerFactory.getLogger(CidynamicvalidationController.class);
    @Resource
    private CidynamicvalidationService cidynamicvalidationService;
    @Resource
    private CidynamicvalidationitemService cidynamicvalidationitemService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = " 获取动态校验规则详细信息", notes = "获取动态校验规则详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiDynamicValidation.List")
    public R<CidynamicvalidationPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.cidynamicvalidationService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiDynamicValidation.List")
    public R<PageInfo<CidynamicvalidationitemdetailPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiDynamicValidation.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.cidynamicvalidationService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取动态校验规则详细信息", notes = "获取动态校验规则详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiDynamicValidation.List")
    public R<CidynamicvalidationPojo> getBillEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.cidynamicvalidationService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiDynamicValidation.List")
    public R<PageInfo<CidynamicvalidationPojo>> getBillList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiDynamicValidation.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.cidynamicvalidationService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiDynamicValidation.List")
    public R<PageInfo<CidynamicvalidationPojo>> getPageTh(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiDynamicValidation.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.cidynamicvalidationService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增动态校验规则", notes = "新增动态校验规则", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiDynamicValidation.Add")
    public R<CidynamicvalidationPojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            CidynamicvalidationPojo cidynamicvalidationPojo = JSONArray.parseObject(json, CidynamicvalidationPojo.class);

            cidynamicvalidationPojo.setCreateby(loginUser.getRealName());   // 创建者
            cidynamicvalidationPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            cidynamicvalidationPojo.setCreatedate(new Date());   // 创建时间
            cidynamicvalidationPojo.setLister(loginUser.getRealname());   // 制表
            cidynamicvalidationPojo.setListerid(loginUser.getUserid());    // 制表id            
            cidynamicvalidationPojo.setModifydate(new Date());   //修改时间
            cidynamicvalidationPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.cidynamicvalidationService.insert(cidynamicvalidationPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改动态校验规则", notes = "修改动态校验规则", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiDynamicValidation.Edit")
    @DynamicValidation(code = "CiDynamicValidationBill", type = Operation.UPDATE)
    public R<CidynamicvalidationPojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            CidynamicvalidationPojo cidynamicvalidationPojo = JSONArray.parseObject(json, CidynamicvalidationPojo.class);

            cidynamicvalidationPojo.setLister(loginUser.getRealname());   // 制表
            cidynamicvalidationPojo.setListerid(loginUser.getUserid());    // 制表id   
            cidynamicvalidationPojo.setModifydate(new Date());   //修改时间
            cidynamicvalidationPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.cidynamicvalidationService.update(cidynamicvalidationPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除动态校验规则", notes = "删除动态校验规则", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiDynamicValidation.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cidynamicvalidationService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    @ApiOperation(value = " 新增动态校验规则Item", notes = "新增动态校验规则Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiDynamicValidation.Add")
    public R<CidynamicvalidationitemPojo> createItem(@RequestBody String json) {
        try {
            CidynamicvalidationitemPojo cidynamicvalidationitemPojo = JSONArray.parseObject(json, CidynamicvalidationitemPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            cidynamicvalidationitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.cidynamicvalidationitemService.insert(cidynamicvalidationitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 修改动态校验规则Item", notes = "修改动态校验规则Item", produces = "application/json")
    @RequestMapping(value = "/updateItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiDynamicValidation.Edit")
    public R<CidynamicvalidationitemPojo> updateItem(@RequestBody String json) {
        try {
            CidynamicvalidationitemPojo cidynamicvalidationitemPojo = JSONArray.parseObject(json, CidynamicvalidationitemPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            cidynamicvalidationitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.cidynamicvalidationitemService.update(cidynamicvalidationitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除动态校验规则Item", notes = "删除动态校验规则Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiDynamicValidation.Delete")
    public R<Integer> deleteItem(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cidynamicvalidationitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiDynamicValidation.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        CidynamicvalidationPojo cidynamicvalidationPojo = this.cidynamicvalidationService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(cidynamicvalidationPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = cidynamicvalidationPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    CidynamicvalidationitemPojo cidynamicvalidationitemPojo = new CidynamicvalidationitemPojo();
                    cidynamicvalidationPojo.getItem().add(cidynamicvalidationitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(cidynamicvalidationPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

