package inks.system.controller;

import cn.hutool.core.util.IdUtil;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.AESUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PiuserloginPojo;
import inks.system.domain.pojo.PiusersecretPojo;
import inks.system.service.PiuserloginService;
import inks.system.service.PiusersecretService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 用户登录表(PiUserLogin)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 14:36:11
 */
@RestController
@RequestMapping("SYSM01B2")
@Api(tags = "SYSM01B2:用户登录表")
public class SYSM01B2Controller extends PiuserloginController {
    /**
     * 服务对象
     */
    @Resource
    private PiuserloginService piuserloginService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 服务对象
     */
    @Resource
    private PiusersecretService piusersecretService;

    /**
     * 编辑数据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "初始化密码", notes = "初始化密码", produces = "application/json")
    @RequestMapping(value = "/initPwd", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiUserLogin.Admin")
    public R<Integer> initPwd(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            if ("default".equals(loginUser.getTenantid())) {
                PiuserloginPojo piuserloginPojo = this.piuserloginService.getEntityByUserid(key);
                if (piuserloginPojo != null) {
                    piuserloginPojo.setUserpassword(AESUtil.Encrypt("inks@2025"));
                    this.piuserloginService.update(piuserloginPojo);
                    return R.ok(1);
                } else {
                    return R.fail("未找到相关用户");
                }
            } else {
                return R.fail("请联系平台管理员操作");
            }
        } catch (
                Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "OMS初始化密码", notes = "初始化密码,key为userid", produces = "application/json")
    @RequestMapping(value = "/initPwdByTen", method = RequestMethod.GET)
    public R<Integer> initPwdByTen(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            if (loginUser.getIsadmin() == 1) {
                List<PiuserloginPojo> piuserloginList = this.piuserloginService.getListByUserid(key, loginUser.getTenantid());
                if (piuserloginList == null) return R.fail("未找到相关用户");
                if (piuserloginList.size() == 1) {
                    PiuserloginPojo piuserloginPojo = piuserloginList.get(0);
                    piuserloginPojo.setUserpassword(AESUtil.Encrypt("inks@2025"));
                    this.piuserloginService.update(piuserloginPojo);
                    return R.ok(1);
                } else if (piuserloginList.size() > 1) {
                    return R.fail("该用户关联多个账套,无权限初始化密码");
                }
            } else {
                return R.fail("请联系平台管理员操作");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
        return R.ok(1);
    }


    /**
     * 编辑数据
     *
     * @param np 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改密码", notes = "修改密码 op为旧密码,np为新密码", produces = "application/json")
    @RequestMapping(value = "/changePwd", method = RequestMethod.GET)
    public R<Integer> changePwd(String op, String np) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            PiuserloginPojo piuserloginPojo = this.piuserloginService.getEntityByUserid(loginUser.getUserid());
            if (piuserloginPojo != null) {
                if (AESUtil.Encrypt(op).equals(piuserloginPojo.getUserpassword())) {
                    piuserloginPojo.setUserpassword(AESUtil.Encrypt(np));
                    this.piuserloginService.update(piuserloginPojo);
                    return R.ok(1);
                } else {
                    return R.fail("原密码错误");
                }

            } else {
                return R.fail("未找到相关用户");
            }
        } catch (
                Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @return 新增结果
     */
    @ApiOperation(value = "get授权Secret", notes = "get授权Secret", produces = "application/json")
    @RequestMapping(value = "/getSecret", method = RequestMethod.GET)
    public R<String> getSecret() {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            PiusersecretPojo piusersecretPojo = this.piusersecretService.getEntityByUserid(loginUser.getUserid(), loginUser.getTenantid());
            if (piusersecretPojo == null) {
                return R.fail(loginUser.getRealname() + " 授权已失效，请重复生成");
            } else {
                return R.ok(piusersecretPojo.getSecretcode());
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @return 新增结果
     */
    @ApiOperation(value = " 新增用户授权Secret", notes = "新增用户授权Secret", produces = "application/json")
    @RequestMapping(value = "/createSecret", method = RequestMethod.GET)
    public R<String> createSecret() {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            PiusersecretPojo piusersecretPojo = this.piusersecretService.getEntityByUserid(loginUser.getUserid(), loginUser.getTenantid());
            if (piusersecretPojo != null) {
                return R.fail(loginUser.getRealname() + " 已有授权，禁止重复生成");
            } else {
                piusersecretPojo = new PiusersecretPojo();
                String key = IdUtil.simpleUUID().substring(1, 21);
                piusersecretPojo.setSecretcode(key);
                piusersecretPojo.setUserid(loginUser.getUserid());
                piusersecretPojo.setCreateby(loginUser.getRealName());   // 创建者
                piusersecretPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
                piusersecretPojo.setCreatedate(new Date());   // 创建时间
                piusersecretPojo.setLister(loginUser.getRealname());   // 制表
                piusersecretPojo.setListerid(loginUser.getUserid());    // 制表id
                piusersecretPojo.setModifydate(new Date());   //修改时间
                piusersecretPojo.setTenantid(loginUser.getTenantid());   //租户id
                return R.ok(this.piusersecretService.insert(piusersecretPojo).getSecretcode());
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "修改用户授权key", notes = "修改用户授权key", produces = "application/json")
    @RequestMapping(value = "/refreshSecret", method = RequestMethod.GET)
    public R<String> refreshSecret() {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            PiusersecretPojo piusersecretPojo = this.piusersecretService.getEntityByUserid(loginUser.getUserid(), loginUser.getTenantid());
            if (piusersecretPojo == null) {
                return R.fail(loginUser.getRealname() + " 授权已失效，请重复生成");
            } else {
                String key = IdUtil.simpleUUID().substring(1, 21);
                piusersecretPojo.setSecretcode(key);
                piusersecretPojo.setLister(loginUser.getRealname());   // 制表
                piusersecretPojo.setListerid(loginUser.getUserid());    // 制表id
                piusersecretPojo.setTenantid(loginUser.getTenantid());   //租户id
                piusersecretPojo.setModifydate(new Date());   //修改时间
                return R.ok(this.piusersecretService.update(piusersecretPojo).getSecretcode());
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除用户授权key", notes = "删除用户授权key", produces = "application/json")
    @RequestMapping(value = "/deleteSecret", method = RequestMethod.GET)
    public R<Integer> deleteSecret() {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            PiusersecretPojo piusersecretPojo = this.piusersecretService.getEntityByUserid(loginUser.getUserid(), loginUser.getTenantid());
            if (piusersecretPojo == null) {
                return R.fail(loginUser.getRealname() + " 授权已失效，请重复生成");
            } else {
                return R.ok(this.piusersecretService.delete(piusersecretPojo.getId(), loginUser.getTenantid()));
            }

        } catch (
                Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
