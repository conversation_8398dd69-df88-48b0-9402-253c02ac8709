package inks.system.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiformvaildPojo;
import inks.system.service.CiformvaildService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 窗体验证(CiFormVaild)表控制层
 * 【注意！：和SYSM07B20:窗体中心 毫无关系！！！】
 *
 * <AUTHOR>
 * @since 2025-07-31 17:03:52
 */
@RestController
@RequestMapping("SYSM07B21")
@Api(tags = "SYSM07B21:窗体验证")
public class SYSM07B21Controller extends CiformvaildController {
    @Resource
    private CiformvaildService ciformvaildService;

    @Resource
    private TokenService tokenService;


    @ApiOperation(value = "通过FormCode 获取窗体验证详细信息", notes = "获取窗体验证详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByFormCode", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiFormVaild.List")
    public R<CiformvaildPojo> getBillEntityByFormCode(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.ciformvaildService.getBillEntityByFormCode(key, loginUser.getTenantid()));
    }
}
