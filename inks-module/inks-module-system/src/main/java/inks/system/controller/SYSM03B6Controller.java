package inks.system.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PirolemenuwebPojo;
import inks.system.service.PirolemenuwebService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 角色菜单Web(PiRoleMenuWeb)表控制层
 *
 * <AUTHOR>
 * @since 2024-06-11 13:55:54
 */
@RestController
@RequestMapping("SYSM03B6")
@Api(tags = "SYSM03B6:角色菜单Web")
public class SYSM03B6Controller extends PirolemenuwebController {

    @Resource
    private PirolemenuwebService pirolemenuwebService;

    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "删除角色菜单Web  ByRoleidAndNavidAndTid", notes = "删除角色菜单Web", produces = "application/json")
    @RequestMapping(value = "/deleteByRoleidAndNavid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiRoleMenuWeb.Delete")
    public R<Integer> deleteByRoleidAndNavid(String roleid, String navid) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pirolemenuwebService.deleteByRoleidAndNavid(roleid, navid, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    // json示例 "    {\" +
    //                "    \"roleid\": \"12345\",\n" +
    //                "    \"delete\": [\"navid1\", \"navid2\", \"navid3\"],\n" +
    //                "    \"create\": [\"navi4\", \"navi5\"]\n" +
    //                "}"
    @ApiOperation(value = "批量创建/删除角色菜单Web  通过一个Roleid和多个Navids", notes = "", produces = "application/json")
    @RequestMapping(value = "/batchCreateDelete", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiRoleMenuWeb.Delete")
    public R<Integer> batchCreateDelete(@RequestBody String json) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            // 解析JSON字符串
            JSONObject jsonObject = JSON.parseObject(json);
            String roleid = jsonObject.getString("roleid");
            JSONArray deleteArray = jsonObject.getJSONArray("delete");
            List<String> deleteNavids = deleteArray != null ? deleteArray.toJavaList(String.class) : null;
            JSONArray createArray = jsonObject.getJSONArray("create");
            List<String> createNavids = createArray != null ? createArray.toJavaList(String.class) : null;
            return R.ok(this.pirolemenuwebService.batchCreateDelete(roleid, deleteNavids, createNavids, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按角色查询权限", notes = "按角色查询权限", produces = "application/json")
    @RequestMapping(value = "/getListByRole", method = RequestMethod.GET)
    public R<List<PirolemenuwebPojo>> getListByRole(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.pirolemenuwebService.getListByRole(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
