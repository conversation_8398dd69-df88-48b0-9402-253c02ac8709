package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PiadminloginPojo;
import inks.system.service.PiadminloginService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

/**
 * 管理员登录(PiAdminLogin)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 13:55:37
 */
public class PiadminloginController {
    /**
     * 服务对象
     */
    @Resource
    private PiadminloginService piadminloginService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取管理员登录详细信息", notes = "获取管理员登录详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiAdminLogin.List")
    public R<PiadminloginPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.piadminloginService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiAdminLogin.List")
    public R<PageInfo<PiadminloginPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiAdminLogin.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.piadminloginService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增管理员登录", notes = "新增管理员登录", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiAdminLogin.Add")
    public R<PiadminloginPojo> create(@RequestBody String json) {
        try {
            PiadminloginPojo piadminloginPojo = JSONArray.parseObject(json, PiadminloginPojo.class);

            //  LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            // piadminloginPojo.setLister(loginUser.getRealname());   //用户名
            //   piadminloginPojo.setTenantid(loginUser.getTenantid());   //租户id
            //  piadminloginPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.piadminloginService.insert(piadminloginPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改管理员登录", notes = "修改管理员登录", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiAdminLogin.Edit")
    public R<PiadminloginPojo> update(@RequestBody String json) {
        try {
            PiadminloginPojo piadminloginPojo = JSONArray.parseObject(json, PiadminloginPojo.class);

            //  LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //  piadminloginPojo.setLister(loginUser.getRealname());   //用户名
            //    piadminloginPojo.setTenantid(loginUser.getTenantid());   //租户id
            //    piadminloginPojo.setModifydate(new Date());   //修改时间
//            piadminloginPojo.setAssessor(""); //审核员
//            piadminloginPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.piadminloginService.update(piadminloginPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除管理员登录", notes = "删除管理员登录", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiAdminLogin.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.piadminloginService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiAdminLogin.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        PiadminloginPojo piadminloginPojo = this.piadminloginService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(piadminloginPojo);

        //从redis中获取XML内容
        String xml = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (xml != null || !xml.equals("") || xml != "") {
            content = xml;
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

