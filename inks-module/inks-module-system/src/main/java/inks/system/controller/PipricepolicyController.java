package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PipricepolicyPojo;
import inks.system.domain.pojo.PipricepolicyitemPojo;
import inks.system.domain.pojo.PipricepolicyitemdetailPojo;
import inks.system.service.PipricepolicyService;
import inks.system.service.PipricepolicyitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 功能价格(Pipricepolicy)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 14:32:48
 */

public class PipricepolicyController {
    /**
     * 服务对象
     */
    @Resource
    private PipricepolicyService pipricepolicyService;

    /**
     * 服务对象Item
     */
    @Resource
    private PipricepolicyitemService pipricepolicyitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取功能价格详细信息", notes = "获取功能价格详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiPricePolicy.List")
    public R<PipricepolicyPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pipricepolicyService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiPricePolicy.List")
    public R<PageInfo<PipricepolicyitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiPricePolicy.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.pipricepolicyService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取功能价格详细信息", notes = "获取功能价格详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiPricePolicy.List")
    public R<PipricepolicyPojo> getBillEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pipricepolicyService.getBillEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiPricePolicy.List")
    public R<PageInfo<PipricepolicyPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiPricePolicy.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.pipricepolicyService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiPricePolicy.List")
    public R<PageInfo<PipricepolicyPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiPricePolicy.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.pipricepolicyService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增功能价格", notes = "新增功能价格", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiPricePolicy.Add")
    public R<PipricepolicyPojo> create(@RequestBody String json) {
        try {
            PipricepolicyPojo pipricepolicyPojo = JSONArray.parseObject(json, PipricepolicyPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pipricepolicyPojo.setCreateby(loginUser.getRealname());
            pipricepolicyPojo.setCreatebyid(loginUser.getUserid());
            pipricepolicyPojo.setLister(loginUser.getRealname());   //用户名
            pipricepolicyPojo.setListerid(loginUser.getUserid());
            pipricepolicyPojo.setModifydate(new Date());   //修改时间
            pipricepolicyPojo.setAssessor(""); //审核员
            pipricepolicyPojo.setAssessorid("");
            pipricepolicyPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.pipricepolicyService.insert(pipricepolicyPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改功能价格", notes = "修改功能价格", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiPricePolicy.Edit")
    public R<PipricepolicyPojo> update(@RequestBody String json) {
        try {
            PipricepolicyPojo pipricepolicyPojo = JSONArray.parseObject(json, PipricepolicyPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pipricepolicyPojo.setLister(loginUser.getRealname());   //用户名
            pipricepolicyPojo.setListerid(loginUser.getUserid());
            pipricepolicyPojo.setModifydate(new Date());   //修改时间
            pipricepolicyPojo.setAssessor(""); //审核员
            pipricepolicyPojo.setAssessorid("");
            pipricepolicyPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.pipricepolicyService.update(pipricepolicyPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除功能价格", notes = "删除功能价格", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiPricePolicy.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pipricepolicyService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增功能价格Item", notes = "新增功能价格Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiPricePolicy.Add")
    public R<PipricepolicyitemPojo> createItem(@RequestBody String json) {
        try {
            PipricepolicyitemPojo pipricepolicyitemPojo = JSONArray.parseObject(json, PipricepolicyitemPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //      pipricepolicyitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.pipricepolicyitemService.insert(pipricepolicyitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除功能价格Item", notes = "删除功能价格Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiPricePolicy.Delete")
    public R<Integer> deleteItem(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pipricepolicyitemService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核功能价格", notes = "审核功能价格", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiPricePolicy.Approval")
    public R<PipricepolicyPojo> approval(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            PipricepolicyPojo pipricepolicyPojo = this.pipricepolicyService.getEntity(key);
            if (pipricepolicyPojo.getAssessor().equals("")) {
                pipricepolicyPojo.setAssessor(loginUser.getRealname()); //审核员
                pipricepolicyPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                pipricepolicyPojo.setAssessor(""); //审核员
                pipricepolicyPojo.setAssessorid(""); //审核员
            }
            pipricepolicyPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.pipricepolicyService.approval(pipricepolicyPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiPricePolicy.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        PipricepolicyPojo pipricepolicyPojo = this.pipricepolicyService.getBillEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(pipricepolicyPojo);
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(pipricepolicyPojo.getItem());

        //从redis中获取XML内容
        String xml = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (xml != null || !xml.equals("") || xml != "") {
            content = xml;
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取功能价格详细信息", notes = "获取功能价格详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByFunction", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiPricePolicy.List")
    public R<PipricepolicyPojo> getBillEntityByFunction(String key) {
        try {

            return R.ok(this.pipricepolicyService.getBillEntityByFunction(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}

