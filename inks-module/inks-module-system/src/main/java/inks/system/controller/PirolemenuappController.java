package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PirolemenuappPojo;
import inks.system.service.PirolemenuappService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 角色菜单App(PiRoleMenuApp)表控制层
 *
 * <AUTHOR>
 * @since 2024-06-11 13:55:54
 */
//@RestController
//@RequestMapping("pirolemenuapp")
public class PirolemenuappController {

    private final static Logger logger = LoggerFactory.getLogger(PirolemenuappController.class);
    @Resource
    private PirolemenuappService pirolemenuappService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = " 获取角色菜单App详细信息", notes = "获取角色菜单App详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiRoleMenuApp.List")
    public R<PirolemenuappPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.pirolemenuappService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiRoleMenuApp.List")
    public R<PageInfo<PirolemenuappPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiRoleMenuApp.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            return R.ok(this.pirolemenuappService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增角色菜单App", notes = "新增角色菜单App", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiRoleMenuApp.Add")
    public R<PirolemenuappPojo> create(@RequestBody String json) {
        try {
            PirolemenuappPojo pirolemenuappPojo = JSONArray.parseObject(json, PirolemenuappPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pirolemenuappPojo.setCreateby(loginUser.getRealName());   // 创建者
            pirolemenuappPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            pirolemenuappPojo.setCreatedate(new Date());   // 创建时间
            pirolemenuappPojo.setLister(loginUser.getRealname());   // 制表
            pirolemenuappPojo.setListerid(loginUser.getUserid());    // 制表id  
            pirolemenuappPojo.setModifydate(new Date());   //修改时间
            pirolemenuappPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.pirolemenuappService.insert(pirolemenuappPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改角色菜单App", notes = "修改角色菜单App", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiRoleMenuApp.Edit")
    public R<PirolemenuappPojo> update(@RequestBody String json) {
        try {
            PirolemenuappPojo pirolemenuappPojo = JSONArray.parseObject(json, PirolemenuappPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pirolemenuappPojo.setLister(loginUser.getRealname());   // 制表
            pirolemenuappPojo.setListerid(loginUser.getUserid());    // 制表id  
            pirolemenuappPojo.setTenantid(loginUser.getTenantid());   //租户id
            pirolemenuappPojo.setModifydate(new Date());   //修改时间
//            pirolemenuappPojo.setAssessor(""); // 审核员
//            pirolemenuappPojo.setAssessorid(""); // 审核员id
//            pirolemenuappPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.pirolemenuappService.update(pirolemenuappPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除角色菜单App", notes = "删除角色菜单App", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiRoleMenuApp.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pirolemenuappService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiRoleMenuApp.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        PirolemenuappPojo pirolemenuappPojo = this.pirolemenuappService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(pirolemenuappPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

