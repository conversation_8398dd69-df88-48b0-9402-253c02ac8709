package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PifunctionmenufrmPojo;
import inks.system.domain.pojo.PifunctionmenuwebPojo;
import inks.system.service.PifunctionmenufrmService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 服务Frm关系(PiFunctionMenuFrm)表控制层
 *
 * <AUTHOR>
 * @since 2024-05-05 16:28:10
 */
//@RestController
//@RequestMapping("pifunctionmenufrm")
public class PifunctionmenufrmController {

    private final static Logger logger = LoggerFactory.getLogger(PifunctionmenufrmController.class);
    @Resource
    private PifunctionmenufrmService pifunctionmenufrmService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = " 获取服务Frm关系详细信息", notes = "获取服务Frm关系详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionMenuFrm.List")
    public R<PifunctionmenufrmPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pifunctionmenufrmService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiFunctionMenuFrm.List")
    public R<PageInfo<PifunctionmenufrmPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiFunctionMenuFrm.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.pifunctionmenufrmService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增服务Frm关系", notes = "新增服务Frm关系", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiFunctionMenuFrm.Add")
    public R<PifunctionmenufrmPojo> create(@RequestBody String json) {
        try {
            PifunctionmenufrmPojo pifunctionmenufrmPojo = JSONArray.parseObject(json, PifunctionmenufrmPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pifunctionmenufrmPojo.setCreateby(loginUser.getRealName());   // 创建者
            pifunctionmenufrmPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            pifunctionmenufrmPojo.setCreatedate(new Date());   // 创建时间
            pifunctionmenufrmPojo.setLister(loginUser.getRealname());   // 制表
            pifunctionmenufrmPojo.setListerid(loginUser.getUserid());    // 制表id  
            pifunctionmenufrmPojo.setModifydate(new Date());   //修改时间
            pifunctionmenufrmPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.pifunctionmenufrmService.insert(pifunctionmenufrmPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改服务Frm关系", notes = "修改服务Frm关系", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiFunctionMenuFrm.Edit")
    public R<PifunctionmenufrmPojo> update(@RequestBody String json) {
        try {
            PifunctionmenufrmPojo pifunctionmenufrmPojo = JSONArray.parseObject(json, PifunctionmenufrmPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pifunctionmenufrmPojo.setLister(loginUser.getRealname());   // 制表
            pifunctionmenufrmPojo.setListerid(loginUser.getUserid());    // 制表id  
            pifunctionmenufrmPojo.setTenantid(loginUser.getTenantid());   //租户id
            pifunctionmenufrmPojo.setModifydate(new Date());   //修改时间
//            pifunctionmenufrmPojo.setAssessor(""); // 审核员
//            pifunctionmenufrmPojo.setAssessorid(""); // 审核员id
//            pifunctionmenufrmPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.pifunctionmenufrmService.update(pifunctionmenufrmPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除服务Frm关系", notes = "删除服务Frm关系", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionMenuFrm.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pifunctionmenufrmService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionMenuFrm.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        PifunctionmenufrmPojo pifunctionmenufrmPojo = this.pifunctionmenufrmService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(pifunctionmenufrmPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "根据服务号获取web菜单关系List", notes = "根据服务号获取web菜单关系List", produces = "application/json")
    @RequestMapping(value = "/getListByFunction", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionMenuWeb.List")
    public R<List<PifunctionmenuwebPojo>> getListByFunction(String key) {
        try {
            return R.ok(this.pifunctionmenufrmService.getListByFunction(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}

