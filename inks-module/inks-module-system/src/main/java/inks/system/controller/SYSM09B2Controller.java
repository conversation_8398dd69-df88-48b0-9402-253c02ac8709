package inks.system.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CibigdataPojo;
import inks.system.service.CibigdataService;
import inks.system.service.CibigdataitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 数据大屏(Cibigdata)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-25 10:31:59
 */
@RestController
@RequestMapping("SYSM09B2")
@Api(tags = "SYSM09B2:数据大屏")
public class SYSM09B2Controller extends CibigdataController {

    /**
     * 服务对象
     */
    @Resource
    private CibigdataService cibigdataService;

    /**
     * 服务对象Item
     */
    @Resource
    private CibigdataitemService cibigdataitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getList", method = RequestMethod.GET)
    public R<List<CibigdataPojo>> getList() {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cibigdataService.getListByTenant(loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
