package inks.system.controller;

import inks.common.core.constant.InksConstants;
import inks.common.core.domain.SerialNoPojo;
import inks.common.core.utils.BillCodeUtil;
import inks.common.core.utils.DateUtils;
import inks.common.redis.service.RedisService;
import inks.system.domain.CibillcodeEntity;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Component
public class SerialNoUtils implements ApplicationContextAware {
    public static final String REDIS_SERIALNO_REFNO = "refno:";
    // 日志记录器
    private static final Logger log = LoggerFactory.getLogger(SerialNoUtils.class);
    //使用ApplicationContext获取Bean： 如果你的类必须保持静态字段，并且你希望访问Spring的bean（如 RedisService 和 DataSource），你可以通过 ApplicationContext 来获取这些bean
    private static RedisService staticRedisService;
    private static DataSource staticDataSource;

    /**
     * 生成序列号的主方法
     *
     * @param moduleCode    模块代码
     * @param tablename_req 请求的表名
     * @param prefix_req    请求的前缀
     * @param tid           租户ID
     * @return 生成的序列号
     * @throws SQLException 数据库操作异常
     */
    public static String generateSerialNo(String moduleCode, String tablename_req, String prefix_req, String tid) throws SQLException {
        // 记录方法开始时间，用于性能监控
        long startTime = System.currentTimeMillis();

        // 存放序列号的Redis key
        String redisKey = REDIS_SERIALNO_REFNO + moduleCode + tid;

        // 初始化序列号为当前时间
        String serialNo = DateUtils.dateTimeNow();  // YYYYMMDDHHMMSS
        String renfosn = "";

        // 根据模块代码和租户ID获取编码规则实体
        CibillcodeEntity cibillcodeEntity = getEntityByModuleCode(moduleCode, tid);
        // 如果没有找到，尝试获取默认租户的规则
        if (cibillcodeEntity == null) {
            cibillcodeEntity = getEntityByModuleCode(moduleCode, InksConstants.DEFAULT_TENANT);
        }

        // 如果数据库没有此编码规则，则手动创建一个CibillcodeEntity对象
        // 要求格式： “BD-yyyyMM-[0000] ,以月计数； 排序字段 createdate
        // Bus_Deliery 若prefix_req没有传，则取2个大写BD
        if (cibillcodeEntity == null && StringUtils.isNotBlank(tablename_req)) {
            cibillcodeEntity = new CibillcodeEntity();
            cibillcodeEntity.setTablename(tablename_req);
            cibillcodeEntity.setColumnname("RefNo");
            cibillcodeEntity.setDatecolumn("CreateDate");
            // 若未传入前缀，则默认取表名前俩大写作为前缀
            if (StringUtils.isBlank(prefix_req)) {
                prefix_req = getPrefixByTableName(tablename_req);
            }

            // 设置默认的编码规则：前缀-年-月-序号
            cibillcodeEntity.setPrefix1(prefix_req);
            cibillcodeEntity.setSuffix1("-");
            cibillcodeEntity.setPrefix2("YYYY");
            cibillcodeEntity.setSuffix2("-");
            cibillcodeEntity.setPrefix3("MM");
            cibillcodeEntity.setSuffix3("-");
            cibillcodeEntity.setPrefix4("[0000]");
            cibillcodeEntity.setSuffix4("");
            cibillcodeEntity.setPrefix5("");
            cibillcodeEntity.setSuffix5("");
            cibillcodeEntity.setCounttype("month");
            cibillcodeEntity.setStep(1);
        }

        // 如果成功获取或创建了
        // 编码规则
        if (cibillcodeEntity != null) {
            // 转换编码规则实体为序列号POJO
            SerialNoPojo serialNoPojo = new SerialNoPojo();
            BeanUtils.copyProperties(cibillcodeEntity, serialNoPojo);
            // 获取前缀
            String prefixDB = BillCodeUtil.getPrefix(serialNoPojo);

            // 从redis中获取最新序号
            String redisSerialNo = staticRedisService.getCacheObject(redisKey);
            if (StringUtils.isNotBlank(redisSerialNo)) {
                if (redisSerialNo.length() > prefixDB.length()) {
                    renfosn = redisSerialNo.substring(prefixDB.length()); // 去掉前缀(包括-)
                    log.info("----------moduleCode: " + moduleCode + "： 从redis中获取最新序号(RefNo)：" + redisSerialNo);
                }
            } else {
                try {
                    // 获取当前日期
                    Date currentDate = new Date();
                    // 从数据库获取当前序号
                    renfosn = getSerialNo(cibillcodeEntity, prefixDB.length() + 1, tid, currentDate);
                    log.info("----------moduleCode: " + moduleCode + "： redis中无,从数据库获取最新序号(数字后缀)：" + renfosn);
                } catch (Exception e) {
                    log.error("----------moduleCode: " + moduleCode + "： 生成序号失败", e);
                    throw new RuntimeException("生成序号失败", e);
                }
            }
            // 如果获取的序号不是数字，重置为1
            if (!StringUtils.isNumeric(renfosn)) {
                renfosn = "1";
            }

            // 生成最终的单据序列号
            serialNo = BillCodeUtil.getSerialNo(serialNoPojo, renfosn);
        }

        // 计算方法执行耗时
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        // 记录生成的序列号信息
        log.info("----------moduleCode: " + moduleCode + "： 编码生成方法 执行时间: " + duration + " 毫秒, 最终生成编码：" + serialNo);

        return serialNo;
    }


    public static void saveRedisSerialNo(String serialNo, String moduleCode, String tid) throws SQLException {
        // 存放序列号的Redis key
        String redisKey = REDIS_SERIALNO_REFNO + moduleCode + tid;
        // 保存序列号到Redis
        staticRedisService.setCacheObject(redisKey, serialNo, 12L, TimeUnit.HOURS);
        log.info("----------moduleCode: " + moduleCode + "： 成功保存序列号(RefNo)到Redis：" + serialNo);
    }


    /**
     * 根据模块代码和租户ID获取编码规则实体
     *
     * @param moduleCode 模块代码
     * @param tid        租户ID
     * @return CibillcodeEntity 编码规则实体
     * @throws SQLException 数据库查询异常
     */
    public static CibillcodeEntity getEntityByModuleCode(String moduleCode, String tid) throws SQLException {
        // 查询SQL，获取指定模块和租户的编码规则
        String sql = "SELECT * FROM CiBillCode WHERE ModuleCode = ? AND Tenantid = ?";

        try (Connection connection = staticDataSource.getConnection();
             PreparedStatement ps = connection.prepareStatement(sql)) {

            ps.setString(1, moduleCode);
            ps.setString(2, tid);

            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    // 创建并填充编码规则实体对象
                    CibillcodeEntity cibillcodeEntity = new CibillcodeEntity();
                    // 逐一设置实体属性，对应数据库字段
                    cibillcodeEntity.setModulecode(rs.getString("ModuleCode"));
                    cibillcodeEntity.setTenantid(rs.getString("Tenantid"));
                    cibillcodeEntity.setTablename(rs.getString("TableName"));
                    cibillcodeEntity.setColumnname(rs.getString("ColumnName"));
                    cibillcodeEntity.setDatecolumn(rs.getString("DateColumn"));
                    cibillcodeEntity.setPrefix1(rs.getString("Prefix1"));
                    cibillcodeEntity.setSuffix1(rs.getString("Suffix1"));
                    cibillcodeEntity.setPrefix2(rs.getString("Prefix2"));
                    cibillcodeEntity.setSuffix2(rs.getString("Suffix2"));
                    cibillcodeEntity.setPrefix3(rs.getString("Prefix3"));
                    cibillcodeEntity.setSuffix3(rs.getString("Suffix3"));
                    cibillcodeEntity.setPrefix4(rs.getString("Prefix4"));
                    cibillcodeEntity.setSuffix4(rs.getString("Suffix4"));
                    cibillcodeEntity.setPrefix5(rs.getString("Prefix5"));
                    cibillcodeEntity.setSuffix5(rs.getString("Suffix5"));
                    cibillcodeEntity.setCounttype(rs.getString("CountType"));
                    cibillcodeEntity.setStep(rs.getInt("Step"));
                    cibillcodeEntity.setDbfilter(rs.getString("DBFilter"));

                    return cibillcodeEntity;
                }
            }
        } catch (SQLException e) {
            // 记录查询异常日志
            log.error("根据模块代码获取实体时发生错误", e);
            throw e;
        }
        return null;
    }

    /**
     * 获取序列号
     *
     * @param entity      编码规则实体
     * @param subindex    截取位置
     * @param tid         租户ID
     * @param currentDate 当前日期
     * @return 序列号
     * @throws SQLException 数据库查询异常
     */
    public static String getSerialNo(CibillcodeEntity entity, int subindex, String tid, Date currentDate) throws SQLException {
        // 构建动态SQL查询语句
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT IFNULL(MAX(SUBSTRING(RefNo, ?, 4)), 1) AS RefNo FROM ")
                .append(entity.getTablename()) // 获取表名
                .append(" WHERE Tenantid = ?");

        // 根据CountType计数类型（年/月/日）添加日期过滤条件
        if ("year".equals(entity.getCounttype())) {
            sql.append(" AND DATE_FORMAT(").append(entity.getDatecolumn()).append(", '%Y') = DATE_FORMAT(?, '%Y')");
        } else if ("month".equals(entity.getCounttype())) {
            sql.append(" AND DATE_FORMAT(").append(entity.getDatecolumn()).append(", '%Y-%m') = DATE_FORMAT(?, '%Y-%m')");
        } else if ("day".equals(entity.getCounttype())) {
            sql.append(" AND DATE_FORMAT(").append(entity.getDatecolumn()).append(", '%Y-%m-%d') = DATE_FORMAT(?, '%Y-%m-%d')");
        }

        // 如果存在额外的DBfilter数据库过滤条件，添加到SQL中
        if (StringUtils.isNotBlank(entity.getDbfilter())) {
            sql.append(" AND ").append(entity.getDbfilter());
        }

        // 按单据日期降序排序，取最新记录
        sql.append(" ORDER BY BillDate DESC LIMIT 1");

        try (Connection connection = staticDataSource.getConnection(); // 使用 DataSource 获取数据库连接
             PreparedStatement ps = connection.prepareStatement(sql.toString())) { // 准备 SQL 查询

            // 设置参数值
            ps.setInt(1, subindex); // 设置 SUBSTRING 函数中的 subindex
            ps.setString(2, tid); // 设置 Tenantid 参数
            ps.setDate(3, new java.sql.Date(currentDate.getTime())); // 设置当前日期

            try (ResultSet rs = ps.executeQuery()) { // 执行查询
                if (rs.next()) { // 如果查询有结果
                    return rs.getString("RefNo"); // 返回序列号
                }
            }
        } catch (SQLException e) {
            // 记录获取序列号时的异常
            log.error("获取序列号时发生错误", e);
            throw e;
        }
        return "1"; // 默认返回1作为初始序列号
    }

    /**
     * 根据表名获取前缀
     * 需要根据实际业务逻辑实现
     *
     * @param tablename_req 表名
     * @return 前缀
     */
    // 默认拿表名前2个大写字母作为前缀：假设tablename_req是Bus_Deliery，前缀是BD,
    //                            假设tablename_req是Bus_deliery，前缀是BU
    public static String getPrefixByTableName(String tablename_req) {
        // 只要2个大写字母，就取前2个
        StringBuilder prefix = new StringBuilder(2);
        for (char ch : tablename_req.toCharArray()) {
            if (Character.isUpperCase(ch)) {
                prefix.append(ch);
                if (prefix.length() == 2) {
                    return prefix.toString();
                }
            }
        }
        // 如果没有找到两个大写字母，提取前两个字符
        return tablename_req.substring(0, Math.min(2, tablename_req.length())).toUpperCase();
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        staticRedisService = applicationContext.getBean(RedisService.class);
        staticDataSource = applicationContext.getBean(DataSource.class);
    }
}