package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiformsPojo;
import inks.system.service.CiformsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 窗体中心(CiForms)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-18 15:43:44
 */
@RestController
@RequestMapping("SYSM07B20")
@Api(tags = "SYSM07B20:窗体中心")
public class SYSM07B20Controller extends CiformsController {
    @Resource
    private CiformsService ciformsService;
    @Resource
    private TokenService tokenService;

//接口1:  getListByCode =D01M03B1
//打开窗口时，可以一次取功能全部；


    @ApiOperation(value = " 获取窗体中心List： key即ModuleCode", notes = "", produces = "application/json")
    @RequestMapping(value = "/getListByCode", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiForms.List")
    public R<List<CiformsPojo>> getListByCode(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.ciformsService.getListByCode(key, loginUser.getTenantid()));
    }

    //接口2：getEditEntityByCode=D01M03B1
//只读回Edit窗口；
    @ApiOperation(value = " 获取窗体中心实体 key即ModuleCode type即formtype", notes = "", produces = "application/json")
    @RequestMapping(value = "/getEntityByCode", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiForms.List")
    public R<CiformsPojo> getEntityByCode(String key, int type) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.ciformsService.getEntityByCode(key, type, loginUser.getTenantid()));
    }

    @ApiOperation(value = "所有租户的 按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListAll", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiForms.List")
    public R<PageInfo<CiformsPojo>> getPageListAll(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("CiForms.ModuleCode");
        String qpfilter = "";
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        return R.ok(this.ciformsService.getPageListAll(queryParam));
    }

}
