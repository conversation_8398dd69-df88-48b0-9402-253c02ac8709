package inks.system.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PipermissionPojo;
import inks.system.service.PipermissionService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 权限关系表(PiPermission)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-17 10:37:21
 */
public class PipermissionController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(PipermissionController.class);
    /**
     * 服务对象
     */
    @Resource
    private PipermissionService pipermissionService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取权限关系表详细信息", notes = "获取权限关系表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiPermission.List")
    public R<PipermissionPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.pipermissionService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiPermission.List")
    public R<PageInfo<PipermissionPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiPermission.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.pipermissionService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增权限关系表", notes = "新增权限关系表", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiPermission.Add")
    public R<PipermissionPojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            PipermissionPojo pipermissionPojo = JSONArray.parseObject(json, PipermissionPojo.class);

            pipermissionPojo.setCreateby(loginUser.getRealName());   // 创建者
            pipermissionPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            pipermissionPojo.setCreatedate(new Date());   // 创建时间
            pipermissionPojo.setLister(loginUser.getRealname());   // 制表
            pipermissionPojo.setListerid(loginUser.getUserid());    // 制表id  
            pipermissionPojo.setModifydate(new Date());   //修改时间
            pipermissionPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.pipermissionService.insert(pipermissionPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    // json示例 "    [
    //    {
    //        "resourcetype": "Role",
    //        "resourceid": "1904065812608581632",
    //        "permid": "b33b1cf8-481b-455e-9eac-6b61134af927",
    //        "permcode": "Bus_Quotation.Add",
    //        "permname": "报价单.添加",
    //        "iscreate": 1
    //    },
    //    {
    //        "resourcetype": "Role",
    //        "resourceid": "1904065812608581632",
    //        "permid": "71d690b0-8fc7-4d43-85bc-dd3de901d5ae",
    //        "permcode": "Bus_OrderCost.Approval",
    //        "permname": "核价单.审核",
    //        "iscreate": 0
    //    }
    //]
    @ApiOperation(value = "批量删除【按钮权限】", notes = "", produces = "application/json")
    @RequestMapping(value = "/batchCreateDelete", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiRoleMenuWeb.Delete")
    public R<String> batchCreateDelete(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            // 解析JSON字符串
            List<PipermissionPojo> pipermissionList = JSON.parseObject(json, new TypeReference<List<PipermissionPojo>>() {
            });
            return R.ok(this.pipermissionService.batchCreateDelete(pipermissionList, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改权限关系表", notes = "修改权限关系表", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiPermission.Edit")
    public R<PipermissionPojo> update(@RequestBody String json) {
        try {
            PipermissionPojo pipermissionPojo = JSONArray.parseObject(json, PipermissionPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pipermissionPojo.setLister(loginUser.getRealname());   // 制表
            pipermissionPojo.setListerid(loginUser.getUserid());    // 制表id  
            pipermissionPojo.setTenantid(loginUser.getTenantid());   //租户id
            pipermissionPojo.setModifydate(new Date());   //修改时间
//            pipermissionPojo.setAssessor(""); // 审核员
//            pipermissionPojo.setAssessorid(""); // 审核员id
//            pipermissionPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.pipermissionService.update(pipermissionPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除权限关系表", notes = "删除权限关系表", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiPermission.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pipermissionService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiPermission.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        PipermissionPojo pipermissionPojo = this.pipermissionService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(pipermissionPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

