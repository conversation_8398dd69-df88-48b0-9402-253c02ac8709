package inks.system.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.domain.WebMenuPojo;
import inks.common.core.domain.WebMetaPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PimenuopsPojo;
import inks.system.service.PimenuopsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Ops导航(PiMenuOps)表控制层
 *
 * <AUTHOR>
 * @since 2024-04-24 17:03:52
 */
@RestController
@RequestMapping("SYSM05B6")
@Api(tags = "SYSM05B6:Ops导航")
public class SYSM05B6Controller extends PimenuopsController {
    /**
     * 服务对象
     */
    @Resource
    private PimenuopsService pimenuopsService;

    @Resource
    private TokenService tokenService;

    /**
     * 分页查询
     *
     * @param key 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "项目全菜单", notes = "项目全菜单", produces = "application/json")
    @GetMapping(value = "/getAllListByPid")
    @PreAuthorize(hasPermi = "PiMenuOps.List")
    public R<List<PimenuopsPojo>> getAllListByPid(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<PimenuopsPojo> lst = this.pimenuopsService.getListByPid(key);
            List<PimenuopsPojo> list = new ArrayList<>();
            for (PimenuopsPojo pimenuopsPojo : lst) {
                PimenuopsPojo opsMenuPojo = new PimenuopsPojo();
                BeanUtils.copyProperties(pimenuopsPojo, opsMenuPojo);
                List<PimenuopsPojo> lst2 = this.pimenuopsService.getListByPid(pimenuopsPojo.getNavid());
                for (PimenuopsPojo pojo : lst2) {
                    PimenuopsPojo opsMenuPojo2 = new PimenuopsPojo();
                    BeanUtils.copyProperties(pojo, opsMenuPojo2);
                    List<PimenuopsPojo> lst3 = this.pimenuopsService.getListByPid(pojo.getNavid());
                    for (PimenuopsPojo value : lst3) {
                        PimenuopsPojo opsMenuPojo3 = new PimenuopsPojo();
                        BeanUtils.copyProperties(value, opsMenuPojo3);
                        list.add(opsMenuPojo3);
                    }
                    list.add(opsMenuPojo2);
                }
                list.add(opsMenuPojo);
            }
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "项目全菜单", notes = "项目全菜单", produces = "application/json")
    @GetMapping(value = "/getTreeList")
    //@PreAuthorize(hasPermi = "PiMenuWeb.List")
    public R<List<WebMenuPojo>> getTreeList(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<PimenuopsPojo> lst = this.pimenuopsService.getListByPid(key);
            List<WebMenuPojo> list = new ArrayList<>();
            for (PimenuopsPojo pimenuopsPojo : lst) {
                WebMenuPojo opsMenuPojo = new WebMenuPojo();
                opsMenuPojo.setName(pimenuopsPojo.getNavname());
                opsMenuPojo.setPath(pimenuopsPojo.getMvcurl());
                opsMenuPojo.setMeta(new WebMetaPojo(pimenuopsPojo.getNavname(), pimenuopsPojo.getImagecss()));
                List<WebMenuPojo> lstChil = new ArrayList<>();
                List<PimenuopsPojo> lst2 = this.pimenuopsService.getListByPid(pimenuopsPojo.getNavid());
                for (PimenuopsPojo pojo : lst2) {
                    WebMenuPojo opsMenuPojo2 = new WebMenuPojo();
                    opsMenuPojo2.setName(pojo.getNavname());
                    opsMenuPojo2.setPath(pojo.getMvcurl());
                    opsMenuPojo2.setMeta(new WebMetaPojo(pojo.getNavname(), pojo.getImagecss()));
                    List<WebMenuPojo> lstChil2 = new ArrayList<>();
                    List<PimenuopsPojo> lst3 = this.pimenuopsService.getListByPid(pojo.getNavid());
                    for (PimenuopsPojo value : lst3) {
                        WebMenuPojo opsMenuPojo3 = new WebMenuPojo();
                        opsMenuPojo3.setName(value.getNavname());
                        opsMenuPojo3.setPath(value.getMvcurl());
                        opsMenuPojo3.setMeta(new WebMetaPojo(value.getNavname(), value.getImagecss()));
                        lstChil2.add(opsMenuPojo3);
                    }
                    opsMenuPojo2.setChildren(lstChil2);
                    lstChil.add(opsMenuPojo2);
                }

                opsMenuPojo.setChildren(lstChil);
                list.add(opsMenuPojo);
            }
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
