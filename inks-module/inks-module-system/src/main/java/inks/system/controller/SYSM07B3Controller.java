package inks.system.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import inks.api.feign.UtilsFeignService;
import inks.common.core.constant.CacheConstants;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CireportsPojo;
import inks.system.service.CireportsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 报表中心(CiReports)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 13:35:24
 */
@RestController
@RequestMapping("SYSM07B3")
@Api(tags = "SYSM07B3:报表中心")
public class SYSM07B3Controller extends CireportsController {

    /**
     * 服务对象
     */
    @Resource
    private CireportsService cireportsService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    @Resource
    private UtilsFeignService utilsFeignService;


    //报表中心加入打印工具设计专用接口
    //类型：get
    //名称：getGrfDesign
    //参数:   key=报表id&redis=1
    //返回：ptid报表id, temp=grfdata/ report_codes:报表id, code = "design",token=token,msg=报表名称；
    @ApiOperation(value = "打印工具设计专用接口", notes = "获取报表设计数据", produces = "application/json")
    @GetMapping("/getGrfDesign")
    public R<Map<String, Object>> getGrfDesign(String key, Integer redis) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            // 从Redis中获取报表数据
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + key);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            // 组装返回数据
            Map<String, Object> mapPrint = new HashMap<>();
            mapPrint.put("ptid", key);
            mapPrint.put("code", "design");
            mapPrint.put("token", loginUser.getToken());
            if (Objects.equals(redis, 1)) {
                mapPrint.put("temp", "report_codes:" + key);   // Text模板
            } else {
                mapPrint.put("temp", reportsPojo.getGrfdata());
            }
            return R.ok(mapPrint);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "拉取默认报表,重复code&name跳过", notes = "拉取默认报表 code可选", produces = "application/json")
    @RequestMapping(value = "/pullDefault", method = RequestMethod.GET)
    public R<List<CireportsPojo>> pullDefault(String code) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            List<CireportsPojo> fmAccountPojos = this.cireportsService.pullDefault(code, loginUser);
            return R.ok(fmAccountPojos);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListAll", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiReports.List")
    public R<PageInfo<CireportsPojo>> getPageListAll(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiReports.ModuleCode");

            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.cireportsService.getPageListAll(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 按模块编码查询报表
     *
     * @param code 主键
     * @return 查询结果
     */
    @ApiOperation(value = "按模块编码查询报表", notes = "按模块编码查询报表", produces = "application/json")
    @RequestMapping(value = "/getListByModuleCode", method = RequestMethod.GET)
    public R<List<CireportsPojo>> getListByModuleCode(String code) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            List<CireportsPojo> list = this.cireportsService.getListByModuleCode(code, loginUser.getTenantid());
            if (list != null) {
                for (CireportsPojo cireportsPojo : list) {
                    String verifyKey = CacheConstants.REPORT_CODES_KEY + cireportsPojo.getId();
                    ReportsPojo reportsPojo = new ReportsPojo();
                    reportsPojo.setRptdata(cireportsPojo.getRptdata());
                    reportsPojo.setPagerow(cireportsPojo.getPagerow());
                    reportsPojo.setTempurl(cireportsPojo.getTempurl());
                    reportsPojo.setPrintersn(cireportsPojo.getPrintersn());
                    reportsPojo.setPaperlength(cireportsPojo.getPaperlength());
                    reportsPojo.setPaperwidth(cireportsPojo.getPaperwidth());
                    reportsPojo.setGrfdata(cireportsPojo.getGrfdata());
                    redisService.setCacheObject(verifyKey, reportsPojo, (long) (60 * 12), TimeUnit.MINUTES);
                    if (cireportsPojo.getGrfdata() != null && !"".equals(cireportsPojo.getGrfdata())) {
                        cireportsPojo.setGrfdata("true");
                    } else {
                        cireportsPojo.setGrfdata(null);
                    }
                    if (cireportsPojo.getRptdata() != null && !"".equals(cireportsPojo.getRptdata())) {
                        cireportsPojo.setRptdata("true");
                    } else {
                        cireportsPojo.setRptdata(null);
                    }
                }
            }
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "传入一主表多子表Map,自定义云打印", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printFreeWebBill", method = RequestMethod.POST)
    public R<String> printFreeWebBill(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();


            //获取传入的自定义 一主表多子表Map
            Map<String, Object> billMap = JSON.parseObject(json, new TypeReference<Map<String, Object>>() {
            });
            // 创建一个新的 Map 来存储除了 "item" 之外的所有键值对
            Map<String, Object> map = new HashMap<>(billMap);
            map.remove("item");
            // 创建一个新的 List<Map> 来只存储 "item" 键值对
            List<Map<String, Object>> lstitem = new ArrayList<>();
            if (billMap.get("item") instanceof List) {
                lstitem = (List<Map<String, Object>>) billMap.get("item");
            }

            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            // map加入realname,username
            map.put("realname", loginUser.getRealname());
            map.put("username", loginUser.getUsername());

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lstitem);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");


            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "自定义云打印:主子表");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "一页两联打印:传入一主表多子表Map,自定义云打印", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printFreeWebBillMulti", method = RequestMethod.POST)
    public R<String> printFreeWebBillMulti(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            //获取传入的自定义 一主表多子表Map
            Map<String, Object> billMap = JSON.parseObject(json, new TypeReference<Map<String, Object>>() {
            });
            // 创建一个新的 Map 来存储除了 "item" 之外的所有键值对
            Map<String, Object> map = new HashMap<>(billMap);
            map.remove("item");
            // 加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
            // map加入realname,username
            map.put("realname", loginUser.getRealname());
            map.put("username", loginUser.getUsername());

            //=========获取单据Item信息========
            // 创建一个新的 List<Map> 来只存储 "item" 键值对
            List<Map<String, Object>> lstitem = new ArrayList<>();
            if (billMap.get("item") instanceof List) {
                lstitem = (List<Map<String, Object>>) billMap.get("item");
            }
            // 一式两份
            List<Map<String, Object>> lst = new ArrayList<>();
            List<Map<String, Object>> lstCopy = new ArrayList<>();
            for (Map<String, Object> item : lstitem) {
                Map<String, Object> hashMap = new HashMap<>(item);
                hashMap.put("ToWho", 1);
                hashMap.put("PageNo", 1);
                lst.add(hashMap);

                Map<String, Object> hashMapCopy = new HashMap<>(hashMap);
                hashMapCopy.put("ToWho", 2);
                lstCopy.add(hashMapCopy);
            }
            lst.addAll(lstCopy);
//            List<Map<String, Object>> lst = lstitem.stream()
//                    .map(item -> {
//                        Map<String, Object> hashMap = new HashMap<>(item);
//                        hashMap.put("ToWho", 1);
//                        hashMap.put("PageNo", 1);
//                        return hashMap;
//                    })
//                    .collect(Collectors.toList());
//            List<Map<String, Object>> lstCopy = lstitem.stream()
//                    .map(item -> {
//                        Map<String, Object> hashMap = new HashMap<>(item);
//                        hashMap.put("ToWho", 2);
//                        hashMap.put("PageNo", 1);
//                        return hashMap;
//                    })
//                    .collect(Collectors.toList());
//            lst.addAll(lstCopy);
            //            PrintColor.red("lst:" + lst);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "自定义云打印:一页两联");
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (StringUtils.isNotBlank(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "传入子表List 自定义云打印", notes = "json=分页参数,ptid打印模版,groupid(可选),sn远程打印SN(可选),redis", produces = "application/json")
    @RequestMapping(value = "/printFreeWebList", method = RequestMethod.POST)
    public R<String> printFreeWebList(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();


            // 报表数据 子表List
            List<Map<String, Object>> lstitem = JSON.parseObject(json, new TypeReference<List<Map<String, Object>>>() {
            });
            //表头转MAP
            Map<String, Object> map = new HashMap<>();
            if (lstitem != null && !lstitem.isEmpty() && lstitem.get(0) != null) {
                map.put("groupname", StringUtils.defaultIfBlank(lstitem.get(0).get("groupname") == null ? "" : lstitem.get(0).get("groupname").toString(), ""));
                map.put("abbreviate", StringUtils.defaultIfBlank(lstitem.get(0).get("abbreviate") == null ? "" : lstitem.get(0).get("abbreviate").toString(), ""));
                map.put("groupuid", StringUtils.defaultIfBlank(lstitem.get(0).get("groupuid") == null ? "" : lstitem.get(0).get("groupuid").toString(), ""));
            }


            // 获取单据表头.加入公司信息
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
            // map加入realname,username
            map.put("realname", loginUser.getRealname());
            map.put("username", loginUser.getUsername());

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lstitem);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");


            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "自定义云打印:子表List");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                redisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
                return R.ok(JSONObject.toJSONString(mapPrint));
            } else {
                // 远程SN打印
                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
                if (rPrint.getCode() == 200) {
                    return R.ok();
                } else {
                    return R.fail(rPrint.getMsg());
                }
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
