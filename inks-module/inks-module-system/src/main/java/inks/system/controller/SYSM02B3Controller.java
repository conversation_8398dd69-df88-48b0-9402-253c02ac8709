package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PipricepolicyPojo;
import inks.system.service.PipricepolicyService;
import inks.system.service.PipricepolicyitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 服务价格表(Pipricepolicy)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 14:32:48
 */
@RestController
@RequestMapping("SYSM02B3")
@Api(tags = "SYSM02B3:服务价格表")
public class SYSM02B3Controller extends PipricepolicyController {

    /**
     * 服务对象
     */
    @Resource
    private PipricepolicyService pipricepolicyService;

    /**
     * 服务对象Item
     */
    @Resource
    private PipricepolicyitemService pipricepolicyitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询公共域服务", notes = "按条件分页查询公共域服务", produces = "application/json")
    @RequestMapping(value = "/getPublicBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiPricePolicy.List")
    public R<PageInfo<PipricepolicyPojo>> getPublicBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || queryParam.getOrderBy() == "")
                queryParam.setOrderBy("PiPricePolicy.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            queryParam.setFilterstr(" and PiPricePolicy.ReleaseDomain=1");
            return R.ok(this.pipricepolicyService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
