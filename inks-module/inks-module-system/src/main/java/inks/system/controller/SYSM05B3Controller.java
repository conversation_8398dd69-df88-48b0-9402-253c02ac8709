package inks.system.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PiweblnkPojo;
import inks.system.service.PiweblnkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 快捷方式(PiWebLnk)表控制层
 *
 * <AUTHOR>
 * @since 2023-05-08 14:11:24
 */
@RestController
@RequestMapping("SYSM05B3")
@Api(tags = "SYSM05B3:快捷方式导航")
public class SYSM05B3Controller extends PiweblnkController {
    /**
     * 服务对象
     */
    @Resource
    private PiweblnkService piweblnkService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 分页查询
     *
     * @param key 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "项目全菜单", notes = "项目全菜单", produces = "application/json")
    @RequestMapping(value = "/getAllListByPid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiWebLnk.List")
    public R<List<PiweblnkPojo>> getAllListByPid(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<PiweblnkPojo> lst = this.piweblnkService.getListByPid(key);
            List<PiweblnkPojo> list = new ArrayList<>();
            for (PiweblnkPojo piweblnkPojo : lst) {
                PiweblnkPojo webMenuPojo = new PiweblnkPojo();
                BeanUtils.copyProperties(piweblnkPojo, webMenuPojo);
                List<PiweblnkPojo> lst2 = this.piweblnkService.getListByPid(piweblnkPojo.getNavid());
                for (PiweblnkPojo pojo : lst2) {
                    PiweblnkPojo webMenuPojo2 = new PiweblnkPojo();
                    BeanUtils.copyProperties(pojo, webMenuPojo2);
                    List<PiweblnkPojo> lst3 = this.piweblnkService.getListByPid(pojo.getNavid());
                    for (PiweblnkPojo value : lst3) {
                        PiweblnkPojo webMenuPojo3 = new PiweblnkPojo();
                        BeanUtils.copyProperties(value, webMenuPojo3);
                        list.add(webMenuPojo3);
                    }
                    list.add(webMenuPojo2);
                }
                list.add(webMenuPojo);
            }
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
