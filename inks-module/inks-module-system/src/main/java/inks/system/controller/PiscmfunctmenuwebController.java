package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PiscmfunctmenuwebPojo;
import inks.system.service.PiscmfunctmenuwebService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * SCM菜单关系(PiScmFunctMenuWeb)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:08
 */
@RestController
@RequestMapping("piscmfunctmenuweb")
public class PiscmfunctmenuwebController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(PiscmfunctmenuwebController.class);
    /**
     * 服务对象
     */
    @Resource
    private PiscmfunctmenuwebService piscmfunctmenuwebService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取SCM菜单关系详细信息", notes = "获取SCM菜单关系详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiScmFunctMenuWeb.List")
    public R<PiscmfunctmenuwebPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.piscmfunctmenuwebService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiScmFunctMenuWeb.List")
    public R<PageInfo<PiscmfunctmenuwebPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiScmFunctMenuWeb.CreateDate");

            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.piscmfunctmenuwebService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增SCM菜单关系", notes = "新增SCM菜单关系", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiScmFunctMenuWeb.Add")
    public R<PiscmfunctmenuwebPojo> create(@RequestBody String json) {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            PiscmfunctmenuwebPojo piscmfunctmenuwebPojo = JSONArray.parseObject(json, PiscmfunctmenuwebPojo.class);

            piscmfunctmenuwebPojo.setCreateby(loginUser.getRealName());   // 创建者
            piscmfunctmenuwebPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            piscmfunctmenuwebPojo.setCreatedate(new Date());   // 创建时间
            piscmfunctmenuwebPojo.setLister(loginUser.getRealname());   // 制表
            piscmfunctmenuwebPojo.setListerid(loginUser.getUserid());    // 制表id  
            piscmfunctmenuwebPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.piscmfunctmenuwebService.insert(piscmfunctmenuwebPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改SCM菜单关系", notes = "修改SCM菜单关系", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiScmFunctMenuWeb.Edit")
    public R<PiscmfunctmenuwebPojo> update(@RequestBody String json) {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            PiscmfunctmenuwebPojo piscmfunctmenuwebPojo = JSONArray.parseObject(json, PiscmfunctmenuwebPojo.class);

            piscmfunctmenuwebPojo.setLister(loginUser.getRealname());   // 制表
            piscmfunctmenuwebPojo.setListerid(loginUser.getUserid());    // 制表id  
            piscmfunctmenuwebPojo.setModifydate(new Date());   //修改时间
//            piscmfunctmenuwebPojo.setAssessor(""); // 审核员
//            piscmfunctmenuwebPojo.setAssessorid(""); // 审核员id
//            piscmfunctmenuwebPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.piscmfunctmenuwebService.update(piscmfunctmenuwebPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除SCM菜单关系", notes = "删除SCM菜单关系", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiScmFunctMenuWeb.Delete")
    public R<Integer> delete(String key) {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.piscmfunctmenuwebService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiScmFunctMenuWeb.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        PiscmfunctmenuwebPojo piscmfunctmenuwebPojo = this.piscmfunctmenuwebService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(piscmfunctmenuwebPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

