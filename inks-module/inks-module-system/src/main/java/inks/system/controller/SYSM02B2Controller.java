package inks.system.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiconfigPojo;
import inks.system.domain.pojo.CireportsPojo;
import inks.system.service.CifnorderService;
import inks.system.service.PisubscriberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 订阅信息表(PiSubscriber)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-10 14:17:23
 */
@RestController
@RequestMapping("SYSM02B2")
@Api(tags = "SYSM02B2:订阅信息表")
public class SYSM02B2Controller extends PisubscriberController {

    /**
     * 服务对象
     */
    @Resource
    private PisubscriberService pisubscriberService;

    /**
     * 服务对象
     */
    @Resource
    private CifnorderService cifnorderService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;


    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = "根据租户获取权限表List", notes = "根据租户获取权限表List", produces = "application/json")
    @RequestMapping(value = "/getConfigListBySelf", method = RequestMethod.GET)
    public R<List<CiconfigPojo>> getConfigListBySelf() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            if (loginUser.getIsadmin() == 1) {
                List<CiconfigPojo> lst = this.pisubscriberService.getTenConfigByTenant(loginUser.getTenantid());
                return R.ok(lst);
            } else {
                return R.fail(403, "权限不足");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }

    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = "根据租户获取默认报表模板List", notes = "根据租户获取默认报表模板List", produces = "application/json")
    @RequestMapping(value = "/getReportsListBySelf", method = RequestMethod.GET)
    public R<List<CireportsPojo>> getReportsListBySelf() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            if (loginUser.getIsadmin() == 1) {
                List<CireportsPojo> lst = this.pisubscriberService.getReportsByTenant(loginUser.getTenantid());
                return R.ok(lst);
            } else {
                return R.fail(403, "权限不足");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }

    }
}
