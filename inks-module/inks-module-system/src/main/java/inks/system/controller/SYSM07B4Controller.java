package inks.system.controller;


import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiwarninguserPojo;
import inks.system.service.CiwarninguserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 预警项目(CiWarning)表控制层
 *
 * <AUTHOR>
 * @since 2022-07-05 20:11:00
 */
@RestController
@RequestMapping("SYSM07B4")
@Api(tags = "SYSM07B4:预警管理")
public class SYSM07B4Controller extends CiwarningController {
    @Resource
    private CiwarninguserService ciwarninguserService;
    @Resource
    private TokenService tokenService;

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增用户预警", notes = "新增用户预警", produces = "application/json")
    @RequestMapping(value = "/createUser", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiWarningUser.Add")
    public R<CiwarninguserPojo> createUser(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            CiwarninguserPojo ciwarninguserPojo = JSONArray.parseObject(json, CiwarninguserPojo.class);

            ciwarninguserPojo.setCreateby(loginUser.getRealName());   // 创建者
            ciwarninguserPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            ciwarninguserPojo.setCreatedate(new Date());   // 创建时间
            ciwarninguserPojo.setLister(loginUser.getRealname());   // 制表
            ciwarninguserPojo.setListerid(loginUser.getUserid());    // 制表id
            ciwarninguserPojo.setModifydate(new Date());   //修改时间
            ciwarninguserPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.ciwarninguserService.insert(ciwarninguserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改用户预警", notes = "修改用户预警", produces = "application/json")
    @RequestMapping(value = "/updateUser", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiWarningUser.Edit")
    public R<CiwarninguserPojo> updateUser(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            CiwarninguserPojo ciwarninguserPojo = JSONArray.parseObject(json, CiwarninguserPojo.class);

            ciwarninguserPojo.setLister(loginUser.getRealname());   // 制表
            ciwarninguserPojo.setListerid(loginUser.getUserid());    // 制表id
            ciwarninguserPojo.setTenantid(loginUser.getTenantid());   //租户id
            ciwarninguserPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.ciwarninguserService.update(ciwarninguserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除用户预警", notes = "删除用户预警", produces = "application/json")
    @RequestMapping(value = "/deleteUser", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiWarningUser.Delete")
    public R<Integer> deleteUser(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.ciwarninguserService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "读取当前用户预警列表", notes = "读取当前用户预警列表", produces = "application/json")
    @RequestMapping(value = "/getListByUser", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiWarningUser.List")
    public R<List<CiwarninguserPojo>> getListByUser() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.ciwarninguserService.getListByUser(loginUser.getUserid(), loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "读取当前用户预警数据", notes = "读取当前用户预警数据", produces = "application/json")
    @RequestMapping(value = "/getWarnListByUser", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiWarningUser.List")
    public R<List<CiwarninguserPojo>> getWarnListByUser() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.ciwarninguserService.getWarnListByUser(loginUser.getUserid(), loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
