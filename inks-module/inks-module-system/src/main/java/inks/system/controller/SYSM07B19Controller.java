package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CibilldraftPojo;
import inks.system.service.CibilldraftService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 单据草稿(CiBillDraft)表控制层
 *
 * <AUTHOR>
 * @since 2024-10-26 10:17:53
 */
@RestController
@RequestMapping("SYSM07B19")
@Api(tags = "SYSM07B19:单据草稿")
public class SYSM07B19Controller extends CibilldraftController {
    @Resource
    private CibilldraftService cibilldraftService;
    @Resource
    private TokenService tokenService;


    @ApiOperation(value = " 入参：{ModuleCode,DraftTitle, BillData}  用 ModuleCode+listerid+tid，查询，如有执行update,如无执行 create", notes = "", produces = "application/json")
    @RequestMapping(value = "/saveDraft", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiBillDraft.Add")
    public R<CibilldraftPojo> saveDraft(@RequestBody String json) {
        try {
            // 入参：{ModuleCode,DraftTitle, BillData}
            CibilldraftPojo cibilldraftPojo = JSONArray.parseObject(json, CibilldraftPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();
            // 检查是否已有草稿
            CibilldraftPojo billDraftDB = this.cibilldraftService.getEntityByModuleCodeAndListerId(cibilldraftPojo.getModulecode(), loginUser.getUserid(), tid);
            cibilldraftPojo.setLister(loginUser.getRealname());   // 制表
            cibilldraftPojo.setListerid(loginUser.getUserid());    // 制表id
            cibilldraftPojo.setModifydate(new Date());   //修改时间
            cibilldraftPojo.setTenantid(tid);   //租户id
            if (billDraftDB != null) {
                cibilldraftPojo.setId(billDraftDB.getId());
                return R.ok(this.cibilldraftService.update(cibilldraftPojo));
            } else {
                cibilldraftPojo.setCreateby(loginUser.getRealName());   // 创建者
                cibilldraftPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
                cibilldraftPojo.setCreatedate(new Date());   // 创建时间
                return R.ok(this.cibilldraftService.insert(cibilldraftPojo));
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "根据ModuleCode或id获取单据草稿数据", notes = "", produces = "application/json")
    @RequestMapping(value = "/getBillData", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiBillDraft.List")
    public R<String> getBillData(@RequestParam(required = false) String code,
                                 @RequestParam(required = false) String key) {
        try {
            // 获取当前登录用户
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();

            // 根据key或code获取草稿数据
            CibilldraftPojo billDataDB = (StringUtils.isNotBlank(key))
                    ? cibilldraftService.getEntity(key, tid) // 使用id
                    : cibilldraftService.getEntityByModuleCodeAndListerId(code, loginUser.getUserid(), tid); // 使用ModuleCode

            // 返回结果
            return R.ok(billDataDB != null ? billDataDB.getBilldata() : null);

        } catch (Exception e) {
            return R.fail("获取单据草稿数据失败: " + e.getMessage());
        }
    }


    @ApiOperation(value = "获取listerid(userid)下的草稿数量", notes = "", produces = "application/json")
    @RequestMapping(value = "/getDraftCount", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiBillDraft.List")
    public R<Integer> getDraftCount() {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            Integer draftCount = this.cibilldraftService.getDraftCountByListerId(loginUser.getUserid(), loginUser.getTenantid());
            return R.ok(draftCount);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取草稿列表 返回功能编码为D01M04B1的数据，如果没有传，就返回全部草稿", notes = "获取该ModuleCode下的草稿列表", produces = "application/json")
    @RequestMapping(value = "/getDraftList", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiBillDraft.List")
    //加一个参数code='D01M04B1',返回功能编码为D01M04B1的数据，如果没有传，就返回全部草稿
    public R<List<CibilldraftPojo>> getDraftList(@RequestParam(required = false) String code) {
        try {
            // 检查listerid是否为userid
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<CibilldraftPojo> draftList = this.cibilldraftService.getDraftList(loginUser.getUserid(), code, loginUser.getTenantid());
            return R.ok(draftList);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
