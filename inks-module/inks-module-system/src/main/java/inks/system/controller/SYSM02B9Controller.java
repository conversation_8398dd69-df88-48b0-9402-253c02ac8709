package inks.system.controller;

import inks.common.core.domain.R;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PifunctionconfigPojo;
import inks.system.service.PifunctionconfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 服务参数关系(PiFunctionConfig)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-25 11:20:10
 */
@RestController
@RequestMapping("SYSM02B9")
@Api(tags = "SYSM02B9:服务参数关系表")
public class SYSM02B9Controller extends PifunctionconfigController {

    /**
     * 服务对象
     */
    @Resource
    private PifunctionconfigService pifunctionconfigService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据服务号获取系统参数关系List", notes = "根据服务号获取系统参数关系List", produces = "application/json")
    @RequestMapping(value = "/getListByFunction", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionMenuApp.List")
    public R<List<PifunctionconfigPojo>> getListByFunction(String key) {
        try {
            return R.ok(this.pifunctionconfigService.getListByFunction(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
