package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PimenufrmPojo;
import inks.system.service.PimenufrmService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * Frm导航(PiMenuFrm)表控制层
 *
 * <AUTHOR>
 * @since 2024-05-05 16:28:24
 */
//@RestController
//@RequestMapping("pimenufrm")
public class PimenufrmController {

    private final static Logger logger = LoggerFactory.getLogger(PimenufrmController.class);
    @Resource
    private PimenufrmService pimenufrmService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = " 获取Frm导航详细信息", notes = "获取Frm导航详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiMenuFrm.List")
    public R<PimenufrmPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pimenufrmService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiMenuFrm.List")
    public R<PageInfo<PimenufrmPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiMenuFrm.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.pimenufrmService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增Frm导航", notes = "新增Frm导航", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiMenuFrm.Add")
    public R<PimenufrmPojo> create(@RequestBody String json) {
        try {
            PimenufrmPojo pimenufrmPojo = JSONArray.parseObject(json, PimenufrmPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pimenufrmPojo.setCreateby(loginUser.getRealName());   // 创建者
            pimenufrmPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            pimenufrmPojo.setCreatedate(new Date());   // 创建时间
            pimenufrmPojo.setLister(loginUser.getRealname());   // 制表
            pimenufrmPojo.setListerid(loginUser.getUserid());    // 制表id  
            pimenufrmPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.pimenufrmService.insert(pimenufrmPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改Frm导航", notes = "修改Frm导航", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiMenuFrm.Edit")
    public R<PimenufrmPojo> update(@RequestBody String json) {
        try {
            PimenufrmPojo pimenufrmPojo = JSONArray.parseObject(json, PimenufrmPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pimenufrmPojo.setLister(loginUser.getRealname());   // 制表
            pimenufrmPojo.setListerid(loginUser.getUserid());    // 制表id  
            pimenufrmPojo.setModifydate(new Date());   //修改时间
//            pimenufrmPojo.setAssessor(""); // 审核员
//            pimenufrmPojo.setAssessorid(""); // 审核员id
//            pimenufrmPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.pimenufrmService.update(pimenufrmPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除Frm导航", notes = "删除Frm导航", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiMenuFrm.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pimenufrmService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiMenuFrm.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        PimenufrmPojo pimenufrmPojo = this.pimenufrmService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(pimenufrmPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

