//package inks.system.controller;
//
//import inks.common.core.domain.LoginUser;
//import inks.common.core.domain.ReportsPojo;
//import inks.common.core.utils.ServletUtils;
//import inks.common.core.utils.bean.BeanUtils;
//import inks.common.core.exception.BaseBusinessException;
//import inks.common.security.annotation.PreAuthorize;
//import inks.common.security.service.TokenService;
//import inks.common.redis.service.RedisService;
//import inks.common.core.domain.R;
//import inks.common.core.domain.QueryParam;
//import inks.system.domain.pojo.PifaceinfoPojo;
//import inks.system.service.PifaceinfoService;
//import com.alibaba.fastjson.JSONArray;
//import com.github.pagehelper.PageInfo;
//import net.sf.jasperreports.engine.*;
//import org.springframework.web.bind.annotation.*;
//import io.swagger.annotations.ApiOperation;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import javax.annotation.Resource;
//import javax.servlet.ServletOutputStream;
//import javax.servlet.http.HttpServletResponse;
//import java.io.ByteArrayInputStream;
//import java.io.IOException;
//import java.io.InputStream;
//import java.util.Date;
//import java.util.Map;
/// **
// * 人脸信息表(PiFaceInfo)表控制层
// *
// * <AUTHOR>
// * @since 2024-11-19 17:02:02
// */
/// /@RestController
/// /@RequestMapping("pifaceinfo")
//public class PifaceinfoController {
//
//    @Resource
//    private PifaceinfoService pifaceinfoService;
//    @Resource
//    private RedisService redisService;
//    @Resource
//    private TokenService tokenService;
//
//    private final static Logger logger = LoggerFactory.getLogger(PifaceinfoController.class);
//
//
//    @ApiOperation(value=" 获取人脸信息表详细信息", notes="获取人脸信息表详细信息", produces="application/json")
//    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
//    @PreAuthorize(hasPermi = "PiFaceInfo.List")
//    public R<PifaceinfoPojo> getEntity(String key) {
//    try {
//
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            return R.ok(this.pifaceinfoService.getEntity(key, loginUser.getTenantid()));
//         }catch (Exception e){
//            return R.fail(e.getMessage());
//        }
//    }
//
//
//    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
//    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
//    @PreAuthorize(hasPermi = "PiFaceInfo.List")
//    public R<PageInfo<PifaceinfoPojo>> getPageList(@RequestBody String json) {
//        try {
//            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
//             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("PiFaceInfo.CreateDate");
//
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            queryParam.setTenantid(loginUser.getTenantid());
//            String qpfilter = "";
//            qpfilter += queryParam.getAllFilter();
//            queryParam.setFilterstr(qpfilter);
//            return R.ok(this.pifaceinfoService.getPageList(queryParam));
//        }catch (Exception e){
//            return R.fail(e.getMessage());
//        }
//    }
//
//
//
//    @ApiOperation(value=" 新增人脸信息表", notes="新增人脸信息表", produces="application/json")
//    //@RequestMapping(value="/create",method= RequestMethod.POST)
//    @PreAuthorize(hasPermi = "PiFaceInfo.Add")
//    public R<PifaceinfoPojo> create(@RequestBody String json) {
//       try {
//       PifaceinfoPojo pifaceinfoPojo = JSONArray.parseObject(json,PifaceinfoPojo.class);
//
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            pifaceinfoPojo.setCreateby(loginUser.getRealName());   // 创建者
//            pifaceinfoPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
//            pifaceinfoPojo.setCreatedate(new Date());   // 创建时间
//            pifaceinfoPojo.setLister(loginUser.getRealname());   // 制表
//            pifaceinfoPojo.setListerid(loginUser.getUserid());    // 制表id
//            pifaceinfoPojo.setModifydate(new Date());   //修改时间
//            pifaceinfoPojo.setTenantid(loginUser.getTenantid());   //租户id
//        return R.ok(this.pifaceinfoService.insert(pifaceinfoPojo));
//    }catch (Exception e){
//            return R.fail(e.getMessage());
//        }
//   }
//
//
//    @ApiOperation(value="修改人脸信息表", notes="修改人脸信息表", produces="application/json")
//    @RequestMapping(value="/update",method= RequestMethod.POST)
//    @PreAuthorize(hasPermi = "PiFaceInfo.Edit")
//    public R<PifaceinfoPojo> update(@RequestBody String json) {
//       try {
//         PifaceinfoPojo pifaceinfoPojo = JSONArray.parseObject(json,PifaceinfoPojo.class);
//
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            pifaceinfoPojo.setLister(loginUser.getRealname());   // 制表
//            pifaceinfoPojo.setListerid(loginUser.getUserid());    // 制表id
//            pifaceinfoPojo.setTenantid(loginUser.getTenantid());   //租户id
//            pifaceinfoPojo.setModifydate(new Date());   //修改时间
/// /            pifaceinfoPojo.setAssessor(""); // 审核员
/// /            pifaceinfoPojo.setAssessorid(""); // 审核员id
/// /            pifaceinfoPojo.setAssessdate(new Date()); //审核时间
//        return R.ok(this.pifaceinfoService.update(pifaceinfoPojo));
//    }catch (Exception e){
//            return R.fail(e.getMessage());
//        }
//    }
//
//
//    @ApiOperation(value="删除人脸信息表", notes="删除人脸信息表", produces="application/json")
//    @RequestMapping(value="/delete",method= RequestMethod.GET)
//    @PreAuthorize(hasPermi = "PiFaceInfo.Delete")
//    public R<Integer> delete(String key) {
//    try {
//
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            return R.ok(this.pifaceinfoService.delete(key, loginUser.getTenantid()));
//     }catch (Exception e){
//            return R.fail(e.getMessage());
//        }
//    }
//
//    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
//    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "PiFaceInfo.Print")
//    public void printBill(String key, String ptid) throws IOException, JRException {
//
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//        //获取单据信息
//        PifaceinfoPojo pifaceinfoPojo = this.pifaceinfoService.getEntity(key,loginUser.getTenantid());
//        //表头转MAP
//        Map<String, Object> map = BeanUtils.beanToMap(pifaceinfoPojo);
//                // 加入公司信息
//        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
//      //从redis中获取Reprot内容
//     ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
//     String content ;
//     if (reportsPojo != null ) {
//         content = reportsPojo.getRptdata();
//     } else {
//         throw new BaseBusinessException("未找到报表");
//     }
//        //报表生成
//        InputStream stream = new ByteArrayInputStream(content.getBytes());
//        HttpServletResponse response= ServletUtils.getResponse();
//        ServletOutputStream os = response.getOutputStream();
//        try {
//            //编译报表
//            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
//            //数据填充
//            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
//            //打印PDF数据流
//            JasperExportManager.exportReportToPdfStream(print, os);
//        } catch (JRException e) {
//            e.printStackTrace();
//        } catch (BaseBusinessException base) {
//            base.getMessage();
//        } finally {
//            os.flush();
//        }
//    }
//}
//
