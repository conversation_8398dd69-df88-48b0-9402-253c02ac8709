package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PiscmfunctmenuappPojo;
import inks.system.service.PiscmfunctmenuappService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * SCMAPP关系(PiScmFunctMenuApp)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@RestController
@RequestMapping("piscmfunctmenuapp")
public class PiscmfunctmenuappController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(PiscmfunctmenuappController.class);
    /**
     * 服务对象
     */
    @Resource
    private PiscmfunctmenuappService piscmfunctmenuappService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取SCMAPP关系详细信息", notes = "获取SCMAPP关系详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiScmFunctMenuApp.List")
    public R<PiscmfunctmenuappPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.piscmfunctmenuappService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiScmFunctMenuApp.List")
    public R<PageInfo<PiscmfunctmenuappPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiScmFunctMenuApp.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.piscmfunctmenuappService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增SCMAPP关系", notes = "新增SCMAPP关系", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiScmFunctMenuApp.Add")
    public R<PiscmfunctmenuappPojo> create(@RequestBody String json) {
        try {
            PiscmfunctmenuappPojo piscmfunctmenuappPojo = JSONArray.parseObject(json, PiscmfunctmenuappPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            piscmfunctmenuappPojo.setCreateby(loginUser.getRealName());   // 创建者
            piscmfunctmenuappPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            piscmfunctmenuappPojo.setCreatedate(new Date());   // 创建时间
            piscmfunctmenuappPojo.setLister(loginUser.getRealname());   // 制表
            piscmfunctmenuappPojo.setListerid(loginUser.getUserid());    // 制表id  
            piscmfunctmenuappPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.piscmfunctmenuappService.insert(piscmfunctmenuappPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改SCMAPP关系", notes = "修改SCMAPP关系", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiScmFunctMenuApp.Edit")
    public R<PiscmfunctmenuappPojo> update(@RequestBody String json) {
        try {
            PiscmfunctmenuappPojo piscmfunctmenuappPojo = JSONArray.parseObject(json, PiscmfunctmenuappPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            piscmfunctmenuappPojo.setLister(loginUser.getRealname());   // 制表
            piscmfunctmenuappPojo.setListerid(loginUser.getUserid());    // 制表id  
            piscmfunctmenuappPojo.setModifydate(new Date());   //修改时间
//            piscmfunctmenuappPojo.setAssessor(""); // 审核员
//            piscmfunctmenuappPojo.setAssessorid(""); // 审核员id
//            piscmfunctmenuappPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.piscmfunctmenuappService.update(piscmfunctmenuappPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除SCMAPP关系", notes = "删除SCMAPP关系", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiScmFunctMenuApp.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.piscmfunctmenuappService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiScmFunctMenuApp.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        PiscmfunctmenuappPojo piscmfunctmenuappPojo = this.piscmfunctmenuappService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(piscmfunctmenuappPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

