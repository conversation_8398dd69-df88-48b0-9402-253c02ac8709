package inks.system.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.CibillcodeEntity;
import inks.system.service.CibillcodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 单据编码(CiBillCode)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 13:27:43
 */
@RestController
@RequestMapping("SYSM07B2")
@Api(tags = "SYSM07B2:单据编码")
public class SYSM07B2Controller extends CibillcodeController {

    @Resource
    private CibillcodeService cibillcodeService;

    @Resource
    private TokenService tokenService;

    @ApiOperation(value = " 获取单据编码", notes = "获取单据", produces = "application/json")
    @RequestMapping(value = "/getBillCode", method = RequestMethod.GET)
    public R<String> getBillCode(String code, @RequestParam(required = false) String tablename, @RequestParam(required = false) String prefix) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cibillcodeService.getSerialNo(code, tablename, prefix, loginUser.getTenantid()));
        } catch (BaseBusinessException e) {
            return R.fail(e.getMessage());
        } catch (Exception e) {
            return R.fail("系统错误：" + e.getMessage());
        }
    }

    @ApiOperation(value = " 批量获取单据编码", notes = "获取单据", produces = "application/json")
    @RequestMapping(value = "/getBillCodeList", method = RequestMethod.GET)
    public R<List<String>> getBillCodeList(String code, Integer num) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cibillcodeService.getSerialNoList(code, num, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "初始化RefNo(RefNo末尾的数字部分覆盖存入Redis,30分钟有效)", notes = "", produces = "application/json")
    @RequestMapping(value = "/initSerialNo", method = RequestMethod.GET)
    public R<String> initSerialNo(String modulecode, String refno) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cibillcodeService.initSerialNo(modulecode, refno, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 拿到Entity用于修改；如果没有时先用于Def的复制一份到tid，再读出", notes = "获取单据编码详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByCode", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiBillCode.List")
    public R<CibillcodeEntity> getEntityByCode(String modulecode) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cibillcodeService.getEntityByCode(modulecode, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
