package inks.system.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.POIUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CixlsinputPojo;
import inks.system.domain.pojo.CixlsinputtitlePojo;
import inks.system.service.CixlsinputService;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * xls导入格式(CiXlsInput)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-29 10:40:17
 */
public class CixlsinputController {
    /**
     * 服务对象
     */
    @Resource
    private CixlsinputService cixlsinputService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取xls导入格式详细信息", notes = "获取xls导入格式详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiXlsInput.List")
    public R<CixlsinputPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cixlsinputService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiXlsInput.List")
    public R<PageInfo<CixlsinputPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiXlsInput.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.cixlsinputService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增xls导入格式", notes = "新增xls导入格式", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiXlsInput.Add")
    public R<CixlsinputPojo> create(@RequestBody String json) {
        try {
            CixlsinputPojo cixlsinputPojo = JSONArray.parseObject(json, CixlsinputPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            cixlsinputPojo.setCreateby(loginUser.getRealname());   // 创建者
            cixlsinputPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            cixlsinputPojo.setCreatedate(new Date());   // 创建时间
            cixlsinputPojo.setLister(loginUser.getRealname());   // 制表
            cixlsinputPojo.setListerid(loginUser.getUserid());    // 制表id  
            cixlsinputPojo.setModifydate(new Date());   //修改时间
            cixlsinputPojo.setTenantid(loginUser.getTenantid());   //租户id
            cixlsinputPojo.setTenantname(loginUser.getTenantinfo().getTenantname());   //租户名称
            return R.ok(this.cixlsinputService.insert(cixlsinputPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改xls导入格式", notes = "修改xls导入格式", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiXlsInput.Edit")
    public R<CixlsinputPojo> update(@RequestBody String json) {
        try {
            CixlsinputPojo cixlsinputPojo = JSONArray.parseObject(json, CixlsinputPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            cixlsinputPojo.setLister(loginUser.getRealname());   // 制表
            cixlsinputPojo.setListerid(loginUser.getUserid());    // 制表id  
            cixlsinputPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.cixlsinputService.update(cixlsinputPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除xls导入格式", notes = "删除xls导入格式", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiXlsInput.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cixlsinputService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 按模块编码查询报表
     *
     * @param code 主键
     * @return 查询结果
     */
    @ApiOperation(value = "按模块编码查询样式", notes = "按模块编码查询样式", produces = "application/json")
    @RequestMapping(value = "/getListByModuleCode", method = RequestMethod.GET)
    public R<List<CixlsinputPojo>> getListByModuleCode(String code) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<CixlsinputPojo> list = this.cixlsinputService.getListByModuleCode(code, loginUser.getTenantid());
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "导出数据模版", notes = "导出数据模版", produces = "application/json")
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    public void exportList(@RequestBody String json, HttpServletRequest request, HttpServletResponse response) {
        try {
            // 将json转为类
            CixlsinputPojo cixlsinputPojo = JSONArray.parseObject(json, CixlsinputPojo.class);

            //创建表头
            List<ExcelExportEntity> beanList = new ArrayList<ExcelExportEntity>();
            //表头名json"[{'title':'编码'},{'title':'名称'},{'title':'规格'},{'title':'货品单位'},{'title':'货品状态'}]";
            String titlejson = cixlsinputPojo.getTitlejson();
            //将JSON转换成list对象
            List<CixlsinputtitlePojo> l = JSONObject.parseArray(titlejson, CixlsinputtitlePojo.class);
            for (int i = 0; i < l.size(); i++) {
                //自定义表头名
                beanList.add(new ExcelExportEntity(l.get(i).getTitle()));
            }
            //创建一个空对象传入生成一张空excle表
            List<Object> list = new ArrayList();
            //创建表格
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("", ""),
                    beanList, list);
            try {
                //下载模板
                POIUtil.downloadWorkbook(workbook, request, response, cixlsinputPojo.getFilename());
            } catch (IOException e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}

