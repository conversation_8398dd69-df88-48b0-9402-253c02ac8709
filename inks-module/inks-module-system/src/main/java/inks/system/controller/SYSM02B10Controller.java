package inks.system.controller;

import inks.common.core.domain.R;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PifunctionwarnPojo;
import inks.system.service.PifunctionwarnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 服务预警关系(PiFunctionWarn)表控制层
 *
 * <AUTHOR>
 * @since 2022-07-05 20:11:43
 */
@RestController
@RequestMapping("SYSM02B10")
@Api(tags = "SYSM02B10:服务预警关系")
public class SYSM02B10Controller extends PifunctionwarnController {

    /**
     * 服务对象
     */
    @Resource
    private PifunctionwarnService pifunctionwarnService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据服务号获取服务预警关系List", notes = "根据服务号获取服务预警关系List", produces = "application/json")
    @RequestMapping(value = "/getListByFunction", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionWarn.List")
    public R<List<PifunctionwarnPojo>> getListByFunction(String key) {
        try {
            return R.ok(this.pifunctionwarnService.getListByFunction(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
