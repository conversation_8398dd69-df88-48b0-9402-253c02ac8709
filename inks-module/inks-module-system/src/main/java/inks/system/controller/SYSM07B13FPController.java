package inks.system.controller;

import inks.common.core.constant.InksConstants;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiformpartPojo;
import inks.system.mapper.CiformpartMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 表单组件(CiFormPart)表控制层
 *
 * <AUTHOR>
 * @since 2024-01-19 13:41:52
 */
@RestController
@RequestMapping("SYSM07B13FP")
@Api(tags = "SYSM07B13FP:表单组件")
public class SYSM07B13FPController extends CiformpartController {
    @Resource
    private CiformpartMapper ciformpartMapper;
    @Resource
    private TokenService tokenService;


    //这个只有default的
    //没有当前租户的
    //这个是系统级的
    @ApiOperation(value = "getPartList?code=d01m03b1&type=item (order by CiFormPart.RowNum asc)", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPartList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiFormPart.List")
    public R<List<CiformpartPojo>> getPartList(String code, String type) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.ciformpartMapper.getPartList(code, type, InksConstants.DEFAULT_TENANT));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
