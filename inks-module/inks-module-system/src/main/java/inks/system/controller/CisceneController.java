package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiscenePojo;
import inks.system.service.CisceneService;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 场景管理(CiScene)表控制层
 *
 * <AUTHOR>
 * @since 2022-11-24 11:34:51
 */
public class CisceneController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(CisceneController.class);
    /**
     * 服务对象
     */
    @Resource
    private CisceneService cisceneService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取场景管理详细信息", notes = "获取场景管理详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    public R<CiscenePojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.cisceneService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    public R<PageInfo<CiscenePojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiScene.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.cisceneService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增场景管理", notes = "新增场景管理", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public R<CiscenePojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            CiscenePojo ciscenePojo = JSONArray.parseObject(json, CiscenePojo.class);

            ciscenePojo.setCreateby(loginUser.getRealName());   // 创建者
            ciscenePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            ciscenePojo.setCreatedate(new Date());   // 创建时间
            ciscenePojo.setLister(loginUser.getRealname());   // 制表
            ciscenePojo.setListerid(loginUser.getUserid());    // 制表id  
            ciscenePojo.setModifydate(new Date());   //修改时间
            ciscenePojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.cisceneService.insert(ciscenePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改场景管理", notes = "修改场景管理", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<CiscenePojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            CiscenePojo ciscenePojo = JSONArray.parseObject(json, CiscenePojo.class);

            ciscenePojo.setLister(loginUser.getRealname());   // 制表
            ciscenePojo.setListerid(loginUser.getUserid());    // 制表id  
            ciscenePojo.setTenantid(loginUser.getTenantid());   //租户id
            ciscenePojo.setModifydate(new Date());   //修改时间
            return R.ok(this.cisceneService.update(ciscenePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除场景管理", notes = "删除场景管理", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.cisceneService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

