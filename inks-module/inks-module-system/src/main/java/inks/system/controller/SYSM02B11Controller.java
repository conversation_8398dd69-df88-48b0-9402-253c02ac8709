package inks.system.controller;

import inks.common.core.domain.R;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PifunctiondashPojo;
import inks.system.service.PifunctiondashService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 服务工作台关系(PiFunctionWarn)表控制层
 *
 * <AUTHOR>
 * @since 2022-07-05 20:11:43
 */
@RestController
@RequestMapping("SYSM02B11")
@Api(tags = "SYSM02B11:服务工作台关系")
public class SYSM02B11Controller extends PifunctiondashController {
    @Resource
    private PifunctiondashService pifunctiondashService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据服务号获取服务工作台关系List", notes = "根据服务号获取服务工作台关系List", produces = "application/json")
    @RequestMapping(value = "/getListByFunction", method = RequestMethod.GET)
    public R<List<PifunctiondashPojo>> getListByFunction(String key) {
        return R.ok(this.pifunctiondashService.getListByFunction(key));
    }
}
