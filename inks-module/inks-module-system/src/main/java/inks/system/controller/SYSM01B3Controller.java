package inks.system.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.service.CibillcodeService;
import inks.system.service.CireportsService;
import inks.system.service.PitenantService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 租户表(PiTenant)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 14:29:17
 */
@RestController
@RequestMapping("SYSM01B3")
@Api(tags = "SYSM01B3:租户表")
public class SYSM01B3Controller extends PitenantController {
    @Resource
    private PitenantService pitenantService;
    @Resource
    private CibillcodeService cibillcodeService;
    @Resource
    private CireportsService cireportsService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "复制一份default的单据编码，tid改为传入的tid", notes = "", produces = "application/json")
    @RequestMapping(value = "/copyBillCode", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiTenant.List")
    public R<String> copyBillCode(String tid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            int count = cibillcodeService.copyBillCode(tid, loginUser);
            return R.ok("复制成功，共复制" + count + "条单据编码数据");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "复制一份default的报表中心，tid改为传入的tid", notes = "", produces = "application/json")
    @RequestMapping(value = "/copyReports", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiTenant.List")
    public R<String> copyReports(String tid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            int count = cireportsService.copyReports(tid, loginUser);
            return R.ok("复制成功，共复制" + count + "条报表中心数据");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //接口：getCountRecord?fn=oms&tid=xxx
    //如果tid空时，比logininfo中取；
    //fn定义要查哪几个表格；
    //比如：oms
    //
    //tablename_comment,   tablename，    count,  firstdate,   lastdate;
    //销售订单  ，        Bus_Machining，   1200， 2023-1-1， 2024-11-25
    @ApiOperation(value = "获取各单据数量，使用时间段", notes = "根据tid和fn查询表格数据统计", produces = "application/json")
    @RequestMapping(value = "/getCountRecord", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiTenant.List")
    public R<List<Map<String, Object>>> omsgetCountRecord(
            @RequestParam(required = false) String fn, // 查询系统 系统关联多个表名 Bus_Machining,Buy_Order...
            @RequestParam(required = false) String tid) {
        try {
            // 如果tid为空，使用登录用户的租户ID
            if (StringUtils.isBlank(tid)) {
                LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
                tid = loginUser.getTenantid();
            }
            // 查询表格统计数据
            List<Map<String, Object>> result = pitenantService.getCountRecord(fn, tid);

            return R.ok(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
