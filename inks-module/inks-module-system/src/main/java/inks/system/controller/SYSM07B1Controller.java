package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.constant.InksConstants;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CidictPojo;
import inks.system.service.CidictService;
import inks.system.service.CidictitemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 数据字典(Cidict)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 13:26:57
 */
@RestController
@RequestMapping("SYSM07B1")
@Api(tags = "SYSM07B1:数据字典")
public class SYSM07B1Controller extends CidictController {

    /**
     * 服务对象
     */
    @Resource
    private CidictService cidictService;

    /**
     * 服务对象Item
     */
    @Resource
    private CidictitemService cidictitemService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取数据字典详细信息", notes = "获取数据字典详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByDictCode", method = RequestMethod.GET)
    public R<CidictPojo> getBillEntityByDictCode(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            CidictPojo cidictPojo = this.cidictService.getBillEntityByDictCode(key, loginUser.getTenantid());
            if (cidictPojo == null) {
                cidictPojo = new CidictPojo();
                cidictPojo.setDictcode(key);
                cidictPojo.setEnabledmark(1);
                cidictPojo.setCreateby(loginUser.getRealName());   // 创建者
                cidictPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
                cidictPojo.setCreatedate(new Date());   // 创建时间
                cidictPojo.setLister(loginUser.getRealname());   // 制表
                cidictPojo.setListerid(loginUser.getUserid());    // 制表id
                cidictPojo.setModifydate(new Date());   //修改时间
                cidictPojo.setTenantid(loginUser.getTenantid());   //租户id
                if (loginUser.getTenantinfo() != null && loginUser.getTenantinfo().getTenantname() != null) {
                    cidictPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
                }
                cidictPojo = this.cidictService.insert(cidictPojo);
            }
            return R.ok(cidictPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改数据字典", notes = "修改数据字典", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiDict.Edit")
    public R<CidictPojo> update(@RequestBody String json) {
        try {
            CidictPojo cidictPojo = JSONArray.parseObject(json, CidictPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            // 是否为默认数据
            if (InksConstants.DEFAULT_TENANT.equals(cidictPojo.getTenantid())) {
                // 是默认数据
                // 当前操作是否为管理员
                if (InksConstants.DEFAULT_TENANT.equals(loginUser.getTenantid())) {
                    // 管理员在修改
                    cidictPojo.setLister(loginUser.getRealname());   // 制表
                    cidictPojo.setListerid(loginUser.getUserid());    // 制表id
                    cidictPojo.setModifydate(new Date());   //修改时间
                    cidictPojo.setTenantid(loginUser.getTenantid());   //租户id
                    return R.ok(this.cidictService.update(cidictPojo));
                } else {
                    // 租户在修改，新建专用字典
                    cidictPojo.setCreateby(loginUser.getRealName());   // 创建者
                    cidictPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
                    cidictPojo.setCreatedate(new Date());   // 创建时间
                    cidictPojo.setLister(loginUser.getRealname());   // 制表
                    cidictPojo.setListerid(loginUser.getUserid());    // 制表id
                    cidictPojo.setModifydate(new Date());   //修改时间
                    cidictPojo.setTenantid(loginUser.getTenantid());   //租户id
                    cidictPojo.setTenantname(loginUser.getTenantinfo().getTenantname());   //租户id
                    return R.ok(this.cidictService.insert(cidictPojo));
                }
            } else {
                // 当前数据已是租户专用数据，执行修改
                cidictPojo.setLister(loginUser.getRealname());   // 制表
                cidictPojo.setListerid(loginUser.getUserid());    // 制表id
                cidictPojo.setModifydate(new Date());   //修改时间
                cidictPojo.setTenantid(loginUser.getTenantid());   //租户id
                return R.ok(this.cidictService.update(cidictPojo));
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
