package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PitenantscmuserPojo;
import inks.system.service.PitenantscmuserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * SCM租户关系表(PiTenantScmUser)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@RestController
@RequestMapping("SYSM11B4")
@Api(tags = "SYSM11B4:租户关系表")
public class SYSM11B4Controller extends PitenantscmuserController {
    @Resource
    private PitenantscmuserService pitenantscmuserService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = " 获取SCM租户关系表详细信息", notes = "获取SCM租户关系表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByUserid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiTenantScmUser.List")
    public R<PitenantscmuserPojo> getEntityByUserid(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pitenantscmuserService.getEntityByUserid(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 获取SCM租户关系表详细信息", notes = "获取SCM租户关系表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityMapByUserid", method = RequestMethod.GET)
    public R<Map<String, Object>> getEntityMapByUserid(String key, String tid) {
        try {

            //LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            PitenantscmuserPojo entityByUserid = this.pitenantscmuserService.getEntityByUserid(key, tid);
            Map<String, Object> stringObjectMap = BeanUtils.beanToMap(entityByUserid);
            return R.ok(stringObjectMap);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增SCM(租户-用户)关联
     *
     * @param userid PiScmUser.userid
     * @return 新增结果
     */
    @ApiOperation(value = " 新增SCM(租户-用户)关联", notes = "新增SCM租户关系表", produces = "application/json")
    @RequestMapping(value = "/createByUserId", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiTenantScmUser.Add")
    public R<PitenantscmuserPojo> createByUserId(String userid) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            PitenantscmuserPojo pitenantscmuserPojo = new PitenantscmuserPojo();
            //(租户-用户)关联
            pitenantscmuserPojo.setUserid(userid);
            pitenantscmuserPojo.setUsername(loginUser.getUsername());
            pitenantscmuserPojo.setTenantid(loginUser.getTenantid());
            pitenantscmuserPojo.setRealname(loginUser.getRealname());
            pitenantscmuserPojo.setIsadmin(loginUser.getIsadmin());
            if (loginUser.getTenantinfo() != null) {
                pitenantscmuserPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
                pitenantscmuserPojo.setDeptcode(loginUser.getTenantinfo().getDeptcode());
                pitenantscmuserPojo.setDeptid(loginUser.getTenantinfo().getDeptid());
                pitenantscmuserPojo.setDeptname(loginUser.getTenantinfo().getDeptname());
            }
            pitenantscmuserPojo.setCreateby(loginUser.getRealName());   // 创建者
            pitenantscmuserPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            pitenantscmuserPojo.setCreatedate(new Date());   // 创建时间
            pitenantscmuserPojo.setLister(loginUser.getRealname());   // 制表
            pitenantscmuserPojo.setListerid(loginUser.getUserid());    // 制表id
            pitenantscmuserPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.pitenantscmuserService.insert(pitenantscmuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "根据当前用户获得租户关系表", notes = "根据当前用户获得租户关系表", produces = "application/json")
    @RequestMapping(value = "/getListByUser", method = RequestMethod.GET)
    public R<List<PitenantscmuserPojo>> getListByUser() {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pitenantscmuserService.getListByUser(loginUser.getUserid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增SCM(租户-用户)关联
     *
     * @param json PiScmUser.userid
     * @return 新增结果
     */
    @ApiOperation(value = " 新增SCM(租户-用户)关联", notes = "新增SCM租户关系表", produces = "application/json")
    @RequestMapping(value = "/createScmUser", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiTenantScmUser.Add")
    public R<PitenantscmuserPojo> createScmUser(@RequestBody String json) {
        try {
            PitenantscmuserPojo pitenantscmuserPojo = JSONArray.parseObject(json, PitenantscmuserPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pitenantscmuserPojo.setTenantid(loginUser.getTenantid());
            if (loginUser.getTenantinfo() != null) {
                pitenantscmuserPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
                pitenantscmuserPojo.setDeptcode(loginUser.getTenantinfo().getDeptcode());
                pitenantscmuserPojo.setDeptid(loginUser.getTenantinfo().getDeptid());
                pitenantscmuserPojo.setDeptname(loginUser.getTenantinfo().getDeptname());
            }
            pitenantscmuserPojo.setCreateby(loginUser.getRealName());   // 创建者
            pitenantscmuserPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            pitenantscmuserPojo.setCreatedate(new Date());   // 创建时间
            pitenantscmuserPojo.setLister(loginUser.getRealname());   // 制表
            pitenantscmuserPojo.setListerid(loginUser.getUserid());    // 制表id
            pitenantscmuserPojo.setModifydate(new Date());   //修改时间

            //判断是否传入userid;有，修改；无，新增
            PitenantscmuserPojo pitenantscmuser = pitenantscmuserService.createScmUser(pitenantscmuserPojo);
            return R.ok(pitenantscmuser);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
