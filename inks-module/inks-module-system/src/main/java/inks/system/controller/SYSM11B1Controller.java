package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.AESUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PiscmjustauthPojo;
import inks.system.domain.pojo.PiscmuserPojo;
import inks.system.service.PiscmjustauthService;
import inks.system.service.PiscmuserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * SCM用户(PiScmUser)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@RestController
@RequestMapping("SYSM11B1")
@Api(tags = "SYSM11B1:SCM用户")
public class SYSM11B1Controller extends PiscmuserController {
    private final String SCANLOGIN_CODE = "scanlogin_code:";
    @Resource
    private PiscmuserService piscmuserService;

    @Resource
    private RedisService redisService;

    @Resource
    private TokenService tokenService;
    @Resource
    private PiscmjustauthService piscmjustauthService;

    /**
     * 通过（手机号,邮箱）查询单条数据UserName只能是手机号或者邮箱
     *
     * @param username 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取SCM用户详细信息ByUserName", notes = "获取SCM用户详细信息ByUserName", produces = "application/json")
    @RequestMapping(value = "/getEntityByUserName", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiScmUser.List")
    public R<PiscmuserPojo> getEntityByUserName(String username) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.piscmuserService.getEntityByUserName(username));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询(该租户下所有用户)", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListByTen", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiScmUser.List")
    public R<PageInfo<PiscmuserPojo>> getPageListByTen(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiScmUser.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.piscmuserService.getPageListByTen(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 接受pms服务传来的openid,key为redis中的key
     */
    @ApiOperation(value = "绑定openid", notes = "绑定openid", produces = "application/json")
    @RequestMapping(value = "/scanWebScmBind", method = RequestMethod.GET)
    public R<String> scanWebScmBind(String openid, String key, String userid, String tenantid, HttpServletRequest request) throws ParseException {
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //判断当前租户下该用户openid是否已绑定
        PiscmuserPojo piscmuserPojo = piscmuserService.getEntityByOpenid(openid, tenantid);
        if (piscmuserPojo != null) {
            return R.ok("用户已绑定openid,若要换绑请先解绑");
        }
        //未绑定：绑定userid和openid(插入Pi_ScmAuthJust)
        PiscmjustauthPojo piscmjustauthPojo = new PiscmjustauthPojo();
        piscmjustauthPojo.setUserid(userid);
        piscmjustauthPojo.setAuthtype("wxmp");
        piscmjustauthPojo.setAuthuuid(openid);
        piscmjustauthPojo.setCreatebyid(userid);  // 创建者id
        piscmjustauthPojo.setCreatedate(new Date());   // 创建时间
//        piscmjustauthPojo.setLister(loginUser.getRealname());   // 制表
        piscmjustauthPojo.setListerid(userid);    // 制表id
        piscmjustauthPojo.setModifydate(new Date());   //修改时间
        piscmjustauthPojo.setTenantid(tenantid);   //租户id
        piscmjustauthService.insert(piscmjustauthPojo);
        //设置当前计算任务进度
        Map<String, Object> missionMsg = new HashMap<>();
        missionMsg.put("code", "200");
        missionMsg.put("msg", "扫码绑定openid成功");
        this.redisService.setCacheMapValue(SCANLOGIN_CODE, key, missionMsg);
        redisService.expire(SCANLOGIN_CODE, 60 * 6);
        return R.ok("扫码绑定openid成功");
    }


    @ApiOperation(value = "解绑openid", notes = "解绑openid", produces = "application/json")
    @RequestMapping(value = "/scanWebScmUnBind", method = RequestMethod.GET)
    public R<String> scanWebScmUnBind(String openid, String key, String userid, String tenantid, HttpServletRequest request) throws ParseException {
//        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //判断当前租户下该用户openid是否已绑定
        PiscmuserPojo piscmuserPojo = piscmuserService.getEntityByOpenid(openid, tenantid);
        if (piscmuserPojo == null) {
            return R.ok("用户未绑定openid,无需解绑");
        }
        //已绑定：解绑userid和openid(删除Pi_ScmAuthJust)
        piscmjustauthService.deleteByOpenid(openid, tenantid);
        //设置当前计算任务进度
        Map<String, Object> missionMsg = new HashMap<>();
        missionMsg.put("code", "200");
        missionMsg.put("msg", "扫码解绑openid成功");
        this.redisService.setCacheMapValue(SCANLOGIN_CODE, key, missionMsg);
        redisService.expire(SCANLOGIN_CODE, 60 * 6);
        return R.ok("扫码解绑openid成功");
    }

    /**
     * @return R<PiscmuserPojo>
     * @Description 初始化密码
     * <AUTHOR>
     * @param[1] keys是userid  初始化为123456
     * @time 2023/4/24 12:46
     */
    @ApiOperation(value = "初始化密码", notes = "初始化密码", produces = "application/json")
    @RequestMapping(value = "/initPassword", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiScmUser.Edit")
    public R<PiscmuserPojo> initPassword(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            PiscmuserPojo piscmuserPojo = new PiscmuserPojo();
            piscmuserPojo.setUserid(key);
            piscmuserPojo.setLister(loginUser.getRealname());   // 制表
            piscmuserPojo.setListerid(loginUser.getUserid());    // 制表id
            piscmuserPojo.setTenantid(loginUser.getTenantid());   //租户id
            piscmuserPojo.setModifydate(new Date());   //修改时间
            //加密密码
            piscmuserPojo.setUserpassword(AESUtil.Encrypt("123456"));
            return R.ok(this.piscmuserService.update(piscmuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
