package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.*;
import inks.system.service.CifnorderService;
import inks.system.service.PisubscriberService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 订阅信息表(PiSubscriber)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-10 14:17:23
 */
@RestController
public class PisubscriberController {
    /**
     * 服务对象
     */
    @Resource
    private PisubscriberService pisubscriberService;

    /**
     * 服务对象
     */
    @Resource
    private CifnorderService cifnorderService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * @Description 将Web菜单列表List<PimenuwebPojo>转为树形结构List<WebMenuPojo>
     */
    public static List<WebMenuPojo> buildWebMenuToChildrenOld(List<PimenuwebPojo> lst) {
        List<WebMenuPojo> list;
        list = new ArrayList<>();
        for (int i = 0; i < lst.size(); i++) {
            if (lst.get(i).getNavtype().equals("1")) {
                WebMenuPojo webMenuPojo = new WebMenuPojo();
                webMenuPojo.setName(lst.get(i).getNavname());
                webMenuPojo.setPath(lst.get(i).getMvcurl());
                webMenuPojo.setMeta(new WebMetaPojo(lst.get(i).getNavname(), lst.get(i).getImagecss()));
                List<WebMenuPojo> lstChil = new ArrayList<>();
                for (int j = 0; j < lst.size(); j++) {
                    if (lst.get(j).getNavpid().equals(lst.get(i).getNavid())) {
                        WebMenuPojo webMenuPojo2 = new WebMenuPojo();
                        webMenuPojo2.setName(lst.get(j).getNavname());
                        webMenuPojo2.setPath(lst.get(j).getMvcurl());
                        webMenuPojo2.setMeta(new WebMetaPojo(lst.get(j).getNavname(), lst.get(j).getImagecss()));
                        List<WebMenuPojo> lstChil2 = new ArrayList<>();
                        for (PimenuwebPojo pimenuwebPojo : lst) {
                            if (pimenuwebPojo.getNavpid().equals(lst.get(j).getNavid())) {
                                WebMenuPojo webMenuPojo3 = new WebMenuPojo();
                                webMenuPojo3.setName(pimenuwebPojo.getNavname());
                                webMenuPojo3.setPath(pimenuwebPojo.getMvcurl());
                                webMenuPojo3.setMeta(new WebMetaPojo(pimenuwebPojo.getNavname(), pimenuwebPojo.getImagecss()));
                                lstChil2.add(webMenuPojo3);
                            }
                        }
                        webMenuPojo2.setChildren(lstChil2);
                        lstChil.add(webMenuPojo2);
                    }
                }
                webMenuPojo.setChildren(lstChil);
                list.add(webMenuPojo);
            }
        }
        return list;
    }

    //如果是按键，navigateurl不为空， 给comp="", redirect=navigateurl 中的内容
    //如果页component=“layout”,按键，component=path中的内容
    public static List<WebMenuPojo> buildWebMenuToChildren(List<PimenuwebPojo> lst) {
        List<WebMenuPojo> list = new ArrayList<>();
        for (int i = 0; i < lst.size(); i++) {
            if (lst.get(i).getNavtype().equals("1")) {  // 页面类型(一级菜单)
                WebMenuPojo webMenuPojo = new WebMenuPojo();
                webMenuPojo.setName(lst.get(i).getNavname());
                webMenuPojo.setPath(lst.get(i).getMvcurl());
                webMenuPojo.setIsmicroapp(lst.get(i).getIsmicroapp());
                webMenuPojo.setMicroappname(lst.get(i).getMicroappname());
                webMenuPojo.setMicroappentry(lst.get(i).getMicroappentry());
                webMenuPojo.setMicroapprule(lst.get(i).getMicroapprule());
                webMenuPojo.setMicroapplocal(lst.get(i).getMicroapplocal());
                // 如果是页面，component="layout"
                webMenuPojo.setComponent("layout");
                webMenuPojo.setMeta(new WebMetaPojo(lst.get(i).getNavname(), lst.get(i).getImagecss()));
                List<WebMenuPojo> lstChil = new ArrayList<>();
                for (int j = 0; j < lst.size(); j++) {
                    if (lst.get(j).getNavpid().equals(lst.get(i).getNavid())) {  // 二级菜单
                        WebMenuPojo webMenuPojo2 = new WebMenuPojo();
                        webMenuPojo2.setName(lst.get(j).getNavname());
                        webMenuPojo2.setPath(lst.get(j).getMvcurl());
                        webMenuPojo2.setIsmicroapp(lst.get(i).getIsmicroapp());
                        webMenuPojo2.setMicroappname(lst.get(i).getMicroappname());
                        webMenuPojo2.setMicroappentry(lst.get(i).getMicroappentry());
                        webMenuPojo2.setMicroapprule(lst.get(i).getMicroapprule());
                        webMenuPojo2.setMicroapplocal(lst.get(i).getMicroapplocal());
                        // 根据导航类型设置component
                        String navType = lst.get(j).getNavtype();
                        if (navType.equals("1")) {  // 页面
                            webMenuPojo2.setComponent("layout");
                        } else if (navType.equals("3")) {  // 按键
                            // 如果是按键且navigateurl不为空，设置redirect
                            if (StringUtils.isNotBlank(lst.get(j).getNavigateurl())) {
                                webMenuPojo2.setComponent("");
                                webMenuPojo2.setRedirect(lst.get(j).getNavigateurl());
                            } else {
                                webMenuPojo2.setComponent(lst.get(i).getMvcurl());
                            }
                        }
                        webMenuPojo2.setMeta(new WebMetaPojo(lst.get(j).getNavname(), lst.get(j).getImagecss()));
                        List<WebMenuPojo> lstChil2 = new ArrayList<>();
                        for (PimenuwebPojo pimenuwebPojo : lst) {
                            if (pimenuwebPojo.getNavpid().equals(lst.get(j).getNavid())) {  // 三级菜单
                                WebMenuPojo webMenuPojo3 = new WebMenuPojo();
                                webMenuPojo3.setName(pimenuwebPojo.getNavname());
                                webMenuPojo3.setPath(pimenuwebPojo.getMvcurl());
                                webMenuPojo3.setIsmicroapp(pimenuwebPojo.getIsmicroapp());
                                webMenuPojo3.setMicroappname(pimenuwebPojo.getMicroappname());
                                webMenuPojo3.setMicroappentry(pimenuwebPojo.getMicroappentry());
                                webMenuPojo3.setMicroapprule(pimenuwebPojo.getMicroapprule());
                                webMenuPojo3.setMicroapplocal(pimenuwebPojo.getMicroapplocal());
                                // 根据导航类型设置component
                                String childNavType = pimenuwebPojo.getNavtype();
                                if (childNavType.equals("1")) {  // 页面
                                    webMenuPojo3.setComponent("layout");
                                } else if (childNavType.equals("3")) {  // 按键
                                    // 如果是按键且navigateurl不为空，设置redirect
                                    if (pimenuwebPojo.getNavigateurl() != null && !pimenuwebPojo.getNavigateurl().isEmpty()) {
                                        webMenuPojo3.setComponent("");
                                        webMenuPojo3.setRedirect(pimenuwebPojo.getNavigateurl());
                                    } else {
                                        webMenuPojo3.setComponent(pimenuwebPojo.getMvcurl());

                                    }
                                }
                                webMenuPojo3.setMeta(new WebMetaPojo(pimenuwebPojo.getNavname(), pimenuwebPojo.getImagecss()));
                                lstChil2.add(webMenuPojo3);
                            }
                        }
                        webMenuPojo2.setChildren(lstChil2);
                        lstChil.add(webMenuPojo2);
                    }
                }
                webMenuPojo.setChildren(lstChil);
                list.add(webMenuPojo);
            }
        }

        return list;
    }


    public static List<WebMenuPojo> buildWebRouteToChildren(List<PimenuwebPojo> lst) {
        List<WebMenuPojo> list = new ArrayList<>();
        for (int i = 0; i < lst.size(); i++) {
            if (lst.get(i).getNavtype().equals("1") && StringUtils.isNotBlank(lst.get(i).getRoutecomp())) {  // 页面类型(一级菜单)
                WebMenuPojo webMenuPojo = new WebMenuPojo();
                webMenuPojo.setName(lst.get(i).getRoutename());
                webMenuPojo.setPath(lst.get(i).getRoutepath());
                webMenuPojo.setComponent(lst.get(i).getRoutecomp());
                webMenuPojo.setMeta(new WebMetaPojo(lst.get(i).getNavname(), lst.get(i).getImagecss()));
                webMenuPojo.setIsmicroapp(lst.get(i).getIsmicroapp());
                webMenuPojo.setMicroappname(lst.get(i).getMicroappname());
                webMenuPojo.setMicroappentry(lst.get(i).getMicroappentry());
                webMenuPojo.setMicroapprule(lst.get(i).getMicroapprule());
                webMenuPojo.setMicroapplocal(lst.get(i).getMicroapplocal());
                List<WebMenuPojo> lstChil = new ArrayList<>();
                for (int j = 0; j < lst.size(); j++) {
                    if (lst.get(j).getNavpid().equals(lst.get(i).getNavid())) {
                        // 二级菜单
                        List<WebMenuPojo> lstChil2 = new ArrayList<>();
                        for (PimenuwebPojo pimenuwebPojo : lst) {
                            if (pimenuwebPojo.getNavpid().equals(lst.get(j).getNavid()) && StringUtils.isNotBlank(pimenuwebPojo.getRoutecomp())) {
                                // 三级菜单
                                WebMenuPojo webMenuPojo3 = new WebMenuPojo();
                                webMenuPojo3.setName(pimenuwebPojo.getRoutename());
                                webMenuPojo3.setPath(pimenuwebPojo.getRoutepath());
                                webMenuPojo3.setComponent(pimenuwebPojo.getRoutecomp());
                                webMenuPojo3.setMeta(new WebMetaPojo(pimenuwebPojo.getNavname(), pimenuwebPojo.getImagecss()));
                                webMenuPojo3.setIsmicroapp(pimenuwebPojo.getIsmicroapp());
                                webMenuPojo3.setMicroappname(pimenuwebPojo.getMicroappname());
                                webMenuPojo3.setMicroappentry(pimenuwebPojo.getMicroappentry());
                                webMenuPojo3.setMicroapprule(pimenuwebPojo.getMicroapprule());
                                webMenuPojo3.setMicroapplocal(pimenuwebPojo.getMicroapplocal());
                                lstChil2.add(webMenuPojo3);
                            }
                        }
                        lstChil.addAll(lstChil2);
                    }
                }
                webMenuPojo.setChildren(lstChil);
                list.add(webMenuPojo);
            }
        }

        return list;
    }

    /**
     * @Description 将APP菜单列表List<PimenuappPojo>转为树形结构List<AppMenuPojo>
     */
    public static List<AppMenuPojo> buildAppMenuToChildren(List<PimenuappPojo> lst) {
        List<AppMenuPojo> list;
        list = new ArrayList<>();
        for (int i = 0; i < lst.size(); i++) {
            if (lst.get(i).getNavtype().equals("1")) {
                AppMenuPojo appMenuPojo = new AppMenuPojo();
                appMenuPojo.setName(lst.get(i).getNavname());
                appMenuPojo.setPath(lst.get(i).getMvcurl());
                appMenuPojo.setMeta(new AppMetaPojo(lst.get(i).getNavname(), lst.get(i).getImagecss(), lst.get(i).getImagestyle()));
                List<AppMenuPojo> lstChil = new ArrayList<>();
                for (int j = 0; j < lst.size(); j++) {
                    if (lst.get(j).getNavpid().equals(lst.get(i).getNavid())) {
                        AppMenuPojo appMenuPojo2 = new AppMenuPojo();
                        appMenuPojo2.setName(lst.get(j).getNavname());
                        appMenuPojo2.setPath(lst.get(j).getMvcurl());
                        appMenuPojo2.setMeta(new AppMetaPojo(lst.get(j).getNavname(), lst.get(j).getImagecss(), lst.get(j).getImagestyle()));
                        List<AppMenuPojo> lstChil2 = new ArrayList<>();
                        for (PimenuappPojo pimenuappPojo : lst) {
                            if (pimenuappPojo.getNavpid().equals(lst.get(j).getNavid())) {
                                AppMenuPojo appMenuPojo3 = new AppMenuPojo();
                                appMenuPojo3.setName(pimenuappPojo.getNavname());
                                appMenuPojo3.setPath(pimenuappPojo.getMvcurl());
                                appMenuPojo3.setMeta(new AppMetaPojo(pimenuappPojo.getNavname(), pimenuappPojo.getImagecss(), pimenuappPojo.getImagestyle()));
                                lstChil2.add(appMenuPojo3);
                            }
                        }
                        appMenuPojo2.setChildren(lstChil2);
                        lstChil.add(appMenuPojo2);
                    }
                }
                appMenuPojo.setChildren(lstChil);
                list.add(appMenuPojo);
            }

        }
        return list;
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取订阅信息表详细信息", notes = "获取订阅信息表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiSubscriber.List")
    public R<PisubscriberPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.pisubscriberService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiSubscriber.List")
    public R<PageInfo<PisubscriberPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiSubscriber.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.pisubscriberService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增订阅信息表", notes = "新增订阅信息表", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiSubscriber.Add")
    public R<PisubscriberPojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            PisubscriberPojo pisubscriberPojo = JSONArray.parseObject(json, PisubscriberPojo.class);

            pisubscriberPojo.setLister(loginUser.getRealname());   //用户名
            pisubscriberPojo.setTenantid(loginUser.getTenantid());   //租户id
            pisubscriberPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.pisubscriberService.insert(pisubscriberPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改订阅信息表", notes = "修改订阅信息表", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiSubscriber.Edit")
    public R<PisubscriberPojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            PisubscriberPojo pisubscriberPojo = JSONArray.parseObject(json, PisubscriberPojo.class);

            pisubscriberPojo.setLister(loginUser.getRealname());   //用户名
            pisubscriberPojo.setTenantid(loginUser.getTenantid());   //租户id
            pisubscriberPojo.setModifydate(new Date());   //修改时间
//            pisubscriberPojo.setAssessor(""); //审核员
//            pisubscriberPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.pisubscriberService.update(pisubscriberPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除订阅信息表", notes = "删除订阅信息表", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiSubscriber.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.pisubscriberService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiSubscriber.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        PisubscriberPojo pisubscriberPojo = this.pisubscriberService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(pisubscriberPojo);

        //从redis中获取XML内容
        String xml = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (xml != null || !xml.equals("") || xml != "") {
            content = xml;
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 新增数据
     *
     * @param key 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 根据订单新增订阅信息表", notes = "根据订单新增订阅信息表", produces = "application/json")
    @RequestMapping(value = "/createbyorder", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiSubscriber.Add")
    public R<List<PisubscriberPojo>> createByOrder(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            CifnorderPojo cifnorderPojo = this.cifnorderService.getBillEntity(key, loginUser.getTenantid());
            if (cifnorderPojo != null) {
                List<PisubscriberPojo> lst = this.pisubscriberService.createByOrder(cifnorderPojo);
                return R.ok(lst);
            } else {
                return R.fail("未找到相关订单");
            }


        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListBySelf", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiSubscriber.List")
    public R<PageInfo<PisubscriberPojo>> getPageListBySelf(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiSubscriber.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.pisubscriberService.getPageListByTenant(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = "根据租户获取MenuWebList", notes = "根据租户获取MenuWebList ?db=1,重读数据库,fncode为主服务编码", produces = "application/json")
    @RequestMapping(value = "/getMenuWebListBySelf", method = RequestMethod.GET)
    public R<List<WebMenuPojo>> getMenuWebListBySelf(Integer db, String fncode) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            List<WebMenuPojo> list; //this.redisService.getCacheObject("tenant_manuweb:" + loginUser.getTenantid());
//            if (db != null && db == 1) // db=1,重读数据库
            List<PimenuwebPojo> lst = this.pisubscriberService.getMenuWebListByTenant(loginUser.getTenantid(), fncode);
            list = buildWebMenuToChildren(lst);
            //this.redisService.setCacheObject("tenant_manuweb:" + loginUser.getTenantid(), list, (long) (1), TimeUnit.DAYS);
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "根据租户获取MenuWebList", notes = "根据租户获取MenuWebList ?db=1,重读数据库,fncode为主服务编码", produces = "application/json")
    @RequestMapping(value = "/getRouteWebListBySelf", method = RequestMethod.GET)
    public R<List<WebMenuPojo>> getRouteWebListBySelf(Integer db, String fncode) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            List<WebMenuPojo> list; //this.redisService.getCacheObject("tenant_manuweb:" + loginUser.getTenantid());
//            if (db != null && db == 1) // db=1,重读数据库
            List<PimenuwebPojo> lst = this.pisubscriberService.getMenuWebListByTenant(loginUser.getTenantid(), fncode);
            list = buildWebRouteToChildren(lst);
            //this.redisService.setCacheObject("tenant_manuweb:" + loginUser.getTenantid(), list, (long) (1), TimeUnit.DAYS);
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = "根据租户获取WebLnkList", notes = "根据租户获取WebLnkList ?db=1,重读数据库,fncode为主服务编码", produces = "application/json")
    @RequestMapping(value = "/getWebLnkListBySelf", method = RequestMethod.GET)
    public R<List<PiweblnkPojo>> getWebLnkListBySelf(Integer db, String fncode) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            List<PiweblnkPojo> lst = this.pisubscriberService.getWebLnkListByTenant(loginUser.getTenantid(), fncode);
            return R.ok(lst);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "根据租户获取WebFrmList", notes = "根据租户获取WebLnkList ?db=1,重读数据库,fncode为主服务编码", produces = "application/json")
    @RequestMapping(value = "/getMenuFrmListBySelf", method = RequestMethod.GET)
    public R<List<PimenufrmPojo>> getMenuFrmListBySelf(Integer db, String fncode) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            List<PimenufrmPojo> lst = this.pisubscriberService.getMenuFrmListByTenant(loginUser.getTenantid(), fncode);
            return R.ok(lst);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "根据租户获取WebNavList", notes = "根据租户获取WebNavList ?db=1,重读数据库,fncode为主服务编码", produces = "application/json")
    @RequestMapping(value = "/getWebNavListBySelf", method = RequestMethod.GET)
    public R<List<PiwebnavPojo>> getWebNavListBySelf(Integer db, String fncode) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            List<PiwebnavPojo> lst = this.pisubscriberService.getWebNavListByTenant(loginUser.getTenantid(), fncode);
            return R.ok(lst);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = "根据租户获取MenuAppList", notes = "根据租户获取MenuAppList", produces = "application/json")
    @RequestMapping(value = "/getMenuAppListBySelf", method = RequestMethod.GET)
    public R<List<AppMenuPojo>> getMenuAppListBySelf() {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<AppMenuPojo> list; //this.redisService.getCacheObject("tenant_manuapp:" + loginUser.getTenantid());
            List<PimenuappPojo> lst = this.pisubscriberService.getMenuAppListByTenant(loginUser.getTenantid());
            list = buildAppMenuToChildren(lst);
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = "根据租户获取权限表List", notes = "根据租户获取权限表List", produces = "application/json")
    @RequestMapping(value = "/getPermAllListBySelf", method = RequestMethod.GET)
    public R<List<PipermcodePojo>> getPermAllListBySelf() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            if (loginUser.getIsadmin() == 1) {
                List<PipermcodePojo> lst = this.pisubscriberService.getPermAllByTenant(loginUser.getTenantid());
                return R.ok(lst);
            } else {
                return R.fail(403, "权限不足");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = "根据租户获取默认角色List", notes = "根据租户获取默认角色List", produces = "application/json")
    @RequestMapping(value = "/getDefRoleListBySelf", method = RequestMethod.GET)
    public R<List<PirolePojo>> getDefRoleListBySelf() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            if (loginUser.getIsadmin() == 1) {
                List<PirolePojo> lst = this.pisubscriberService.getDefRoleByTenant(loginUser.getTenantid());
                return R.ok(lst);
            } else {
                return R.fail("权限不足");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }

    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = "根据租户获取大屏List", notes = "根据租户获取大屏List", produces = "application/json")
    @RequestMapping(value = "/getBigDataListBySelf", method = RequestMethod.GET)
    public R<List<CibigdataPojo>> getBigDataListBySelf() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            //List<CibigdataPojo> lst = this.pisubscriberService.getBigDataByTenant(loginUser.getTenantid());
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }

    }


    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = "根据租户获取预警List", notes = "根据租户获取预警List", produces = "application/json")
    @RequestMapping(value = "/getWarnListBySelf", method = RequestMethod.GET)
    public R<List<CiwarningPojo>> getWarnListBySelf() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            List<CiwarningPojo> lst = this.pisubscriberService.getWarnByTenant(loginUser.getTenantid());
            return R.ok(lst);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }

    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = "根据租户获取工作台List", notes = "根据租户获取工作台List", produces = "application/json")
    @RequestMapping(value = "/getDashListBySelf", method = RequestMethod.GET)
    public R<List<CidashboardPojo>> getDashListBySelf() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            List<CidashboardPojo> lst = this.pisubscriberService.getDashByTenant(loginUser.getTenantid());
            return R.ok(lst);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }

    }

}

