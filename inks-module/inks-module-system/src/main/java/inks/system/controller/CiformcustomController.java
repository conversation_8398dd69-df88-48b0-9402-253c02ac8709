package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiformcustomPojo;
import inks.system.service.CiformcustomService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 自定义界面(CiFormCustom)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-06 12:57:59
 */

public class CiformcustomController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(CiformcustomController.class);
    /**
     * 服务对象
     */
    @Resource
    private CiformcustomService ciformcustomService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取自定义界面详细信息", notes = "获取自定义界面详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiFormCustom.List")
    public R<CiformcustomPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.ciformcustomService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiFormCustom.List")
    public R<PageInfo<CiformcustomPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiFormCustom.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.ciformcustomService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增自定义界面", notes = "新增自定义界面", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiFormCustom.Add")
    public R<CiformcustomPojo> create(@RequestBody String json) {
        try {
            CiformcustomPojo ciformcustomPojo = JSONArray.parseObject(json, CiformcustomPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            ciformcustomPojo.setCreateby(loginUser.getRealName());   // 创建者
            ciformcustomPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            ciformcustomPojo.setCreatedate(new Date());   // 创建时间
            ciformcustomPojo.setLister(loginUser.getRealname());   // 制表
            ciformcustomPojo.setListerid(loginUser.getUserid());    // 制表id  
            ciformcustomPojo.setModifydate(new Date());   //修改时间
            ciformcustomPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.ciformcustomService.insert(ciformcustomPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改自定义界面", notes = "修改自定义界面", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiFormCustom.Edit")
    public R<CiformcustomPojo> update(@RequestBody String json) {
        try {
            CiformcustomPojo ciformcustomPojo = JSONArray.parseObject(json, CiformcustomPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            ciformcustomPojo.setLister(loginUser.getRealname());   // 制表
            ciformcustomPojo.setListerid(loginUser.getUserid());    // 制表id  
            ciformcustomPojo.setTenantid(loginUser.getTenantid());   //租户id
            ciformcustomPojo.setModifydate(new Date());   //修改时间
//            ciformcustomPojo.setAssessor(""); // 审核员
//            ciformcustomPojo.setAssessorid(""); // 审核员id
//            ciformcustomPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.ciformcustomService.update(ciformcustomPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除自定义界面", notes = "删除自定义界面", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiFormCustom.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.ciformcustomService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiFormCustom.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        CiformcustomPojo ciformcustomPojo = this.ciformcustomService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(ciformcustomPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

