package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiweblnkcustPojo;
import inks.system.service.CiweblnkcustService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 自定义webLnk(CiWebLnkCust)表控制层
 *
 * <AUTHOR>
 * @since 2023-05-08 14:57:09
 */
@RestController
@RequestMapping("SYSM07B14")
@Api(tags = "SYSM07B14:自定义webLnk")
public class SYSM07B14Controller extends CiweblnkcustController {
    @Resource
    private CiweblnkcustService ciweblnkcustService;
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param
     * @return 单条数据
     */
    @ApiOperation(value = " 获取当前登录用户的自定义快捷菜单webLnk详细信息", notes = "获取自定义webLnk详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityBySelf", method = RequestMethod.GET)
    public R<CiweblnkcustPojo> getEntityBySelf() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.ciweblnkcustService.getEntityBySelf(loginUser.getUserid(), loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 获取当前登录用户的自定义快捷菜单webLnk详细信息", notes = "获取自定义webLnk详细信息", produces = "application/json")
    @RequestMapping(value = "/getListBySelf", method = RequestMethod.GET)
    public R<List<CiweblnkcustPojo>> getListBySelf(String fncode) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.ciweblnkcustService.getListBySelf(fncode,loginUser.getUserid(), loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改自定义webLnk", notes = "修改自定义webLnk", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<CiweblnkcustPojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            CiweblnkcustPojo ciweblnkcustPojo = JSONArray.parseObject(json, CiweblnkcustPojo.class);

            ciweblnkcustPojo.setLister(loginUser.getRealname());   // 制表
            ciweblnkcustPojo.setListerid(loginUser.getUserid());    // 制表id
            ciweblnkcustPojo.setTenantid(loginUser.getTenantid());   //租户id
            ciweblnkcustPojo.setModifydate(new Date());   //修改时间
            //获取当前登录用户的自定义快捷菜单webLnk详细信息，如果没有则新增，如果有则修改
            CiweblnkcustPojo entityBySelf = ciweblnkcustService.getEntityBySelf(loginUser.getUserid(), loginUser.getTenantid());
            if (entityBySelf == null) {
                ciweblnkcustPojo.setUserid(loginUser.getUserid());
                ciweblnkcustPojo = this.ciweblnkcustService.insert(ciweblnkcustPojo);
            } else {
                entityBySelf.setLnkcontent(ciweblnkcustPojo.getLnkcontent());
                entityBySelf.setFncode(ciweblnkcustPojo.getFncode());
                ciweblnkcustPojo = this.ciweblnkcustService.update(entityBySelf);
            }
            return R.ok(ciweblnkcustPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
