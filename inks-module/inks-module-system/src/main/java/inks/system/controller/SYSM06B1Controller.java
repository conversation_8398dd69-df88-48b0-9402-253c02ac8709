package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.constant.CacheConstants;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiconfigPojo;
import inks.system.service.CiconfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 系统参数(CiConfig)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-27 18:30:49
 */
@RestController
@RequestMapping("SYSM06B1")
@Api(tags = "SYSM06B1:系统参数:平台级")
public class SYSM06B1Controller extends CiconfigController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(SYSM06B1Controller.class);
    /**
     * 服务对象
     */
    @Resource
    private CiconfigService ciconfigService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiConfig.List")
    public R<PageInfo<CiconfigPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiConfig.CreateDate");

            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " and Cfglevel=0";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.ciconfigService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取系统参数详细信息 后期将取消接口", notes = "获取系统参数详细信息", produces = "application/json")
    @RequestMapping(value = "/getTenantConfigValue", method = RequestMethod.GET)
    public R<String> getTenantConfigValue(String key, String tid) {
        try {
            //有Tid参
            if (StringUtils.isBlank(tid)) {
                // 不要改loginUser位置！tid传了不走loginUser
                LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
                if (loginUser == null) {
                    R.fail("读取用户登录信息失败");
                }
                tid = loginUser.getTenantid();
            }
            //从redis中获取Reprot内容
            Map<String, String> mapConfig = this.redisService.getCacheObject(CacheConstants.TENANT_CONFIG_KEY + tid);
            if (mapConfig == null) {
                mapConfig = new HashMap<String, String>();
                List<CiconfigPojo> lst = this.ciconfigService.getListByTenant(tid);
                for (CiconfigPojo pojo : lst) {
                    mapConfig.put(pojo.getCfgkey(), pojo.getCfgvalue());
                }
                this.redisService.setCacheObject(CacheConstants.TENANT_CONFIG_KEY + tid, mapConfig, (long) (365), TimeUnit.DAYS);
                logger.info("重建租户参数缓存" + CacheConstants.TENANT_CONFIG_KEY + tid);
            } else {
                logger.info("读取租户参数缓存" + CacheConstants.TENANT_CONFIG_KEY + tid);
            }

            if (!mapConfig.isEmpty()) {
                return R.ok(mapConfig.get(key));
            } else {
                return R.fail("未找到" + key + "参数");
            }

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = " 获取本租户下所有系统参数详细信息", notes = "获取本租户下所有系统参数详细信息 DB=1 为重新数据", produces = "application/json")
    @RequestMapping(value = "/getConfigTenAll", method = RequestMethod.GET)
    public R<Map<String, String>> getConfigTenAll(Integer db, String tid) {
        try {
            if (db == null) db = 0;
            if (StringUtils.isBlank(tid)) {
                // 不要改loginUser位置！tid传了不走loginUser
                LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
                if (loginUser == null) {
                    R.fail("读取用户登录信息失败");
                }
                tid = loginUser.getTenantid();
            }
            //从redis中获取Reprot内容
            Map<String, String> mapConfig = this.ciconfigService.getMapByTenant(db, tid);
            return R.ok(mapConfig);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取系统参数详细信息", notes = "获取系统参数详细信息", produces = "application/json")
    @RequestMapping(value = "/getConfigValue", method = RequestMethod.GET)
    public R<String> getConfigValue(String key, String tid) {
        try {
            //有Tid参
            if (StringUtils.isBlank(tid)) {
                // 不要改loginUser位置！tid传了不走loginUser
                LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
                if (loginUser == null) {
                    R.fail("读取用户登录信息失败");
                }
                tid = loginUser.getTenantid();
            }
            Map<String, String> mapConfig = this.ciconfigService.getMapByTenant(0, tid);
            if (!mapConfig.isEmpty()) {
                return R.ok(mapConfig.get(key));
            } else {
                return R.fail("未找到" + key + "参数");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 获取系统参数详细信息platform.microapp.d01 查询不要过滤租户", notes = "获取系统参数详细信息", produces = "application/json")
    @RequestMapping(value = "/getMicroAppMapList", method = RequestMethod.GET)
    public R<List<Map<String, String>>> getMicroAppMapList() {
        try {
            List<Map<String, String>> microAppMap = this.ciconfigService.getMicroAppMapList();
            return R.ok(microAppMap);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
