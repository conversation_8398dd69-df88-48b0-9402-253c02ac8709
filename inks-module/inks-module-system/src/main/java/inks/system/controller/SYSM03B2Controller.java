package inks.system.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PipermcodePojo;
import inks.system.domain.pojo.PirolePojo;
import inks.system.domain.pojo.PiuserPojo;
import inks.system.domain.pojo.PiuserrolePojo;
import inks.system.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 角色关系表(PiUserRole)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 14:36:57
 */
@RestController
@RequestMapping("SYSM03B2")
@Api(tags = "SYSM03B2:角色关系表")
public class SYSM03B2Controller extends PiuserroleController {

    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(PiuserroleController.class);
    /**
     * 服务对象
     */
    @Resource
    private PiuserService piuserService;
    /**
     * 服务对象
     */
    @Resource
    private PiroleService piroleService;
    /**
     * 服务对象
     */
    @Resource
    private PiuserroleService piuserroleService;
    @Resource
    private TokenService tokenService;
    /**
     * 服务对象
     */
    @Resource
    private CiconfigService ciconfigService;
    @Resource
    private PifunctionpermService pifunctionpermService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据角色获取关系List", notes = "根据角色获取关系List", produces = "application/json")
    @RequestMapping(value = "/getListByRole", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiUserRole.List")
    public R<List<PiuserrolePojo>> getListByRole(String key) {
        try {
            return R.ok(this.piuserroleService.getListByRole(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据用户获取关系List", notes = "根据用户获取关系List", produces = "application/json")
    @RequestMapping(value = "/getListByUser", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiUserRole.List")
    public R<List<PiuserrolePojo>> getListByUser(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.piuserroleService.getListByUser(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据角色获取用户Bill", notes = "根据角色获取用户Bill", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByRole", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiUserRole.List")
    public R<PiuserrolePojo> getBillEntityByRole(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            PirolePojo pirolePojo = this.piroleService.getEntity(key, loginUser.getTenantid());
            PiuserrolePojo piuserrolePojo = new PiuserrolePojo();
            org.springframework.beans.BeanUtils.copyProperties(pirolePojo, piuserrolePojo);
            List<PiuserrolePojo> item = this.piuserroleService.getListByRole(key);
            piuserrolePojo.setItem(item);
            return R.ok(piuserrolePojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据用户获取角色Bill", notes = "根据用户获取角色Bill", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByUser", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiUserRole.List")
    public R<PiuserrolePojo> getBillEntityByUser(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            PiuserPojo piuserPojo = this.piuserService.getEntity(key);
            PiuserrolePojo piuserrolePojo = new PiuserrolePojo();
            org.springframework.beans.BeanUtils.copyProperties(piuserPojo, piuserrolePojo);
            List<PiuserrolePojo> item = this.piuserroleService.getListByUser(key, loginUser.getTenantid());
            piuserrolePojo.setItem(item);
            return R.ok(piuserrolePojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @ApiOperation(value = "根据用户获取角色Bill", notes = "根据用户获取角色Bill", produces = "application/json")
    @RequestMapping(value = "/getBillEntityBySelf", method = RequestMethod.GET)
    public R<PiuserrolePojo> getBillEntityBySelf() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            PiuserPojo piuserPojo = this.piuserService.getEntity(loginUser.getUserid());
            PiuserrolePojo piuserrolePojo = new PiuserrolePojo();
            org.springframework.beans.BeanUtils.copyProperties(piuserPojo, piuserrolePojo);
            List<PiuserrolePojo> item = this.piuserroleService.getListByUser(loginUser.getUserid(), loginUser.getTenantid());
            piuserrolePojo.setItem(item);
            return R.ok(piuserrolePojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 根据用户获取权限List
     *
     * @return 权限List
     */
    @ApiOperation(value = "根据用户获取权限List", notes = "根据用户获取权限List", produces = "application/json")
    @RequestMapping(value = "/getPermBySelf", method = RequestMethod.GET)
    public R<List<PipermcodePojo>> getPermBySelf() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.piuserroleService.getPermByUser(loginUser.getUserid(), loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 根据用户获取权限List
     *
     * @return 权限List
     */
    @ApiOperation(value = "根据用户获取权限List,key是token值", notes = "根据用户获取权限List", produces = "application/json")
    @RequestMapping(value = "/updateToken", method = RequestMethod.GET)
    public R<Map<String, Object>> updateToken(String key) {
        return updateToken(key, null);
    }

    @ApiOperation(value = "根据用户获取权限List,key是token值 如果functioncode为空，则返回全部权限,否则返回functioncode下对应的权限", notes = "根据用户获取权限List", produces = "application/json")
    @RequestMapping(value = "/updateTokenByFnCode", method = RequestMethod.GET)
    public R<Map<String, Object>> updateToken(String key, @RequestParam(required = false) String functioncode) {
        LoginUser loginUser = this.tokenService.getLoginUser(key);
        try {
            String tid = loginUser.getTenantid();
            // 存储最终user权限
            Set<String> resultPermsSet;
            // 获取userid关联的全部服务的[已得到的]权限permissions
            Set<String> userHasAllPerms = this.piuserroleService.getPermSetByUser(loginUser.getUserid(), tid);
            // 如果functioncode为空，则返回全部权限,否则返回functioncode下对应的权限
            if (StringUtils.isBlank(functioncode)) {
                resultPermsSet = new HashSet<>(userHasAllPerms);
            } else {
                // 通过FunctionCode和tid查询服务下[可设置]的所有权限
                Set<String> functionPerms = pifunctionpermService.getFunctionPermsByFunctionCode(functioncode, tid);
                // 取交集，只留下同时有的值
                resultPermsSet = new HashSet<>(userHasAllPerms);
                resultPermsSet.retainAll(functionPerms);
            }

            logger.info(loginUser.getUsername() + "获得" + resultPermsSet.size() + "条权限, functioncode=" + functioncode);
            loginUser.setPermissions(resultPermsSet);
            //从redis中获取config内容
            Map<String, String> mapConfig = this.ciconfigService.getMapByTenUi(0, tid);
            // 用户参数
            Map<String, String> mapUserCfg = this.ciconfigService.getMapByUserUi(loginUser.getUserid(), tid);
            for (String mapkey : mapUserCfg.keySet()) {
                mapConfig.put(mapkey, mapUserCfg.get(mapkey));
            }
            logger.info(loginUser.getUsername() + "获得" + mapConfig.size() + "条参数");
            loginUser.setConfigs(mapConfig);
            this.tokenService.delLoginUser(key);
            Map<String, Object> map;
            if (StringUtils.isBlank(functioncode)) {
                map = this.tokenService.createToken(loginUser);
            } else {
                map = this.tokenService.createAppToken(loginUser);
            }
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
