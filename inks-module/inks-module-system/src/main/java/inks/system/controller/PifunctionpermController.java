package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PifunctionpermPojo;
import inks.system.service.PifunctionpermService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 服务权限关系(PiFunctionPerm)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-11 17:34:55
 */
public class PifunctionpermController {
    /**
     * 服务对象
     */
    @Resource
    private PifunctionpermService pifunctionpermService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取服务权限关系详细信息", notes = "获取服务权限关系详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionPerm.List")
    public R<PifunctionpermPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pifunctionpermService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiFunctionPerm.List")
    public R<PageInfo<PifunctionpermPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiFunctionPerm.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.pifunctionpermService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增服务权限关系", notes = "新增服务权限关系", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiFunctionPerm.Add")
    public R<PifunctionpermPojo> create(@RequestBody String json) {
        try {
            PifunctionpermPojo pifunctionpermPojo = JSONArray.parseObject(json, PifunctionpermPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pifunctionpermPojo.setCreateby(loginUser.getRealname());   //创建者
            pifunctionpermPojo.setCreatedate(new Date());   //创建时间
            pifunctionpermPojo.setLister(loginUser.getRealname());   //用户名
            pifunctionpermPojo.setModifydate(new Date());   //修改时间
            pifunctionpermPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.pifunctionpermService.insert(pifunctionpermPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改服务权限关系", notes = "修改服务权限关系", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiFunctionPerm.Edit")
    public R<PifunctionpermPojo> update(@RequestBody String json) {
        try {
            PifunctionpermPojo pifunctionpermPojo = JSONArray.parseObject(json, PifunctionpermPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pifunctionpermPojo.setLister(loginUser.getRealname());   //用户名
            pifunctionpermPojo.setTenantid(loginUser.getTenantid());   //租户id
            pifunctionpermPojo.setModifydate(new Date());   //修改时间
//            pifunctionpermPojo.setAssessor(""); //审核员
//            pifunctionpermPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.pifunctionpermService.update(pifunctionpermPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除服务权限关系", notes = "删除服务权限关系", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionPerm.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pifunctionpermService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据服务号获取权限关系List", notes = "根据服务号获取权限关系List", produces = "application/json")
    @RequestMapping(value = "/getListByFunction", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionPerm.List")
    public R<List<PifunctionpermPojo>> getListByFunction(String key) {
        try {
            return R.ok(this.pifunctionpermService.getListByFunction(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

