package inks.system.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CidgformatPojo;
import inks.system.domain.pojo.CiformcustomPojo;
import inks.system.domain.vo.ColumnReqVO;
import inks.system.service.CidgformatService;
import inks.system.service.CiformcustomService;
import inks.system.utils.PrintColor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.PatternMatchUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 列表格式(Cidgformat)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-20 12:43:04
 */
@RestController
@RequestMapping("SYSM07B9")
@Api(tags = "SYSM07B9:列表格式")
public class SYSM07B9Controller extends CidgformatController {
    /**
     * 服务对象
     */
    @Resource
    private CidgformatService cidgformatService;
    @Resource
    private CiformcustomService ciformcustomService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    // =================拷贝PreAuthorizeAspect.hasPermi方法=================
    // 检查用户是否具有指定权限
    public static boolean hasPermi(LoginUser userInfo, String permission) {
        if (userInfo == null) {
            return false;
        } else if (userInfo.getIsadmin() != null && userInfo.getIsadmin() == 1) {
            return true;
        } else {
            return !CollectionUtils.isEmpty(userInfo.getPermissions()) &&
                    hasPermissions(userInfo.getPermissions(), permission);
        }
    }

    // 检查用户是否具有某一权限
    private static boolean hasPermissions(Collection<String> authorities, String permission) {
        return authorities.stream()
                .filter(x -> x != null && !x.trim().isEmpty())
                .anyMatch(x -> PatternMatchUtils.simpleMatch(x, permission));
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改列表格式", notes = "修改列表格式", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<CidgformatPojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            CidgformatPojo cidgformatPojo = JSONArray.parseObject(json, CidgformatPojo.class);
            CidgformatPojo billEntityByCode = this.cidgformatService.getBillEntityByCode(cidgformatPojo.getFormcode(), loginUser.getUserid(), loginUser.getTenantid());
            // 是默认数据
            // 当前操作是否为管理员
            if (billEntityByCode == null) {
                // 租户在修改，新建专用字典
                cidgformatPojo.setCreateby(loginUser.getRealName());   // 创建者
                cidgformatPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
                cidgformatPojo.setCreatedate(new Date());   // 创建时间
                cidgformatPojo.setLister(loginUser.getRealname());   // 制表
                cidgformatPojo.setListerid(loginUser.getUserid());    // 制表id
                cidgformatPojo.setModifydate(new Date());   //修改时间
                cidgformatPojo.setTenantid(loginUser.getTenantid());   //租户id
                cidgformatPojo.setTenantname(loginUser.getTenantinfo().getTenantname());   //租户id
                cidgformatPojo.setDefmark(0);
                return R.ok(this.cidgformatService.insert(cidgformatPojo));
            } else {
                // 管理员在修改
                cidgformatPojo.setLister(loginUser.getRealname());   // 制表
                cidgformatPojo.setListerid(loginUser.getUserid());    // 制表id
                cidgformatPojo.setModifydate(new Date());   //修改时间
                cidgformatPojo.setTenantid(loginUser.getTenantid());   //租户id
                return R.ok(this.cidgformatService.update(cidgformatPojo));
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param code 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取(个人)列表格式详细信息", notes = "获取列表格式详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByCode", method = RequestMethod.GET)
    public R<CidgformatPojo> getBillEntityByCode(String code) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.cidgformatService.getBillEntityByCode(code, loginUser.getUserid(), loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //初始化列设置/initColumns

    @ApiOperation(value = " 获取(默认的)列表格式详细信息", notes = "获取列表格式详细信息", produces = "application/json")
    @RequestMapping(value = "/getTenBillEntityByCode", method = RequestMethod.GET)
    public R<CidgformatPojo> getTenBillEntityByCode(String code) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cidgformatService.getTenBillEntityByCode(code, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "管理员保存(默认的)列表格式,无则插入;有则修改", notes = "修改列表格式", produces = "application/json")
    @RequestMapping(value = "/updateTen", method = RequestMethod.POST)
    public R<CidgformatPojo> updateTen(@RequestBody String json) {
        try {
            CidgformatPojo cidgformatPojo = JSONArray.parseObject(json, CidgformatPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            if (loginUser.getIsadmin() != 1) {
                return R.fail("非管理员禁止保存");
            }
            CidgformatPojo tenBillEntityByCode = this.cidgformatService.getTenBillEntityByCode(cidgformatPojo.getFormcode(), loginUser.getTenantid());
            if (tenBillEntityByCode != null) {
                cidgformatService.delete(tenBillEntityByCode.getId(), loginUser.getTenantid());
            }
            cidgformatPojo.setLister(loginUser.getRealname());   // 制表
            cidgformatPojo.setListerid(loginUser.getUserid());    // 制表id
            cidgformatPojo.setModifydate(new Date());   //修改时间
            cidgformatPojo.setTenantid(loginUser.getTenantid());   //租户id
            cidgformatPojo.setCreateby(loginUser.getRealname());   // 创建者
            cidgformatPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            cidgformatPojo.setCreatedate(new Date());   // 创建时间
            cidgformatPojo.setDefmark(1);
            return R.ok(this.cidgformatService.insert(cidgformatPojo), "插入成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "初始化列设置", notes = "初始化列设置", produces = "application/json")
    @RequestMapping(value = "/initColumns", method = RequestMethod.POST)
    public R<ColumnReqVO> initColumns(@RequestBody String json) {
        // 获取登录用户信息
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            // 1. 解析前端传入的静态 JSON 数据，转换为 ColumnReqVO 对象
            ColumnReqVO staticColumnData = JSONArray.parseObject(json, ColumnReqVO.class);
            String formCode = staticColumnData.getFormcode(); // 例如：D01M03B1
            String subCode = staticColumnData.getSubcode(); // 部件 code  首字母大写 Th/List/Item
            // 定义【静态/动态JSON】：先赋值为静态JSON
            List<ColumnReqVO.ColumnItem> staticDyItems = staticColumnData.getItem();
            if (StringUtils.isBlank(formCode) || StringUtils.isBlank(subCode) || CollectionUtils.isEmpty(staticDyItems)) {
                throw new IllegalArgumentException("入参有空值");
            }
            // 2. 查询动态JSON(自定义表单配置)，有则覆盖【静态/动态JSON】 /SYSM7B13/getEntityByCode
            CiformcustomPojo ciformcustomPojo = this.ciformcustomService.getEntityByCode(formCode, loginUser.getTenantid());
            if (ciformcustomPojo != null) {
                // frmcontent 格式：{"th":{“type”:0,"content":[{"itemcode":"goodsname","itemname":"货品" ... }]},"list":{...},“item”:[{...}]}
                String frmcontent = ciformcustomPojo.getFrmcontent();
                if (StringUtils.isNotBlank(frmcontent)) { // 判空：frmcontent 是否为空
                    JSONObject jsonObject = JSON.parseObject(frmcontent);
                    if (jsonObject != null) {
                        // 获取 subcode 转小写后的键对应的 JSON 对象
                        JSONObject thJson = jsonObject.getJSONObject(subCode.toLowerCase());
                        if (thJson != null) { // 判空：thJson 是否为空
                            JSONArray contentArray = thJson.getJSONArray("content");
                            if (contentArray != null && !contentArray.isEmpty()) { // 判空：contentArray 是否为空
                                // 覆盖掉【静态/动态JSON】
                                staticDyItems = JSONArray.parseArray(contentArray.toJSONString(), ColumnReqVO.ColumnItem.class);
                            }
                        }
                    }
                }
            }

            // specItems：前端查询的其他字段列表：如SPU、货品、核算分组 需要追加到【静态/动态JSON】
            List<ColumnReqVO.ColumnItem> specItems = staticColumnData.getSpec();

            if (CollectionUtils.isNotEmpty(specItems)) {
                // 追加到staticDyItems
                staticDyItems.addAll(specItems);
            }

            // 3. 从数据库读取【列设置JSON】
            ColumnReqVO dgformatColumnData = null;
            String code = formCode + subCode; // 例如：D01M03B1Th
            CidgformatPojo ciDgFormatDB = this.cidgformatService.getBillEntityByCode(code, loginUser.getUserid(), loginUser.getTenantid());
            if (ciDgFormatDB != null) {
                dgformatColumnData = JSONArray.parseObject(JSON.toJSONString(ciDgFormatDB), ColumnReqVO.class);
            }


            // 4. 脱敏处理：若无权限Mat_Access.Amount，且是敏感列sensitivemark等于1，则从【静态/动态JSON】移除敏感列
            String sensitiveCode = staticColumnData.getSensitivecode(); // 如 Mat_Access.Amount
            if (!hasPermi(loginUser, sensitiveCode)) {
                staticDyItems.removeIf(item -> Objects.equals(item.getSensitivemark(), 1));
            }

            // 5. 如果【列设置JSON】有值，则匹配并按照 dgformatColumnData.getItem() 的顺序重组 staticDyItems
            if (dgformatColumnData != null) {
                // 1. 将静态列表按 itemcode 建立映射，方便快速查找
                Map<String, ColumnReqVO.ColumnItem> staticMap = staticDyItems.stream()
                        .collect(Collectors.toMap(
                                ColumnReqVO.ColumnItem::getItemcode,
                                Function.identity(),
                                (oldVal, newVal) -> oldVal));
                // 2. 将数据库配置按 itemcode 建立映射，用来覆盖静态字段
                Map<String, ColumnReqVO.ColumnItem> dbMap = dgformatColumnData.getItem().stream()
                        .collect(Collectors.toMap(
                                ColumnReqVO.ColumnItem::getItemcode,
                                Function.identity(),
                                (oldVal, newVal) -> oldVal));
                // 3. 先按 dgformatColumnData 顺序，把静态中也存在的列取出并覆盖字段
                List<ColumnReqVO.ColumnItem> resultItems = new ArrayList<>();
                for (ColumnReqVO.ColumnItem dgItem : dgformatColumnData.getItem()) {
                    String code2 = dgItem.getItemcode();
                    if (staticMap.containsKey(code2)) {
                        ColumnReqVO.ColumnItem staticItem = staticMap.get(code2);
                        ColumnReqVO.ColumnItem dbItem = dbMap.get(code2);
                        // 覆盖用户保存过的字段（覆盖 minwidth, displaymark, fixed；rownum排序是后面统一重算）
                        staticItem.setMinwidth(dbItem.getMinwidth());
                        staticItem.setDisplaymark(dbItem.getDisplaymark());
                        staticItem.setFixed(dbItem.getFixed());
                        staticItem.setId(null);
                        staticItem.setPid(null);
                        resultItems.add(staticItem);
                    }
                }
                // 4. 再把静态列表里剩下的列追加到尾部
                Set<String> taken = resultItems.stream()
                        .map(ColumnReqVO.ColumnItem::getItemcode)
                        .collect(Collectors.toSet());
                for (ColumnReqVO.ColumnItem staticItem : staticDyItems) {
                    if (!taken.contains(staticItem.getItemcode())) {
                        resultItems.add(staticItem);
                    }
                }
                // 5. 用新的顺序覆盖，并在后面重新设置 rownum
                staticDyItems = resultItems;
            }


            // 6. 重新设置返回体中列的 rownum，从 0 开始连续递增
            // 如果需要先排序，可按原有逻辑进行排序后再重新设置 rownum，这里直接设置
            for (int i = 0; i < staticDyItems.size(); i++) {
                staticDyItems.get(i).setRownum(i);
            }

            // 7. 更新【静态/动态JSON】 中的列设置
            staticColumnData.setItem(staticDyItems);

            PrintColor.red("initColumns: " + staticColumnData);
            return R.ok(staticColumnData);

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "传入列设置Json: 根据其中formcode加载个人列设置；无则新建；有则删光后新建 个人列设置：where CiDgFormat.Listerid = #{userid} and CiDgFormat.Formcode = #{code}", notes = "", produces = "application/json")
    @RequestMapping(value = "/saveByCode", method = RequestMethod.POST)
    @Transactional
    public R<CidgformatPojo> saveByCode(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String userid = loginUser.getUserid();
        try {
            CidgformatPojo cidgformatPojo = JSONArray.parseObject(json, CidgformatPojo.class);
            String formCode = cidgformatPojo.getFormcode();// 例如：D01M03B1List
            // 根据其中formcode加载个人列设置；无则新建；有则删光后新建   个人列设置：where CiDgFormat.Listerid = #{userid}
            CidgformatPojo cidgformatDB = this.cidgformatService.getEntityByCodeUser(formCode, userid, loginUser.getTenantid());
            if (cidgformatDB != null) {
                // 删光
                cidgformatService.delete(cidgformatDB.getId(), loginUser.getTenantid());
            }
            // 新建
            cidgformatPojo.setCreateby(loginUser.getRealName());   // 创建者
            cidgformatPojo.setCreatebyid(userid);  // 创建者id
            cidgformatPojo.setCreatedate(new Date());   // 创建时间
            cidgformatPojo.setLister(loginUser.getRealname());   // 制表
            cidgformatPojo.setListerid(userid);    // 制表id
            cidgformatPojo.setModifydate(new Date());   //修改时间
            cidgformatPojo.setTenantid(loginUser.getTenantid());   //租户id
            cidgformatPojo.setTenantname(loginUser.getTenantinfo().getTenantname());   //租户id
            cidgformatPojo.setDefmark(0);
            return R.ok(this.cidgformatService.insert(cidgformatPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除(重置)列设置 个人列设置：where CiDgFormat.Listerid = #{userid} and CiDgFormat.Formcode = #{code}", notes = "", produces = "application/json")
    @GetMapping(value = "/deleteByCode")
    public R<String> deleteByCode(String code) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            int delete = this.cidgformatService.deleteByCode(code, loginUser.getUserid(), loginUser.getTenantid());
            return R.ok("删除列设置主表条数: " + delete);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
