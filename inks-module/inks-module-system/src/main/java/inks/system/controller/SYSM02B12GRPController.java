package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PifunctionrptgrpPojo;
import inks.system.service.PifunctionrptgrpService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 服务报表分组数据(PiFunctionRptGrp)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-22 10:31:29
 */
@RestController
@RequestMapping("SYSM02B12GRP")
@Api(tags = "SYSM02B12GRP:服务报表分组数据")
public class SYSM02B12GRPController extends PifunctionrptgrpController {
    @Resource
    private PifunctionrptgrpService pifunctionrptgrpService;
    @Resource
    private TokenService tokenService;


    @ApiOperation(value = "忽略tid,通过functionid按条件分页查询 ", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListByFunctionid", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiFunctionRptGrp.List")
    public R<PageInfo<PifunctionrptgrpPojo>> getPageListByFunctionid(@RequestBody String json, String key) {
        if (StringUtils.isBlank(key)) {
            throw new RuntimeException("未传入服务id");
        }
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("PiFunctionRptGrp.CreateDate");
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        queryParam.setTenantid(loginUser.getTenantid());
        String qpfilter = " and PiFunctionRptGrp.Functionid ='" + key + "' ";
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        return R.ok(this.pifunctionrptgrpService.getPageListByFunctionid(queryParam));
    }

    @ApiOperation(value = "分组全树 传入Parentid", notes = "分组全树", produces = "application/json")
    @GetMapping(value = "/getTreeList")
    @PreAuthorize(hasPermi = "PiFunctionRptGrp.List")
    public R<List<PifunctionrptgrpPojo>> getAllListByPid(String key) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            // 获取所有分组项并构建分组树
            // 获取所有分组项
            List<PifunctionrptgrpPojo> list = this.pifunctionrptgrpService.getList(loginUser.getTenantid());
            // 构建分组树
            List<PifunctionrptgrpPojo> pifunctionrptgrpPojos = buildGroupTree(list, key);
            return R.ok(pifunctionrptgrpPojos);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    private List<PifunctionrptgrpPojo> buildGroupTree(List<PifunctionrptgrpPojo> allGroups, String parentid) {
        List<PifunctionrptgrpPojo> result = new ArrayList<>();
        for (PifunctionrptgrpPojo group : allGroups) {
            if (Objects.equals(group.getParentid(), parentid)) {
                PifunctionrptgrpPojo grpPojo = new PifunctionrptgrpPojo();
                BeanUtils.copyProperties(group, grpPojo);
                grpPojo.setChildren(buildGroupTree(allGroups, group.getId()));
                result.add(grpPojo);
            }
        }
        return result;
    }
}
