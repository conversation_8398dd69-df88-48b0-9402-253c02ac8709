package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CibillcodePojo;
import inks.system.service.CibillcodeService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 单据编码(CiBillCode)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 13:27:43
 */
@RestController
@RequestMapping("SYSM07B02")
public class CibillcodeController {
    /**
     * 服务对象
     */
    @Resource
    private CibillcodeService cibillcodeService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;


    @ApiOperation(value = " 获取Redis缓存中的单据编码", notes = "获取单据编码详细信息", produces = "application/json")
    @RequestMapping(value = "/getCache", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiBillCode.List")
    public R<String> getCache(String code) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String tid = loginUser.getTenantid();
        try {
            String redisKey = "refno:" + code + tid;
            String redisSerialNo = redisService.getCacheObject(redisKey);
            //if (StringUtils.isBlank(redisSerialNo)) {
            //    Date currentDate = new Date();
            //    SerialNoPojo cibillcodeEntity = getEntityByModuleCode(code, tid, 0);
            //     getSerialNo(cibillcodeEntity, prefixDB.length() + 1, tid, currentDate);
            //}
            return R.ok(redisSerialNo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取单据编码详细信息", notes = "获取单据编码详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiBillCode.List")
    public R<CibillcodePojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.cibillcodeService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiBillCode.List")
    public R<PageInfo<CibillcodePojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiBillCode.Modulecode");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.cibillcodeService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增单据编码", notes = "新增单据编码", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiBillCode.Add")
    public R<CibillcodePojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            CibillcodePojo cibillcodePojo = JSONArray.parseObject(json, CibillcodePojo.class);

            cibillcodePojo.setLister(loginUser.getRealname());   //用户名
            if (cibillcodePojo.getTenantid() == null) cibillcodePojo.setTenantid(loginUser.getTenantid());   //租户id
            cibillcodePojo.setModifydate(new Date());   //修改时间
            return R.ok(this.cibillcodeService.insert(cibillcodePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改单据编码", notes = "修改单据编码", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiBillCode.Edit")
    public R<CibillcodePojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            CibillcodePojo cibillcodePojo = JSONArray.parseObject(json, CibillcodePojo.class);

            cibillcodePojo.setLister(loginUser.getRealname());   //用户名
            cibillcodePojo.setModifydate(new Date());   //修改时间
            return R.ok(this.cibillcodeService.update(cibillcodePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除单据编码", notes = "删除单据编码", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiBillCode.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cibillcodeService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiBillCode.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        CibillcodePojo cibillcodePojo = this.cibillcodeService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(cibillcodePojo);

        //从redis中获取XML内容
        String xml = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (xml != null || !xml.equals("") || xml != "") {
            content = xml;
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


}

