package inks.system.controller;

import inks.common.core.domain.R;
import inks.common.security.annotation.PreAuthorize;
import inks.system.domain.pojo.PifunctionweblnkPojo;
import inks.system.service.PifunctionweblnkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Web快捷方式(PiFunctionWebLnk)表控制层
 *
 * <AUTHOR>
 * @since 2023-05-08 14:39:08
 */
@RestController
@RequestMapping("SYSM02B13")
@Api(tags = "SYSM02B13:服务菜单关系Web快捷方式")
public class SYSM02B13Controller extends PifunctionweblnkController {
    @Resource
    private PifunctionweblnkService pifunctionweblnkService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据服务号获取`Web快捷方式`菜单关系List", notes = "根据服务号获取Web快捷方式菜单关系List", produces = "application/json")
    @RequestMapping(value = "/getListByFunction", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionWebLnk.List")
    public R<List<PifunctionweblnkPojo>> getListByFunction(String key) {
        return R.ok(this.pifunctionweblnkService.getListByFunction(key));
    }
}
