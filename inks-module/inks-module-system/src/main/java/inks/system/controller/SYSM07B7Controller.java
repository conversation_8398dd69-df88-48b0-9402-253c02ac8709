package inks.system.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.service.TokenService;
import inks.system.domain.vo.ValidationResponse;
import inks.system.service.CivalidatorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 数据验证(CiValidator)表控制层
 *
 * <AUTHOR>
 * @since 2024-11-15 10:02:13
 */
@RestController
@RequestMapping("SYSM07B7")
@Api(tags = "SYSM07B7:数据验证")
public class SYSM07B7Controller extends CivalidatorController {

    @Resource
    private CivalidatorService civalidatorService;
    @Resource
    private TokenService tokenService;


    @ApiOperation(value = "数据验证", notes = "根据验证编码和数据对象进行验证", produces = "application/json")
    @PostMapping("/validate")
    public R<ValidationResponse> validate(@RequestParam String valicode, @RequestBody Map<String, Object> dataObj) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            return R.ok(civalidatorService.validate(valicode, dataObj, loginUser));

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
