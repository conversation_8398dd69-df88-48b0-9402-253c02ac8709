package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CivalidatorPojo;
import inks.system.service.CivalidatorService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 数据验证(CiValidator)表控制层
 *
 * <AUTHOR>
 * @since 2024-11-15 10:02:13
 */
//@RestController
//@RequestMapping("civalidator")
public class CivalidatorController {

    private final static Logger logger = LoggerFactory.getLogger(CivalidatorController.class);
    @Resource
    private CivalidatorService civalidatorService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = " 获取数据验证详细信息", notes = "获取数据验证详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiValidator.List")
    public R<CivalidatorPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.civalidatorService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiValidator.List")
    public R<PageInfo<CivalidatorPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiValidator.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.civalidatorService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增数据验证", notes = "新增数据验证", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiValidator.Add")
    public R<CivalidatorPojo> create(@RequestBody String json) {
        try {
            CivalidatorPojo civalidatorPojo = JSONArray.parseObject(json, CivalidatorPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            civalidatorPojo.setCreateby(loginUser.getRealName());   // 创建者
            civalidatorPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            civalidatorPojo.setCreatedate(new Date());   // 创建时间
            civalidatorPojo.setLister(loginUser.getRealname());   // 制表
            civalidatorPojo.setListerid(loginUser.getUserid());    // 制表id  
            civalidatorPojo.setModifydate(new Date());   //修改时间
            civalidatorPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.civalidatorService.insert(civalidatorPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改数据验证", notes = "修改数据验证", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiValidator.Edit")
    public R<CivalidatorPojo> update(@RequestBody String json) {
        try {
            CivalidatorPojo civalidatorPojo = JSONArray.parseObject(json, CivalidatorPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            civalidatorPojo.setLister(loginUser.getRealname());   // 制表
            civalidatorPojo.setListerid(loginUser.getUserid());    // 制表id  
            civalidatorPojo.setTenantid(loginUser.getTenantid());   //租户id
            civalidatorPojo.setModifydate(new Date());   //修改时间
//            civalidatorPojo.setAssessor(""); // 审核员
//            civalidatorPojo.setAssessorid(""); // 审核员id
//            civalidatorPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.civalidatorService.update(civalidatorPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除数据验证", notes = "删除数据验证", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiValidator.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.civalidatorService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiValidator.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        CivalidatorPojo civalidatorPojo = this.civalidatorService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(civalidatorPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

