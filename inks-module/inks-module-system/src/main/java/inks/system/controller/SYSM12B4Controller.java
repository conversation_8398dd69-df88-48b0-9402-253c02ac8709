package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PitenantdmsuserPojo;
import inks.system.service.PitenantdmsuserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * DMS租户关系表(PiTenantDmsUser)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@RestController
@RequestMapping("SYSM12B4")
@Api(tags = "SYSM12B4:租户关系表")
public class SYSM12B4Controller extends PitenantdmsuserController {
    @Resource
    private PitenantdmsuserService pitenantdmsuserService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = " 获取DMS租户关系表详细信息", notes = "获取DMS租户关系表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByUserid", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiTenantDmsUser.List")
    public R<PitenantdmsuserPojo> getEntityByUserid(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pitenantdmsuserService.getEntityByUserid(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 获取DMS租户关系表详细信息", notes = "获取DMS租户关系表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityMapByUserid", method = RequestMethod.GET)
    public R<Map<String, Object>> getEntityMapByUserid(String key, String tid) {
        try {

            //LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            PitenantdmsuserPojo entityByUserid = this.pitenantdmsuserService.getEntityByUserid(key, tid);
            Map<String, Object> stringObjectMap = BeanUtils.beanToMap(entityByUserid);
            return R.ok(stringObjectMap);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增DMS(租户-用户)关联
     *
     * @param userid PiDmsUser.userid
     * @return 新增结果
     */
    @ApiOperation(value = " 新增DMS(租户-用户)关联", notes = "新增DMS租户关系表", produces = "application/json")
    @RequestMapping(value = "/createByUserId", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiTenantDmsUser.Add")
    public R<PitenantdmsuserPojo> createByUserId(String userid) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            PitenantdmsuserPojo pitenantdmsuserPojo = new PitenantdmsuserPojo();
            //(租户-用户)关联
            pitenantdmsuserPojo.setUserid(userid);
            pitenantdmsuserPojo.setUsername(loginUser.getUsername());
            pitenantdmsuserPojo.setTenantid(loginUser.getTenantid());
            pitenantdmsuserPojo.setRealname(loginUser.getRealname());
            pitenantdmsuserPojo.setIsadmin(loginUser.getIsadmin());
            if (loginUser.getTenantinfo() != null) {
                pitenantdmsuserPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
                pitenantdmsuserPojo.setDeptcode(loginUser.getTenantinfo().getDeptcode());
                pitenantdmsuserPojo.setDeptid(loginUser.getTenantinfo().getDeptid());
                pitenantdmsuserPojo.setDeptname(loginUser.getTenantinfo().getDeptname());
            }
            pitenantdmsuserPojo.setCreateby(loginUser.getRealName());   // 创建者
            pitenantdmsuserPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            pitenantdmsuserPojo.setCreatedate(new Date());   // 创建时间
            pitenantdmsuserPojo.setLister(loginUser.getRealname());   // 制表
            pitenantdmsuserPojo.setListerid(loginUser.getUserid());    // 制表id
            pitenantdmsuserPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.pitenantdmsuserService.insert(pitenantdmsuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "根据当前用户获得租户关系表", notes = "根据当前用户获得租户关系表", produces = "application/json")
    @RequestMapping(value = "/getListByUser", method = RequestMethod.GET)
    public R<List<PitenantdmsuserPojo>> getListByUser() {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pitenantdmsuserService.getListByUser(loginUser.getUserid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增DMS(租户-用户)关联
     *
     * @param json PiDmsUser.userid
     * @return 新增结果
     */
    @ApiOperation(value = " 新增DMS(租户-用户)关联", notes = "新增DMS租户关系表", produces = "application/json")
    @RequestMapping(value = "/createDmsUser", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiTenantDmsUser.Add")
    public R<PitenantdmsuserPojo> createDmsUser(@RequestBody String json) {
        try {
            PitenantdmsuserPojo pitenantdmsuserPojo = JSONArray.parseObject(json, PitenantdmsuserPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pitenantdmsuserPojo.setTenantid(loginUser.getTenantid());
            if (loginUser.getTenantinfo() != null) {
                pitenantdmsuserPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
                pitenantdmsuserPojo.setDeptcode(loginUser.getTenantinfo().getDeptcode());
                pitenantdmsuserPojo.setDeptid(loginUser.getTenantinfo().getDeptid());
                pitenantdmsuserPojo.setDeptname(loginUser.getTenantinfo().getDeptname());
            }
            pitenantdmsuserPojo.setCreateby(loginUser.getRealName());   // 创建者
            pitenantdmsuserPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            pitenantdmsuserPojo.setCreatedate(new Date());   // 创建时间
            pitenantdmsuserPojo.setLister(loginUser.getRealname());   // 制表
            pitenantdmsuserPojo.setListerid(loginUser.getUserid());    // 制表id
            pitenantdmsuserPojo.setModifydate(new Date());   //修改时间

            //判断是否传入userid;有，修改；无，新增
            PitenantdmsuserPojo pitenantdmsuser = pitenantdmsuserService.createDmsUser(pitenantdmsuserPojo);
            return R.ok(pitenantdmsuser);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
