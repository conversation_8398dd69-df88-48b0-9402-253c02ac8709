package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PifunctionrptgrpPojo;
import inks.system.service.PifunctionrptgrpService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 服务报表分组数据(PiFunctionRptGrp)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-22 10:31:29
 */
//@RestController
//@RequestMapping("pifunctionrptgrp")
public class PifunctionrptgrpController {

    @Resource
    private PifunctionrptgrpService pifunctionrptgrpService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    private final static Logger logger = LoggerFactory.getLogger(PifunctionrptgrpController.class);


    @ApiOperation(value = " 获取服务报表分组数据详细信息", notes = "获取服务报表分组数据详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionRptGrp.List")
    public R<PifunctionrptgrpPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.pifunctionrptgrpService.getEntity(key, loginUser.getTenantid()));
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiFunctionRptGrp.List")
    public R<PageInfo<PifunctionrptgrpPojo>> getPageList(@RequestBody String json) {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("PiFunctionRptGrp.CreateDate");
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        queryParam.setTenantid(loginUser.getTenantid());
        String qpfilter = "";
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        return R.ok(this.pifunctionrptgrpService.getPageList(queryParam));
    }


    @ApiOperation(value = " 新增服务报表分组数据", notes = "新增服务报表分组数据", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiFunctionRptGrp.Add")
    public R<PifunctionrptgrpPojo> create(@RequestBody String json) {
        PifunctionrptgrpPojo pifunctionrptgrpPojo = JSONArray.parseObject(json, PifunctionrptgrpPojo.class);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        pifunctionrptgrpPojo.setCreateby(loginUser.getRealName());   // 创建者
        pifunctionrptgrpPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
        pifunctionrptgrpPojo.setCreatedate(new Date());   // 创建时间
        pifunctionrptgrpPojo.setLister(loginUser.getRealname());   // 制表
        pifunctionrptgrpPojo.setListerid(loginUser.getUserid());    // 制表id
        pifunctionrptgrpPojo.setModifydate(new Date());   //修改时间
        pifunctionrptgrpPojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.pifunctionrptgrpService.insert(pifunctionrptgrpPojo));
    }


    @ApiOperation(value = "修改服务报表分组数据", notes = "修改服务报表分组数据", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiFunctionRptGrp.Edit")
    public R<PifunctionrptgrpPojo> update(@RequestBody String json) {
        PifunctionrptgrpPojo pifunctionrptgrpPojo = JSONArray.parseObject(json, PifunctionrptgrpPojo.class);
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        pifunctionrptgrpPojo.setLister(loginUser.getRealname());   // 制表
        pifunctionrptgrpPojo.setListerid(loginUser.getUserid());    // 制表id
        pifunctionrptgrpPojo.setTenantid(loginUser.getTenantid());   //租户id
        pifunctionrptgrpPojo.setModifydate(new Date());   //修改时间
//            pifunctionrptgrpPojo.setAssessor(""); // 审核员
//            pifunctionrptgrpPojo.setAssessorid(""); // 审核员id
//            pifunctionrptgrpPojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.pifunctionrptgrpService.update(pifunctionrptgrpPojo));
    }


    @ApiOperation(value = "删除服务报表分组数据", notes = "删除服务报表分组数据", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionRptGrp.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.pifunctionrptgrpService.delete(key, loginUser.getTenantid()));
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionRptGrp.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        PifunctionrptgrpPojo pifunctionrptgrpPojo = this.pifunctionrptgrpService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(pifunctionrptgrpPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

