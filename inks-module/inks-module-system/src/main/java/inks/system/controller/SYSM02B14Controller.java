package inks.system.controller;

import inks.common.core.domain.R;
import inks.common.security.annotation.PreAuthorize;
import inks.system.domain.pojo.PifunctionwebnavPojo;
import inks.system.service.PifunctionwebnavService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * web导航(PiFunctionWebNav)表控制层
 *
 * <AUTHOR>
 * @since 2024-03-01 15:36:24
 */
@RestController
@RequestMapping("SYSM02B14")
@Api(tags = "SYSM02B14:web服务导航")
public class SYSM02B14Controller extends PifunctionwebnavController {
    @Resource
    private PifunctionwebnavService pifunctionwebnavService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据服务号获取`Web快捷方式`菜单关系List", notes = "根据服务号获取Web快捷方式菜单关系List", produces = "application/json")
    @RequestMapping(value = "/getListByFunction", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionWebLnk.List")
    public R<List<PifunctionwebnavPojo>> getListByFunction(String key) {
        try {
            return R.ok(this.pifunctionwebnavService.getListByFunction(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
