package inks.system.controller;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import inks.api.feign.UtilsFeignService;
import inks.common.core.domain.FileInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.service.TokenService;
import inks.system.utils.PrintColor;
import inks.system.utils.mysqldump.FileHelper;
import inks.system.utils.mysqldump.JdbcConnection;
import inks.system.utils.mysqldump.MysqlExport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.Connection;
import java.time.Duration;
import java.time.LocalDate;
import java.util.concurrent.TimeUnit;


/**
 * 数据备份
 *
 * <AUTHOR>
 * @since 2021-11-08 13:55:37
 */
@RestController
@RequestMapping("SYSM04B4")
@Api(tags = "SYSM04B4:数据备份")
public class SYSM04B4Controller {
    @Resource
    private TokenService tokenService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Value("${spring.datasource.dynamic.datasource.inkssaas.url:}")
    private String dbUrl;

    @Value("${spring.datasource.dynamic.datasource.inkssaas.username:}")
    private String dbUsername;

    @Value("${spring.datasource.dynamic.datasource.inkssaas.password:}")
    private String dbPassword;
    @Value("${spring.mail.ipAddress}")
    private String ipAddress;
    @Value("${spring.mail.toEmail}")
    private String toEmail;
    @Resource
    private UtilsFeignService utilsFeignService;
    @Resource
    private MailController mailController;

    @ApiOperation(value = " 本机Mysql备份 【发送Feign请求上传minio】 未指定文件夹前缀则放入“通用”文件夹", notes = "Mysql备份", produces = "application/json")
    @RequestMapping(value = "/mysqlBackup", method = RequestMethod.GET)
    public R<String> mysqlBackup(@RequestParam(defaultValue = "通用(未指定前缀)") String uplaodPrefix, @RequestParam(required = false) String zipPassword) {
        return mysqlBackupByUrl(dbUrl, dbUsername, dbPassword, uplaodPrefix, zipPassword);
    }


    @ApiOperation(value = "Mysql备份By连接名和密码【发送Feign请求上传minio】", notes = "Mysql备份", produces = "application/json")
    @RequestMapping(value = "/mysqlBackupByUrl", method = RequestMethod.GET)
    public R<String> mysqlBackupByUrl(String dbUrl, String dbUsername, String dbPassword, String uplaodPrefix, String zipPassword) {
        // 定义锁的名称和锁过期时间3000秒
        String lockName = "mysql_backup_lock";
        long expireTime = 3000000L;
        // 尝试获取锁
        Boolean success = redisTemplate.opsForValue().setIfAbsent(lockName, "locked", Duration.ofMillis(expireTime));
        if (Boolean.FALSE.equals(success)) {
            return R.fail("有其他备份任务正在进行，请稍后再试。");
        }
        String exportFilePath = null; // 将exportFilePath声明在try块外部
        long startTime = System.currentTimeMillis(); // 记录整个方法开始时间
        try {
            String jdbcUrl = dbUrl + "&user=" + dbUsername + "&password=" + dbPassword;
            PrintColor.red("本次备份数据库地址:" + jdbcUrl);
            Connection connection = JdbcConnection.getConnection(jdbcUrl);
            // 保存到当前工作目录
            String currentWorkingDirectory = System.getProperty("user.dir");
            PrintColor.green("------------------成功建立连接,开始备份-------------------");

            // 备份操作耗时
            long backupStartTime = System.currentTimeMillis();
            MysqlExport mysqlExport = new MysqlExport(connection, currentWorkingDirectory);
            // 导出数据库到文件  .zip文件
            exportFilePath = mysqlExport.export(zipPassword);
            long backupEndTime = System.currentTimeMillis();
            PrintColor.red("备份完成，耗时：" + (backupEndTime - backupStartTime) + " 毫秒");

            // 上传操作耗时
            long uploadStartTime = System.currentTimeMillis();
            OkHttpClient client = new OkHttpClient.Builder()
                    .readTimeout(5, TimeUnit.MINUTES)
                    .build();
            // 获取文件大小 单位字节转MB
            long fileSize = Files.size(Paths.get(exportFilePath));
            String fileSizeInMB = String.format("%.2f", fileSize / 1024.0 / 1024.0);
            PrintColor.red("备份文件大小：" + fileSizeInMB + " MB");
            PrintColor.red("上传開始 文件地址exportFilePath:" + exportFilePath);


            // 获取星期几的英文全称 例如：星期一 Monday 星期二 Tuesday 星期三 Wednesday 星期四 Thursday 星期五 Friday 星期六 Saturday 星期日 Sunday
            String objectname = LocalDate.now().getDayOfWeek().name();
            // 上传文件到minio 自定义前缀 如 n001  则存到minio地址为 http://dev.inksyun.com:9080/utils/mysql/n001/W112.zip
            R<FileInfo> fileInfoR = utilsFeignService.uploadByPath(exportFilePath, "utils", "mysql/" + uplaodPrefix, objectname, "minio", "");//token不需要传
            FileInfo fileInfo = fileInfoR.getData();
            PrintColor.red("上传完成：" + fileInfo);
            String fileUrl = fileInfo.getFileurl();
            long uploadEndTime = System.currentTimeMillis();
            PrintColor.red("上传完成，耗时：" + (uploadEndTime - uploadStartTime) + " 毫秒");

            // 发送邮件操作耗时
            long mailStartTime = System.currentTimeMillis();
            String[] urlParts = dbUrl.split("//"); // 拆分URL
            String dbInfo = urlParts[1].split("\\?")[0]; // 提取主机名、端口号和数据库名部分
            String subject = ipAddress + ": Mysql备份--" + dbInfo;
            String content = "<html>" +
                    "<body style='font-family: Arial, sans-serif;'>" +
                    "<h2 style='color: #2E8B57;'>MySQL备份通知</h2>" +
                    "<p>您好，</p>" +
                    "<p style='font-size: 14px;'>MySQL备份已经成功完成，详情如下：</p>" +
                    "<table style='border-collapse: collapse; width: 100%; font-size: 14px;'>" +
                    "<tr>" +
                    "<td style='border: 1px solid #dddddd; padding: 8px; background-color: #f9f9f9;'>数据库地址：</td>" +
                    "<td style='border: 1px solid #dddddd; padding: 8px;'>" + dbInfo + "</td>" +
                    "</tr>" +
                    "<tr>" +
                    "<td style='border: 1px solid #dddddd; padding: 8px; background-color: #f9f9f9;'>文件名：</td>" +
                    "<td style='border: 1px solid #dddddd; padding: 8px;'>" + exportFilePath + "</td>" +
                    "</tr>" +
                    "<tr>" +
                    "<td style='border: 1px solid #dddddd; padding: 8px; background-color: #f9f9f9;'>文件大小：</td>" +
                    "<td style='border: 1px solid #dddddd; padding: 8px;'>" + fileSizeInMB + " MB</td>" +
                    "</tr>" +
                    "<tr>" +
                    "<td style='border: 1px solid #dddddd; padding: 8px; background-color: #f9f9f9;'>Minio地址：</td>" +
                    "<td style='border: 1px solid #dddddd; padding: 8px;'><a href='" + fileUrl + "'>" + fileUrl + "</a></td>" +
                    "</tr>" +
                    "</table>" +
                    "<br>" +
                    "<p style='font-size: 12px; color: #999999;'>此邮件为自动发送，请勿回复。</p>" +
                    "</body>" +
                    "</html>";


            mailController.sendEmail(toEmail, subject, content);
            long mailEndTime = System.currentTimeMillis();
            PrintColor.zi("邮件发送成功，耗时：" + (mailEndTime - mailStartTime) + " 毫秒");

            String resultMsg = "备份完成，耗时：" + (backupEndTime - backupStartTime) + " 毫秒"
                    + "\n\n文件大小：" + fileSizeInMB + " MB"
                    + "\n\nminio地址为：" + fileUrl
                    + "\n\n本次备份数据库地址:" + jdbcUrl;
            PrintColor.zi(resultMsg);
            return R.ok(resultMsg);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        } finally {
            // 释放锁
            redisTemplate.delete(lockName);
            // 删除备份文件
            if (exportFilePath != null) {
                FileHelper.delete(exportFilePath);
            }
            long endTime = System.currentTimeMillis();
            PrintColor.red("整个备份任务耗时：" + (endTime - startTime) + " 毫秒");
        }
    }


    @ApiOperation(value = "Mysql备份By连接名和密码【暂未使用：发送http请求上传minio】", notes = "Mysql备份", produces = "application/json")
    @RequestMapping(value = "/mysqlBackupByUrl_Http", method = RequestMethod.GET)
    public R<String> mysqlBackupByUrl_Http(String dbUrl, String dbUsername, String dbPassword) {
        // 定义锁的名称和锁过期时间3000秒
        String lockName = "mysql_backup_lock";
        long expireTime = 3000000L; //
        // 尝试获取锁
        Boolean success = redisTemplate.opsForValue().setIfAbsent(lockName, "locked", Duration.ofMillis(expireTime));
        if (!success) {
            return R.fail("有其他备份任务正在进行，请稍后再试。");
        }
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            // 建立(要备份)数据库连接
//            Connection connection = JdbcConnection.getConnection("192.168.99.111:53308", "inkssaas", "root", "asd@123456");
//            String jdbcUrl = "******************************************************************************************************************************************************************************************************************************";
            String jdbcUrl = dbUrl + "&user=" + dbUsername + "&password=" + dbPassword;
            Connection connection = JdbcConnection.getConnection(jdbcUrl);
            // 创建导出对象，传入参数：数据库连接对象，要导出的数据库名，导出的路径
            // 保存到当前工作目录
            String currentWorkingDirectory = System.getProperty("user.dir");
            MysqlExport mysqlExport = new MysqlExport(connection, currentWorkingDirectory);
            // 导出数据库到文件
            String exportFilePath = mysqlExport.export(null);
            // 上传.zip压缩文件到minio 设置响应等待时间为5分钟
            OkHttpClient client = new OkHttpClient.Builder()
                    .readTimeout(5, TimeUnit.MINUTES)
                    .build();
            // 创建请求体
            RequestBody requestBody = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("filepath", exportFilePath)
                    .addFormDataPart("bucket", "utils")
                    .addFormDataPart("dir", "mysql")
                    .addFormDataPart("osstype", "minio")
                    .build();
            // 构建请求
            Request request = new Request.Builder()
                    .url("http://dev.inksyun.com:31080/utils/D96M16B1/uploadByPath")
                    .headers(Headers.of("Authorization", "bcdb"))
                    .post(requestBody)
                    .build();
            // 发送请求并获取响应
            Response response = client.newCall(request).execute();
            // 处理响应结果
            String fileUrl = "";
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode responseJson = objectMapper.readTree(responseBody);
                fileUrl = responseJson.get("data").get("fileurl").asText();
                System.out.println("fileUrl = " + fileUrl);
            } else {
                throw new IOException("Unexpected code " + response);
            }
            // 上传完成了把 .zip压缩文件也删除了
            FileHelper.delete(exportFilePath);
            System.out.println("备份成功,文件名为(已删除):" + exportFilePath + "\n\nminio地址为：" + fileUrl);
            return R.ok("备份成功,文件名为(已删除):" + exportFilePath + "\n\nminio地址为：" + fileUrl);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        } finally {
            // 释放锁
            redisTemplate.delete(lockName);
        }
    }


    @ApiOperation(value = "上传Mysql备份文件By路径", notes = "Mysql备份", produces = "application/json")
    @RequestMapping(value = "/uploadMysql200M", method = RequestMethod.GET)
    public R<String> uploadMysql200M(String path) throws IOException {

        // 上传.zip压缩文件到minio 设置响应等待时间为5分钟
        OkHttpClient client = new OkHttpClient.Builder()
                .readTimeout(5, TimeUnit.MINUTES)
                .build();
        // 创建请求体
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("filepath", path)
                .addFormDataPart("bucket", "utils")
                .addFormDataPart("dir", "mysql")
                .addFormDataPart("osstype", "minio")
                .build();
        // 构建请求
        Request request = new Request.Builder()
                .url("http://dev.inksyun.com:31080/utils/D96M16B1/uploadByPath")
                .headers(Headers.of("Authorization", "bcdb"))
                .post(requestBody)
                .build();
        // 发送请求并获取响应
        Response response = client.newCall(request).execute();
        // 处理响应结果
        String fileUrl = "";
        if (response.isSuccessful()) {
            String responseBody = response.body().string();
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode responseJson = objectMapper.readTree(responseBody);
            fileUrl = responseJson.get("data").get("fileurl").asText();
            System.out.println("fileUrl = " + fileUrl);
        } else {
            throw new IOException("Unexpected code " + response);
        }
        System.out.println("备份成功,文件名为:" + path + "\n\nminio地址为：" + fileUrl);
        return R.ok("备份成功,文件名为:" + path + "\n\nminio地址为：" + fileUrl);
    }
}
