package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import inks.common.core.domain.*;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiloginlogPojo;
import inks.system.service.CiloginlogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 登录日志(CiLoginLog)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 13:56:40
 */
@RestController
@RequestMapping("SYSM08B1")
@Api(tags = "SYSM08B1:登录日志")
public class SYSM08B1Controller extends CiloginlogController {

    @Resource
    private CiloginlogService ciloginlogService;

    @Resource
    private TokenService tokenService;
    @Resource
    private JavaMailSender javaMailSender;

    @Value("${spring.mail.toEmail}")
    private String toEmail;

    @Value("${spring.mail.ipAddress}")
    private String ipAddress;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    //将【统计后】的登录日志List<Map<String, Object>>转换为html格式,便于预览
    private static String listMapToHtml(String startDateFormat, String endDateFormat, List<Map<String, Object>> loginLogList) {
        // 生成每个admin用户登录情况的HTML表格
        StringBuilder tableContent = new StringBuilder();
        tableContent.append("<table style='border-collapse: collapse; width: 100%;'>");
        tableContent.append("<tr style='background-color: #f2f2f2;'><th style='padding: 8px; text-align: left;'>用户名</th><th style='padding: 8px; text-align: left;'>登录账号</th><th style='padding: 8px; text-align: left;'>登录成功次数</th><th style='padding: 8px; text-align: left;'>登录失败次数</th><th style='padding: 8px; text-align: left;'>最多登录地点</th><th style='padding: 8px; text-align: left;'>浏览器名称</th><th style='padding: 8px; text-align: left;'>最近访问时间</th><th style='padding: 8px; text-align: left;'>租户名称</th></tr>");
        boolean isAlternateRow = false;
        for (Map<String, Object> loginLog : loginLogList) {
            tableContent.append("<tr style='background-color: ").append(isAlternateRow ? "#ffffff;" : "#f9f9f9;").append("'>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.get("用户名")).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.get("登录账号")).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.get("登录成功次数")).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.get("登录失败次数")).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.get("登录地点")).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.get("浏览器名称")).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.get("最近访问时间")).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.get("租户名称")).append("</td>");
            tableContent.append("</tr>");
            isAlternateRow = !isAlternateRow; // 切换交替行的背景颜色
        }
        tableContent.append("</table>");
        // 构造完整的邮件内容
        String content =
                "<h2>统计时间段内每个用户登录情况(" + startDateFormat + "~" + endDateFormat + ")</h2>" +
                        "<div style='margin-top: 10px;'>" +
                        tableContent +
                        "</div>";
        return content;
    }

    //将原始的登录日志List<CiloginlogPojo>转换为html格式,便于预览
    private static String listToHtml(String startDateFormat, String endDateFormat, List<CiloginlogPojo> allLoginLogList) {
        // 生成每个admin用户登录情况的HTML表格
        StringBuilder tableContent = new StringBuilder();
        tableContent.append("<table style='border-collapse: collapse; width: 100%;'>");
        tableContent.append("<tr style='background-color: #f2f2f2;'><th style='padding: 8px; text-align: left;'>ID</th><th style='padding: 8px; text-align: left;'>用户ID</th><th style='padding: 8px; text-align: left;'>登录号</th><th style='padding: 8px; text-align: left;'>中文名</th><th style='padding: 8px; text-align: left;'>主机IP</th><th style='padding: 8px; text-align: left;'>主机地址</th><th style='padding: 8px; text-align: left;'>浏览器名称</th><th style='padding: 8px; text-align: left;'>操作系统</th><th style='padding: 8px; text-align: left;'>登录/登出</th><th style='padding: 8px; text-align: left;'>登录状态</th><th style='padding: 8px; text-align: left;'>访问时间</th><th style='padding: 8px; text-align: left;'>租户</th></tr>");
        boolean isAlternateRow = false;
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (CiloginlogPojo loginLog : allLoginLogList) {
            tableContent.append("<tr style='background-color: ").append(isAlternateRow ? "#ffffff;" : "#f9f9f9;").append("'>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.getId()).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.getUserid()).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.getUsername()).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.getRealname()).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.getIpaddr()).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.getLoginlocation()).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.getBrowsername()).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.getHostsystem()).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.getDirection()).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.getLoginstatus() == 0 ? "成功" : "失败").append("</td>");
//            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.getLoginmsg()).append("</td>");   <th style='padding: 8px; text-align: left;'>操作信息</th>
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(formatter.format(loginLog.getLogintime())).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.getTenantname()).append("</td>");
            tableContent.append("</tr>");
            isAlternateRow = !isAlternateRow; // 切换交替行的背景颜色
        }
        tableContent.append("</table>");
        // 构造完整的邮件内容(解决中文乱码)
        String content =
                "<!DOCTYPE html>" +
                        "<html lang=\"en\">" +
                        "<head>" +
                        "    <meta charset=\"UTF-8\">" +
                        "    <title>Log</title>" +
                        "</head>" +
                        "<body>" +
                        "  <h2>统计时间段内所有用户登录明细(" + startDateFormat + "~" + endDateFormat + ")</h2>" +
                        "  <div style='margin-top: 10px;'>" +
                        tableContent +
                        "  </div>" +
                        "</body>" +
                        "</html>";
        return content;
    }

    @ApiOperation(value = "根据时间段删除登录日志  指定租户,不传tid则删所有租户", notes = "", produces = "application/json")
    @RequestMapping(value = "/deleteByTime", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiLoginLog.Delete")
    public R<String> deleteByTime(@RequestBody String json, @RequestParam(required = false) String tid) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getDateRange() == null) {
                throw new Exception("DateRange时间范围不能为空");
            }
            if (!"default".equals(loginUser.getTenantid())) {
                throw new Exception("非系统管理员");
            }
            queryParam.setTenantid(tid);
            int i = this.ciloginlogService.deleteByTime(queryParam);
            return R.ok("成功删除" + i + "行日志数据");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按日期获得登录记录数", notes = "按日期获得登录记录数", produces = "application/json")
    @RequestMapping(value = "/getCountListByPro", method = RequestMethod.POST)
    public R<List<ChartPojo>> getCountListByPro(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiLoginLog.LoginTime");
            if (queryParam.getDateRange().getStartDate() == null)
                queryParam.getDateRange().setStartDate(new Date());
            if (queryParam.getDateRange().getEndDate() == null)
                queryParam.getDateRange().setEndDate(new Date());

//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            queryParam.setTenantid(loginUser.getTenantid());
//            queryParam.setFilterstr(" and Userid='" + loginUser.getUserid() + "'");
            return R.ok(this.ciloginlogService.getCountListByPro(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按日期获得登录记录数", notes = "按日期获得登录记录数", produces = "application/json")
    @RequestMapping(value = "/getCountListByCity", method = RequestMethod.POST)
    public R<List<ChartPojo>> getCountListByCity(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiLoginLog.LoginTime");
            if (queryParam.getDateRange().getStartDate() == null)
                queryParam.getDateRange().setStartDate(new Date());
            if (queryParam.getDateRange().getEndDate() == null)
                queryParam.getDateRange().setEndDate(new Date());

//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            queryParam.setTenantid(loginUser.getTenantid());
//            queryParam.setFilterstr(" and Userid='" + loginUser.getUserid() + "'");
            return R.ok(this.ciloginlogService.getCountListByCity(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "通过租户id查询统计后租户下登录日志", notes = "通过租户id查询租户下登录日志", produces = "application/json")
    @RequestMapping(value = "/getCountPageListByTen", method = RequestMethod.POST)
    public R<List<Map<String, Object>>> getCountPageListByTen(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiLoginLog.LoginTime");
            //不传时间默认查询最近7天
            Date endDate = new Date();
            Date startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);
            if (queryParam.getDateRange() == null) {
                DateRange dateRange = new DateRange("LoginTime", startDate, endDate);
                queryParam.setDateRange(dateRange);
            }

            queryParam.setTenantid(loginUser.getTenantid());
            //queryParam.setFilterstr(" and Userid='" + loginUser.getUserid() + "'");
            return R.ok(this.ciloginlogService.getCountPageListByTen(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "租户下 查询每个用户登录排行 scope=0本月，1近7天", notes = "", produces = "application/json")
    @RequestMapping(value = "/getCountSuccessByUser", method = RequestMethod.GET)
    public R<List<Map<String, Object>>> getCountSuccessByUser(@RequestParam(defaultValue = "1") Integer scope) {
        try {
            Date endDate = new Date();
            Date startDate;

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endDate);

            if (scope == 0) { // 本月
                startDate = DateUtils.truncate(calendar.getTime(), Calendar.MONTH);
            } else { // 近7天
                calendar.add(Calendar.DAY_OF_MONTH, -7);
                startDate = calendar.getTime();
            }

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String tenantId = loginUser.getTenantid();

            return R.ok(ciloginlogService.getCountSuccessByUser(startDate, endDate, tenantId));
        } catch (Exception e) {
            return R.fail("获取用户登录排行失败：" + e.getMessage());
        }
    }

    /**
     * @return R<List < Map < Object>>>
     * @Description 统计时间段内每个admin用户登录情况
     * <AUTHOR>
     * @param[1] startDate
     * @param[2] endDate
     * @time 2023/5/24 14:15
     */
    @ApiOperation(value = "统计时间段内每个admin用户登录情况", notes = "统计时间段内每个admin用户登录情况", produces = "application/json")
    @RequestMapping(value = "/getEveryAdminLogin", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "CiLoginLog.List")
    public R<List<Map<String, Object>>> getEveryAdminLogin(String startDate, String endDate) {
        try {

//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date startdate = formatter.parse(startDate);
            Date enddate = formatter.parse(endDate);
            return R.ok(this.ciloginlogService.getEveryAdminLogin(startdate, enddate));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "统计时间段内每个普通用户登录情况（全租户）", notes = "统计时间段内每个admin用户登录情况", produces = "application/json")
    @RequestMapping(value = "/getEveryUserLogin", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "CiLoginLog.List")
    public R<List<Map<String, Object>>> getEveryUserLogin(String startDate, String endDate) {
        try {

//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date startdate = formatter.parse(startDate);
            Date enddate = formatter.parse(endDate);
            return R.ok(this.ciloginlogService.getEveryUserLogin(startdate, enddate));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "统计时间段内每个租户登录情况", notes = "统计时间段内每个admin用户登录情况", produces = "application/json")
    @RequestMapping(value = "/getEveryTenantLogin", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "CiLoginLog.List")
    public R<List<Map<String, Object>>> getEveryTenantLogin(String startDate, String endDate) {
        try {

//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date startdate = formatter.parse(startDate);
            Date enddate = formatter.parse(endDate);
            return R.ok(this.ciloginlogService.getEveryTenantLogin(startdate, enddate));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @Description 发送最近一周的admin用户登录情况到管理员邮箱
     * <AUTHOR>
     * @time 2023/5/24 15:34
     */
    @Scheduled(cron = "0 0 0 ? * MON") // 每周一0点执行
    @RequestMapping(value = "/sendEmailAdminLogin", method = RequestMethod.GET)
    public R<String> sendEmailAdminLogin() throws MessagingException {
        // 定义锁的名称和锁过期时间3000秒
        String lockName = "sendEmailAdminLogin_lock";
        long expireTime = 3000000L; //
        // 尝试获取锁
        Boolean success = redisTemplate.opsForValue().setIfAbsent(lockName, "locked", Duration.ofMillis(expireTime));
        if (!success) {
            return R.fail("有其他登录邮件任务正在进行，请稍后再试。");
        }
        try {
            // startDate为当前时间的前一周，endDate为当前时间
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date endDate = new Date();
            Date startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);
            String startDateFormat = formatter.format(startDate);
            String endDateFormat = formatter.format(endDate);
            //发送邮件(最近一周的admin用户登录情况)
            String subject = ipAddress + ": Admin登录日志-" + endDateFormat;
            // 获得统计后的登录日志并转为html
            List<Map<String, Object>> everyAdminLogin = this.ciloginlogService.getEveryAdminLogin(startDate, endDate);
            String content = listMapToHtml(startDateFormat, endDateFormat, everyAdminLogin);
            // 获取所有登录日志并转为html并上传为html文件
            List<CiloginlogPojo> allAdminLogin = this.ciloginlogService.getAllAdminLogin(startDate, endDate);
            String allContent = listToHtml(startDateFormat, endDateFormat, allAdminLogin);
            String fileurl = null;
            try { // 可能上传失败
                fileurl = stringToHtml(allContent);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            if (fileurl != null) {
                content += "<h2>用户登录日志详情：<a href=\"" + fileurl + "\" style=\"text-decoration: none; color: #3366cc;\">点击查看</a></h2>";
            } else {
                content += "<h2>用户登录日志详情：上传失败</h2>";
            }
            // 发送邮件
            sendEmail(toEmail, subject, content);
            System.out.println("\u001B[31m============定时任务邮件已发送(Admin登录日志)给：" + toEmail + "==========\u001B[0m");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 释放锁
            redisTemplate.delete(lockName);
        }
        return R.ok("admin登录邮件发送成功。");
    }

    /**
     * @Description 发送最近一周每个User用户登录情况的邮件
     * <AUTHOR>
     * @time 2023/5/24 15:34
     */
    @Scheduled(cron = "2 0 0 ? * MON") // 每周一0点执行
    @RequestMapping(value = "/sendEmailUserLogin", method = RequestMethod.GET)
    public R<String> sendEmailUserLogin() throws MessagingException {
        // 定义锁的名称和锁过期时间3000秒
        String lockName = "sendEmailUserLogin_lock";
        long expireTime = 3000000L; //
        // 尝试获取锁
        Boolean success = redisTemplate.opsForValue().setIfAbsent(lockName, "locked", Duration.ofMillis(expireTime));
        if (!success) {
            return R.fail("有其他登录邮件任务正在进行，请稍后再试。");
        }
        try {
            // startDate为当前时间的前一周，endDate为当前时间
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date endDate = new Date();
            Date startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);
            String startDateFormat = formatter.format(startDate);
            String endDateFormat = formatter.format(endDate);
            //发送邮件(最近一周user用户登录情况)
            String subject = ipAddress + ": 用户登录日志-" + endDateFormat;
            // 获取统计后的登录日志并转为html
            List<Map<String, Object>> everyUserLogin = this.ciloginlogService.getEveryUserLogin(startDate, endDate);
            String content = listMapToHtml(startDateFormat, endDateFormat, everyUserLogin);
            // 获取所有登录日志并转为html并上传为html文件
            List<CiloginlogPojo> allUserLogin = this.ciloginlogService.getAllUserLogin(startDate, endDate);
            String allContent = listToHtml(startDateFormat, endDateFormat, allUserLogin);
            String fileurl = null;
            try { // 可能上传失败
                fileurl = stringToHtml(allContent);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            if (fileurl != null) {
                content += "<h2>用户登录日志详情：<a href=\"" + fileurl + "\" style=\"text-decoration: none; color: #3366cc;\">点击查看</a></h2>";
            } else {
                content += "<h2>用户登录日志详情：上传失败</h2>";
            }
            // 发送邮件
            sendEmail(toEmail, subject, content);

            System.out.println("\u001B[31m============定时任务邮件已发送(User登录日志)给：" + toEmail + "==========\u001B[0m");
        } catch (MessagingException e) {
            throw new RuntimeException(e);
        } finally {
            // 释放锁
            redisTemplate.delete(lockName);
        }
        return R.ok("user登录邮件发送成功。");
    }

    /**
     * @Description 发送最近一周每个租户登录情况的邮件
     * <AUTHOR>
     * @time 2023/5/24 15:34
     */
    @Scheduled(cron = "1 0 0 ? * MON") // 每周一0点执行
    @RequestMapping(value = "/sendEmailTenantLogin", method = RequestMethod.GET)
    public R<String> sendEmailTenantLogin() throws MessagingException {
        // 定义锁的名称和锁过期时间3000秒
        String lockName = "sendEmailTenantLogin_lock";
        long expireTime = 3000000L; //
        // 尝试获取锁
        Boolean success = redisTemplate.opsForValue().setIfAbsent(lockName, "locked", Duration.ofMillis(expireTime));
        if (Boolean.FALSE.equals(success)) {
            return R.fail("有其他登录邮件任务正在进行，请稍后再试。");
        }
        try {
            // startDate为当前时间的前一周，endDate为当前时间
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date endDate = new Date();
            Date startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);
            String startDateFormat = formatter.format(startDate);
            String endDateFormat = formatter.format(endDate);
            //发送邮件(最近一周每个租户登录情况)
            String subject = ipAddress + ": 租户登录日志-" + endDateFormat;
            // 获取统计后的登录日志并转为html
            List<Map<String, Object>> everyTenantLogin = this.ciloginlogService.getEveryTenantLogin(startDate, endDate);
            String content = this.listMapToHtmlByTen(startDateFormat, endDateFormat, everyTenantLogin);
            // 获取所有登录日志并转为html并上传为html文件
            List<CiloginlogPojo> allUserLogin = this.ciloginlogService.getAllUserLogin(startDate, endDate);
            String allContent = listToHtml(startDateFormat, endDateFormat, allUserLogin);
            String fileurl = null;
            try { // 可能上传失败
                fileurl = stringToHtml(allContent);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            if (fileurl != null) {
                content += "<h2>用户登录日志详情：<a href=\"" + fileurl + "\" style=\"text-decoration: none; color: #3366cc;\">点击查看</a></h2>";
            } else {
                content += "<h2>用户登录日志详情：上传失败</h2>";
            }
            // 发送邮件
            sendEmail(toEmail, subject, content);

            System.out.println("\u001B[31m============定时任务邮件已发送(Tenant登录日志)给：" + toEmail + "==========\u001B[0m");
        } catch (MessagingException e) {
            throw new RuntimeException(e);
        } finally {
            // 释放锁
            redisTemplate.delete(lockName);
        }
        return R.ok("租户登录邮件发送成功。");
    }

    private String listMapToHtmlByTen(String startDateFormat, String endDateFormat, List<Map<String, Object>> everyTenantLogin) {
        // 生成每个租户登录情况的HTML表格
        StringBuilder tableContent = new StringBuilder();
        tableContent.append("<table style='border-collapse: collapse; width: 100%;'>");
        tableContent.append("<tr style='background-color: #f2f2f2;'><th style='padding: 8px; text-align: left;'>租户名称</th><th style='padding: 8px; text-align: left;'>登录成功次数</th><th style='padding: 8px; text-align: left;'>登录失败次数</th><th style='padding: 8px; text-align: left;'>最多登录地点</th><th style='padding: 8px; text-align: left;'>浏览器名称</th><th style='padding: 8px; text-align: left;'>最近访问时间</th><th style='padding: 8px; text-align: left;'>租户id</th></tr>");
        boolean isAlternateRow = false;
        for (Map<String, Object> loginLog : everyTenantLogin) {
            tableContent.append("<tr style='background-color: ").append(isAlternateRow ? "#ffffff;" : "#f9f9f9;").append("'>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.get("租户名称")).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.get("登录成功次数")).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.get("登录失败次数")).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.get("登录地点")).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.get("浏览器名称")).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.get("最近访问时间")).append("</td>");
            tableContent.append("<td style='padding: 8px; text-align: left;'>").append(loginLog.get("租户id")).append("</td>");
            tableContent.append("</tr>");
            isAlternateRow = !isAlternateRow; // 切换交替行的背景颜色
        }
        tableContent.append("</table>");
        // 构造完整的邮件内容
        String content =
                "<h2>统计时间段内每个租户登录情况(" + startDateFormat + "~" + endDateFormat + ")</h2>" +
                        "<div style='margin-top: 10px;'>" +
                        tableContent +
                        "</div>";
        return content;
    }

    @ApiOperation(value = "测试发送邮件,直接调用即可", notes = "发送邮件", produces = "application/json")
    @RequestMapping(value = "/sendEmail", method = RequestMethod.POST)
    public R<String> sendEmail() {
        try {
            //模拟设置参数
            String to = "<EMAIL>";
            String subject = "测试邮件";
            String content = "测试内容";
            sendEmail(to, subject, content);
            return R.ok("发送成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * @Description 发邮件
     * <AUTHOR>
     * @param[1] to 收件人邮箱
     * @param[2] subject 主题
     * @param[3] content 内容
     * @time 2023/5/24 22:08
     */
    public void sendEmail(String to, String subject, String content) throws MessagingException {
        MimeMessage message = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
        helper.setFrom("<EMAIL>");
        helper.setTo(to);
        helper.setSubject(subject);
        //String content111 = "<h1>一号html</h1><h3>3号html</h3>";
        helper.setText(content, true); // true表示使用HTML格式
        javaMailSender.send(message);
    }

    /**
     * @Description 字符串转为html并上传
     * <AUTHOR>
     * @param[1] content
     * @time 2023/5/24 16:25
     */
    @RequestMapping(value = "/stringToHtml", method = RequestMethod.GET)
    public String stringToHtml(String content) {
        // 进行文件上传
        try {
            OkHttpClient client = new OkHttpClient();
            // 构建请求体
            MultipartBody requestBody = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("content", content)
                    .addFormDataPart("bucket", "log")
                    .addFormDataPart("dir", "loginlog")
                    .build();
            // 构建请求对象
            Request request = new Request.Builder()
                    .url("http://dev.inksyun.com:31080/utils/D96M16B1/uploadHtml")
//                    .url("http://api.inkstech.com:8080/utils/D96M16B1/uploadHtml")
//                    .headers(Headers.of("Authorization", "bcdb"))
                    .post(requestBody)
                    .build();
            // 发送请求并获取响应
            Response response = client.newCall(request).execute();
            // 处理响应结果
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode responseJson = objectMapper.readTree(responseBody);
                String fileUrl = responseJson.get("data").get("fileurl").asText();
                return fileUrl;
            } else {
                throw new IOException("Unexpected code " + response);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
}
