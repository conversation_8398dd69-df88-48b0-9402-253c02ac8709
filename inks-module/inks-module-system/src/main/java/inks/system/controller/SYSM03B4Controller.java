package inks.system.controller;

import inks.common.core.domain.R;
import inks.system.domain.pojo.PipermissionPojo;
import inks.system.service.PipermissionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 权限关系表(PiPermission)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-14 15:13:02
 */
@RestController
@RequestMapping("SYSM03B4")
@Api(tags = "SYSM03B4:权限关系表")
public class SYSM03B4Controller extends PipermissionController {

    @Resource
    private PipermissionService pipermissionService;

    @ApiOperation(value = "按角色查询权限", notes = "按角色查询权限", produces = "application/json")
    @RequestMapping(value = "/getListByRole", method = RequestMethod.GET)
    public R<List<PipermissionPojo>> getListByRole(String key) {
        try {
            return R.ok(this.pipermissionService.getListByRole(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取一个用户的所有权限", notes = "获取一个用户的所有权限", produces = "application/json")
    @RequestMapping(value = "/getUserAllPerm", method = RequestMethod.GET)
    public R<List<PipermissionPojo>> getUserAllPerm(String key) {
        try {
            return R.ok(this.pipermissionService.getUserAllPerm(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
