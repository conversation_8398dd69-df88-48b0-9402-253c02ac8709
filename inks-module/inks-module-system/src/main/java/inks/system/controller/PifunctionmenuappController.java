package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PifunctionmenuappPojo;
import inks.system.service.PifunctionmenuappService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 服务菜单关系app(PiFunctionMenuApp)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-11 17:34:39
 */

public class PifunctionmenuappController {
    /**
     * 服务对象
     */
    @Resource
    private PifunctionmenuappService pifunctionmenuappService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取服务菜单关系app详细信息", notes = "获取服务菜单关系app详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionMenuApp.List")
    public R<PifunctionmenuappPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pifunctionmenuappService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiFunctionMenuApp.List")
    public R<PageInfo<PifunctionmenuappPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiFunctionMenuApp.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.pifunctionmenuappService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增服务菜单关系app", notes = "新增服务菜单关系app", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiFunctionMenuApp.Add")
    public R<PifunctionmenuappPojo> create(@RequestBody String json) {
        try {
            PifunctionmenuappPojo pifunctionmenuappPojo = JSONArray.parseObject(json, PifunctionmenuappPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            this.redisService.deleteObject("tenant_manuapp:" + loginUser.getTenantid());
            pifunctionmenuappPojo.setCreateby(loginUser.getRealname());   //创建者
            pifunctionmenuappPojo.setCreatedate(new Date());   //创建时间
            pifunctionmenuappPojo.setLister(loginUser.getRealname());   //用户名
            pifunctionmenuappPojo.setModifydate(new Date());   //修改时间
            pifunctionmenuappPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.pifunctionmenuappService.insert(pifunctionmenuappPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改服务菜单关系app", notes = "修改服务菜单关系app", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiFunctionMenuApp.Edit")
    public R<PifunctionmenuappPojo> update(@RequestBody String json) {
        try {
            PifunctionmenuappPojo pifunctionmenuappPojo = JSONArray.parseObject(json, PifunctionmenuappPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            this.redisService.deleteObject("tenant_manuapp:" + loginUser.getTenantid());
            pifunctionmenuappPojo.setLister(loginUser.getRealname());   //用户名
            pifunctionmenuappPojo.setTenantid(loginUser.getTenantid());   //租户id
            pifunctionmenuappPojo.setModifydate(new Date());   //修改时间
//            pifunctionmenuappPojo.setAssessor(""); //审核员
//            pifunctionmenuappPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.pifunctionmenuappService.update(pifunctionmenuappPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除服务菜单关系app", notes = "删除服务菜单关系app", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionMenuApp.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            this.redisService.deleteObject("tenant_manuapp:" + loginUser.getTenantid());
            return R.ok(this.pifunctionmenuappService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据服务号获取app菜单关系List", notes = "根据服务号获取app菜单关系List", produces = "application/json")
    @RequestMapping(value = "/getListByFunction", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionMenuApp.List")
    public R<List<PifunctionmenuappPojo>> getListByFunction(String key) {
        try {
            return R.ok(this.pifunctionmenuappService.getListByFunction(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

