package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CitablecustomPojo;
import inks.system.service.CitablecustomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 自定义字段(CiTableCustom)表控制层
 *
 * <AUTHOR>
 * @since 2022-08-10 14:17:51
 */
@RestController
@RequestMapping("SYSM07B6")
@Api(tags = "SYSM07B6:自定义列")
public class SYSM07B6Controller extends CitablecustomController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(CitablecustomController.class);
    /**
     * 服务对象
     */
    @Resource
    private CitablecustomService citablecustomService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "按功能编码获得明细", notes = "按功能编码获得明细", produces = "application/json")
    @RequestMapping(value = "/getListByCode", method = RequestMethod.GET)
    public R<List<CitablecustomPojo>> getListByCode(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.citablecustomService.getListByCode(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "按功能编码获得默认明细", notes = "按功能编码获得默认明细", produces = "application/json")
    @RequestMapping(value = "/getDefListByCode", method = RequestMethod.GET)
    public R<List<CitablecustomPojo>> getDefListByCode(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.citablecustomService.getListByCode(key, "default"));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "批量修改自定义字段", notes = "批量修改自定义字段", produces = "application/json")
    @RequestMapping(value = "/updateList", method = RequestMethod.POST)
    public R<List<CitablecustomPojo>> updateList(@RequestBody String json) {
        try {
            List<CitablecustomPojo> lst = JSONArray.parseArray(json, CitablecustomPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            for (CitablecustomPojo citablecustomPojo : lst) {
                if (citablecustomPojo.getTenantid().equals("default"))
                    citablecustomPojo.setId(null);
                citablecustomPojo.setLister(loginUser.getRealname());   // 制表
                citablecustomPojo.setListerid(loginUser.getUserid());    // 制表id
                citablecustomPojo.setTenantid(loginUser.getTenantid());   //租户id
                citablecustomPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
                citablecustomPojo.setModifydate(new Date());   //修改时间
            }
            return R.ok(this.citablecustomService.updateList(lst));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "按功能编码获得明细", notes = "按功能编码获得明细", produces = "application/json")
    @RequestMapping(value = "/getListByGroupid", method = RequestMethod.GET)
    public R<List<CitablecustomPojo>> getListByGroupid(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<CitablecustomPojo> lst = this.citablecustomService.getListByGroupid(key, loginUser.getTenantid());
            if ((lst == null || lst.size() == 0) && !loginUser.getTenantid().equals("default")) {
                List<CitablecustomPojo> lstDef = this.citablecustomService.getListByGroupid(key, "default");
                return R.ok(lstDef);
            } else {
                return R.ok(lst);
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
