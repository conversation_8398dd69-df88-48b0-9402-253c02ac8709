package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PirmsfunctPojo;
import inks.system.service.PirmsfunctService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * RMS功能(PiRmsFunct)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:09
 */
@RestController
@RequestMapping("pirmsfunct")
public class PirmsfunctController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(PirmsfunctController.class);
    /**
     * 服务对象
     */
    @Resource
    private PirmsfunctService pirmsfunctService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取RMS功能详细信息", notes = "获取RMS功能详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiRmsFunct.List")
    public R<PirmsfunctPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pirmsfunctService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiRmsFunct.List")
    public R<PageInfo<PirmsfunctPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiRmsFunct.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.pirmsfunctService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增RMS功能", notes = "新增RMS功能", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiRmsFunct.Add")
    public R<PirmsfunctPojo> create(@RequestBody String json) {
        try {
            PirmsfunctPojo pirmsfunctPojo = JSONArray.parseObject(json, PirmsfunctPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pirmsfunctPojo.setCreateby(loginUser.getRealName());   // 创建者
            pirmsfunctPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            pirmsfunctPojo.setCreatedate(new Date());   // 创建时间
            pirmsfunctPojo.setLister(loginUser.getRealname());   // 制表
            pirmsfunctPojo.setListerid(loginUser.getUserid());    // 制表id  
            pirmsfunctPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.pirmsfunctService.insert(pirmsfunctPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改RMS功能", notes = "修改RMS功能", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiRmsFunct.Edit")
    public R<PirmsfunctPojo> update(@RequestBody String json) {
        try {
            PirmsfunctPojo pirmsfunctPojo = JSONArray.parseObject(json, PirmsfunctPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pirmsfunctPojo.setLister(loginUser.getRealname());   // 制表
            pirmsfunctPojo.setListerid(loginUser.getUserid());    // 制表id  
            pirmsfunctPojo.setModifydate(new Date());   //修改时间
//            pirmsfunctPojo.setAssessor(""); // 审核员
//            pirmsfunctPojo.setAssessorid(""); // 审核员id
//            pirmsfunctPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.pirmsfunctService.update(pirmsfunctPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除RMS功能", notes = "删除RMS功能", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiRmsFunct.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pirmsfunctService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiRmsFunct.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        PirmsfunctPojo pirmsfunctPojo = this.pirmsfunctService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(pirmsfunctPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

