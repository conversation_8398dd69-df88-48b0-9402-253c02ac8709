package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.alipay.api.AlipayApiException;
import com.alipay.api.internal.util.AlipaySignature;
import com.github.pagehelper.PageInfo;
import inks.common.core.constant.InksConstants;
import inks.common.core.domain.AlipayBean;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.*;
import inks.system.service.*;
import inks.system.utils.AlipayUtil;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 销售订单(Cifnorder)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-09 20:42:14
 */

public class CifnorderController {
    /**
     * 服务对象
     */
    @Resource
    private CifnorderService cifnorderService;

    /**
     * 服务对象Item
     */
    @Resource
    private CifnorderitemService cifnorderitemService;

    /**
     * 服务对象
     */
    @Resource
    private PisubscriberService pisubscriberService;


    /**
     * 服务对象
     */
    @Resource
    private CibillcodeService cibillcodeService;

    /**
     * 服务对象
     */
    @Resource
    private PitenantService pitenantService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;


    @Value("${spring.alipay.app_id:2021002193654512}")
    private String app_id;
    @Value("${spring.alipay.merchant_private_key:MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCp5GUiy/0XAOva05toOXMCH7GGxeNh+5O58bO8SxlGPFhDvLvt6zwPM7celRXaR1xM24MurFd8u5KHIqX+Dtik8kdG7vruXrGNr2oBGVelB9Qqo8IcgjxMEr+Zk+A3RToXv//+b44C3Hxc/F2VqfTrLU/jK54AvG4ekz+YWAE65/Y0U7JTHXRzf+epV5Z57O1fVNQXE6oY3mWGO9xrhwELH6Y/R1QnnUhJA6EDJp3HNs1cKDR4VdoPfC0jR/+LgDNL5xzp9C47EvWCbFXI2BRBmMzRLGlHe9iwUKQ24baX1gChc1iefuiYcZR5UyxENRzH1Kx6ZtJSxbn+PS69vfLvAgMBAAECggEALmmm94qi6dXmmTGWEzMeqEXgSeFl7S69fN77K0WY8gcqVdcJwEWzcrO+Iyy3e5pjLNwLSoTqobjcnu5oSq/jn6xQrKA9DUHxX7O8UfCKcRtiawOx0/gAYQf+MAamCnNvG23okaoIMd/qWbzYFDsKHfWTDYys7aaMru2rQgNI0r54PRzvKfT11bdOGoJpuxof9KXkSs52FeumyTqicDrb/TMJnqcZ2mSyROuFx1fffsF5Gy/m/GT1j/hbxmc/7PIEJjHFn2Y6uQc3/IgXugOBW/VvCI6nZ5d0dtkGhsuh3GTllHIUQMHidCkSd5Kt0SZUVivL1DeXnHAFPj3HB2BjAQKBgQD6L6S4/6DUsUNtPxc7fAowVSckvMgMyFVWfzSdFOyIw6YWZnto6nm+DHM3YPHD8q4ZapEjoIVLGty05a1y+wN3G36NpQhq2WilvdKso/XJlguHfh+6Ne/+XAsarONzSs8Aw923w0iN1VRGuCuIQyJcdEJKt1oMBPdDN3bKae47eQKBgQCt1xVKNtS0p0gXVEgSu/FzZDuDMGPaGzSwBAiumMIaIx0J/Sld1YYwpdUfAwMb2gLBt89QRE/1YGfwIwXBH3G8/J7W2/jjMSMXNO2PfbeTqEB5SQQ4PqGkl1a7pLqmeYq+/QvH77bhbLODvIF48IrHsoMGeu/HnnSH9aF70U2fpwKBgQD4M3XulqP+/hEPe4zX1ZniE0hvGN46WDDZS/9tawmyMK8//9Gk5yF6Gq3fHc0cAVEZI/DzcFdsiCy3RjiKHl50tpEZgsVUA3XfH++2mD7KC5JKjCR7vvDl3nihsS1A/tFYR/hZS2JaW25tbl65oyieojP2Heo2jGKli4vNCcRvuQKBgBAjW3KcTHrI3MRtqpMyp2OS9oNjRqHEzMtaVIQA4mQSxmta+TAg1mhFvNc4LF21i3P4YGKPNO0OmheM6MfY53In8Yb4vM46jYtl+nfLr/MhN3sK8D1YcyEYe5DH5hv2RI8eZq2U72FV+gRMx6g2jGulgsxjNUdOynqkru0TrSUZAoGBAJ3xVSnilF0ro+Q+f4UpUcyyoGD4Tz2RhpW1EzEg9nJn1Iemcz5GP3yhZ4/wryzqFDhG2Cdt4AR41kmCSdQt12o0K7c0jKN+0GDD/QyOlhYnnTfHCq/LRJQVOdeXiaUuIsH8s1FhnizwgTRylZLBaa1nED1X2IrlRQZWLim4vOec}")
    private String merchant_private_key;
    @Value("${spring.alipay.alipay_public_key:MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAly1cHQo5mMudSB2Ty0ioP8DXdA/RwMLGd0dvC6s6u8POmqv/K7mpATRSmy67Byp3dPfYH29ZAlWqsnlz+PARC7XbKDrSimVI1EpFeC2M79oX9AgkuMTJP6al+ULGvXeRZyF9CkAtWx2yaGpODKrzbhqJxFKRqSp8fWhejLZpNhmIK7i9FVKaM1zNMH2umGF/4uOp1WHxNcKCev1phq4Qn9511cVA3XH/zoJuAcenjfymifcflyfbSqtZpNSS2RohRhrhewav4cIPu3tLzdd6kOlhoFDHMJUT+HIVzI5nxvsTFyAR2Cts4KqvuO1FDCXyeJ54XQw4y0x8Rm0UrASRcQIDAQAB}")
    private String alipay_public_key;
    @Value("${spring.alipay.gatewayUrl:https://openapi.alipay.com/gateway.do}")
    private String gatewayUrl;
    @Value("${spring.alipay.charset:utf-8}")
    private String charset;
    @Value("${spring.alipay.sign_type:RSA2}")
    private String sign_type;
    @Value("${spring.alipay.notify_url:http://inks.tpddns.net:8081/system/SYSM10B2/notifyPayResult}")
    private String notify_url;
    @Value("${spring.alipay.return_url:http://www.inkstech.com}")
    private String return_url;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取销售订单详细信息", notes = "获取销售订单详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiFnOrder.List")
    public R<CifnorderPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cifnorderService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiFnOrder.List")
    public R<PageInfo<CifnorderitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiFnOrder.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.cifnorderService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取销售订单详细信息", notes = "获取销售订单详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiFnOrder.List")
    public R<CifnorderPojo> getBillEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cifnorderService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiFnOrder.List")
    public R<PageInfo<CifnorderPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiFnOrder.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.cifnorderService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiFnOrder.List")
    public R<PageInfo<CifnorderPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiFnOrder.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.cifnorderService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增销售订单", notes = "新增销售订单", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiFnOrder.Add")
    public R<CifnorderPojo> create(@RequestBody String json) {
        try {
            CifnorderPojo cifnorderPojo = JSONArray.parseObject(json, CifnorderPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            cifnorderPojo.setLister(loginUser.getRealname());   //用户名
            cifnorderPojo.setTenantid(loginUser.getTenantid());   //租户id
            cifnorderPojo.setModifydate(new Date());   //修改时间
            cifnorderPojo.setUserid(loginUser.getUserid());  //用户信息
            cifnorderPojo.setUsername(loginUser.getUsername());    //用户名
            cifnorderPojo.setRealname(loginUser.getRealname());  //中文名
            PitenantPojo pitenantPojo = pitenantService.getEntity(loginUser.getTenantid());
            cifnorderPojo.setTenantcode(pitenantPojo.getTenantcode());
            cifnorderPojo.setTenantname(pitenantPojo.getTenantname());
            cifnorderPojo.setCompany(pitenantPojo.getCompany());
            //Item子表处理
            List<CifnorderitemPojo> lst = cifnorderPojo.getItem();
            Double billAmt = 0D;
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    billAmt += lst.get(i).getTaxamount();
                }
            }
            cifnorderPojo.setBilltaxamount(billAmt);
            // 订单号
            cifnorderPojo.setRefno(cibillcodeService.getSerialNo("SYSM10B2", InksConstants.DEFAULT_TENANT));
            // 订单号状态
            cifnorderPojo.setStatecode("new");  //new 待支付、 pay 已支付

            return R.ok(this.cifnorderService.insert(cifnorderPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改销售订单", notes = "修改销售订单", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiFnOrder.Edit")
    public R<CifnorderPojo> update(@RequestBody String json) {
        try {
            CifnorderPojo cifnorderPojo = JSONArray.parseObject(json, CifnorderPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            cifnorderPojo.setLister(loginUser.getRealname());   //用户名
            cifnorderPojo.setTenantid(loginUser.getTenantid());   //租户id
            cifnorderPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.cifnorderService.update(cifnorderPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除销售订单", notes = "删除销售订单", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiFnOrder.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cifnorderService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增销售订单Item", notes = "新增销售订单Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiFnOrder.Add")
    public R<CifnorderitemPojo> createItem(@RequestBody String json) {
        try {
            CifnorderitemPojo cifnorderitemPojo = JSONArray.parseObject(json, CifnorderitemPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            // cifnorderitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.cifnorderitemService.insert(cifnorderitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除销售订单Item", notes = "删除销售订单Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiFnOrder.Delete")
    public R<Integer> deleteItem(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cifnorderitemService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiFnOrder.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        CifnorderPojo cifnorderPojo = this.cifnorderService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(cifnorderPojo);
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(cifnorderPojo.getItem());

        //从redis中获取XML内容
        String xml = redisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (xml != null || !xml.equals("") || xml != "") {
            content = xml;
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


    /*阿里支付*/
    @RequestMapping(value = "/alipay", method = RequestMethod.GET)
    public R<String> alipay(String key) throws AlipayApiException {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            //获取单据信息
            CifnorderPojo cifnorderPojo = this.cifnorderService.getEntity(key, loginUser.getTenantid());
            if (cifnorderPojo == null) {
                return R.fail("未找到相关订单");
            } else if (cifnorderPojo.getStatecode().equals("pay")) {
                return R.fail("订单已支付,请勿重复提交");
            }

            AlipayBean alipayBean = new AlipayBean();
            alipayBean.setOut_trade_no(cifnorderPojo.getRefno());
            alipayBean.setSubject("应凯管理平台订单");
            alipayBean.setTotal_amount(new StringBuffer().append(cifnorderPojo.getBilltaxamount()));

            AlipayUtil alipayUtil = new AlipayUtil();
            //打开支付页面
            String str = alipayUtil.connect(alipayBean, gatewayUrl, app_id,
                    merchant_private_key, charset, alipay_public_key,
                    sign_type, return_url, notify_url);

            return R.ok(str);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 支付成功的回调接口
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/notifyPayResult")
    public String notifyPayResult(HttpServletRequest request) {
        System.out.println("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<进入支付宝回调->>>>>>>>>>>>>>>>>>>>>>>>>");
        // 1.从支付宝回调的request域中取值放到map中
        Map<String, String[]> requestParams = request.getParameterMap();
        try {
            Map<String, String> params = new HashMap();
            for (String name : requestParams.keySet()) {
                String[] values = requestParams.get(name);
                String valueStr = "";
                for (int i = 0; i < values.length; i++) {
                    valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
                }
                params.put(name, valueStr);
            }
            // 商户订单号
            String outTradeNo = params.get("out_trade_no");
            // 订单号
            String TradeNo = params.get("trade_no");
            //交易状态
            String tradeStatus = params.get("trade_status");
            //3.签名验证(对支付宝返回的数据验证，确定是支付宝返回的)
            boolean signVerified = false;
            try {
                //3.1调用SDK验证签名
                signVerified = AlipaySignature.rsaCheckV1(params,
                        alipay_public_key,
                        charset,
                        sign_type); // 调用SDK验证签名

            } catch (Exception e) {
                e.printStackTrace();
            }
            System.out.println("--------------->验签结果:" + signVerified);
            //4.对验签进行处理
            if (signVerified) {
                //验签通过
                //只处理支付成功的订单: 修改交易表状态,支付成功
                if ("TRADE_FINISHED".equals(tradeStatus) || "TRADE_SUCCESS".equals(tradeStatus)) {
                    //根据订单号查找订单,防止多次回调的问题
                    CifnorderPojo cifnorderPojo = this.cifnorderService.getEntityByRefno(outTradeNo);
                    if (cifnorderPojo != null) {
                        cifnorderPojo = this.cifnorderService.getBillEntity(cifnorderPojo.getId(), cifnorderPojo.getTenantid());
                        cifnorderPojo.setPaybillcode(TradeNo);
                        List<PisubscriberPojo> lst = this.pisubscriberService.createByOrder(cifnorderPojo);
                    }
                    return "success";
                } else {
                    return "failure";
                }
            } else {
                //验签不通过
                System.err.println("-------------------->验签失败");
                return "failure";
            }

        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

}

