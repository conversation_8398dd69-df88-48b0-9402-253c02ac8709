package inks.system.controller;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PiauthcodePojo;
import inks.system.service.PiauthcodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 授权码(PiAuthCode)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-22 12:44:38
 */
@RestController
@RequestMapping("SYSM01B9")
@Api(tags = "SYSM01B9:登录授权码")
public class SYSM01B9Controller extends PiauthcodeController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(PiauthcodeController.class);
    /**
     * 服务对象
     */
    @Resource
    private PiauthcodeService piauthcodeService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增授权码", notes = "新增授权码", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiAuthCode.Add")
    public R<PiauthcodePojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            PiauthcodePojo piauthcodePojo = JSONArray.parseObject(json, PiauthcodePojo.class);

            piauthcodePojo.setCreateby(loginUser.getRealName());   // 创建者
            piauthcodePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            piauthcodePojo.setCreatedate(new Date());   // 创建时间
            piauthcodePojo.setLister(loginUser.getRealname());   // 制表
            piauthcodePojo.setListerid(loginUser.getUserid());    // 制表id
            piauthcodePojo.setModifydate(new Date());   //修改时间
            piauthcodePojo.setTenantid(loginUser.getTenantid());   //租户id
            piauthcodePojo.setAuthcode(IdUtil.simpleUUID().substring(1, 21));  // code
            return R.ok(this.piauthcodeService.insert(piauthcodePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
