package inks.system.controller;

import com.alibaba.fastjson.JSONObject;
import inks.common.core.constant.CacheConstants;
import inks.common.core.domain.EmailPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiemailPojo;
import inks.system.service.CiconfigService;
import inks.system.service.CiemailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 邮件模板(CiEmail)表控制层
 *
 * <AUTHOR>
 * @since 2022-04-29 15:00:41
 */
@RestController
@RequestMapping("SYSM07B11")
@Api(tags = "SYSM07B11：邮件模板")
public class SYSM07B11Controller extends CiemailController {
    /**
     * 服务对象
     */
    @Resource
    private CiemailService ciemailService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    @Resource
    private JavaMailSenderImpl mailSender;

    /**
     * 服务对象
     */
    @Resource
    private CiconfigService ciconfigService;

    /**
     * 按模块编码查询报表
     *
     * @param code 主键
     * @return 查询结果
     */
    @ApiOperation(value = "按模块编码查询报表", notes = "按模块编码查询报表", produces = "application/json")
    @RequestMapping(value = "/getListByModuleCode", method = RequestMethod.GET)
    public R<List<CiemailPojo>> getListByModuleCode(String code) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            List<CiemailPojo> list = this.ciemailService.getListByModuleCode(code, loginUser.getTenantid());
            if (list != null) {
                for (int i = 0; i < list.size(); i++) {
                    String verifyKey = CacheConstants.EMAIL_CODES_KEY + list.get(i).getId();
                    EmailPojo emailPojo = new EmailPojo();
                    emailPojo.setDeftojson(list.get(i).getDeftojson());
                    emailPojo.setEmailtemplate(list.get(i).getEmailtemplate());
                    emailPojo.setPagerow(list.get(i).getPagerow());
                    redisService.setCacheObject(verifyKey, emailPojo, (long) (60 * 12), TimeUnit.MINUTES);
                    list.get(i).setEmailtemplate(null);
                    list.get(i).setDeftojson(null);
                }
            }
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 按模块编码查询报表
     *
     * @param key 主键
     * @return 查询结果
     */
    @ApiOperation(value = "按模块编码查询报表", notes = "按模块编码查询报表", produces = "application/json")
    @RequestMapping(value = "/sendEmail", method = RequestMethod.GET)
    public R sendEmail(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            //从redis中获取Email内容
            EmailPojo emailPojo = redisService.getCacheObject(CacheConstants.EMAIL_CODES_KEY + key);
            if (emailPojo == null) {
                throw new BaseBusinessException("未找到报表");
            }

            //从redis中获取Reprot内容
            Map<String, String> mapConfig = this.ciconfigService.getMapByTenant(0, loginUser.getTenantid());
            //创建发送邮件对象
            MimeMessage mimeMessage = this.mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);
            if (mapConfig.containsKey("system.mail.username")) {
                this.mailSender.setUsername(mapConfig.get("system.mail.username"));
            }
            if (mapConfig.containsKey("system.mail.password")) {
                this.mailSender.setPassword(mapConfig.get("system.mail.password"));
            }
            if (mapConfig.containsKey("system.mail.host")) {
                this.mailSender.setHost(mapConfig.get("system.mail.host"));
            }
//            if (mapConfig.containsKey("system.mail.sender") ) {
//                helper.setFrom(mapConfig.get("system.mail.sender"));
//            }else
//            {
//                helper.setFrom("应凯平台");
//            }
            // 创建邮件发送者地址
            helper.setFrom(this.mailSender.getUsername());
            helper.setTo(emailPojo.getDeftojson());
            helper.setSubject(emailPojo.getEmailname());
            helper.setText(emailPojo.getEmailtemplate(), true);
            mailSender.send(mimeMessage);
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 按模块编码查询报表
     *
     * @return 查询结果
     */
    @ApiOperation(value = "邮件通知测试", notes = "邮件通知测试 setto", produces = "application/json")
    @RequestMapping(value = "/sendTest", method = RequestMethod.POST)
    public R sendTest(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            JSONObject jsonObject = JSONObject.parseObject(json);
            String setto; //"<EMAIL>";
            if (jsonObject.containsKey("setto")) {
                setto = jsonObject.get("setto").toString();
            } else {
                return R.fail("缺少参数setto");
            }

            //从redis中获取Reprot内容
            Map<String, String> mapConfig = this.ciconfigService.getMapByTenant(0, loginUser.getTenantid());
            //创建发送邮件对象
            MimeMessage mimeMessage = this.mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);
            if (mapConfig.containsKey("system.mail.username")) {
                this.mailSender.setUsername(mapConfig.get("system.mail.username"));

            }
            if (mapConfig.containsKey("system.mail.password")) {
                this.mailSender.setPassword(mapConfig.get("system.mail.password"));
            }
            if (mapConfig.containsKey("system.mail.host")) {
                this.mailSender.setHost(mapConfig.get("system.mail.host"));
            }
//            if (mapConfig.containsKey("system.mail.sender") ) {
//                helper.setFrom(mapConfig.get("system.mail.sender"));
//            }else
//            {
//                helper.setFrom("<EMAIL>");
//            }
            // 创建邮件发送者地址
            helper.setFrom(this.mailSender.getUsername());
            helper.setTo(setto);
            helper.setSubject("邮件发放测试");
            helper.setText("测试邮件" + new Date(), true);
            mailSender.send(mimeMessage);
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
