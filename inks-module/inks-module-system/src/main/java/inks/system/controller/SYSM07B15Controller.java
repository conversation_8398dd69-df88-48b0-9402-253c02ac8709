package inks.system.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CibillexpressionPojo;
import inks.system.service.CibillexpressionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 单据公式(CiBillExpression)表控制层
 *
 * <AUTHOR>
 * @since 2023-05-29 10:52:48
 */
@RestController
@RequestMapping("SYSM07B15")
@Api(tags = "SYSM07B15:单据公式")
public class SYSM07B15Controller extends CibillexpressionController {
    @Resource
    private CibillexpressionService cibillexpressionService;
    @Resource
    private TokenService tokenService;

    /**
     * 通过ModuleCode查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "通过ModuleCode获取单据公式详细信息", notes = "获取单据公式详细信息", produces = "application/json")
    @RequestMapping(value = "/getListByCode", method = RequestMethod.GET)
    // @PreAuthorize(hasPermi = "CiBillExpression.List")
    public R<List<CibillexpressionPojo>> getListByCode(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cibillexpressionService.getListByCode(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
