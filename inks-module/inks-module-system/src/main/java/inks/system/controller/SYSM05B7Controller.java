package inks.system.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.domain.WebMenuPojo;
import inks.common.core.domain.WebMetaPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PimenufrmPojo;
import inks.system.service.PimenufrmService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Frm导航(PiMenuFrm)表控制层
 *
 * <AUTHOR>
 * @since 2024-05-05 16:28:24
 */
@RestController
@RequestMapping("SYSM05B7")
@Api(tags = "SYSM05B7:Frm端菜单")
public class SYSM05B7Controller extends PimenufrmController {
    /**
     * 服务对象
     */
    @Resource
    private PimenufrmService pimenufrmService;

    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "项目全菜单", notes = "项目全菜单", produces = "application/json")
    @GetMapping(value = "/getAllListByPid")
    @PreAuthorize(hasPermi = "PiMenuFrm.List")
    public R<List<PimenufrmPojo>> getAllListByPid(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            // 获取所有菜单项并构建菜单树
            List<PimenufrmPojo> menuTree = getMenuTree(key);

            return R.ok(menuTree);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "项目全菜单", notes = "项目全菜单", produces = "application/json")
    @GetMapping(value = "/getTreeList")
    //@PreAuthorize(hasPermi = "PiMenuWeb.List")
    public R<List<WebMenuPojo>> getTreeList(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());

            // 获取所有菜单项并构建Web菜单树
            List<WebMenuPojo> webMenuTree = getWebMenuTree(key);

            return R.ok(webMenuTree);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    private List<PimenufrmPojo> getMenuTree(String navpid) {
        // 获取所有菜单项
        List<PimenufrmPojo> allMenus = this.pimenufrmService.getAllMenus();
        // 构建菜单树
        return buildMenuTree(allMenus, navpid);
    }

    private List<PimenufrmPojo> buildMenuTree(List<PimenufrmPojo> allMenus, String navpid) {
        List<PimenufrmPojo> result = new ArrayList<>();
        for (PimenufrmPojo menu : allMenus) {
            if (Objects.equals(menu.getNavpid(), navpid)) {
                PimenufrmPojo frmMenuPojo = new PimenufrmPojo();
                BeanUtils.copyProperties(menu, frmMenuPojo);
                frmMenuPojo.setChildren(buildMenuTree(allMenus, menu.getNavid()));
                result.add(frmMenuPojo);
            }
        }
        return result;
    }

    private List<WebMenuPojo> getWebMenuTree(String navpid) {
        // 获取所有菜单项
        List<PimenufrmPojo> allMenus = this.pimenufrmService.getAllMenus();
        // 构建Web菜单树
        return buildWebMenuTree(allMenus, navpid);
    }

    private List<WebMenuPojo> buildWebMenuTree(List<PimenufrmPojo> allMenus, String navpid) {
        List<WebMenuPojo> result = new ArrayList<>();
        for (PimenufrmPojo menu : allMenus) {
            if (Objects.equals(menu.getNavpid(), navpid)) {
                WebMenuPojo webMenuPojo = new WebMenuPojo();
                webMenuPojo.setName(menu.getNavname());
                webMenuPojo.setPath(menu.getMvcurl());
                webMenuPojo.setMeta(new WebMetaPojo(menu.getNavname(), menu.getImagecss()));
                webMenuPojo.setChildren(buildWebMenuTree(allMenus, menu.getNavid()));
                result.add(webMenuPojo);
            }
        }
        return result;
    }


//    @ApiOperation(value = "项目全菜单", notes = "项目全菜单", produces = "application/json")
//    @GetMapping(value = "/getAllListByPid")
//    @PreAuthorize(hasPermi = "PiMenuFrm.List")
//    public R<List<PimenufrmPojo>> getAllListByPid(String key) {
//        try {
//            
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            List<PimenufrmPojo> lst = this.pimenufrmService.getListByPid(key);
//            List<PimenufrmPojo> list = new ArrayList<>();
//            for (PimenufrmPojo pimenufrmPojo : lst) {
//                PimenufrmPojo frmMenuPojo = new PimenufrmPojo();
//                BeanUtils.copyProperties(pimenufrmPojo, frmMenuPojo);
//                List<PimenufrmPojo> lst2 = this.pimenufrmService.getListByPid(pimenufrmPojo.getNavid());
//                for (PimenufrmPojo pojo : lst2) {
//                    PimenufrmPojo frmMenuPojo2 = new PimenufrmPojo();
//                    BeanUtils.copyProperties(pojo, frmMenuPojo2);
//                    List<PimenufrmPojo> lst3 = this.pimenufrmService.getListByPid(pojo.getNavid());
//                    for (PimenufrmPojo value : lst3) {
//                        PimenufrmPojo frmMenuPojo3 = new PimenufrmPojo();
//                        BeanUtils.copyProperties(value, frmMenuPojo3);
//                        list.add(frmMenuPojo3);
//                    }
//                    list.add(frmMenuPojo2);
//                }
//                list.add(frmMenuPojo);
//            }
//            return R.ok(list);
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    @ApiOperation(value = "项目全菜单", notes = "项目全菜单", produces = "application/json")
//    @GetMapping(value = "/getTreeList")
//    //@PreAuthorize(hasPermi = "PiMenuWeb.List")
//    public R<List<WebMenuPojo>> getTreeList(String key) {
//        try {
//            
//            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
//            List<PimenufrmPojo> lst = this.pimenufrmService.getListByPid(key);
//            List<WebMenuPojo> list = new ArrayList<>();
//            for (PimenufrmPojo pimenufrmPojo : lst) {
//                WebMenuPojo frmMenuPojo = new WebMenuPojo();
//                frmMenuPojo.setName(pimenufrmPojo.getNavname());
//                frmMenuPojo.setPath(pimenufrmPojo.getMvcurl());
//                frmMenuPojo.setMeta(new WebMetaPojo(pimenufrmPojo.getNavname(), pimenufrmPojo.getImagecss()));
//                List<WebMenuPojo> lstChil = new ArrayList<>();
//                List<PimenufrmPojo> lst2 = this.pimenufrmService.getListByPid(pimenufrmPojo.getNavid());
//                for (PimenufrmPojo pojo : lst2) {
//                    WebMenuPojo frmMenuPojo2 = new WebMenuPojo();
//                    frmMenuPojo2.setName(pojo.getNavname());
//                    frmMenuPojo2.setPath(pojo.getMvcurl());
//                    frmMenuPojo2.setMeta(new WebMetaPojo(pojo.getNavname(), pojo.getImagecss()));
//                    List<WebMenuPojo> lstChil2 = new ArrayList<>();
//                    List<PimenufrmPojo> lst3 = this.pimenufrmService.getListByPid(pojo.getNavid());
//                    for (PimenufrmPojo value : lst3) {
//                        WebMenuPojo frmMenuPojo3 = new WebMenuPojo();
//                        frmMenuPojo3.setName(value.getNavname());
//                        frmMenuPojo3.setPath(value.getMvcurl());
//                        frmMenuPojo3.setMeta(new WebMetaPojo(value.getNavname(), value.getImagecss()));
//                        lstChil2.add(frmMenuPojo3);
//                    }
//                    frmMenuPojo2.setChildren(lstChil2);
//                    lstChil.add(frmMenuPojo2);
//                }
//
//                frmMenuPojo.setChildren(lstChil);
//                list.add(frmMenuPojo);
//            }
//            return R.ok(list);
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }

}

