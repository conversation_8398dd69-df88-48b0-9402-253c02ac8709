package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CibilldraftPojo;
import inks.system.service.CibilldraftService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 单据草稿(CiBillDraft)表控制层
 *
 * <AUTHOR>
 * @since 2024-10-26 10:17:53
 */
//@RestController
//@RequestMapping("cibilldraft")
public class CibilldraftController {

    private final static Logger logger = LoggerFactory.getLogger(CibilldraftController.class);
    @Resource
    private CibilldraftService cibilldraftService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = " 获取单据草稿详细信息", notes = "获取单据草稿详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiBillDraft.List")
    public R<CibilldraftPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cibilldraftService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiBillDraft.List")
    public R<PageInfo<CibilldraftPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("CiBillDraft.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.cibilldraftService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增单据草稿", notes = "新增单据草稿", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiBillDraft.Add")
    public R<CibilldraftPojo> create(@RequestBody String json) {
        try {
            CibilldraftPojo cibilldraftPojo = JSONArray.parseObject(json, CibilldraftPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            cibilldraftPojo.setCreateby(loginUser.getRealName());   // 创建者
            cibilldraftPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            cibilldraftPojo.setCreatedate(new Date());   // 创建时间
            cibilldraftPojo.setLister(loginUser.getRealname());   // 制表
            cibilldraftPojo.setListerid(loginUser.getUserid());    // 制表id  
            cibilldraftPojo.setModifydate(new Date());   //修改时间
            cibilldraftPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.cibilldraftService.insert(cibilldraftPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改单据草稿", notes = "修改单据草稿", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiBillDraft.Edit")
    public R<CibilldraftPojo> update(@RequestBody String json) {
        try {
            CibilldraftPojo cibilldraftPojo = JSONArray.parseObject(json, CibilldraftPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            cibilldraftPojo.setLister(loginUser.getRealname());   // 制表
            cibilldraftPojo.setListerid(loginUser.getUserid());    // 制表id  
            cibilldraftPojo.setTenantid(loginUser.getTenantid());   //租户id
            cibilldraftPojo.setModifydate(new Date());   //修改时间
//            cibilldraftPojo.setAssessor(""); // 审核员
//            cibilldraftPojo.setAssessorid(""); // 审核员id
//            cibilldraftPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.cibilldraftService.update(cibilldraftPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除单据草稿", notes = "删除单据草稿", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiBillDraft.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.cibilldraftService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiBillDraft.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        CibilldraftPojo cibilldraftPojo = this.cibilldraftService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(cibilldraftPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

