package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PifunctionmenuopsPojo;
import inks.system.domain.pojo.PifunctionmenuwebPojo;
import inks.system.service.PifunctionmenuopsService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 服务Form关系(PiFunctionMenuOps)表控制层
 *
 * <AUTHOR>
 * @since 2024-04-24 17:03:52
 */
//@RestController
//@RequestMapping("pifunctionmenuops")
public class PifunctionmenuopsController {

    private final static Logger logger = LoggerFactory.getLogger(PifunctionmenuopsController.class);
    @Resource
    private PifunctionmenuopsService pifunctionmenuopsService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    @ApiOperation(value = " 获取服务Form关系详细信息", notes = "获取服务Form关系详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionMenuOps.List")
    public R<PifunctionmenuopsPojo> getEntity(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pifunctionmenuopsService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiFunctionMenuOps.List")
    public R<PageInfo<PifunctionmenuopsPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("PiFunctionMenuOps.CreateDate");

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.pifunctionmenuopsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增服务Form关系", notes = "新增服务Form关系", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiFunctionMenuOps.Add")
    public R<PifunctionmenuopsPojo> create(@RequestBody String json) {
        try {
            PifunctionmenuopsPojo pifunctionmenuopsPojo = JSONArray.parseObject(json, PifunctionmenuopsPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pifunctionmenuopsPojo.setCreateby(loginUser.getRealName());   // 创建者
            pifunctionmenuopsPojo.setCreatedate(new Date());   // 创建时间
            pifunctionmenuopsPojo.setLister(loginUser.getRealname());   // 制表
            pifunctionmenuopsPojo.setModifydate(new Date());   //修改时间
            pifunctionmenuopsPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.pifunctionmenuopsService.insert(pifunctionmenuopsPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改服务Form关系", notes = "修改服务Form关系", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiFunctionMenuOps.Edit")
    public R<PifunctionmenuopsPojo> update(@RequestBody String json) {
        try {
            PifunctionmenuopsPojo pifunctionmenuopsPojo = JSONArray.parseObject(json, PifunctionmenuopsPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            pifunctionmenuopsPojo.setLister(loginUser.getRealname());   // 制表
            pifunctionmenuopsPojo.setTenantid(loginUser.getTenantid());   //租户id
            pifunctionmenuopsPojo.setModifydate(new Date());   //修改时间
//            pifunctionmenuopsPojo.setAssessor(""); // 审核员
//            pifunctionmenuopsPojo.setAssessorid(""); // 审核员id
//            pifunctionmenuopsPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.pifunctionmenuopsService.update(pifunctionmenuopsPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除服务Form关系", notes = "删除服务Form关系", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionMenuOps.Delete")
    public R<Integer> delete(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pifunctionmenuopsService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionMenuOps.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        PifunctionmenuopsPojo pifunctionmenuopsPojo = this.pifunctionmenuopsService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(pifunctionmenuopsPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据服务号获取web菜单关系List", notes = "根据服务号获取web菜单关系List", produces = "application/json")
    @RequestMapping(value = "/getListByFunction", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiFunctionMenuWeb.List")
    public R<List<PifunctionmenuwebPojo>> getListByFunction(String key) {
        try {
            return R.ok(this.pifunctionmenuopsService.getListByFunction(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}

