package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PiuserPojo;
import inks.system.service.PiuserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 用户表(PiUser)表控制层
 *
 * <AUTHOR>
 * @since 2021-11-08 14:35:48
 */
@RestController
@RequestMapping("SYSM01B1")
@Api(tags = "SYSM01B1:用户表")
public class SYSM01B1Controller extends PiuserController {
    /**
     * 服务对象
     */
    @Resource
    private PiuserService piuserService;

    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增用户表", notes = "新增用户表", produces = "application/json")
    @RequestMapping(value = "/createByTen", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "PiUser.Add")
    public R<PiuserPojo> createByTen(@RequestBody String json) {
        try {
            PiuserPojo piuserPojo = JSONArray.parseObject(json, PiuserPojo.class);

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            piuserPojo.setLister(loginUser.getRealname());   //用户名
            piuserPojo.setCreateby(loginUser.getRealname());
            piuserPojo.setModifydate(new Date());   //修改时间
            piuserPojo.setRevision(0);
            return R.ok(this.piuserService.createByTenant(piuserPojo, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
