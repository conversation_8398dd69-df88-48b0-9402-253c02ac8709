package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiformsPojo;
import inks.system.service.CiformsService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 窗体中心(CiForms)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-18 15:43:44
 */
//@RestController
//@RequestMapping("ciforms")
public class CiformsController {

    @Resource
    private CiformsService ciformsService;
    @Resource
    private RedisService redisService;
    @Resource
    private TokenService tokenService;

    private final static Logger logger = LoggerFactory.getLogger(CiformsController.class);


    @ApiOperation(value = " 获取窗体中心详细信息 去租户只要key", notes = "获取窗体中心详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiForms.List")
    public R<CiformsPojo> getEntity(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.ciformsService.getEntity(key));
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiForms.List")
    public R<PageInfo<CiformsPojo>> getPageList(@RequestBody String json) {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("CiForms.CreateDate");
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        queryParam.setTenantid(loginUser.getTenantid());
        String qpfilter = "";
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        return R.ok(this.ciformsService.getPageList(queryParam));
    }


    @ApiOperation(value = " 新增窗体中心 由前端传tid", notes = "新增窗体中心", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiForms.Add")
    public R<CiformsPojo> create(@RequestBody String json) {
        CiformsPojo ciformsPojo = JSONArray.parseObject(json, CiformsPojo.class);
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        ciformsPojo.setCreateby(loginUser.getRealName());   // 创建者
        ciformsPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
        ciformsPojo.setCreatedate(new Date());   // 创建时间
        ciformsPojo.setLister(loginUser.getRealname());   // 制表
        ciformsPojo.setListerid(loginUser.getUserid());    // 制表id
        ciformsPojo.setModifydate(new Date());   //修改时间
        //ciformsPojo.setTenantid(loginUser.getTenantid());   //租户id
        return R.ok(this.ciformsService.insert(ciformsPojo));
    }


    @ApiOperation(value = "修改窗体中心 由前端传tid", notes = "修改窗体中心", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiForms.Edit")
    public R<CiformsPojo> update(@RequestBody String json) {
        CiformsPojo ciformsPojo = JSONArray.parseObject(json, CiformsPojo.class);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        ciformsPojo.setLister(loginUser.getRealname());   // 制表
        ciformsPojo.setListerid(loginUser.getUserid());    // 制表id
        //ciformsPojo.setTenantid(loginUser.getTenantid());   //租户id
        ciformsPojo.setModifydate(new Date());   //修改时间
//            ciformsPojo.setAssessor(""); // 审核员
//            ciformsPojo.setAssessorid(""); // 审核员id
//            ciformsPojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.ciformsService.update(ciformsPojo));
    }


    @ApiOperation(value = "删除窗体中心 去tid", notes = "删除窗体中心", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiForms.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.ciformsService.delete(key));
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiForms.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        CiformsPojo ciformsPojo = this.ciformsService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(ciformsPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = redisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

