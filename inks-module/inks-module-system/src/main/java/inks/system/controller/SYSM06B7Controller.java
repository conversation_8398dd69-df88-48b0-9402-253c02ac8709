package inks.system.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiintroPojo;
import inks.system.service.CiintroService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 功能简介(CiIntro)表控制层
 *
 * <AUTHOR>
 * @since 2022-07-07 20:44:33
 */
@RestController
@RequestMapping("SYSM06B7")
@Api(tags = "SYSM06B7:功能简介")
public class SYSM06B7Controller extends CiintroController {

    /**
     * 服务对象
     */
    @Resource
    private CiintroService ciintroService;

    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取功能简介详细信息By功能code", notes = "获取功能简介详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByCode", method = RequestMethod.GET)
    public R<CiintroPojo> getEntityByCode(String key) {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.ciintroService.getEntityByCode(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
