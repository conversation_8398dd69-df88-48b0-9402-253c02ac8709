package inks.system.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.domain.WebMenuPojo;
import inks.common.core.domain.WebMetaPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.redis.service.RedisService;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PidmsfunctmenuwebPojo;
import inks.system.domain.pojo.PidmsuserPojo;
import inks.system.domain.pojo.PimenuwebPojo;
import inks.system.service.PidmsfunctmenuwebService;
import inks.system.service.PidmsuserService;
import inks.system.service.PitenantdmsuserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * DMSWeb菜单关系(PiDmsFunctMenuWeb)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-06 16:27:08
 */
@RestController
@RequestMapping("SYSM12B5")
@Api(tags = "SYSM12B5:DMS功能Web菜单")
public class SYSM12B5Controller extends PidmsfunctmenuwebController {

    @Resource
    private PidmsfunctmenuwebService pidmsfunctmenuwebService;
    /**
     * 引用Redis服务
     */
    @Resource
    private RedisService redisService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;
    @Resource
    private PitenantdmsuserService pitenantdmsuserService;
    @Resource
    private PidmsuserService pidmsuserService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = "根据服务号获取web菜单关系List", notes = "根据服务号获取web菜单关系List", produces = "application/json")
    @RequestMapping(value = "/getListByFunction", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "PiDmsFunctMenuWeb.List")
    public R<List<PidmsfunctmenuwebPojo>> getListByFunction(String key) {
        try {
            return R.ok(this.pidmsfunctmenuwebService.getListByFunction(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 通过主键查询单条数据  Eric 20230408
     *
     * @return 单条数据
     */
    @ApiOperation(value = "根据当前用户信息获取web菜单关系List", notes = "根据当前用户信息获取web菜单关系List", produces = "application/json")
    @RequestMapping(value = "/getListByLoginUser", method = RequestMethod.GET)
    public R<List<WebMenuPojo>> getListByLoginUser(Integer db) {
        try {

            LoginUser loginUser = this.tokenService.getLoginUser(ServletUtils.getRequest());
            PidmsuserPojo entityByUserid = pidmsuserService.getEntityByUserid(loginUser.getUserid(), loginUser.getTenantid());
            if (entityByUserid == null || StringUtils.isBlank(entityByUserid.getDmsfunctids())) {
                return R.fail("用户未分配服务号");
            }
            List<WebMenuPojo> list = null; //this.redisService.getCacheObject("tenant_manuweb:" + loginUser.getTenantid());
            if (db != null && db == 1) list = null;  // db=1,重读数据库
            if (list == null) {
                List<PimenuwebPojo> lst = this.pidmsfunctmenuwebService.getListByDmsFunctids(entityByUserid.getDmsfunctids());
                list = new ArrayList<>();
                for (int i = 0; i < lst.size(); i++) {
                    if (lst.get(i).getNavtype().equals("1")) {
                        WebMenuPojo webMenuPojo = new WebMenuPojo();
                        webMenuPojo.setName(lst.get(i).getNavname());
                        webMenuPojo.setPath(lst.get(i).getMvcurl());
                        webMenuPojo.setMeta(new WebMetaPojo(lst.get(i).getNavname(), lst.get(i).getImagecss()));
                        List<WebMenuPojo> lstChil = new ArrayList<>();
                        for (int j = 0; j < lst.size(); j++) {
                            if (lst.get(j).getNavpid().equals(lst.get(i).getNavid())) {
                                WebMenuPojo webMenuPojo2 = new WebMenuPojo();
                                webMenuPojo2.setName(lst.get(j).getNavname());
                                webMenuPojo2.setPath(lst.get(j).getMvcurl());
                                webMenuPojo2.setMeta(new WebMetaPojo(lst.get(j).getNavname(), lst.get(j).getImagecss()));
                                List<WebMenuPojo> lstChil2 = new ArrayList<>();
                                for (int k = 0; k < lst.size(); k++) {
                                    if (lst.get(k).getNavpid().equals(lst.get(j).getNavid())) {
                                        WebMenuPojo webMenuPojo3 = new WebMenuPojo();
                                        webMenuPojo3.setName(lst.get(k).getNavname());
                                        webMenuPojo3.setPath(lst.get(k).getMvcurl());
                                        webMenuPojo3.setMeta(new WebMetaPojo(lst.get(k).getNavname(), lst.get(k).getImagecss()));
                                        lstChil2.add(webMenuPojo3);
                                    }
                                }
                                webMenuPojo2.setChildren(lstChil2);
                                lstChil.add(webMenuPojo2);
                            }
                        }
                        webMenuPojo.setChildren(lstChil);
                        list.add(webMenuPojo);
                    }
                }
                //this.redisService.setCacheObject("tenant_manuweb:" + loginUser.getTenantid(), list, (long) (1), TimeUnit.DAYS);
            }
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
