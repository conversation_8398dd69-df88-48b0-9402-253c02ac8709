package inks.system.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.PifunctionPojo;
import inks.system.service.PifunctionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 服务总表(PiFunction)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-29 20:02:54
 */
@RestController
@RequestMapping("SYSM02B1")
@Api(tags = "SYSM02B1:服务总表")
public class SYSM02B1Controller extends PifunctionController {

    @Resource
    private PifunctionService pifunctionService;

    @Resource
    private TokenService tokenService;

    @ApiOperation(value = "根据租户获取服务总表List", notes = "根据租户获取权限表List", produces = "application/json")
    @RequestMapping(value = "/getFunctionListBySelf", method = RequestMethod.GET)
    public R<List<PifunctionPojo>> getFunctionListBySelf() {
        try {

            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            List<PifunctionPojo> lst = this.pifunctionService.getFunctionListBySelf(loginUser.getTenantid());
            return R.ok(lst);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }

    }
}
