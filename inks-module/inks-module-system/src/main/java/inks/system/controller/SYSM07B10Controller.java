package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.domain.pojo.CiwebprinterPojo;
import inks.system.service.CiwebprinterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 网络打印机(CiWebPrinter)表控制层
 *
 * <AUTHOR>
 * @since 2022-03-08 15:45:14
 */
@RestController
@RequestMapping("SYSM07B10")
@Api(tags = "SYSM07B10：网络打印机")
public class SYSM07B10Controller extends CiwebprinterController {
    /**
     * 服务对象
     */
    @Resource
    private CiwebprinterService ciwebprinterService;
    /**
     * 引用Token服务
     */
    @Resource
    private TokenService tokenService;

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getListByModuleCode", method = RequestMethod.GET)
    @PreAuthorize(hasPermi = "CiWebPrinter.List")
    public R<List<CiwebprinterPojo>> getListByModuleCode(String code) {
        LoginUser loginUser = this.tokenService.getLoginUser(ServletUtils.getRequest());
        try {

            return R.ok(this.ciwebprinterService.getListByModuleCode(code, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增网络打印机", notes = "新增网络打印机", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiWebPrinter.Add")
    public R<CiwebprinterPojo> create(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            CiwebprinterPojo ciwebprinterPojo = JSONArray.parseObject(json, CiwebprinterPojo.class);

            ciwebprinterPojo.setCreateby(loginUser.getRealName());   // 创建者
            ciwebprinterPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            ciwebprinterPojo.setCreatedate(new Date());   // 创建时间
            ciwebprinterPojo.setLister(loginUser.getRealname());   // 制表
            ciwebprinterPojo.setListerid(loginUser.getUserid());    // 制表id
            ciwebprinterPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.ciwebprinterService.insert(ciwebprinterPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改网络打印机", notes = "修改网络打印机", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiWebPrinter.Edit")
    public R<CiwebprinterPojo> update(@RequestBody String json) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        try {
            CiwebprinterPojo ciwebprinterPojo = JSONArray.parseObject(json, CiwebprinterPojo.class);

            ciwebprinterPojo.setLister(loginUser.getRealname());   // 制表
            ciwebprinterPojo.setListerid(loginUser.getUserid());    // 制表id
            ciwebprinterPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.ciwebprinterService.update(ciwebprinterPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
