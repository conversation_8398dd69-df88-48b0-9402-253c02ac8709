package inks.system.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.security.annotation.PreAuthorize;
import inks.common.security.service.TokenService;
import inks.system.service.CioperlogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 操作日志(CiOperLog)表控制层
 *
 * <AUTHOR>
 * @since 2021-12-22 13:38:46
 */
@RestController
@RequestMapping("SYSM08B2")
@Api(tags = "SYSM08B2:操作日志")
public class SYSM08B2Controller extends CioperlogController {

    @Resource
    private CioperlogService cioperlogService;

    @Resource
    private TokenService tokenService;


    @ApiOperation(value = "根据时间段删除操作日志 指定租户,不传tid则删所有租户", notes = "根据时间段删除操作日志", produces = "application/json")
    @RequestMapping(value = "/deleteByTime", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "CiOperLog.Delete")
    public R<String> deleteByTime(@RequestBody String json, @RequestParam(required = false) String tid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getDateRange() == null) {
                throw new Exception("DateRange时间范围不能为空");
            }
            LoginUser loginUser = tokenService.getLoginUser();
            if (!"default".equals(loginUser.getTenantid())) {
                throw new Exception("非系统管理员");
            }
            queryParam.setTenantid(tid);
            int i = this.cioperlogService.deleteByTime(queryParam);
            return R.ok("成功删除" + i + "行日志数据");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
