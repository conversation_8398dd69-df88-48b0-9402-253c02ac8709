apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: svc-system
  name: svc-system
  namespace: inksdev   #一定要写名称空间
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  selector:
    matchLabels:
      app: svc-system
  strategy:
    rollingUpdate:
      maxSurge: 50%
      maxUnavailable: 50%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: svc-system
    spec:
      volumes:
        - name: volume-yaml
          configMap:
            name: svc-system-yaml
            items:
              - key: application-prod.yml
                path: application-prod.yml
            defaultMode: 420
      imagePullSecrets:
        - name: aliyun-docker-hub  #提前在项目下配置访问阿里云的账号密码
      containers:
        - image: $REGISTRY/$DOCKERHUB_NAMESPACE/svc-system:SNAPSHOT-$BUILD_NUMBER
          imagePullPolicy: Always
          name: svc-system
          ports:
            - containerPort: 8080
              protocol: TCP
          volumeMounts:
            - name: volume-yaml
              readOnly: true
              mountPath: /home/<USER>/application-prod.yml
              subPath: application-prod.yml
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: svc-system
  name: svc-system
  namespace: inksdev
spec:
  ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 8080
      nodePort: 30012
  selector:
    app: svc-system
  sessionAffinity: None
  type: NodePort